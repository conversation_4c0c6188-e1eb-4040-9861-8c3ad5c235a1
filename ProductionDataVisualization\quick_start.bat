@echo off
title 快速启动测试

echo ========================================
echo   快速启动测试
echo ========================================
echo.

echo [INFO] 当前目录: %cd%
echo [INFO] 切换到前端目录...
cd frontend

echo [INFO] 检查package.json...
if exist package.json (
    echo [SUCCESS] package.json 存在
) else (
    echo [ERROR] package.json 不存在
    pause
    exit /b 1
)

echo [INFO] 检查node_modules...
if exist node_modules (
    echo [SUCCESS] node_modules 存在
) else (
    echo [INFO] 安装依赖...
    npm install
)

echo [INFO] 尝试启动...
start "React App" cmd /k "npm start"

echo [INFO] 等待5秒...
timeout /t 5 /nobreak

echo [INFO] 检查端口3000...
netstat -ano | findstr :3000

echo.
echo [INFO] 如果看到端口3000被占用，说明启动成功
echo [INFO] 请访问 http://localhost:3000
echo.

pause
