# 网络配置工具

本目录包含网络配置和IP地址检测相关的工具。

## 🔧 工具列表

### IP地址自动检测工具
- `auto-detect-ip.bat` - 完整功能的IP检测工具（批处理版本）
- `Auto-DetectIP.ps1` - 功能最强大的IP检测工具（PowerShell版本，推荐）
- `simple-ip-detect.bat` - 简化版IP检测工具

### 网络诊断工具
- `network-config-detector.html` - 可视化网络配置检测器

## 📋 使用方法

### 快速配置（推荐）
```cmd
# 运行简化版工具
simple-ip-detect.bat

# 或运行PowerShell版本（功能更强大）
powershell -ExecutionPolicy Bypass -File Auto-DetectIP.ps1
```

### 网络诊断
访问 `network-config-detector.html` 进行可视化网络诊断。

## 🎯 功能说明

这些工具会自动：
1. 检测当前服务器的IP地址
2. 更新前端配置文件（.env 和 .env.production）
3. 生成自适应启动脚本
4. 解决IP地址变化导致的访问问题
