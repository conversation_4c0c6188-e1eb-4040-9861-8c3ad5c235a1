import React, { useState } from 'react';
import { Form, Input, Button, Alert, Typography, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import authService from '../../services/authService';
import { useNavigate, Link } from 'react-router-dom';

const { Title, Text } = Typography;

const AssanLoginForm = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const onFinish = async (values) => {
    setLoading(true);
    setError('');
    
    try {
      await authService.login(values.username, values.password);
      navigate('/dashboard');
    } catch (err) {
      setError(err.message || '登录失败，请检查用户名和密码');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="assan-login-container">
      {/* 背景装饰 */}
      <div className="background-decoration">
        <div className="decoration-circle circle-1"></div>
        <div className="decoration-circle circle-2"></div>
        <div className="decoration-circle circle-3"></div>
        <div className="decoration-circle circle-4"></div>
        <div className="decoration-circle circle-5"></div>

        {/* 粒子效果 */}
        <div className="particles">
          {[...Array(20)].map((_, i) => (
            <div key={i} className={`particle particle-${i + 1}`}></div>
          ))}
        </div>

        {/* 光线效果 */}
        <div className="light-rays">
          <div className="ray ray-1"></div>
          <div className="ray ray-2"></div>
          <div className="ray ray-3"></div>
        </div>
      </div>

      {/* 主登录卡片 */}
      <motion.div
        className="login-card"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        {/* 头部区域 */}
        <div className="login-header">
          <motion.div
            className="brand-logo"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5, type: "spring" }}
          >
            <div className="logo-icon">
              <UserOutlined />
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
          >
            <Title level={1} className="login-title">
              欢迎回来
            </Title>
            <Text className="login-subtitle">
              登录您的生产数据可视化平台账户
            </Text>
          </motion.div>
        </div>

        {/* 错误提示 */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="error-alert"
          >
            <Alert 
              message={error} 
              type="error" 
              showIcon 
              className="custom-alert"
            />
          </motion.div>
        )}

        {/* 登录表单 */}
        <Form
          name="login"
          form={form}
          onFinish={onFinish}
          layout="vertical"
          className="login-form"
        >
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入用户名' }]}
            >
              <Input 
                prefix={<UserOutlined className="input-icon" />} 
                placeholder="用户名" 
                className="custom-input"
                size="large"
              />
            </Form.Item>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5, duration: 0.6 }}
          >
            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password
                prefix={<LockOutlined className="input-icon" />}
                placeholder="密码"
                className="custom-input"
                size="large"
              />
            </Form.Item>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="form-options"
          >
            <Form.Item name="remember" valuePropName="checked">
              <Checkbox className="remember-checkbox">
                记住我
              </Checkbox>
            </Form.Item>
            <Link to="/forgot-password" className="forgot-link">
              忘记密码？
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.6 }}
          >
            <Button
              type="primary"
              htmlType="submit"
              block
              loading={loading}
              className="login-button"
              size="large"
              icon={<LoginOutlined />}
            >
              登录
            </Button>
          </motion.div>
        </Form>

        {/* 注册链接 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.6 }}
          className="register-section"
        >
          <Text className="register-text">
            还没有账户？
            <Link to="/register" className="register-link">
              立即注册
            </Link>
          </Text>
        </motion.div>
      </motion.div>

      {/* Assan风格样式 */}
      <style jsx="true">{`
        .assan-login-container {
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
            linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          position: relative;
          overflow: hidden;
          padding: 20px;
        }

        .background-decoration {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
        }

        .decoration-circle {
          position: absolute;
          border-radius: 50%;
          background: linear-gradient(45deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          animation: float 8s ease-in-out infinite;
          box-shadow:
            0 8px 32px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .circle-1 {
          width: 300px;
          height: 300px;
          top: 5%;
          left: 5%;
          animation-delay: 0s;
        }

        .circle-2 {
          width: 200px;
          height: 200px;
          top: 55%;
          right: 10%;
          animation-delay: 2.5s;
        }

        .circle-3 {
          width: 150px;
          height: 150px;
          bottom: 15%;
          left: 15%;
          animation-delay: 5s;
        }

        .circle-4 {
          width: 80px;
          height: 80px;
          top: 30%;
          right: 25%;
          animation-delay: 1.5s;
        }

        .circle-5 {
          width: 120px;
          height: 120px;
          bottom: 40%;
          right: 5%;
          animation-delay: 3.5s;
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg) scale(1);
            opacity: 0.7;
          }
          33% {
            transform: translateY(-30px) rotate(120deg) scale(1.1);
            opacity: 0.9;
          }
          66% {
            transform: translateY(-15px) rotate(240deg) scale(0.9);
            opacity: 0.8;
          }
        }

        .login-card {
          width: 100%;
          max-width: 480px;
          background:
            linear-gradient(145deg,
              rgba(255, 255, 255, 0.95) 0%,
              rgba(248, 250, 252, 0.9) 50%,
              rgba(255, 255, 255, 0.95) 100%
            );
          backdrop-filter: blur(25px);
          border-radius: 24px;
          padding: 48px;
          box-shadow:
            0 32px 64px rgba(102, 126, 234, 0.15),
            0 16px 32px rgba(118, 75, 162, 0.1),
            0 8px 16px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.9),
            inset 0 -1px 0 rgba(255, 255, 255, 0.5);
          border: 1px solid rgba(255, 255, 255, 0.8);
          position: relative;
          z-index: 1;
          overflow: hidden;
        }

        .login-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
          animation: shimmer 3s infinite;
          pointer-events: none;
        }

        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }

        .login-header {
          text-align: center;
          margin-bottom: 40px;
        }

        .brand-logo {
          margin-bottom: 24px;
        }

        .logo-icon {
          width: 88px;
          height: 88px;
          background:
            linear-gradient(135deg, #667eea 0%, #764ba2 100%),
            linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto;
          box-shadow:
            0 12px 40px rgba(102, 126, 234, 0.4),
            0 4px 12px rgba(118, 75, 162, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
          font-size: 36px;
          color: white;
          position: relative;
          overflow: hidden;
        }

        .logo-icon::before {
          content: '';
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
          animation: logoShine 2s infinite;
        }

        @keyframes logoShine {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        /* 粒子效果 */
        .particles {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
        }

        .particle {
          position: absolute;
          width: 4px;
          height: 4px;
          background: rgba(255, 255, 255, 0.6);
          border-radius: 50%;
          animation: particleFloat 15s infinite linear;
        }

        .particle:nth-child(odd) {
          background: rgba(102, 126, 234, 0.4);
          animation-duration: 20s;
        }

        .particle:nth-child(3n) {
          background: rgba(118, 75, 162, 0.4);
          animation-duration: 25s;
        }

        @keyframes particleFloat {
          0% {
            transform: translateY(100vh) translateX(0) rotate(0deg);
            opacity: 0;
          }
          10% {
            opacity: 1;
          }
          90% {
            opacity: 1;
          }
          100% {
            transform: translateY(-100px) translateX(100px) rotate(360deg);
            opacity: 0;
          }
        }

        /* 光线效果 */
        .light-rays {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
        }

        .ray {
          position: absolute;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
          animation: rayMove 8s infinite ease-in-out;
        }

        .ray-1 {
          width: 2px;
          height: 100%;
          left: 20%;
          animation-delay: 0s;
        }

        .ray-2 {
          width: 1px;
          height: 100%;
          left: 60%;
          animation-delay: 2s;
        }

        .ray-3 {
          width: 3px;
          height: 100%;
          left: 80%;
          animation-delay: 4s;
        }

        @keyframes rayMove {
          0%, 100% {
            opacity: 0;
            transform: translateX(-50px);
          }
          50% {
            opacity: 1;
            transform: translateX(50px);
          }
        }

        /* 粒子随机位置 */
        .particle-1 { left: 10%; animation-delay: 0s; }
        .particle-2 { left: 20%; animation-delay: 1s; }
        .particle-3 { left: 30%; animation-delay: 2s; }
        .particle-4 { left: 40%; animation-delay: 3s; }
        .particle-5 { left: 50%; animation-delay: 4s; }
        .particle-6 { left: 60%; animation-delay: 5s; }
        .particle-7 { left: 70%; animation-delay: 6s; }
        .particle-8 { left: 80%; animation-delay: 7s; }
        .particle-9 { left: 90%; animation-delay: 8s; }
        .particle-10 { left: 15%; animation-delay: 9s; }
        .particle-11 { left: 25%; animation-delay: 10s; }
        .particle-12 { left: 35%; animation-delay: 11s; }
        .particle-13 { left: 45%; animation-delay: 12s; }
        .particle-14 { left: 55%; animation-delay: 13s; }
        .particle-15 { left: 65%; animation-delay: 14s; }
        .particle-16 { left: 75%; animation-delay: 15s; }
        .particle-17 { left: 85%; animation-delay: 16s; }
        .particle-18 { left: 95%; animation-delay: 17s; }
        .particle-19 { left: 5%; animation-delay: 18s; }
        .particle-20 { left: 95%; animation-delay: 19s; }

        .login-title {
          font-size: 36px !important;
          font-weight: 700 !important;
          background: linear-gradient(135deg, #667eea, #764ba2) !important;
          -webkit-background-clip: text !important;
          -webkit-text-fill-color: transparent !important;
          background-clip: text !important;
          margin-bottom: 12px !important;
          line-height: 1.2 !important;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        }

        .login-subtitle {
          font-size: 16px;
          color: #718096;
          line-height: 1.5;
        }

        .error-alert {
          margin-bottom: 24px;
        }

        .custom-alert {
          border-radius: 12px;
          border: none;
          background: rgba(254, 242, 242, 0.9);
        }

        .login-form {
          margin-bottom: 24px;
        }

        .custom-input {
          height: 56px !important;
          border-radius: 12px !important;
          border: 2px solid #e2e8f0 !important;
          background: rgba(255, 255, 255, 0.8) !important;
          font-size: 16px !important;
          transition: all 0.3s ease !important;
        }

        .custom-input:hover,
        .custom-input:focus {
          border-color: #667eea !important;
          box-shadow:
            0 0 0 3px rgba(102, 126, 234, 0.15) !important,
            0 4px 20px rgba(102, 126, 234, 0.1) !important,
            inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
          background: rgba(255, 255, 255, 0.98) !important;
          transform: translateY(-1px) !important;
        }

        .input-icon {
          color: #a0aec0;
          font-size: 18px;
        }

        .form-options {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 32px;
        }

        .remember-checkbox {
          color: #4a5568;
        }

        .forgot-link {
          color: #667eea;
          text-decoration: none;
          font-weight: 500;
          transition: color 0.3s ease;
        }

        .forgot-link:hover {
          color: #5a67d8;
        }

        .login-button {
          height: 56px !important;
          border-radius: 12px !important;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          border: none !important;
          font-size: 16px !important;
          font-weight: 600 !important;
          box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3) !important;
          transition: all 0.3s ease !important;
        }

        .login-button:hover {
          transform: translateY(-3px) scale(1.02) !important;
          box-shadow:
            0 12px 30px rgba(102, 126, 234, 0.5) !important,
            0 6px 15px rgba(118, 75, 162, 0.3) !important;
          background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
        }

        .login-button:active {
          transform: translateY(-1px) scale(0.98) !important;
          transition: all 0.1s ease !important;
        }

        .register-section {
          text-align: center;
          padding-top: 24px;
          border-top: 1px solid #e2e8f0;
        }

        .register-text {
          color: #718096;
          font-size: 15px;
        }

        .register-link {
          color: #667eea;
          text-decoration: none;
          font-weight: 600;
          margin-left: 8px;
          transition: color 0.3s ease;
        }

        .register-link:hover {
          color: #5a67d8;
        }

        @media (max-width: 480px) {
          .login-card {
            padding: 32px 24px;
            margin: 20px;
          }
          
          .login-title {
            font-size: 28px !important;
          }
          
          .form-options {
            flex-direction: column;
            gap: 16px;
            align-items: flex-start;
          }
        }
      `}</style>
    </div>
  );
};

export default AssanLoginForm;
