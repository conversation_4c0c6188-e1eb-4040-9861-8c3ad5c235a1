# 生产数据可视化系统

## 📋 项目概述

这是一个基于React和.NET Core的生产数据可视化系统，专为远大医药天天明制药有限公司的眼药制造偏差报告系统设计。

### 🏢 公司信息
- **公司名称**: 远大医药天天明制药有限公司
- **业务领域**: 眼药制造
- **系统用途**: 生产偏差报告和数据可视化

## 🚀 快速开始

### 📋 系统要求

- **操作系统**: Windows 10/11
- **数据库**: SQL Server (本地安装)
- **Node.js**: 16.0+ 
- **.NET**: 6.0+
- **浏览器**: Chrome, Firefox, Edge (最新版本)

### ⚡ 一键启动

1. **克隆项目**
   ```cmd
   git clone [项目地址]
   cd ProductionDataVisualization
   ```

2. **自动配置网络**
   ```cmd
   # 方法1: 使用PowerShell (推荐)
   powershell -ExecutionPolicy Bypass -File tools/network/Auto-DetectIP.ps1
   
   # 方法2: 使用批处理
   tools/network/simple-ip-detect.bat
   ```

3. **启动系统**
   ```cmd
   # 启动后端服务
   scripts/start_system.bat
   
   # 或使用自适应启动脚本
   start-adaptive.bat
   ```

4. **访问系统**
   - 本机访问: http://localhost:3000
   - 网络访问: http://[您的IP]:3000

## 📁 项目结构

```
ProductionDataVisualization/
├── frontend/                 # React前端应用
│   ├── src/                 # 源代码
│   ├── public/              # 静态资源
│   └── package.json         # 依赖配置
├── backend/                 # .NET Core后端API
│   ├── SqlServerAPI/        # SQL Server API项目
│   └── database/            # 数据库相关文件
├── tools/                   # 工具和实用程序
│   ├── network/             # 网络配置工具
│   └── testing/             # 测试工具
├── scripts/                 # 启动脚本
├── docs/                    # 文档
└── logs/                    # 日志文件
```

## 🔧 详细配置

### 🌐 网络配置

系统支持自动IP地址检测，解决网络变化问题：

#### 自动配置 (推荐)
```cmd
# PowerShell版本 (功能最强大)
powershell -ExecutionPolicy Bypass -File tools/network/Auto-DetectIP.ps1

# 简化版本
tools/network/simple-ip-detect.bat
```

#### 手动配置
编辑 `frontend/.env.production` 文件：
```env
REACT_APP_API_URL=http://[您的IP地址]:5000
REACT_APP_ENVIRONMENT=production
```

### 🗄️ 数据库配置

系统使用SQL Server作为数据存储：

1. **确保SQL Server已安装并运行**
2. **系统会自动创建数据库和表结构**
3. **默认连接字符串**: `Server=.;Database=ProductionDataVisualizationDb;Integrated Security=true;`

### 👥 用户管理

#### 默认用户账户
- **管理员**: admin / admin123
- **测试用户**: testuser / test123

#### 添加新用户
1. 登录系统后访问"用户管理"页面
2. 点击"新增用户"按钮
3. 填写用户信息并保存

## 🛠️ 开发指南

### 🔨 本地开发

1. **安装依赖**
   ```cmd
   # 前端依赖
   cd frontend
   npm install
   
   # 后端依赖 (自动管理)
   cd ../backend/SqlServerAPI
   dotnet restore
   ```

2. **启动开发服务器**
   ```cmd
   # 启动后端 (端口5000)
   cd backend/SqlServerAPI
   dotnet run
   
   # 启动前端 (端口3000)
   cd frontend
   npm start
   ```

### 🧪 测试工具

系统提供多种测试工具：

#### 网络诊断
- **网络配置检测器**: `tools/testing/network-config-detector.html`
- **功能**: 自动检测网络配置，测试API连接

#### 功能测试
- **登录测试**: `tools/testing/test-login-live.html`
- **移动端测试**: `tools/testing/mobile-permissions-test.html`

### 📊 API文档

#### 认证API
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出

#### 用户管理API
- `GET /api/simple-auth/users` - 获取用户列表
- `POST /api/simple-auth/register` - 注册新用户

#### 数据API
- `GET /api/data/production` - 获取生产数据
- `POST /api/data/deviation` - 提交偏差报告

## 🚨 故障排除

### 常见问题

#### 1. 其他电脑无法访问系统
**解决方案**: 运行网络配置工具
```cmd
tools/network/simple-ip-detect.bat
```

#### 2. 用户管理页面显示用户数量不正确
**原因**: IP地址配置问题
**解决方案**: 重新运行网络配置工具并重启前端服务

#### 3. 数据库连接失败
**检查项目**:
- SQL Server服务是否运行
- 连接字符串是否正确
- 数据库权限是否足够

#### 4. 前端编译错误
**解决方案**:
```cmd
cd frontend
npm install
npm start
```

### 🔍 日志查看

系统日志位于 `logs/` 目录：
- `backend.log` - 后端服务日志
- `frontend_dev.log` - 前端开发日志
- `system_start.log` - 系统启动日志

## 🚀 部署指南

### 📦 生产环境部署

#### 1. 服务器准备
- Windows Server 2016+ 或 Windows 10/11
- SQL Server 2017+ (Express版本即可)
- IIS 10+ (可选，用于生产部署)

#### 2. 部署步骤
```cmd
# 1. 复制项目文件到服务器
# 2. 运行网络配置
tools/network/Auto-DetectIP.ps1

# 3. 启动服务
scripts/start_system.bat

# 4. 验证部署
# 访问 http://[服务器IP]:3000
```

#### 3. 防火墙配置
确保以下端口开放：
- **3000** - 前端服务
- **5000** - 后端API
- **1433** - SQL Server (如需远程访问)

### 🔄 系统维护

#### 定期维护任务
- **日志清理**: 定期清理 `logs/` 目录
- **数据备份**: 备份SQL Server数据库
- **系统更新**: 更新依赖包和安全补丁

#### 监控检查
- 服务运行状态
- 磁盘空间使用
- 数据库性能
- 网络连接状态

## 📞 技术支持

### 🆘 获取帮助

1. **查看日志文件** - 检查 `logs/` 目录中的相关日志
2. **运行诊断工具** - 使用 `tools/testing/` 中的测试工具
3. **检查网络配置** - 运行网络配置检测器

### 📋 报告问题

报告问题时请提供：
- 操作系统版本
- 浏览器类型和版本
- 错误信息截图
- 相关日志文件内容

## 📄 许可证

本项目为远大医药天天明制药有限公司内部使用系统。

---

**版本**: 1.0.0
**最后更新**: 2024年
**维护团队**: 系统开发团队
