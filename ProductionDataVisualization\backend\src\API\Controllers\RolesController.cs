using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Domain.UserAggregate;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace API.Controllers
{
    /// <summary>
    /// 角色管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")]
    public class RolesController : ControllerBase
    {
        private readonly IRoleRepository _roleRepository;
        private readonly IPermissionRepository _permissionRepository;
        private readonly ILogger<RolesController> _logger;

        public RolesController(
            IRoleRepository roleRepository,
            IPermissionRepository permissionRepository,
            ILogger<RolesController> logger)
        {
            _roleRepository = roleRepository;
            _permissionRepository = permissionRepository;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有角色
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetRoles()
        {
            try
            {
                var roles = await _roleRepository.GetAllAsync();
                var roleDtos = roles.Select(r => new
                {
                    Id = r.Id,
                    Name = r.Name,
                    Description = r.Description,
                    CreatedAt = r.CreatedAt,
                    ModifiedAt = r.ModifiedAt
                });

                return Ok(roleDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取角色列表失败");
                return StatusCode(500, new { message = "获取角色列表失败，请稍后再试" });
            }
        }

        /// <summary>
        /// 获取角色详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetRole(Guid id)
        {
            try
            {
                var role = await _roleRepository.GetByIdAsync(id);
                if (role == null)
                {
                    return NotFound(new { message = "角色不存在" });
                }

                var permissions = await _permissionRepository.GetRolePermissionsAsync(role.Id);
                var permissionDtos = permissions.Select(p => new
                {
                    Id = p.Id,
                    Name = p.Name,
                    Description = p.Description,
                    Code = p.Code
                }).ToList();

                var roleDto = new
                {
                    Id = role.Id,
                    Name = role.Name,
                    Description = role.Description,
                    Permissions = permissionDtos,
                    CreatedAt = role.CreatedAt,
                    ModifiedAt = role.ModifiedAt
                };

                return Ok(roleDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取角色详情失败");
                return StatusCode(500, new { message = "获取角色详情失败，请稍后再试" });
            }
        }

        /// <summary>
        /// 创建角色
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> CreateRole([FromBody] CreateRoleRequest request)
        {
            try
            {
                // 检查角色名称是否已存在
                var existingRole = await _roleRepository.FindByNameAsync(request.Name);
                if (existingRole != null)
                {
                    return BadRequest(new { message = "角色名称已存在" });
                }

                // 创建角色
                var role = new Role(request.Name, request.Description);

                // 保存角色
                await _roleRepository.AddAsync(role);

                // 分配权限
                if (request.PermissionIds != null && request.PermissionIds.Any())
                {
                    foreach (var permissionId in request.PermissionIds)
                    {
                        var permission = await _permissionRepository.GetByIdAsync(permissionId);
                        if (permission != null)
                        {
                            role.AddPermission(permission);
                        }
                    }
                }

                await _roleRepository.UnitOfWork.SaveChangesAsync();

                _logger.LogInformation("角色创建成功: {Name}", role.Name);

                return CreatedAtAction(nameof(GetRole), new { id = role.Id }, new { id = role.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建角色失败");
                return StatusCode(500, new { message = "创建角色失败，请稍后再试" });
            }
        }

        /// <summary>
        /// 更新角色
        /// </summary>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateRole(Guid id, [FromBody] UpdateRoleRequest request)
        {
            try
            {
                var role = await _roleRepository.GetByIdAsync(id);
                if (role == null)
                {
                    return NotFound(new { message = "角色不存在" });
                }

                // 如果要更改角色名称，检查名称是否已被其他角色使用
                if (request.Name != role.Name)
                {
                    var existingRole = await _roleRepository.FindByNameAsync(request.Name);
                    if (existingRole != null && existingRole.Id != id)
                    {
                        return BadRequest(new { message = "角色名称已被其他角色使用" });
                    }
                }

                // 更新角色信息
                role.Update(request.Name, request.Description);
                await _roleRepository.UpdateAsync(role);
                await _roleRepository.UnitOfWork.SaveChangesAsync();

                _logger.LogInformation("角色更新成功: {Name}", role.Name);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新角色失败");
                return StatusCode(500, new { message = "更新角色失败，请稍后再试" });
            }
        }

        /// <summary>
        /// 删除角色
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteRole(Guid id)
        {
            try
            {
                var role = await _roleRepository.GetByIdAsync(id);
                if (role == null)
                {
                    return NotFound(new { message = "角色不存在" });
                }

                // 检查是否为预定义角色
                if (id == Role.AdminRoleId || id == Role.DataImporterRoleId || id == Role.ViewerRoleId)
                {
                    return BadRequest(new { message = "预定义角色不能删除" });
                }

                await _roleRepository.DeleteAsync(role);
                await _roleRepository.UnitOfWork.SaveChangesAsync();

                _logger.LogInformation("角色删除成功: {Name}", role.Name);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除角色失败");
                return StatusCode(500, new { message = "删除角色失败，请稍后再试" });
            }
        }

        /// <summary>
        /// 更新角色权限
        /// </summary>
        [HttpPut("{id}/permissions")]
        public async Task<IActionResult> UpdateRolePermissions(Guid id, [FromBody] UpdateRolePermissionsRequest request)
        {
            try
            {
                var role = await _roleRepository.GetByIdAsync(id);
                if (role == null)
                {
                    return NotFound(new { message = "角色不存在" });
                }

                // 获取当前角色的权限
                var currentPermissions = await _permissionRepository.GetRolePermissionsAsync(role.Id);
                
                // 移除不在请求中的权限
                foreach (var permission in currentPermissions)
                {
                    if (!request.PermissionIds.Contains(permission.Id))
                    {
                        role.RemovePermission(permission);
                    }
                }

                // 添加新的权限
                foreach (var permissionId in request.PermissionIds)
                {
                    if (!currentPermissions.Any(p => p.Id == permissionId))
                    {
                        var permission = await _permissionRepository.GetByIdAsync(permissionId);
                        if (permission != null)
                        {
                            role.AddPermission(permission);
                        }
                    }
                }

                await _roleRepository.UnitOfWork.SaveChangesAsync();

                _logger.LogInformation("角色权限更新成功: {Name}", role.Name);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新角色权限失败");
                return StatusCode(500, new { message = "更新角色权限失败，请稍后再试" });
            }
        }
    }

    /// <summary>
    /// 创建角色请求
    /// </summary>
    public class CreateRoleRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<Guid>? PermissionIds { get; set; }
    }

    /// <summary>
    /// 更新角色请求
    /// </summary>
    public class UpdateRoleRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 更新角色权限请求
    /// </summary>
    public class UpdateRolePermissionsRequest
    {
        public List<Guid> PermissionIds { get; set; } = new List<Guid>();
    }
} 