import React, { useState } from 'react';
import { Form, Input, Button, Alert, Typography } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, UserAddOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import authService from '../../services/authService';
import { useNavigate, Link } from 'react-router-dom';

const { Title, Text } = Typography;

const RegisterForm = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const onFinish = async (values) => {
    if (values.password !== values.confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }
    
    setLoading(true);
    setError('');
    setSuccess('');
    
    try {
      await authService.register(
        values.username, 
        values.email, 
        values.password, 
        values.fullName
      );
      
      setSuccess('注册成功！即将跳转到登录页面...');
      
      // 成功动画
      const successAnimation = {
        scale: [1, 1.02, 1],
        opacity: [1, 0.8, 0],
        transition: { duration: 0.5 }
      };
      
      // 3秒后跳转到登录页面
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (err) {
      setError(err.message || '注册失败，请稍后再试');
      // 错误抖动动画
      const formElement = document.querySelector('.assan-form');
      if (formElement) {
        formElement.classList.add('shake-animation');
        setTimeout(() => {
          formElement.classList.remove('shake-animation');
        }, 500);
      }
    } finally {
      setLoading(false);
    }
  };

  // 表单项动画变体
  const formItemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.3 + i * 0.1,
        duration: 0.5,
        ease: "easeOut"
      }
    })
  };

  // 现代简约风格样式
  const styles = {
    registerContainer: {
      width: '100%',
      maxWidth: '420px',
      padding: '40px 30px',
      borderRadius: '16px',
      backgroundColor: 'rgba(255, 255, 255, 1)',
      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
      position: 'relative',
      overflow: 'hidden',
      zIndex: 1
    },
    formHeader: {
      marginBottom: '32px',
      textAlign: 'center',
      position: 'relative'
    },
    logoContainer: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: '16px'
    },
    logo: {
      width: '60px',
      height: '60px',
      borderRadius: '50%',
      background: 'linear-gradient(135deg, #333333, #666666)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      boxShadow: '0 0 15px rgba(0, 0, 0, 0.2)',
      position: 'relative',
      overflow: 'hidden'
    },
    logoIcon: {
      fontSize: '32px',
      color: 'white',
      zIndex: 2
    },
    logoGlow: {
      position: 'absolute',
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      background: 'radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3) 0%, transparent 70%)',
      zIndex: 1
    },
    title: {
      color: '#333',
      fontSize: '28px',
      fontWeight: '600',
      marginBottom: '8px'
    },
    subtitle: {
      color: '#666',
      fontSize: '16px',
      fontWeight: '400'
    },
    inputContainer: {
      marginBottom: '24px',
      position: 'relative'
    },
    input: {
      height: '50px',
      fontSize: '16px',
      borderRadius: '8px',
      border: '1px solid #E0E0E0',
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
      transition: 'all 0.25s ease'
    },
    inputIcon: {
      color: '#666666',
      fontSize: '18px'
    },
    registerButton: {
      height: '50px',
      fontSize: '16px',
      fontWeight: '500',
      borderRadius: '8px',
      background: 'linear-gradient(135deg, #333333, #666666)',
      border: 'none',
      boxShadow: '0 2px 5px rgba(0, 0, 0, 0.2)',
      position: 'relative',
      overflow: 'hidden',
      transition: 'all 0.25s ease'
    },
    buttonGlow: {
      position: 'absolute',
      top: '0',
      left: '-100%',
      width: '100%',
      height: '100%',
      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
      zIndex: 1
    },
    buttonText: {
      position: 'relative',
      zIndex: 2
    },
    loginText: {
      marginTop: '24px',
      textAlign: 'center',
      fontSize: '14px',
      color: '#666'
    },
    loginLink: {
      color: '#333333',
      fontWeight: '500',
      marginLeft: '4px',
      transition: 'all 0.15s ease'
    }
  };

  // 标题动画变体
  const titleVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };
  
  // 子标题动画变体
  const subtitleVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: {
        duration: 0.5,
        delay: 0.2
      }
    }
  };
  
  // 按钮动画变体
  const buttonVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        duration: 0.5,
        delay: 0.6
      }
    },
    hover: {
      scale: 1.02,
      boxShadow: '0 4px 10px rgba(0, 0, 0, 0.3)',
      transition: { duration: 0.3 }
    },
    tap: {
      scale: 0.98,
      boxShadow: '0 2px 5px rgba(0, 0, 0, 0.2)',
      transition: { duration: 0.1 }
    }
  };
  
  // 按钮光效动画
  const buttonGlowVariants = {
    initial: { 
      x: "-100%"
    },
    animate: { 
      x: "200%",
      transition: { 
        repeat: Infinity,
        repeatDelay: 3,
        duration: 1.5,
        ease: "easeInOut"
      }
    }
  };

  // Logo动画
  const logoVariants = {
    hidden: { opacity: 0, scale: 0.8, rotate: -10 },
    visible: { 
      opacity: 1, 
      scale: 1,
      rotate: 0,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 15,
        delay: 0.1
      }
    },
    hover: {
      scale: 1.05,
      rotate: 5,
      boxShadow: '0 0 15px rgba(0, 0, 0, 0.3)',
      transition: { duration: 0.3 }
    }
  };

  return (
    <div className="register-form-container assan-form" style={styles.registerContainer}>
      <div className="register-header" style={styles.formHeader}>
        <div style={styles.logoContainer}>
          <motion.div
            style={styles.logo}
            variants={logoVariants}
            initial="hidden"
            animate="visible"
            whileHover="hover"
          >
            <div style={styles.logoGlow}></div>
            <UserAddOutlined style={styles.logoIcon} />
          </motion.div>
        </div>
        
        <motion.div
          variants={titleVariants}
          initial="hidden"
          animate="visible"
        >
          <Title level={2} style={styles.title}>创建账户</Title>
        </motion.div>
        
        <motion.div
          variants={subtitleVariants}
          initial="hidden"
          animate="visible"
        >
          <Text style={styles.subtitle}>加入我们，开始您的职业之旅</Text>
        </motion.div>
      </div>
      
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Alert 
            message={error} 
            type="error" 
            showIcon 
            style={{
              marginBottom: '24px',
              borderRadius: '8px',
              border: '1px solid rgba(239, 68, 68, 0.2)',
              background: 'rgba(239, 68, 68, 0.05)'
            }}
            action={
              <Button size="small" type="text" onClick={() => setError('')}>
                关闭
              </Button>
            }
          />
        </motion.div>
      )}
      
      {success && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Alert 
            message={success} 
            type="success" 
            showIcon 
            style={{
              marginBottom: '24px',
              borderRadius: '8px',
              border: '1px solid rgba(34, 197, 94, 0.2)',
              background: 'rgba(34, 197, 94, 0.05)'
            }}
          />
        </motion.div>
      )}
      
      <Form
        name="register"
        form={form}
        initialValues={{ remember: true }}
        onFinish={onFinish}
        size="large"
        layout="vertical"
      >
        <motion.div
          custom={0}
          variants={formItemVariants}
          initial="hidden"
          animate="visible"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名!' },
              { min: 3, message: '用户名至少3个字符' },
              { max: 20, message: '用户名最多20个字符' }
            ]}
            style={styles.inputContainer}
          >
            <Input 
              prefix={<UserOutlined style={styles.inputIcon} />} 
              placeholder="用户名" 
              style={styles.input}
              className="register-input"
            />
          </Form.Item>
        </motion.div>
        
        <motion.div
          custom={1}
          variants={formItemVariants}
          initial="hidden"
          animate="visible"
        >
          <Form.Item
            name="email"
            rules={[
              { required: true, message: '请输入邮箱!' },
              { type: 'email', message: '请输入有效的邮箱地址!' }
            ]}
            style={styles.inputContainer}
          >
            <Input 
              prefix={<MailOutlined style={styles.inputIcon} />} 
              placeholder="邮箱" 
              style={styles.input}
              className="register-input"
            />
          </Form.Item>
        </motion.div>
        
        <motion.div
          custom={2}
          variants={formItemVariants}
          initial="hidden"
          animate="visible"
        >
          <Form.Item
            name="fullName"
            rules={[{ required: true, message: '请输入姓名!' }]}
            style={styles.inputContainer}
          >
            <Input 
              prefix={<UserAddOutlined style={styles.inputIcon} />} 
              placeholder="姓名" 
              style={styles.input}
              className="register-input"
            />
          </Form.Item>
        </motion.div>
        
        <motion.div
          custom={3}
          variants={formItemVariants}
          initial="hidden"
          animate="visible"
        >
          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码!' },
              { min: 6, message: '密码至少6个字符' }
            ]}
            style={styles.inputContainer}
          >
            <Input.Password
              prefix={<LockOutlined style={styles.inputIcon} />}
              placeholder="密码"
              style={styles.input}
              className="register-input"
            />
          </Form.Item>
        </motion.div>
        
        <motion.div
          custom={4}
          variants={formItemVariants}
          initial="hidden"
          animate="visible"
        >
          <Form.Item
            name="confirmPassword"
            rules={[
              { required: true, message: '请确认密码!' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致!'));
                },
              }),
            ]}
            style={styles.inputContainer}
          >
            <Input.Password
              prefix={<LockOutlined style={styles.inputIcon} />}
              placeholder="确认密码"
              style={styles.input}
              className="register-input"
            />
          </Form.Item>
        </motion.div>
        
        <motion.div
          variants={buttonVariants}
          initial="hidden"
          animate="visible"
          whileHover="hover"
          whileTap="tap"
        >
          <Button
            type="primary"
            htmlType="submit"
            block
            loading={loading}
            style={styles.registerButton}
            icon={<ArrowRightOutlined />}
          >
            <motion.div
              style={styles.buttonGlow}
              variants={buttonGlowVariants}
              initial="initial"
              animate="animate"
            />
            <span style={styles.buttonText}>注册</span>
          </Button>
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          <div style={styles.loginText}>
            <Text>已有账号?</Text>
            <Link to="/login" style={styles.loginLink}>
              立即登录
            </Link>
          </div>
        </motion.div>
      </Form>
      
      <style jsx="true">{`
        .register-input:focus {
          border-color: #666666;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .shake-animation {
          animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
        }
        
        @keyframes shake {
          10%, 90% { transform: translateX(-1px); }
          20%, 80% { transform: translateX(2px); }
          30%, 50%, 70% { transform: translateX(-4px); }
          40%, 60% { transform: translateX(4px); }
        }
      `}</style>
    </div>
  );
};

export default RegisterForm; 