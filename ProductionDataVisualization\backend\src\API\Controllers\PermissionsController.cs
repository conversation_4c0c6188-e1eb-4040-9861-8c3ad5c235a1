using System;
using System.Linq;
using System.Threading.Tasks;
using Domain.UserAggregate;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace API.Controllers
{
    /// <summary>
    /// 权限管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")]
    public class PermissionsController : ControllerBase
    {
        private readonly IPermissionRepository _permissionRepository;
        private readonly ILogger<PermissionsController> _logger;

        public PermissionsController(
            IPermissionRepository permissionRepository,
            ILogger<PermissionsController> logger)
        {
            _permissionRepository = permissionRepository;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有权限
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetPermissions()
        {
            try
            {
                var permissions = await _permissionRepository.GetAllAsync();
                var permissionDtos = permissions.Select(p => new
                {
                    Id = p.Id,
                    Name = p.Name,
                    Description = p.Description,
                    Code = p.Code,
                    CreatedAt = p.CreatedAt,
                    ModifiedAt = p.ModifiedAt
                });

                return Ok(permissionDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取权限列表失败");
                return StatusCode(500, new { message = "获取权限列表失败，请稍后再试" });
            }
        }

        /// <summary>
        /// 获取权限详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetPermission(Guid id)
        {
            try
            {
                var permission = await _permissionRepository.GetByIdAsync(id);
                if (permission == null)
                {
                    return NotFound(new { message = "权限不存在" });
                }

                var permissionDto = new
                {
                    Id = permission.Id,
                    Name = permission.Name,
                    Description = permission.Description,
                    Code = permission.Code,
                    CreatedAt = permission.CreatedAt,
                    ModifiedAt = permission.ModifiedAt
                };

                return Ok(permissionDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取权限详情失败");
                return StatusCode(500, new { message = "获取权限详情失败，请稍后再试" });
            }
        }

        /// <summary>
        /// 创建权限
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> CreatePermission([FromBody] CreatePermissionRequest request)
        {
            try
            {
                // 检查权限名称是否已存在
                var existingPermission = await _permissionRepository.GetByNameAsync(request.Name);
                if (existingPermission != null)
                {
                    return BadRequest(new { message = "权限名称已存在" });
                }

                // 创建权限
                var permission = new Permission(request.Name, request.Description, request.Code);

                // 保存权限
                await _permissionRepository.AddAsync(permission);
                await _permissionRepository.UnitOfWork.SaveChangesAsync();

                _logger.LogInformation("权限创建成功: {Name}", permission.Name);

                return CreatedAtAction(nameof(GetPermission), new { id = permission.Id }, new { id = permission.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建权限失败");
                return StatusCode(500, new { message = "创建权限失败，请稍后再试" });
            }
        }

        /// <summary>
        /// 更新权限
        /// </summary>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdatePermission(Guid id, [FromBody] UpdatePermissionRequest request)
        {
            try
            {
                var permission = await _permissionRepository.GetByIdAsync(id);
                if (permission == null)
                {
                    return NotFound(new { message = "权限不存在" });
                }

                // 检查是否为预定义权限
                if (id == Permission.ManageUsersPermissionId || 
                    id == Permission.ImportDataPermissionId || 
                    id == Permission.ViewDashboardsPermissionId || 
                    id == Permission.ManageChartsPermissionId || 
                    id == Permission.ManageSystemPermissionId)
                {
                    return BadRequest(new { message = "预定义权限不能修改" });
                }

                // 如果要更改权限名称，检查名称是否已被其他权限使用
                if (request.Name != permission.Name)
                {
                    var existingPermission = await _permissionRepository.GetByNameAsync(request.Name);
                    if (existingPermission != null && existingPermission.Id != id)
                    {
                        return BadRequest(new { message = "权限名称已被其他权限使用" });
                    }
                }

                // 更新权限信息
                permission.Update(request.Name, request.Description);
                await _permissionRepository.UpdateAsync(permission);
                await _permissionRepository.UnitOfWork.SaveChangesAsync();

                _logger.LogInformation("权限更新成功: {Name}", permission.Name);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新权限失败");
                return StatusCode(500, new { message = "更新权限失败，请稍后再试" });
            }
        }

        /// <summary>
        /// 删除权限
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeletePermission(Guid id)
        {
            try
            {
                var permission = await _permissionRepository.GetByIdAsync(id);
                if (permission == null)
                {
                    return NotFound(new { message = "权限不存在" });
                }

                // 检查是否为预定义权限
                if (id == Permission.ManageUsersPermissionId || 
                    id == Permission.ImportDataPermissionId || 
                    id == Permission.ViewDashboardsPermissionId || 
                    id == Permission.ManageChartsPermissionId || 
                    id == Permission.ManageSystemPermissionId)
                {
                    return BadRequest(new { message = "预定义权限不能删除" });
                }

                await _permissionRepository.DeleteAsync(permission);
                await _permissionRepository.UnitOfWork.SaveChangesAsync();

                _logger.LogInformation("权限删除成功: {Name}", permission.Name);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除权限失败");
                return StatusCode(500, new { message = "删除权限失败，请稍后再试" });
            }
        }
    }

    /// <summary>
    /// 创建权限请求
    /// </summary>
    public class CreatePermissionRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
    }

    /// <summary>
    /// 更新权限请求
    /// </summary>
    public class UpdatePermissionRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }
}