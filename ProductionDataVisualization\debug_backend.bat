@echo off
title 后端调试启动器

echo ========================================
echo   后端调试启动器
echo ========================================
echo.

cd backend\SqlServerAPI

echo [INFO] 检查.NET环境...
dotnet --version
if %errorlevel% neq 0 (
    echo [ERROR] .NET未安装或配置错误
    pause
    exit /b 1
)

echo.
echo [INFO] 清理项目...
dotnet clean

echo.
echo [INFO] 恢复依赖包...
dotnet restore

echo.
echo [INFO] 编译项目...
dotnet build --verbosity normal

if %errorlevel% neq 0 (
    echo [ERROR] 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo [SUCCESS] 编译成功！
echo.
echo [INFO] 启动后端服务...
dotnet run

pause
