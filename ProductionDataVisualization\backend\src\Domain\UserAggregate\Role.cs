using System;
using System.Collections.Generic;
using Domain.Common;

namespace Domain.UserAggregate
{
    /// <summary>
    /// 角色实体
    /// </summary>
    public class Role : Entity, IAggregateRoot
    {
        public string Name { get; private set; }
        public string Description { get; private set; }

        private readonly List<RolePermission> _rolePermissions = new();
        public IReadOnlyCollection<RolePermission> RolePermissions => _rolePermissions.AsReadOnly();

        private readonly List<UserRole> _userRoles = new();
        public IReadOnlyCollection<UserRole> UserRoles => _userRoles.AsReadOnly();

        // 预定义角色
        public static readonly Guid AdminRoleId = new Guid("8D04DCE2-969A-435D-BBA4-DF3F325983DC");
        public static readonly Guid DataImporterRoleId = new Guid("8D04DCE2-969A-435D-BBA4-DF3F325983DD");
        public static readonly Guid ViewerRoleId = new Guid("8D04DCE2-969A-435D-BBA4-DF3F325983DE");

        // 防止无参构造函数被外部调用
        private Role() { }

        public Role(string name, string description)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("角色名称不能为空", nameof(name));

            Name = name;
            Description = description;
        }

        public Role(Guid id, string name, string description) : base(id)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("角色名称不能为空", nameof(name));

            Name = name;
            Description = description;
        }

        public void Update(string name, string description)
        {
            if (!string.IsNullOrWhiteSpace(name))
                Name = name;

            if (description != null)
                Description = description;

            ModifiedAt = DateTime.UtcNow;
        }

        public void AddPermission(Permission permission)
        {
            if (permission == null)
                throw new ArgumentNullException(nameof(permission));

            if (!_rolePermissions.Exists(rp => rp.PermissionId == permission.Id))
            {
                _rolePermissions.Add(new RolePermission(this, permission));
                ModifiedAt = DateTime.UtcNow;
            }
        }

        public void RemovePermission(Permission permission)
        {
            if (permission == null)
                throw new ArgumentNullException(nameof(permission));

            var rolePermission = _rolePermissions.Find(rp => rp.PermissionId == permission.Id);
            if (rolePermission != null)
            {
                _rolePermissions.Remove(rolePermission);
                ModifiedAt = DateTime.UtcNow;
            }
        }
    }
} 