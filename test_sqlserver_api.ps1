# 测试SQL Server API

Write-Host "=== 测试SQL Server API ===" -ForegroundColor Yellow

# 1. 测试健康检查
Write-Host "`n1. 测试健康检查" -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/health' -Method GET
    Write-Host "健康检查成功!" -ForegroundColor Green
    $response | ConvertTo-Json
} catch {
    Write-Host "健康检查失败!" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

# 2. 测试用户列表
Write-Host "`n2. 测试用户列表" -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/users' -Method GET
    Write-Host "用户列表获取成功!" -ForegroundColor Green
    Write-Host "用户数量: $($response.items.Count)"
    foreach ($user in $response.items) {
        Write-Host "  - $($user.username) ($($user.email))" -ForegroundColor Green
    }
} catch {
    Write-Host "用户列表获取失败!" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

# 3. 测试用户注册
Write-Host "`n3. 测试用户注册" -ForegroundColor Cyan
$timestamp = [DateTimeOffset]::UtcNow.ToUnixTimeSeconds()
$newUser = @{
    username = "sqluser_$timestamp"
    email = "sqluser_$<EMAIL>"
    password = "password123"
    fullName = "SQL Server用户_$timestamp"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/auth/register' -Method POST -Body $newUser -ContentType 'application/json'
    Write-Host "用户注册成功!" -ForegroundColor Green
    $response | ConvertTo-Json
} catch {
    Write-Host "用户注册失败!" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

# 4. 测试用户登录
Write-Host "`n4. 测试用户登录" -ForegroundColor Cyan
$loginData = @{
    usernameOrEmail = "admin"
    password = "admin123"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/auth/login' -Method POST -Body $loginData -ContentType 'application/json'
    Write-Host "用户登录成功!" -ForegroundColor Green
    Write-Host "用户: $($response.username)" -ForegroundColor Green
    Write-Host "角色: $($response.roles -join ', ')" -ForegroundColor Green
} catch {
    Write-Host "用户登录失败!" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

Write-Host "`n=== SQL Server API测试完成 ===" -ForegroundColor Yellow
