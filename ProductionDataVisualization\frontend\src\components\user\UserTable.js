import React, { useState } from 'react';
import { Table, Button, Space, Popconfirm, Tag, message } from 'antd';
import { EditOutlined, DeleteOutlined, UserOutlined, KeyOutlined } from '@ant-design/icons';

// 表格样式
const TABLE_STYLES = {
  header: {
    fontSize: '14px',
    fontWeight: 600,
    color: '#262626',
    textTransform: 'none',
    letterSpacing: '0.3px'
  },
  cell: {
    fontSize: '14px',
    fontWeight: 400,
    color: '#595959'
  },
  button: {
    fontSize: '14px',
    fontWeight: 500,
    letterSpacing: '0.3px'
  },
  tag: {
    fontWeight: 500,
    fontSize: '12px',
    padding: '2px 8px',
    borderRadius: '4px',
    marginBottom: '4px'
  },
  popconfirmTitle: {
    fontSize: '14px',
    fontWeight: 400,
    color: '#595959',
    lineHeight: 1.5
  }
};

const UserTable = ({ 
  users, 
  loading, 
  pagination, 
  onChange, 
  onEdit, 
  onDelete, 
  onAssignRoles,
  checkPermission
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const handleDelete = async (userId) => {
    try {
      await onDelete(userId);
      message.success('用户删除成功');
    } catch (error) {
      message.error('删除用户失败：' + (error.message || '未知错误'));
    }
  };

  // 确保checkPermission函数存在，否则使用默认函数
  const hasPermission = (permission) => {
    if (typeof checkPermission === 'function') {
      const result = checkPermission(permission);
      console.log(`UserTable - 权限检查 ${permission}: ${result}`);
      return result;
    }
    console.warn('UserTable: checkPermission函数未提供');
    return false;
  };

  // 自定义标签颜色
  const getRoleTagColor = (role) => {
    switch(role) {
      case 'Admin':
        return '#595959'; // 中灰色
      case 'Manager':
        return '#8c8c8c'; // 浅灰色
      default:
        return '#bfbfbf'; // 更浅的灰色
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      ellipsis: true,
      onHeaderCell: () => ({
        style: TABLE_STYLES.header
      }),
      onCell: () => ({
        style: TABLE_STYLES.cell
      })
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      sorter: true,
      onHeaderCell: () => ({
        style: TABLE_STYLES.header
      }),
      onCell: () => ({
        style: TABLE_STYLES.cell
      })
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      key: 'fullName',
      onHeaderCell: () => ({
        style: TABLE_STYLES.header
      }),
      onCell: () => ({
        style: TABLE_STYLES.cell
      })
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      onHeaderCell: () => ({
        style: TABLE_STYLES.header
      }),
      onCell: () => ({
        style: TABLE_STYLES.cell
      })
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      onHeaderCell: () => ({
        style: TABLE_STYLES.header
      }),
      render: (roles) => (
        <>
          {Array.isArray(roles) ? roles.map(role => {
            return (
              <Tag 
                color={getRoleTagColor(role)} 
                key={role} 
                style={{...TABLE_STYLES.tag}}
              >
                {role}
              </Tag>
            );
          }) : '无角色'}
        </>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      onHeaderCell: () => ({
        style: TABLE_STYLES.header
      }),
      render: (_, record) => (
        <Space size="middle">
          {hasPermission('EditUser') && (
            <Button 
              type="primary" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => onEdit(record)}
              style={TABLE_STYLES.button}
            >
              编辑
            </Button>
          )}
          
          {hasPermission('EditUser') && (
            <Button 
              type="default" 
              icon={<UserOutlined />} 
              size="small"
              onClick={() => onAssignRoles(record)}
              style={TABLE_STYLES.button}
            >
              角色
            </Button>
          )}
          
          {hasPermission('DeleteUser') && (
            <Popconfirm
              title={<span style={TABLE_STYLES.popconfirmTitle}>确定要删除此用户吗?</span>}
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button 
                type="danger" 
                icon={<DeleteOutlined />} 
                size="small"
                danger
                style={TABLE_STYLES.button}
              >
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  const tableProps = {
    components: {
      header: {
        cell: (props) => <th style={TABLE_STYLES.header} {...props} />,
      },
      body: {
        cell: (props) => <td style={TABLE_STYLES.cell} {...props} />,
      },
    },
  };

  return (
    <Table
      {...tableProps}
      rowSelection={rowSelection}
      columns={columns}
      dataSource={users}
      rowKey="id"
      pagination={pagination}
      loading={loading}
      onChange={onChange}
    />
  );
};

export default UserTable; 