-- 🚨 紧急数据库恢复脚本
-- 重建被误删的系统表

USE ProductionDataVisualizationDb;
GO

PRINT '🚨 开始紧急数据库恢复...';

-- 🔧 第1步：重建FileTableMappings表
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'FileTableMappings')
BEGIN
    PRINT '📋 重建FileTableMappings表...';
    
    CREATE TABLE [FileTableMappings] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [FileName] NVARCHAR(255) NOT NULL,
        [TableName] NVARCHAR(255) NOT NULL,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [LastImportAt] DATETIME2 NULL,
        [TotalRows] INT NOT NULL DEFAULT 0,
        [IsActive] BIT NOT NULL DEFAULT 1,
        CONSTRAINT UK_FileTableMappings_FileName UNIQUE (FileName),
        CONSTRAINT UK_FileTableMappings_TableName UNIQUE (TableName)
    );
    
    PRINT '✅ FileTableMappings表重建完成';
END
ELSE
BEGIN
    PRINT '✅ FileTableMappings表已存在';
END

-- 🔧 第2步：重建ImportTasks表
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'ImportTasks')
BEGIN
    PRINT '📋 重建ImportTasks表...';
    
    CREATE TABLE [ImportTasks] (
        [Id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [FileName] NVARCHAR(255) NOT NULL,
        [TableName] NVARCHAR(255) NOT NULL,
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
        [TotalRows] INT NOT NULL DEFAULT 0,
        [ProcessedRows] INT NOT NULL DEFAULT 0,
        [ErrorMessage] NVARCHAR(MAX) NULL,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [StartedAt] DATETIME2 NULL,
        [CompletedAt] DATETIME2 NULL,
        [ProcessingTimeMs] BIGINT NULL
    );
    
    PRINT '✅ ImportTasks表重建完成';
END
ELSE
BEGIN
    PRINT '✅ ImportTasks表已存在';
END

-- 🔧 第3步：重建Users表（如果被删除）
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Users')
BEGIN
    PRINT '👤 重建Users表...';
    
    CREATE TABLE [Users] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [Username] NVARCHAR(50) NOT NULL UNIQUE,
        [PasswordHash] NVARCHAR(255) NOT NULL,
        [Email] NVARCHAR(100) NULL,
        [FullName] NVARCHAR(100) NULL,
        [Role] NVARCHAR(50) NOT NULL DEFAULT 'User',
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [LastLoginAt] DATETIME2 NULL
    );
    
    -- 重新创建默认管理员用户
    INSERT INTO [Users] (Username, PasswordHash, Email, FullName, Role, IsActive)
    VALUES ('admin', 'admin123', '<EMAIL>', '系统管理员', 'Admin', 1);
    
    PRINT '✅ Users表重建完成，默认管理员用户已创建';
END
ELSE
BEGIN
    PRINT '✅ Users表已存在';
END

-- 🔧 第4步：检查并恢复数据表
PRINT '🔍 检查现有数据表...';

DECLARE @DataTableCount INT;
SELECT @DataTableCount = COUNT(*)
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE' 
  AND TABLE_NAME LIKE 'Data_%';

PRINT '📊 当前数据表数量: ' + CAST(@DataTableCount AS NVARCHAR(10));

IF @DataTableCount = 0
BEGIN
    PRINT '⚠️ 警告：没有找到任何数据表 (Data_*)';
    PRINT '💡 建议：重新导入数据文件以重建数据表';
END
ELSE
BEGIN
    PRINT '✅ 找到 ' + CAST(@DataTableCount AS NVARCHAR(10)) + ' 个数据表';
    
    -- 显示现有数据表
    PRINT '📋 现有数据表列表:';
    DECLARE @TableName NVARCHAR(255);
    DECLARE @RowCount INT;
    
    DECLARE table_cursor CURSOR FOR
    SELECT TABLE_NAME 
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_TYPE = 'BASE TABLE' 
      AND TABLE_NAME LIKE 'Data_%'
    ORDER BY TABLE_NAME;
    
    OPEN table_cursor;
    FETCH NEXT FROM table_cursor INTO @TableName;
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        -- 获取行数
        DECLARE @SQL NVARCHAR(MAX) = 'SELECT @Count = COUNT(*) FROM [' + @TableName + ']';
        EXEC sp_executesql @SQL, N'@Count INT OUTPUT', @Count = @RowCount OUTPUT;
        
        PRINT '  - ' + @TableName + ' (' + CAST(@RowCount AS NVARCHAR(10)) + ' 行)';
        
        FETCH NEXT FROM table_cursor INTO @TableName;
    END
    
    CLOSE table_cursor;
    DEALLOCATE table_cursor;
END

-- 🔧 第5步：重建FileTableMappings的数据（基于现有数据表）
PRINT '🔄 重建FileTableMappings映射关系...';

-- 清空可能的残留数据
DELETE FROM [FileTableMappings];

-- 基于现有数据表重建映射
INSERT INTO [FileTableMappings] (FileName, TableName, TotalRows, LastImportAt)
SELECT 
    REPLACE(TABLE_NAME, 'Data_', '') + '.txt' as FileName,
    TABLE_NAME as TableName,
    0 as TotalRows,  -- 稍后更新
    GETUTCDATE() as LastImportAt
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE' 
  AND TABLE_NAME LIKE 'Data_%';

-- 更新实际行数
DECLARE mapping_cursor CURSOR FOR
SELECT TableName FROM [FileTableMappings];

OPEN mapping_cursor;
FETCH NEXT FROM mapping_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    SET @SQL = 'UPDATE [FileTableMappings] SET TotalRows = (SELECT COUNT(*) FROM [' + @TableName + ']) WHERE TableName = ''' + @TableName + '''';
    EXEC sp_executesql @SQL;
    
    FETCH NEXT FROM mapping_cursor INTO @TableName;
END

CLOSE mapping_cursor;
DEALLOCATE mapping_cursor;

PRINT '✅ FileTableMappings映射关系重建完成';

-- 🎉 恢复完成报告
PRINT '';
PRINT '🎉 紧急数据库恢复完成！';
PRINT '';
PRINT '📋 恢复摘要:';
PRINT '✅ 系统表已重建';
PRINT '✅ 用户表已恢复';
PRINT '✅ 文件映射已重建';

SELECT 
    '数据表' as 类型,
    COUNT(*) as 数量
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE' 
  AND TABLE_NAME LIKE 'Data_%'

UNION ALL

SELECT 
    '文件映射' as 类型,
    COUNT(*) as 数量
FROM [FileTableMappings]

UNION ALL

SELECT 
    '用户账户' as 类型,
    COUNT(*) as 数量
FROM [Users];

PRINT '';
PRINT '🚀 系统已准备就绪，可以重新启动服务！';
