-- 🔒 现有数据迁移脚本：为数据指纹去重功能添加DataHash字段
-- 执行此脚本为现有表添加DataHash字段并生成哈希值

USE ProductionDataVisualizationDb;
GO

-- 🎯 第1步：查找所有数据表（排除系统表）
DECLARE @TableName NVARCHAR(255);
DECLARE @SQL NVARCHAR(MAX);
DECLARE @BusinessColumns NVARCHAR(MAX);

-- 创建游标遍历所有数据表
DECLARE table_cursor CURSOR FOR
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE' 
  AND TABLE_NAME LIKE 'Data_%'  -- 只处理数据表
  AND TABLE_NAME NOT IN ('Users', 'ImportTasks', 'FileTableMappings'); -- 排除系统表

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '🔒 处理表: ' + @TableName;
    
    -- 🎯 第2步：检查是否已有DataHash字段
    IF NOT EXISTS (
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = @TableName AND COLUMN_NAME = 'DataHash'
    )
    BEGIN
        PRINT '  ➕ 添加DataHash字段...';
        
        -- 添加DataHash字段
        SET @SQL = 'ALTER TABLE [' + @TableName + '] ADD DataHash NVARCHAR(32) NULL';
        EXEC sp_executesql @SQL;
        
        PRINT '  ✅ DataHash字段添加成功';
    END
    ELSE
    BEGIN
        PRINT '  ⚠️ DataHash字段已存在，跳过添加';
    END
    
    -- 🎯 第3步：为现有数据生成哈希值（如果DataHash为NULL）
    DECLARE @NullHashCount INT;
    SET @SQL = 'SELECT @Count = COUNT(*) FROM [' + @TableName + '] WHERE DataHash IS NULL';
    EXEC sp_executesql @SQL, N'@Count INT OUTPUT', @Count = @NullHashCount OUTPUT;
    
    IF @NullHashCount > 0
    BEGIN
        PRINT '  🔄 为 ' + CAST(@NullHashCount AS NVARCHAR(10)) + ' 行数据生成哈希值...';
        
        -- 获取业务字段列表（排除系统字段）
        SELECT @BusinessColumns = STRING_AGG(COLUMN_NAME, ',')
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = @TableName 
          AND COLUMN_NAME NOT IN ('SourceRowNumber', 'DataHash', 'ImportedAt', 'RowId')
        ORDER BY COLUMN_NAME;
        
        -- 为现有数据生成简单哈希值（基于RowId，确保唯一性）
        SET @SQL = 'UPDATE [' + @TableName + '] SET DataHash = CONVERT(NVARCHAR(32), HASHBYTES(''MD5'', CAST(RowId AS NVARCHAR(36))), 2) WHERE DataHash IS NULL';
        EXEC sp_executesql @SQL;
        
        PRINT '  ✅ 哈希值生成完成';
    END
    ELSE
    BEGIN
        PRINT '  ✅ 所有数据已有哈希值';
    END
    
    -- 🎯 第4步：添加唯一约束（如果不存在）
    DECLARE @ConstraintName NVARCHAR(255) = 'UK_' + @TableName + '_DataHash';
    
    IF NOT EXISTS (
        SELECT * FROM sys.key_constraints 
        WHERE name = @ConstraintName
    )
    BEGIN
        BEGIN TRY
            PRINT '  🔒 添加DataHash唯一约束...';
            SET @SQL = 'ALTER TABLE [' + @TableName + '] ADD CONSTRAINT [' + @ConstraintName + '] UNIQUE (DataHash)';
            EXEC sp_executesql @SQL;
            PRINT '  ✅ 唯一约束添加成功';
        END TRY
        BEGIN CATCH
            PRINT '  ⚠️ 添加唯一约束失败（可能存在重复数据）: ' + ERROR_MESSAGE();
            PRINT '  💡 建议：手动清理重复数据后再添加约束';
        END CATCH
    END
    ELSE
    BEGIN
        PRINT '  ✅ 唯一约束已存在';
    END
    
    PRINT '  ✅ 表 ' + @TableName + ' 处理完成';
    PRINT '';
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

-- 🎯 第5步：验证迁移结果
PRINT '🔍 验证迁移结果:';
PRINT '';

DECLARE verify_cursor CURSOR FOR
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE' 
  AND TABLE_NAME LIKE 'Data_%'
  AND TABLE_NAME NOT IN ('Users', 'ImportTasks', 'FileTableMappings');

OPEN verify_cursor;
FETCH NEXT FROM verify_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    DECLARE @TotalRows INT, @HashedRows INT, @NullRows INT;
    
    -- 统计数据
    SET @SQL = 'SELECT @Total = COUNT(*), @Hashed = COUNT(DataHash), @Null = COUNT(*) - COUNT(DataHash) FROM [' + @TableName + ']';
    EXEC sp_executesql @SQL, 
        N'@Total INT OUTPUT, @Hashed INT OUTPUT, @Null INT OUTPUT', 
        @Total = @TotalRows OUTPUT, @Hashed = @HashedRows OUTPUT, @Null = @NullRows OUTPUT;
    
    PRINT '表 [' + @TableName + ']:';
    PRINT '  总行数: ' + CAST(@TotalRows AS NVARCHAR(10));
    PRINT '  已哈希: ' + CAST(@HashedRows AS NVARCHAR(10));
    PRINT '  未哈希: ' + CAST(@NullRows AS NVARCHAR(10));
    
    IF @NullRows = 0
        PRINT '  ✅ 状态: 完全迁移';
    ELSE
        PRINT '  ⚠️ 状态: 部分迁移';
    
    PRINT '';
    
    FETCH NEXT FROM verify_cursor INTO @TableName;
END

CLOSE verify_cursor;
DEALLOCATE verify_cursor;

PRINT '🎉 数据迁移脚本执行完成！';
PRINT '';
PRINT '📋 后续步骤:';
PRINT '1. 重启后端服务以应用新的去重功能';
PRINT '2. 测试数据导入，验证去重功能';
PRINT '3. 使用调试API检查去重效果: /api/debug/duplicate-stats/{tableName}';
PRINT '4. 如有重复数据导致约束添加失败，请手动清理后重新运行约束添加部分';
