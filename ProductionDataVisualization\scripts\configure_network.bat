@echo off
chcp 65001 > nul
echo 配置网络访问权限...
echo ================================

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 以管理员身份运行
) else (
    echo ❌ 需要管理员权限来配置防火墙
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo 正在配置Windows防火墙规则...

REM 添加前端端口3000的防火墙规则
echo 配置前端端口3000...
netsh advfirewall firewall delete rule name="React Dev Server (3000)" >nul 2>&1
netsh advfirewall firewall add rule name="React Dev Server (3000)" dir=in action=allow protocol=TCP localport=3000
if %errorLevel% == 0 (
    echo ✅ 前端端口3000防火墙规则添加成功
) else (
    echo ❌ 前端端口3000防火墙规则添加失败
)

REM 添加后端端口5000的防火墙规则
echo 配置后端端口5000...
netsh advfirewall firewall delete rule name="SQL Server API (5000)" >nul 2>&1
netsh advfirewall firewall add rule name="SQL Server API (5000)" dir=in action=allow protocol=TCP localport=5000
if %errorLevel% == 0 (
    echo ✅ 后端端口5000防火墙规则添加成功
) else (
    echo ❌ 后端端口5000防火墙规则添加失败
)

echo.
echo 获取本机IP地址信息...
echo ================================

REM 获取本机IP地址
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4"') do (
    set ip=%%i
    set ip=!ip: =!
    if not "!ip!"=="" (
        echo 本机IP地址: !ip!
        echo 前端访问地址: http://!ip!:3000
        echo 后端API地址: http://!ip!:5000
        echo.
    )
)

REM 获取计算机名
echo 计算机名: %COMPUTERNAME%
echo 前端访问地址: http://%COMPUTERNAME%:3000
echo 后端API地址: http://%COMPUTERNAME%:5000

echo.
echo ================================
echo 网络配置完成！
echo.
echo 其他电脑现在可以通过以下地址访问系统：
echo 1. 使用IP地址访问（推荐）
echo 2. 使用计算机名访问（需要在同一域或工作组）
echo.
echo 注意事项：
echo - 确保所有电脑在同一局域网内
echo - 如果仍无法访问，请检查网络连接和路由器设置
echo - 某些企业网络可能有额外的安全限制
echo.
pause
