# 生产数据可视化系统脚本说明

本目录包含用于管理生产数据可视化系统的核心脚本。

## 核心系统脚本

为了简化系统的操作，我们提供了两个核心脚本文件：

### start_system.bat - 一键启动系统

该脚本实现了完整系统的一键启动：

- **自动化启动流程**：无需用户交互，直接启动完整系统
- **后台运行**：即使关闭命令窗口，系统仍继续运行
- **多重启动方式**：自动尝试多种前端启动方法，确保系统可用
- **自动错误处理**：如遇问题自动尝试备用方案
- **状态反馈**：提供清晰的启动状态信息
- **日志记录**：所有操作记录到日志文件，便于排查问题

**使用方法**：双击运行 `start_system.bat`，系统将自动启动并打开浏览器访问界面。

### stop_system.bat - 一键停止系统

该脚本实现了系统的完全停止：

- **全面停止**：停止所有系统相关进程和服务
- **强制终止**：确保所有占用端口的进程被终止
- **多重检查**：进行多次验证，确保系统完全停止
- **状态反馈**：提供清晰的停止状态信息
- **日志记录**：所有操作记录到日志文件，便于排查问题

**使用方法**：双击运行 `stop_system.bat`，系统将完全停止所有相关服务。

## 系统界面选项

系统提供了两种前端界面选项：

1. **标准React应用** - 完整功能的前端界面（端口3001）
2. **direct.html** - 直接访问模式，不依赖任何构建工具

## 使用流程

### 标准使用流程
1. 双击运行 `start_system.bat`
2. 系统自动启动后端API和前端服务
3. 浏览器自动打开访问系统界面
4. 使用完毕后，运行 `stop_system.bat` 停止系统

### 注意事项
- 所有功能均支持中英文双语显示
- 系统日志保存在 `logs` 目录下
- 如果标准前端无法访问，系统会自动使用direct.html作为备用 