# 服务器内部错误诊断指南

## 当前状态
- ✅ 后端服务已启动 (http://localhost:5000)
- ✅ 自动恢复功能已部署
- ❌ 前端显示"服务器内部错误"

## 诊断步骤

### 1. 测试基本连接
在浏览器中访问：
- http://localhost:5000/api/health
- http://localhost:5000/api/database/health

### 2. 检查数据库状态
如果数据库健康检查显示问题，可以：
- 访问 http://localhost:5000/api/database/recover (POST请求)
- 或者在后端控制台查看自动恢复日志

### 3. 测试数据导入
1. 使用提供的测试文件：`test_data.csv`
2. 在前端上传这个文件
3. 观察后端控制台的详细日志

### 4. 预期的自动恢复流程

如果数据库表结构有问题，您应该看到：

```
⚠️ 检测到关键表缺失，尝试自动恢复...
表存在性检查: ImportTasks=false, FileTableMappings=false, Users=true
开始自动恢复数据库表结构...
用户表恢复完成
数据导入表恢复完成
✅ 数据库表结构自动恢复成功！
```

### 5. 如果自动恢复失败

前端会显示：
```
数据库结构缺失
检测到数据库表结构被删除，系统正在尝试自动恢复。
请稍后重试，或联系管理员手动恢复数据库结构。
```

### 6. 手动恢复选项

如果自动恢复失败，可以：
1. 重启后端服务（会重新创建表）
2. 使用手动恢复API
3. 检查SQL Server连接和权限

## 测试文件说明

`test_data.csv` 包含：
- 5行测试数据
- 中文变量名（测试中文支持）
- 标准的时间和数值格式
- 适合测试表名生成：`Data_test_data`

## 下一步

1. 上传 `test_data.csv` 文件
2. 观察后端控制台日志
3. 如果看到自动恢复消息，等待完成
4. 重新尝试导入
5. 报告具体的错误信息以便进一步诊断
