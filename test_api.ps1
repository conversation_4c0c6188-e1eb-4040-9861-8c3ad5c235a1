$body = '{"username":"admin","password":"admin123"}'

try {
    Write-Host "测试登录API..." -ForegroundColor Yellow
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/auth/login' -Method POST -Body $body -ContentType 'application/json'
    Write-Host "LOGIN SUCCESS!" -ForegroundColor Green
    $response | ConvertTo-Json
} catch {
    Write-Host "LOGIN FAILED!" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

try {
    Write-Host "测试用户注册API..." -ForegroundColor Yellow
    $registerBody = '{"username":"testuser","email":"<EMAIL>","password":"password123","fullName":"测试用户"}'
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/simple-auth/register' -Method POST -Body $registerBody -ContentType 'application/json'
    Write-Host "REGISTER SUCCESS!" -ForegroundColor Green
    $response | ConvertTo-Json
} catch {
    Write-Host "REGISTER FAILED!" -ForegroundColor Red
    Write-Host $_.Exception.Message
}
