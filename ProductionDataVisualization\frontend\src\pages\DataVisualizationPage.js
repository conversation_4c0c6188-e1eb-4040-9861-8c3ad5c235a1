import React, { useState, useEffect, useMemo } from 'react';
import {
  Row,
  Col,
  Card,
  Button,
  Space,
  Typography,
  Alert,
  Spin,
  Modal,
  message
} from 'antd';
import {
  LineChartOutlined,
  BarChartOutlined,
  PieChartOutlined,
  TableOutlined,
  SettingOutlined,
  FilterOutlined,
  ExclamationCircleOutlined,
  SaveOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import CleanAssanLayout from '../components/layout/CleanAssanLayout';

// 导入图表组件
import LineChart from '../components/charts/LineChart';
import BarChart from '../components/charts/BarChart';
import PieChart from '../components/charts/PieChart';
import DataTable from '../components/charts/DataTable';

// 导入控制面板组件
import DataFilterPanel from '../components/charts/DataFilterPanel';
import ChartConfigPanel from '../components/charts/ChartConfigPanel';
import AnomalyDetectionConfig from '../components/charts/AnomalyDetectionConfig';
import AnomalyPanel from '../components/charts/AnomalyPanel';

// 导入服务
import chartConfigService from '../services/chartConfigService';

const { Title, Text } = Typography;

const DataVisualizationPage = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [columns, setColumns] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [chartType, setChartType] = useState('line');
  const [chartConfig, setChartConfig] = useState({});
  const [anomalyConfig, setAnomalyConfig] = useState({ enabled: false });
  const [anomalies, setAnomalies] = useState([]);
  const [showConfigPanel, setShowConfigPanel] = useState(false);
  const [showFilterPanel, setShowFilterPanel] = useState(true);
  const [showAnomalyPanel, setShowAnomalyPanel] = useState(false);

  // 模拟数据生成
  const generateSampleData = () => {
    setLoading(true);
    
    setTimeout(() => {
      const sampleData = [];
      const categories = ['产品A', '产品B', '产品C', '产品D', '产品E'];
      
      for (let i = 0; i < 50; i++) {
        const baseValue = Math.random() * 100 + 50;
        const anomalyFactor = Math.random() < 0.1 ? (Math.random() * 2 + 2) : 1; // 10%概率产生异常
        
        sampleData.push({
          id: i + 1,
          category: categories[i % categories.length],
          value: Math.round(baseValue * anomalyFactor * 100) / 100,
          quality: Math.round((Math.random() * 20 + 80) * 100) / 100,
          temperature: Math.round((Math.random() * 10 + 20) * 100) / 100,
          humidity: Math.round((Math.random() * 20 + 40) * 100) / 100,
          date: new Date(2024, 0, i + 1).toISOString().split('T')[0],
          status: Math.random() > 0.2 ? '正常' : '异常'
        });
      }
      
      const sampleColumns = [
        { title: 'ID', dataIndex: 'id', type: 'number' },
        { title: '产品类别', dataIndex: 'category', type: 'text' },
        { title: '数值', dataIndex: 'value', type: 'number', format: 'decimal', precision: 2 },
        { title: '质量指标', dataIndex: 'quality', type: 'number', format: 'percentage' },
        { title: '温度', dataIndex: 'temperature', type: 'number', format: 'decimal', precision: 1 },
        { title: '湿度', dataIndex: 'humidity', type: 'number', format: 'percentage' },
        { title: '日期', dataIndex: 'date', type: 'date' },
        { title: '状态', dataIndex: 'status', type: 'text' }
      ];
      
      setData(sampleData);
      setFilteredData(sampleData);
      setColumns(sampleColumns);
      setLoading(false);
      
      message.success('示例数据加载完成');
    }, 1000);
  };

  // 图表类型选项
  const chartTypeOptions = [
    { type: 'line', label: '折线图', icon: <LineChartOutlined /> },
    { type: 'bar', label: '柱状图', icon: <BarChartOutlined /> },
    { type: 'pie', label: '饼图', icon: <PieChartOutlined /> },
    { type: 'table', label: '数据表', icon: <TableOutlined /> }
  ];

  // 准备图表数据
  const chartData = useMemo(() => {
    if (!filteredData.length) return null;

    switch (chartType) {
      case 'line':
      case 'bar':
        return {
          categories: filteredData.map(item => item.category),
          series: [
            {
              name: '数值',
              data: filteredData.map(item => item.value),
              color: '#1890ff'
            },
            {
              name: '质量指标',
              data: filteredData.map(item => item.quality),
              color: '#52c41a'
            }
          ]
        };
      
      case 'pie':
        const categoryStats = {};
        filteredData.forEach(item => {
          categoryStats[item.category] = (categoryStats[item.category] || 0) + item.value;
        });
        
        return Object.entries(categoryStats).map(([name, value]) => ({
          name,
          value: Math.round(value * 100) / 100
        }));
      
      default:
        return filteredData;
    }
  }, [filteredData, chartType]);

  // 过滤器变化处理
  const handleFilterChange = (filters, activeFilters) => {
    let filtered = [...data];
    
    // 应用全局搜索
    if (filters.globalSearch) {
      const searchTerm = filters.globalSearch.toLowerCase();
      filtered = filtered.filter(row =>
        Object.values(row).some(value =>
          value?.toString().toLowerCase().includes(searchTerm)
        )
      );
    }
    
    // 应用字段过滤器
    Object.entries(filters).forEach(([field, filterValue]) => {
      if (field === 'globalSearch' || !filterValue) return;
      
      if (typeof filterValue === 'object') {
        if (filterValue.min != null) {
          filtered = filtered.filter(row => row[field] >= filterValue.min);
        }
        if (filterValue.max != null) {
          filtered = filtered.filter(row => row[field] <= filterValue.max);
        }
        if (filterValue.values && filterValue.values.length > 0) {
          filtered = filtered.filter(row => filterValue.values.includes(row[field]));
        }
        if (filterValue.search) {
          const searchTerm = filterValue.search.toLowerCase();
          filtered = filtered.filter(row =>
            row[field]?.toString().toLowerCase().includes(searchTerm)
          );
        }
      }
    });
    
    setFilteredData(filtered);
    message.info(`筛选结果: ${filtered.length} 条数据`);
  };

  // 异常检测结果处理
  const handleAnomalyDetection = (detectedAnomalies, stats) => {
    setAnomalies(detectedAnomalies);
    setShowAnomalyPanel(detectedAnomalies.length > 0);
    
    if (detectedAnomalies.length > 0) {
      message.warning(`检测到 ${detectedAnomalies.length} 个异常数据点`);
    }
  };

  // 保存配置
  const saveCurrentConfig = () => {
    const configId = `config_${Date.now()}`;
    const metadata = {
      name: `${chartType}图表配置`,
      description: `${chartType}图表的自定义配置`,
      type: chartType,
      category: 'custom'
    };
    
    chartConfigService.saveConfig(configId, chartConfig, metadata);
  };

  // 渲染图表
  const renderChart = () => {
    if (!chartData) {
      return (
        <Alert
          message="暂无数据"
          description="请先加载数据或调整过滤条件"
          type="info"
          showIcon
        />
      );
    }

    const commonProps = {
      loading,
      height: chartConfig.height || 400,
      enableAnomalyDetection: anomalyConfig.enabled,
      anomalyMethod: anomalyConfig.method,
      anomalyThreshold: anomalyConfig.threshold
    };

    switch (chartType) {
      case 'line':
        return (
          <LineChart
            data={chartData}
            title={chartConfig.title || '数据趋势图'}
            smooth={chartConfig.smooth}
            showArea={chartConfig.showArea}
            showSymbol={chartConfig.showSymbol}
            {...commonProps}
          />
        );
      
      case 'bar':
        return (
          <BarChart
            data={chartData}
            title={chartConfig.title || '数据对比图'}
            barWidth={chartConfig.barWidth}
            borderRadius={chartConfig.borderRadius}
            {...commonProps}
          />
        );
      
      case 'pie':
        return (
          <PieChart
            data={chartData}
            title={chartConfig.title || '数据分布图'}
            radius={chartConfig.radius}
            showLabel={chartConfig.showLabel}
            showLabelLine={chartConfig.showLabelLine}
            {...commonProps}
          />
        );
      
      case 'table':
        return (
          <DataTable
            data={filteredData}
            columns={columns}
            title={chartConfig.title || '数据表格'}
            enableAnomalyDetection={anomalyConfig.enabled}
            anomalyMethod={anomalyConfig.method}
            anomalyThreshold={anomalyConfig.threshold}
            height={chartConfig.height}
            loading={loading}
          />
        );
      
      default:
        return null;
    }
  };

  // 初始化
  useEffect(() => {
    generateSampleData();
  }, []);

  return (
    <CleanAssanLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {/* 页面标题 */}
        <Card style={{ marginBottom: 16 }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={2} style={{ margin: 0 }}>
                数据可视化分析
              </Title>
              <Text type="secondary">
                生产数据的可视化展示和异常检测分析
              </Text>
            </Col>
            <Col>
              <Space>
                <Button 
                  icon={<LoadingOutlined />} 
                  onClick={generateSampleData}
                  loading={loading}
                >
                  加载示例数据
                </Button>
                <Button 
                  icon={<FilterOutlined />} 
                  type={showFilterPanel ? 'primary' : 'default'}
                  onClick={() => setShowFilterPanel(!showFilterPanel)}
                >
                  数据过滤
                </Button>
                <Button 
                  icon={<SettingOutlined />} 
                  type={showConfigPanel ? 'primary' : 'default'}
                  onClick={() => setShowConfigPanel(!showConfigPanel)}
                >
                  图表配置
                </Button>
                <Button 
                  icon={<SaveOutlined />} 
                  onClick={saveCurrentConfig}
                  disabled={Object.keys(chartConfig).length === 0}
                >
                  保存配置
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        <Row gutter={16}>
          {/* 左侧控制面板 */}
          <Col span={6}>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              {/* 图表类型选择 */}
              <Card title="图表类型" size="small">
                <Space direction="vertical" style={{ width: '100%' }}>
                  {chartTypeOptions.map(option => (
                    <Button
                      key={option.type}
                      type={chartType === option.type ? 'primary' : 'default'}
                      icon={option.icon}
                      onClick={() => setChartType(option.type)}
                      style={{ width: '100%', textAlign: 'left' }}
                    >
                      {option.label}
                    </Button>
                  ))}
                </Space>
              </Card>

              {/* 异常检测配置 */}
              <AnomalyDetectionConfig
                data={filteredData.map(item => item.value)}
                onConfigChange={setAnomalyConfig}
                onDetectionResult={handleAnomalyDetection}
              />

              {/* 数据过滤面板 */}
              <AnimatePresence>
                {showFilterPanel && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <DataFilterPanel
                      data={data}
                      columns={columns}
                      onFilterChange={handleFilterChange}
                      collapsible={false}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </Space>
          </Col>

          {/* 中间图表区域 */}
          <Col span={showConfigPanel ? 12 : 18}>
            <Spin spinning={loading} tip="数据加载中...">
              {renderChart()}
            </Spin>
          </Col>

          {/* 右侧配置面板 */}
          <AnimatePresence>
            {showConfigPanel && (
              <Col span={6}>
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3 }}
                >
                  <ChartConfigPanel
                    chartType={chartType}
                    config={chartConfig}
                    columns={columns}
                    onConfigChange={setChartConfig}
                  />
                </motion.div>
              </Col>
            )}
          </AnimatePresence>
        </Row>

        {/* 异常数据面板 */}
        <AnimatePresence>
          {showAnomalyPanel && anomalies.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.3 }}
              style={{ marginTop: 16 }}
            >
              <AnomalyPanel
                anomalies={anomalies}
                data={filteredData}
                columns={columns}
                onAnomalyClick={(anomaly) => {
                  message.info(`异常数据: 索引 ${anomaly.index}, 数值 ${anomaly.value}`);
                }}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </CleanAssanLayout>
  );
};

export default DataVisualizationPage;
