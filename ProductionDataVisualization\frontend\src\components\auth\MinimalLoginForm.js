import React, { useState } from 'react';
import { Form, Input, Button, Checkbox, Alert, Typography, Divider } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import authService from '../../services/authService';
import { useNavigate, Link } from 'react-router-dom';

const { Title, Text } = Typography;

const MinimalLoginForm = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const onFinish = async (values) => {
    setLoading(true);
    setError('');
    
    try {
      await authService.login(values.username, values.password);
      
      // 成功动画
      await new Promise(resolve => setTimeout(resolve, 500));
      
      navigate('/dashboard');
    } catch (err) {
      setError(err.message || 'Login failed. Please check your credentials.');
      
      // 错误震动动画
      const formElement = document.querySelector('.login-form-container');
      if (formElement) {
        formElement.classList.add('shake');
        setTimeout(() => {
          formElement.classList.remove('shake');
        }, 600);
      }
    } finally {
      setLoading(false);
    }
  };

  // 动画变体
  const formItemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.4,
        ease: "easeOut"
      }
    })
  };

  return (
    <div className="login-form-container">
      <Title level={3} className="form-title">Sign In</Title>
      <Text className="form-subtitle">Welcome back to your account</Text>
      
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="login-error"
        >
          <Alert 
            message={error} 
            type="error" 
            showIcon 
            closable
            onClose={() => setError('')}
          />
        </motion.div>
      )}
      
      <Form
        name="login"
        form={form}
        initialValues={{ remember: true }}
        onFinish={onFinish}
        size="large"
        layout="vertical"
        className="login-form"
      >
        <motion.div
          custom={0}
          variants={formItemVariants}
          initial="hidden"
          animate="visible"
        >
          <Form.Item
            name="username"
            label="Username"
            rules={[{ required: true, message: 'Please enter your username' }]}
          >
            <Input
              prefix={<UserOutlined className="input-icon" />}
              placeholder="Enter your username"
              className="login-input"
            />
          </Form.Item>
        </motion.div>
        
        <motion.div
          custom={1}
          variants={formItemVariants}
          initial="hidden"
          animate="visible"
        >
          <Form.Item
            name="password"
            label="Password"
            rules={[{ required: true, message: 'Please enter your password' }]}
          >
            <Input.Password
              prefix={<LockOutlined className="input-icon" />}
              placeholder="Enter your password"
              className="login-input"
            />
          </Form.Item>
        </motion.div>
        
        <motion.div
          custom={2}
          variants={formItemVariants}
          initial="hidden"
          animate="visible"
          className="login-form-options"
        >
          <Form.Item name="remember" valuePropName="checked" noStyle>
            <Checkbox>Remember me</Checkbox>
          </Form.Item>
          
          <Link to="/forgot-password" className="forgot-password">
            Forgot password?
          </Link>
        </motion.div>
        
        <motion.div
          custom={3}
          variants={formItemVariants}
          initial="hidden"
          animate="visible"
        >
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            className="login-button"
            block
          >
            Sign in
          </Button>
        </motion.div>
        
        <motion.div
          custom={4}
          variants={formItemVariants}
          initial="hidden"
          animate="visible"
        >
          <Divider plain>
            <Text type="secondary" style={{ fontSize: '14px' }}>Or sign in with</Text>
          </Divider>
          
          <div className="social-login">
            <Button className="social-button google-button">Google</Button>
            <Button className="social-button linkedin-button">LinkedIn</Button>
          </div>
        </motion.div>
        
        <motion.div
          custom={5}
          variants={formItemVariants}
          initial="hidden"
          animate="visible"
          className="register-link"
        >
          <Text type="secondary">Don't have an account?</Text>
          <Link to="/register">Sign up now</Link>
        </motion.div>
      </Form>

      <style jsx="true">{`
        .login-form-container {
          width: 100%;
        }
        
        .form-title {
          font-weight: 700 !important;
          margin-bottom: 8px !important;
          color: #333 !important;
        }
        
        .form-subtitle {
          display: block;
          color: #666;
          margin-bottom: 24px;
        }
        
        .login-error {
          margin-bottom: 20px;
        }
        
        .login-form {
          width: 100%;
        }
        
        .login-form .ant-form-item-label > label {
          color: #333;
          font-weight: 500;
        }
        
        .login-input {
          height: 48px;
          border-radius: 6px;
          border: 1px solid #e2e8f0;
          background: #ffffff;
          transition: all 0.3s ease;
        }
        
        .login-input:hover,
        .login-input:focus {
          border-color: #35A87C;
          box-shadow: 0 0 0 2px rgba(53, 168, 124, 0.1);
        }
        
        .input-icon {
          color: #94a3b8;
        }
        
        .login-form-options {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24px;
        }
        
        .forgot-password {
          color: #35A87C;
          font-size: 14px;
          transition: color 0.3s;
        }
        
        .forgot-password:hover {
          color: #2c8a66;
          text-decoration: underline;
        }
        
        .login-button {
          height: 48px;
          border-radius: 6px;
          background: #35A87C;
          border: none;
          font-weight: 600;
          transition: all 0.3s ease;
          margin-bottom: 24px;
        }
        
        .login-button:hover {
          background: #2c8a66;
          box-shadow: 0 4px 12px rgba(53, 168, 124, 0.2);
        }
        
        .social-login {
          display: flex;
          gap: 16px;
          margin-bottom: 24px;
        }
        
        .social-button {
          flex: 1;
          height: 44px;
          border-radius: 6px;
          font-weight: 500;
          border: 1px solid #e2e8f0;
          background: #ffffff;
          color: #333;
        }
        
        .social-button:hover {
          border-color: #35A87C;
          color: #35A87C;
        }
        
        .register-link {
          text-align: center;
        }
        
        .register-link a {
          color: #35A87C;
          font-weight: 600;
          margin-left: 8px;
        }
        
        .register-link a:hover {
          text-decoration: underline;
        }
        
        /* 错误震动动画 */
        @keyframes shake {
          0%, 100% { transform: translateX(0); }
          10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
          20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        
        .shake {
          animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
        }
        
        /* 修改Ant Design默认样式 */
        .ant-checkbox-checked .ant-checkbox-inner {
          background-color: #35A87C;
          border-color: #35A87C;
        }
        
        .ant-checkbox-wrapper:hover .ant-checkbox-inner,
        .ant-checkbox:hover .ant-checkbox-inner,
        .ant-checkbox-input:focus + .ant-checkbox-inner {
          border-color: #35A87C;
        }
      `}</style>
    </div>
  );
};

export default MinimalLoginForm; 