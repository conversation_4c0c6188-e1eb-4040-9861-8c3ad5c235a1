using Serilog;
using Serilog.Events;
using Serilog.Formatting.Compact;

namespace API.Extensions
{
    /// <summary>
    /// 日志配置扩展
    /// </summary>
    public static class LoggingExtensions
    {
        /// <summary>
        /// 配置Serilog日志
        /// </summary>
        public static void ConfigureSerilog(this WebApplicationBuilder builder)
        {
            // 创建日志目录
            var logDirectory = Path.Combine(Directory.GetCurrentDirectory(), "logs");
            Directory.CreateDirectory(logDirectory);

            // 配置Serilog
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Information()
                .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
                .MinimumLevel.Override("Microsoft.Hosting.Lifetime", LogEventLevel.Information)
                .MinimumLevel.Override("System", LogEventLevel.Warning)
                .Enrich.FromLogContext()
                .Enrich.WithProperty("Application", "ProductionDataVisualization")
                .Enrich.WithProperty("Environment", builder.Environment.EnvironmentName)
                .WriteTo.Console(
                    outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
                .WriteTo.File(
                    path: Path.Combine(logDirectory, "app-.log"),
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30,
                    outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
                .WriteTo.File(
                    formatter: new CompactJsonFormatter(),
                    path: Path.Combine(logDirectory, "app-.json"),
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30)
                .WriteTo.File(
                    path: Path.Combine(logDirectory, "errors-.log"),
                    restrictedToMinimumLevel: LogEventLevel.Error,
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 90,
                    outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
                .CreateLogger();

            // 使用Serilog作为日志提供程序
            builder.Host.UseSerilog();
        }

        /// <summary>
        /// 添加请求日志中间件
        /// </summary>
        public static void UseRequestLogging(this WebApplication app)
        {
            app.UseSerilogRequestLogging(options =>
            {
                // 自定义请求日志格式
                options.MessageTemplate = "HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms";
                
                // 增强请求日志信息
                options.EnrichDiagnosticContext = (diagnosticContext, httpContext) =>
                {
                    diagnosticContext.Set("RequestHost", httpContext.Request.Host.Value);
                    diagnosticContext.Set("RequestScheme", httpContext.Request.Scheme);
                    diagnosticContext.Set("UserAgent", httpContext.Request.Headers["User-Agent"].FirstOrDefault());
                    diagnosticContext.Set("ClientIP", httpContext.Connection.RemoteIpAddress?.ToString());
                    
                    // 添加用户信息（如果已认证）
                    if (httpContext.User.Identity?.IsAuthenticated == true)
                    {
                        diagnosticContext.Set("UserId", httpContext.User.FindFirst("sub")?.Value);
                        diagnosticContext.Set("Username", httpContext.User.FindFirst("username")?.Value);
                    }

                    // 添加响应大小
                    if (httpContext.Response.ContentLength.HasValue)
                    {
                        diagnosticContext.Set("ResponseSize", httpContext.Response.ContentLength.Value);
                    }
                };

                // 设置日志级别
                options.GetLevel = (httpContext, elapsed, ex) =>
                {
                    if (ex != null) return LogEventLevel.Error;
                    if (httpContext.Response.StatusCode >= 500) return LogEventLevel.Error;
                    if (httpContext.Response.StatusCode >= 400) return LogEventLevel.Warning;
                    if (elapsed > 5000) return LogEventLevel.Warning; // 超过5秒的请求
                    return LogEventLevel.Information;
                };
            });
        }
    }

    /// <summary>
    /// 性能监控中间件
    /// </summary>
    public class PerformanceMonitoringMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<PerformanceMonitoringMiddleware> _logger;

        public PerformanceMonitoringMiddleware(RequestDelegate next, ILogger<PerformanceMonitoringMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                await _next(context);
            }
            finally
            {
                stopwatch.Stop();
                
                // 记录慢请求
                if (stopwatch.ElapsedMilliseconds > 3000) // 超过3秒
                {
                    _logger.LogWarning("慢请求检测: {Method} {Path} 耗时 {ElapsedMs}ms",
                        context.Request.Method,
                        context.Request.Path,
                        stopwatch.ElapsedMilliseconds);
                }

                // 记录性能指标
                _logger.LogDebug("请求性能: {Method} {Path} {StatusCode} {ElapsedMs}ms",
                    context.Request.Method,
                    context.Request.Path,
                    context.Response.StatusCode,
                    stopwatch.ElapsedMilliseconds);
            }
        }
    }

    /// <summary>
    /// 性能监控扩展
    /// </summary>
    public static class PerformanceMonitoringExtensions
    {
        public static IApplicationBuilder UsePerformanceMonitoring(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<PerformanceMonitoringMiddleware>();
        }
    }
}
