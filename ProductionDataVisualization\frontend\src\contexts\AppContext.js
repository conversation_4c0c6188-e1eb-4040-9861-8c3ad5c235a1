import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { message } from 'antd';
import authService from '../services/authService';
import cacheManager from '../utils/cache';

// 初始状态
const initialState = {
  // 用户状态
  user: null,
  isAuthenticated: false,
  isLoading: false,
  
  // 应用状态
  theme: 'light',
  language: 'zh-CN',
  sidebarCollapsed: false,
  
  // 数据状态
  dataCategories: [],
  dataSources: [],
  charts: [],
  dashboards: [],
  
  // UI状态
  notifications: [],
  modals: {},
  
  // 错误状态
  errors: [],
  
  // 性能监控
  performance: {
    apiCalls: 0,
    slowRequests: 0,
    errors: 0
  }
};

// Action类型
const ActionTypes = {
  // 用户相关
  SET_USER: 'SET_USER',
  SET_LOADING: 'SET_LOADING',
  LOGOUT: 'LOGOUT',
  
  // 应用设置
  SET_THEME: 'SET_THEME',
  SET_LANGUAGE: 'SET_LANGUAGE',
  TOGGLE_SIDEBAR: 'TOGGLE_SIDEBAR',
  
  // 数据相关
  SET_DATA_CATEGORIES: 'SET_DATA_CATEGORIES',
  SET_DATA_SOURCES: 'SET_DATA_SOURCES',
  SET_CHARTS: 'SET_CHARTS',
  SET_DASHBOARDS: 'SET_DASHBOARDS',
  
  // UI相关
  ADD_NOTIFICATION: 'ADD_NOTIFICATION',
  REMOVE_NOTIFICATION: 'REMOVE_NOTIFICATION',
  SET_MODAL: 'SET_MODAL',
  CLOSE_MODAL: 'CLOSE_MODAL',
  
  // 错误处理
  ADD_ERROR: 'ADD_ERROR',
  CLEAR_ERRORS: 'CLEAR_ERRORS',
  
  // 性能监控
  INCREMENT_API_CALLS: 'INCREMENT_API_CALLS',
  INCREMENT_SLOW_REQUESTS: 'INCREMENT_SLOW_REQUESTS',
  INCREMENT_ERRORS: 'INCREMENT_ERRORS'
};

// Reducer函数
const appReducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.SET_USER:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false
      };
      
    case ActionTypes.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload
      };
      
    case ActionTypes.LOGOUT:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false
      };
      
    case ActionTypes.SET_THEME:
      return {
        ...state,
        theme: action.payload
      };
      
    case ActionTypes.SET_LANGUAGE:
      return {
        ...state,
        language: action.payload
      };
      
    case ActionTypes.TOGGLE_SIDEBAR:
      return {
        ...state,
        sidebarCollapsed: !state.sidebarCollapsed
      };
      
    case ActionTypes.SET_DATA_CATEGORIES:
      return {
        ...state,
        dataCategories: action.payload
      };
      
    case ActionTypes.SET_DATA_SOURCES:
      return {
        ...state,
        dataSources: action.payload
      };
      
    case ActionTypes.SET_CHARTS:
      return {
        ...state,
        charts: action.payload
      };
      
    case ActionTypes.SET_DASHBOARDS:
      return {
        ...state,
        dashboards: action.payload
      };
      
    case ActionTypes.ADD_NOTIFICATION:
      return {
        ...state,
        notifications: [...state.notifications, action.payload]
      };
      
    case ActionTypes.REMOVE_NOTIFICATION:
      return {
        ...state,
        notifications: state.notifications.filter(n => n.id !== action.payload)
      };
      
    case ActionTypes.SET_MODAL:
      return {
        ...state,
        modals: {
          ...state.modals,
          [action.payload.name]: action.payload.props
        }
      };
      
    case ActionTypes.CLOSE_MODAL:
      return {
        ...state,
        modals: {
          ...state.modals,
          [action.payload]: null
        }
      };
      
    case ActionTypes.ADD_ERROR:
      return {
        ...state,
        errors: [...state.errors, action.payload]
      };
      
    case ActionTypes.CLEAR_ERRORS:
      return {
        ...state,
        errors: []
      };
      
    case ActionTypes.INCREMENT_API_CALLS:
      return {
        ...state,
        performance: {
          ...state.performance,
          apiCalls: state.performance.apiCalls + 1
        }
      };
      
    case ActionTypes.INCREMENT_SLOW_REQUESTS:
      return {
        ...state,
        performance: {
          ...state.performance,
          slowRequests: state.performance.slowRequests + 1
        }
      };
      
    case ActionTypes.INCREMENT_ERRORS:
      return {
        ...state,
        performance: {
          ...state.performance,
          errors: state.performance.errors + 1
        }
      };
      
    default:
      return state;
  }
};

// 创建Context
const AppContext = createContext();

// Context Provider组件
export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // 初始化应用状态
  useEffect(() => {
    initializeApp();
  }, []);

  // 初始化应用
  const initializeApp = async () => {
    try {
      dispatch({ type: ActionTypes.SET_LOADING, payload: true });
      
      // 从缓存恢复设置
      const cachedTheme = cacheManager.get('theme', true);
      const cachedLanguage = cacheManager.get('language', true);
      const cachedSidebarState = cacheManager.get('sidebarCollapsed', true);
      
      if (cachedTheme) {
        dispatch({ type: ActionTypes.SET_THEME, payload: cachedTheme });
      }
      
      if (cachedLanguage) {
        dispatch({ type: ActionTypes.SET_LANGUAGE, payload: cachedLanguage });
      }
      
      if (cachedSidebarState !== null) {
        dispatch({ type: ActionTypes.TOGGLE_SIDEBAR });
      }
      
      // 检查用户认证状态
      if (authService.isAuthenticated()) {
        const userInfo = authService.getCurrentUser();
        dispatch({ type: ActionTypes.SET_USER, payload: userInfo });
      }
      
    } catch (error) {
      console.error('应用初始化失败:', error);
      dispatch({ type: ActionTypes.ADD_ERROR, payload: error.message });
    } finally {
      dispatch({ type: ActionTypes.SET_LOADING, payload: false });
    }
  };

  // Action创建函数
  const actions = {
    // 用户相关
    setUser: (user) => {
      dispatch({ type: ActionTypes.SET_USER, payload: user });
    },
    
    logout: () => {
      authService.logout();
      dispatch({ type: ActionTypes.LOGOUT });
      cacheManager.clear();
      message.success('已成功退出登录');
    },
    
    // 应用设置
    setTheme: (theme) => {
      dispatch({ type: ActionTypes.SET_THEME, payload: theme });
      cacheManager.set('theme', theme, { persistent: true });
    },
    
    setLanguage: (language) => {
      dispatch({ type: ActionTypes.SET_LANGUAGE, payload: language });
      cacheManager.set('language', language, { persistent: true });
    },
    
    toggleSidebar: () => {
      dispatch({ type: ActionTypes.TOGGLE_SIDEBAR });
      cacheManager.set('sidebarCollapsed', !state.sidebarCollapsed, { persistent: true });
    },
    
    // 数据相关
    setDataCategories: (categories) => {
      dispatch({ type: ActionTypes.SET_DATA_CATEGORIES, payload: categories });
    },
    
    setDataSources: (sources) => {
      dispatch({ type: ActionTypes.SET_DATA_SOURCES, payload: sources });
    },
    
    setCharts: (charts) => {
      dispatch({ type: ActionTypes.SET_CHARTS, payload: charts });
    },
    
    setDashboards: (dashboards) => {
      dispatch({ type: ActionTypes.SET_DASHBOARDS, payload: dashboards });
    },
    
    // 通知相关
    addNotification: (notification) => {
      const id = Date.now().toString();
      dispatch({ 
        type: ActionTypes.ADD_NOTIFICATION, 
        payload: { ...notification, id } 
      });
      
      // 自动移除通知
      setTimeout(() => {
        actions.removeNotification(id);
      }, notification.duration || 5000);
    },
    
    removeNotification: (id) => {
      dispatch({ type: ActionTypes.REMOVE_NOTIFICATION, payload: id });
    },
    
    // 模态框相关
    openModal: (name, props = {}) => {
      dispatch({ 
        type: ActionTypes.SET_MODAL, 
        payload: { name, props } 
      });
    },
    
    closeModal: (name) => {
      dispatch({ type: ActionTypes.CLOSE_MODAL, payload: name });
    },
    
    // 错误处理
    addError: (error) => {
      dispatch({ type: ActionTypes.ADD_ERROR, payload: error });
      message.error(error);
    },
    
    clearErrors: () => {
      dispatch({ type: ActionTypes.CLEAR_ERRORS });
    },
    
    // 性能监控
    incrementApiCalls: () => {
      dispatch({ type: ActionTypes.INCREMENT_API_CALLS });
    },
    
    incrementSlowRequests: () => {
      dispatch({ type: ActionTypes.INCREMENT_SLOW_REQUESTS });
    },
    
    incrementErrors: () => {
      dispatch({ type: ActionTypes.INCREMENT_ERRORS });
    }
  };

  const value = {
    state,
    actions,
    dispatch
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

// 自定义Hook
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
