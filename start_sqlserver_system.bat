@echo off
title 生产数据可视化系统 - SQL Server版本

echo ==========================================
echo   生产数据可视化系统 - SQL Server版本
echo ==========================================
echo.

echo [1] 检查SQL Server连接...
echo.

powershell -ExecutionPolicy Bypass -Command "try { $conn = New-Object System.Data.SqlClient.SqlConnection('Server=localhost\SQLEXPRESS;Database=ProductionDataVisualizationDb;Trusted_Connection=true;TrustServerCertificate=true;'); $conn.Open(); Write-Host '✓ SQL Server连接正常' -ForegroundColor Green; $conn.Close() } catch { Write-Host '✗ SQL Server连接失败' -ForegroundColor Red; exit 1 }"

if %errorlevel% neq 0 (
    echo SQL Server连接失败，请检查SQL Server是否运行
    pause
    exit /b 1
)

echo.
echo [2] 启动SQL Server后端...
echo.

cd /d "ProductionDataVisualization\backend\SqlServerAPI\bin\Release\net8.0\win-x64\publish"

if not exist "SqlServerAPI.exe" (
    echo 错误: 找不到 SqlServerAPI.exe
    echo 请先构建项目: dotnet publish -c Release
    pause
    exit /b 1
)

start /B SqlServerAPI.exe
cd /d "%~dp0"

echo 等待后端启动...
timeout /t 15 /nobreak >nul

echo.
echo [3] 验证后端API...
echo.

curl -s http://localhost:5000/api/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 后端API正常运行
) else (
    echo ✗ 后端API启动失败
    pause
    exit /b 1
)

echo.
echo [4] 启动前端应用...
echo.

curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 前端应用已运行
) else (
    echo 启动React前端...
    cd /d "ProductionDataVisualization\frontend"
    start /B npm start
    cd /d "%~dp0"
    
    echo 等待前端启动...
    timeout /t 20 /nobreak >nul
)

echo.
echo [5] 系统状态检查...
echo.

echo 后端健康检查:
curl -s http://localhost:5000/api/health
echo.
echo.

echo 数据库连接测试:
powershell -ExecutionPolicy Bypass -Command "try { $conn = New-Object System.Data.SqlClient.SqlConnection('Server=localhost\SQLEXPRESS;Database=ProductionDataVisualizationDb;Trusted_Connection=true;TrustServerCertificate=true;'); $conn.Open(); $cmd = $conn.CreateCommand(); $cmd.CommandText = 'SELECT COUNT(*) FROM Users'; $userCount = $cmd.ExecuteScalar(); Write-Host \"用户数量: $userCount\" -ForegroundColor Green; $conn.Close() } catch { Write-Host '数据库查询失败' -ForegroundColor Red }"
echo.

echo ==========================================
echo   SQL Server系统启动完成!
echo ==========================================
echo.
echo 🎉 系统信息:
echo   ✓ 数据库: SQL Server (ProductionDataVisualizationDb)
echo   ✓ 后端API: http://localhost:5000
echo   ✓ 前端应用: http://localhost:3000
echo   ✓ 数据存储: 长期存储在SQL Server中
echo.
echo 🔐 登录信息:
echo   用户名: admin
echo   密码: admin123
echo.
echo 📊 数据特点:
echo   ✓ 用户信息: 存储在SQL Server Users表
echo   ✓ 导入数据: 存储在SQL Server动态表
echo   ✓ 系统配置: 存储在SQL Server SystemSettings表
echo   ✓ 长期保存: 所有数据持久化存储
echo.
echo 正在打开浏览器...
start http://localhost:3000
echo.
echo 现在所有数据都保存在SQL Server中，适合长期存储!
echo.

pause
