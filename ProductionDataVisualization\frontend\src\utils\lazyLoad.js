import React, { Suspense } from 'react';
import { Spin } from 'antd';

/**
 * 懒加载组件包装器
 * @param {Function} importFunc - 动态导入函数
 * @param {Object} fallback - 加载时的占位组件
 * @returns {React.Component} 懒加载组件
 */
export const lazyLoad = (importFunc, fallback = null) => {
  const LazyComponent = React.lazy(importFunc);
  
  return (props) => (
    <Suspense 
      fallback={
        fallback || (
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '200px' 
          }}>
            <Spin size="large" tip="加载中..." />
          </div>
        )
      }
    >
      <LazyComponent {...props} />
    </Suspense>
  );
};

/**
 * 页面级懒加载
 * @param {Function} importFunc - 动态导入函数
 * @returns {React.Component} 懒加载页面组件
 */
export const lazyLoadPage = (importFunc) => {
  return lazyLoad(importFunc, (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '100vh',
      flexDirection: 'column',
      gap: '16px'
    }}>
      <Spin size="large" />
      <div style={{ color: '#666', fontSize: '16px' }}>页面加载中...</div>
    </div>
  ));
};

/**
 * 图表组件懒加载
 * @param {Function} importFunc - 动态导入函数
 * @returns {React.Component} 懒加载图表组件
 */
export const lazyLoadChart = (importFunc) => {
  return lazyLoad(importFunc, (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '300px',
      border: '1px dashed #d9d9d9',
      borderRadius: '8px',
      backgroundColor: '#fafafa'
    }}>
      <Spin size="large" tip="图表加载中..." />
    </div>
  ));
};

/**
 * 模态框组件懒加载
 * @param {Function} importFunc - 动态导入函数
 * @returns {React.Component} 懒加载模态框组件
 */
export const lazyLoadModal = (importFunc) => {
  return lazyLoad(importFunc, (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '100px'
    }}>
      <Spin tip="加载中..." />
    </div>
  ));
};

// 预定义的懒加载页面组件
export const LazyDashboard = lazyLoadPage(() => import('../pages/Dashboard'));
export const LazyUserManagement = lazyLoadPage(() => import('../pages/UserManagement'));
export const LazyLogin = lazyLoadPage(() => import('../pages/Login'));
export const LazyRegister = lazyLoadPage(() => import('../pages/Register'));

// Assan风格页面懒加载
export const LazyAssanLogin = lazyLoadPage(() => import('../pages/AssanLogin'));
export const LazyAssanRegister = lazyLoadPage(() => import('../pages/AssanRegister'));

// 预定义的懒加载图表组件
export const LazyBarChart = lazyLoadChart(() => import('../components/charts/EnhancedBarChart'));
export const LazyLineChart = lazyLoadChart(() => import('../components/charts/EnhancedLineChart'));
export const LazyPieChart = lazyLoadChart(() => import('../components/charts/EnhancedPieChart'));
