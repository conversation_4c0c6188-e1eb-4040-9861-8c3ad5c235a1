<!DOCTYPE html>
<html>
<head>
    <title>前端连接修复工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 12px 24px; margin: 8px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        button:hover { background-color: #0056b3; }
        button.danger { background-color: #dc3545; }
        button.danger:hover { background-color: #c82333; }
        button.success { background-color: #28a745; }
        button.success:hover { background-color: #218838; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        input { padding: 8px; margin: 5px; border: 1px solid #ccc; border-radius: 3px; width: 200px; }
        label { display: inline-block; width: 100px; font-weight: bold; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1, h2, h3 { color: #333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 前端连接修复工具</h1>
        
        <div class="test-result info">
            <strong>🎯 目标:</strong> 解决"无法连接到服务器"的问题<br>
            <strong>📍 后端地址:</strong> http://localhost:5000<br>
            <strong>📍 前端地址:</strong> http://localhost:3000
        </div>

        <div class="section">
            <h3>📊 系统状态检查</h3>
            <div id="systemStatus">
                <div>后端API: <span class="status-indicator status-offline"></span><span id="backendStatus">检查中...</span></div>
                <div>前端应用: <span class="status-indicator status-offline"></span><span id="frontendStatus">检查中...</span></div>
                <div>数据库: <span class="status-indicator status-offline"></span><span id="databaseStatus">检查中...</span></div>
            </div>
        </div>

        <div class="section">
            <h3>🔧 快速修复操作</h3>
            <button onclick="checkAllServices()">🔍 检查所有服务</button>
            <button onclick="clearBrowserCache()" class="danger">🗑️ 清除浏览器缓存</button>
            <button onclick="testDirectConnection()">🌐 测试直接连接</button>
            <button onclick="fixCorsIssues()" class="success">🔗 修复CORS问题</button>
        </div>

        <div class="section">
            <h3>📋 用户注册测试</h3>
            <div>
                <label>用户名:</label>
                <input type="text" id="testUsername" placeholder="输入测试用户名">
            </div>
            <div>
                <label>邮箱:</label>
                <input type="email" id="testEmail" placeholder="输入测试邮箱">
            </div>
            <div>
                <label>密码:</label>
                <input type="password" id="testPassword" value="password123" placeholder="输入密码">
            </div>
            <div>
                <label>全名:</label>
                <input type="text" id="testFullName" value="测试用户" placeholder="输入全名">
            </div>
            <button onclick="generateTestUser()">🎲 生成测试用户</button>
            <button onclick="testUserRegistration()" class="success">✅ 测试注册</button>
        </div>

        <div class="section">
            <h3>📝 诊断日志</h3>
            <button onclick="clearResults()">清除日志</button>
            <div id="results"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            const indicator = element.parentElement.querySelector('.status-indicator');
            element.textContent = message;
            indicator.className = `status-indicator ${status ? 'status-online' : 'status-offline'}`;
        }
        
        async function checkBackendHealth() {
            try {
                const response = await fetch(`${API_BASE}/api/health`, {
                    method: 'GET',
                    mode: 'cors',
                    cache: 'no-cache'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('backendStatus', true, '✅ 正常运行');
                    addResult(`✅ 后端API正常: ${data.status}`, 'success');
                    return true;
                } else {
                    updateStatus('backendStatus', false, `❌ HTTP ${response.status}`);
                    addResult(`❌ 后端API错误: HTTP ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                updateStatus('backendStatus', false, '❌ 连接失败');
                addResult(`❌ 后端连接失败: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function checkDatabaseHealth() {
            try {
                const response = await fetch(`${API_BASE}/api/health/database`, {
                    method: 'GET',
                    mode: 'cors',
                    cache: 'no-cache'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('databaseStatus', true, '✅ 连接正常');
                    addResult(`✅ 数据库正常: ${data.userCount} 个用户`, 'success');
                    return true;
                } else {
                    updateStatus('databaseStatus', false, `❌ HTTP ${response.status}`);
                    addResult(`❌ 数据库错误: HTTP ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                updateStatus('databaseStatus', false, '❌ 连接失败');
                addResult(`❌ 数据库连接失败: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function checkFrontendHealth() {
            try {
                const response = await fetch('http://localhost:3000', {
                    method: 'GET',
                    mode: 'no-cors',
                    cache: 'no-cache'
                });
                
                updateStatus('frontendStatus', true, '✅ 正常运行');
                addResult('✅ 前端应用正常运行', 'success');
                return true;
            } catch (error) {
                updateStatus('frontendStatus', false, '❌ 连接失败');
                addResult(`❌ 前端连接失败: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function checkAllServices() {
            addResult('🔍 开始检查所有服务...', 'info');
            
            const backendOk = await checkBackendHealth();
            const databaseOk = await checkDatabaseHealth();
            const frontendOk = await checkFrontendHealth();
            
            if (backendOk && databaseOk && frontendOk) {
                addResult('🎉 所有服务正常运行！', 'success');
            } else {
                addResult('⚠️ 部分服务存在问题，请查看详细信息', 'warning');
            }
        }
        
        function clearBrowserCache() {
            addResult('🗑️ 清除浏览器缓存...', 'info');
            
            // 清除localStorage
            localStorage.clear();
            addResult('✅ localStorage已清除', 'success');
            
            // 清除sessionStorage
            sessionStorage.clear();
            addResult('✅ sessionStorage已清除', 'success');
            
            // 提示用户手动清除缓存
            addResult('⚠️ 请手动清除浏览器缓存：按 Ctrl+Shift+R 强制刷新', 'warning');
            
            setTimeout(() => {
                addResult('🔄 正在重新加载页面...', 'info');
                window.location.reload(true);
            }, 2000);
        }
        
        async function testDirectConnection() {
            addResult('🌐 测试直接连接...', 'info');
            
            const testUrls = [
                `${API_BASE}/api/health`,
                `${API_BASE}/api/health/database`,
                `${API_BASE}/api/simple-auth/users`,
                `${API_BASE}/api/auth/login`
            ];
            
            for (const url of testUrls) {
                try {
                    const response = await fetch(url, {
                        method: 'GET',
                        mode: 'cors',
                        cache: 'no-cache'
                    });
                    
                    if (response.ok) {
                        addResult(`✅ ${url} - 连接成功`, 'success');
                    } else {
                        addResult(`❌ ${url} - HTTP ${response.status}`, 'error');
                    }
                } catch (error) {
                    addResult(`❌ ${url} - ${error.message}`, 'error');
                }
            }
        }
        
        function fixCorsIssues() {
            addResult('🔗 尝试修复CORS问题...', 'info');
            
            // 设置一些可能有帮助的headers
            const testFetch = async () => {
                try {
                    const response = await fetch(`${API_BASE}/api/health`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json',
                        },
                        mode: 'cors',
                        credentials: 'omit',
                        cache: 'no-cache'
                    });
                    
                    if (response.ok) {
                        addResult('✅ CORS问题已修复！', 'success');
                    } else {
                        addResult(`❌ CORS修复失败: HTTP ${response.status}`, 'error');
                    }
                } catch (error) {
                    addResult(`❌ CORS修复失败: ${error.message}`, 'error');
                }
            };
            
            testFetch();
        }
        
        function generateTestUser() {
            const timestamp = Date.now();
            document.getElementById('testUsername').value = `user_${timestamp}`;
            document.getElementById('testEmail').value = `user_${timestamp}@example.com`;
            document.getElementById('testPassword').value = 'password123';
            document.getElementById('testFullName').value = `测试用户_${timestamp}`;
            addResult('🎲 已生成测试用户信息', 'success');
        }
        
        async function testUserRegistration() {
            const username = document.getElementById('testUsername').value;
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;
            const fullName = document.getElementById('testFullName').value;
            
            if (!username || !email || !password || !fullName) {
                addResult('❌ 请填写所有字段', 'error');
                return;
            }
            
            const userData = {
                username: username,
                email: email,
                password: password,
                fullName: fullName
            };
            
            addResult(`🔍 测试用户注册: ${username}`, 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    mode: 'cors',
                    body: JSON.stringify(userData)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`🎉 用户注册成功！<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    const errorData = await response.json().catch(() => ({ message: '无法解析错误响应' }));
                    addResult(`❌ 用户注册失败: HTTP ${response.status}<br><pre>${JSON.stringify(errorData, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`❌ 网络错误: ${error.message}`, 'error');
                addResult('💡 这可能是前端无法连接到后端的问题', 'warning');
            }
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            addResult('🚀 前端连接修复工具已加载', 'info');
            generateTestUser();
            setTimeout(checkAllServices, 1000);
        };
    </script>
</body>
</html>
