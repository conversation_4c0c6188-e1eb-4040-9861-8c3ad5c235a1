{"@t":"2025-07-16T03:04:33.5398541Z","@mt":"Now listening on: {address}","address":"http://localhost:5000","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-16T03:04:33.5760333Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-16T03:04:33.5765214Z","@mt":"Hosting environment: {EnvName}","EnvName":"Production","SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-16T03:04:33.5768964Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"C:\\Users\\<USER>\\Documents\\Trae_Files\\Report\\ProductionDataVisualization\\backend\\src\\API","SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-16T03:04:54.2012539Z","@mt":"HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms","@r":["16.0312"],"@tr":"bd9b8978da43ae147db8899c9c4cf375","@sp":"3e0fed001bffad90","RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","RequestMethod":"OPTIONS","RequestPath":"/api/auth/me","StatusCode":204,"Elapsed":16.0312,"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE42M4SDBPP:00000001","ConnectionId":"0HNE42M4SDBPP","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-16T03:04:54.2012504Z","@mt":"HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms","@r":["16.0523"],"@tr":"758804d7fa02ac9dcc5b9335726115ad","@sp":"5bacf1a862892f2d","RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","RequestMethod":"OPTIONS","RequestPath":"/api/auth/me","StatusCode":204,"Elapsed":16.0523,"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE42M4SDBPO:00000001","ConnectionId":"0HNE42M4SDBPO","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-16T03:04:54.5312883Z","@mt":"HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms","@r":["312.3969"],"@l":"Warning","@tr":"bde90e15a9985f7b2b0bf2552e43a6c1","@sp":"b4ee7f440b1a9b5b","RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","RequestMethod":"GET","RequestPath":"/api/auth/me","StatusCode":404,"Elapsed":312.3969,"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE42M4SDBPP:00000002","ConnectionId":"0HNE42M4SDBPP","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-16T03:04:54.5422472Z","@mt":"HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms","@r":["7.5344"],"@l":"Warning","@tr":"7fef6cda6df6da23a06f94d4ea4b950b","@sp":"2329736a874fd10a","RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","RequestMethod":"GET","RequestPath":"/api/auth/me","StatusCode":404,"Elapsed":7.5344,"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE42M4SDBPP:00000003","ConnectionId":"0HNE42M4SDBPP","Application":"ProductionDataVisualization","Environment":"Production"}
