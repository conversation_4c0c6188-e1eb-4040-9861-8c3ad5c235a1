@echo off
title 测试修复：覆盖模式和1000行限制

echo ========================================
echo   测试修复：覆盖模式和1000行限制
echo ========================================
echo.

echo [INFO] 修复内容:
echo 1. 覆盖模式现在会完全清空整个表
echo 2. 移除了1000行导入限制，支持导入全部数据
echo 3. 区分预览数据和导入数据
echo.

echo [INFO] 启动后端服务...
cd backend\SqlServerAPI
start "Backend" cmd /k "dotnet run"

echo [INFO] 等待后端启动...
timeout /t 10 /nobreak

echo [INFO] 启动前端...
cd ..\..\frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 15 /nobreak

echo.
echo ========================================
echo   测试指南
echo ========================================
echo.
echo 测试1: 验证1000行限制修复
echo 1. 准备一个超过1000行的数据文件
echo 2. 访问: http://localhost:3000/data-import
echo 3. 上传文件并查看解析结果
echo 4. 注意预览显示行数 vs 总行数
echo 5. 完成导入后检查数据库实际行数
echo.
echo 测试2: 验证覆盖模式修复
echo 1. 导入一个文件（记录行数）
echo 2. 修改文件内容（增加或减少行数）
echo 3. 再次导入相同文件名的文件
echo 4. 选择"覆盖数据"模式
echo 5. 检查数据库中是否完全替换为新数据
echo.
echo 预期结果:
echo - 导入时处理全部行数，不限制1000行
echo - 覆盖模式完全清空表后重新导入
echo - 后端控制台显示详细的导入进度
echo.

pause
