import React, { useEffect, useRef, useState } from 'react';

const ElegantTechBackground = ({ 
  particleCount = 60, 
  lineDistance = 120, 
  particleSpeed = 0.3,
  showGrid = true,
  showFloatingElements = true
}) => {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const particlesRef = useRef([]);
  const mouseRef = useRef({ x: 0, y: 0 });
  const [time, setTime] = useState(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    let width = window.innerWidth;
    let height = window.innerHeight;

    // 设置画布尺寸
    const resizeCanvas = () => {
      width = window.innerWidth;
      height = window.innerHeight;
      canvas.width = width;
      canvas.height = height;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // 鼠标跟踪
    const handleMouseMove = (e) => {
      mouseRef.current = { x: e.clientX, y: e.clientY };
    };
    window.addEventListener('mousemove', handleMouseMove);

    // 精致粒子类
    class ElegantParticle {
      constructor() {
        this.x = Math.random() * width;
        this.y = Math.random() * height;
        this.vx = (Math.random() - 0.5) * particleSpeed;
        this.vy = (Math.random() - 0.5) * particleSpeed;
        this.radius = Math.random() * 1.5 + 0.5;
        this.opacity = Math.random() * 0.4 + 0.2;
        this.pulseSpeed = Math.random() * 0.02 + 0.01;
        this.pulsePhase = Math.random() * Math.PI * 2;
        this.hue = Math.random() * 60 + 200; // 蓝紫色调
      }

      update(time) {
        this.x += this.vx;
        this.y += this.vy;

        // 边界检测
        if (this.x < 0 || this.x > width) this.vx *= -1;
        if (this.y < 0 || this.y > height) this.vy *= -1;

        // 轻微脉冲效果
        this.currentRadius = this.radius + Math.sin(time * this.pulseSpeed + this.pulsePhase) * 0.3;
        this.currentOpacity = this.opacity + Math.sin(time * this.pulseSpeed + this.pulsePhase) * 0.1;

        // 鼠标交互（更温和）
        const dx = mouseRef.current.x - this.x;
        const dy = mouseRef.current.y - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < 150) {
          const force = (150 - distance) / 150;
          this.vx += (dx / distance) * force * 0.005;
          this.vy += (dy / distance) * force * 0.005;
        }
      }

      draw(ctx) {
        // 柔和发光效果
        const gradient = ctx.createRadialGradient(
          this.x, this.y, 0,
          this.x, this.y, this.currentRadius * 2
        );
        gradient.addColorStop(0, `hsla(${this.hue}, 70%, 80%, ${this.currentOpacity})`);
        gradient.addColorStop(0.7, `hsla(${this.hue}, 70%, 70%, ${this.currentOpacity * 0.3})`);
        gradient.addColorStop(1, `hsla(${this.hue}, 70%, 60%, 0)`);

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.currentRadius * 2, 0, Math.PI * 2);
        ctx.fill();

        // 核心粒子
        ctx.fillStyle = `hsla(${this.hue}, 80%, 90%, ${this.currentOpacity * 0.8})`;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.currentRadius, 0, Math.PI * 2);
        ctx.fill();
      }
    }

    // 初始化粒子
    particlesRef.current = [];
    for (let i = 0; i < particleCount; i++) {
      particlesRef.current.push(new ElegantParticle());
    }

    // 绘制精致网格
    const drawElegantGrid = (ctx, time) => {
      if (!showGrid) return;
      
      const gridSize = 80;
      const offsetX = (time * 0.02) % gridSize;
      const offsetY = (time * 0.015) % gridSize;
      
      ctx.strokeStyle = `hsla(220, 50%, 80%, 0.15)`;
      ctx.lineWidth = 0.5;
      
      // 垂直线
      for (let x = -offsetX; x < width + gridSize; x += gridSize) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
      }
      
      // 水平线
      for (let y = -offsetY; y < height + gridSize; y += gridSize) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
      }
    };

    // 绘制浮动几何元素
    const drawFloatingElements = (ctx, time) => {
      if (!showFloatingElements) return;
      
      // 浮动圆环
      const rings = 3;
      for (let i = 0; i < rings; i++) {
        const x = width * (0.2 + i * 0.3);
        const y = height * (0.3 + Math.sin(time * 0.001 + i) * 0.1);
        const radius = 40 + Math.sin(time * 0.002 + i) * 10;
        
        ctx.strokeStyle = `hsla(${200 + i * 20}, 60%, 70%, 0.2)`;
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.stroke();
        
        // 内圈
        ctx.strokeStyle = `hsla(${200 + i * 20}, 60%, 80%, 0.1)`;
        ctx.beginPath();
        ctx.arc(x, y, radius * 0.7, 0, Math.PI * 2);
        ctx.stroke();
      }
      
      // 浮动三角形
      const triangles = 2;
      for (let i = 0; i < triangles; i++) {
        const x = width * (0.7 + i * 0.2);
        const y = height * (0.6 + Math.sin(time * 0.0015 + i) * 0.15);
        const size = 25 + Math.sin(time * 0.003 + i) * 5;
        
        ctx.strokeStyle = `hsla(${180 + i * 30}, 50%, 75%, 0.15)`;
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(x, y - size);
        ctx.lineTo(x - size * 0.866, y + size * 0.5);
        ctx.lineTo(x + size * 0.866, y + size * 0.5);
        ctx.closePath();
        ctx.stroke();
      }
    };

    // 绘制连接线
    const drawConnections = (ctx) => {
      const particles = particlesRef.current;
      
      for (let i = 0; i < particles.length; i++) {
        for (let j = i + 1; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x;
          const dy = particles[i].y - particles[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < lineDistance) {
            const opacity = (1 - distance / lineDistance) * 0.2;
            ctx.strokeStyle = `hsla(220, 60%, 80%, ${opacity})`;
            ctx.lineWidth = 0.5;
            ctx.beginPath();
            ctx.moveTo(particles[i].x, particles[i].y);
            ctx.lineTo(particles[j].x, particles[j].y);
            ctx.stroke();
          }
        }
      }
    };

    // 主动画循环
    const animate = (currentTime) => {
      setTime(currentTime);
      
      // 清空画布
      ctx.clearRect(0, 0, width, height);
      
      // 绘制背景效果
      drawElegantGrid(ctx, currentTime);
      drawFloatingElements(ctx, currentTime);
      
      // 更新和绘制粒子
      particlesRef.current.forEach(particle => {
        particle.update(currentTime);
        particle.draw(ctx);
      });
      
      // 绘制连接线
      drawConnections(ctx);
      
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', resizeCanvas);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [particleCount, lineDistance, particleSpeed, showGrid, showFloatingElements]);

  // 修改return部分
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      zIndex: -1,
      background: `
        linear-gradient(135deg, 
          #ffffff 0%, 
          #f8faff 25%, 
          #f5f7fa 50%, 
          #f0f4f8 75%, 
          #ffffff 100%
        )
      `,
      overflow: 'hidden'
    }}>
      <canvas
        ref={canvasRef}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          opacity: 0.5 // 降低粒子效果的不透明度，使其更加微妙
        }}
      />
      
      {/* 更加微妙的光效层 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: `
          radial-gradient(circle at 20% 20%, hsla(220, 30%, 95%, 0.2) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, hsla(240, 20%, 95%, 0.15) 0%, transparent 50%),
          radial-gradient(circle at 60% 40%, hsla(200, 40%, 97%, 0.1) 0%, transparent 50%)
        `,
        pointerEvents: 'none'
      }} />
    </div>
  );
};

export default ElegantTechBackground;
