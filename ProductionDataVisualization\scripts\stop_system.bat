@echo off
chcp 65001 > nul
title 生产数据可视化系统停止器 / Production Data Visualization System Stopper

echo ===================================================
echo    生产数据可视化系统停止器
echo    Production Data Visualization System Stopper
echo ===================================================
echo.
echo 正在停止系统，请稍候...
echo Stopping system, please wait...
echo.

REM 设置工作目录为脚本所在目录
cd /d %~dp0

REM 创建日志目录（如果不存在）
if not exist ..\logs mkdir ..\logs

REM 创建停止日志文件
echo 系统停止时间: %date% %time% > ..\logs\system_stop.log

REM 停止占用5000端口的进程（后端）
echo 停止后端服务... / Stopping backend service...
echo 停止后端服务... / Stopping backend service... >> ..\logs\system_stop.log

set backend_stopped=false
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000 ^| findstr LISTENING') do (
    echo 终止进程 / Terminating process: %%a
    echo 终止进程 / Terminating process: %%a >> ..\logs\system_stop.log
    taskkill /F /PID %%a >nul 2>nul
    set backend_stopped=true
)

if "%backend_stopped%"=="true" (
    echo [成功/SUCCESS] 后端服务已停止 / Backend service stopped
    echo [成功/SUCCESS] 后端服务已停止 / Backend service stopped >> ..\logs\system_stop.log
) else (
    echo [信息/INFO] 未检测到运行中的后端服务 / No running backend service detected
    echo [信息/INFO] 未检测到运行中的后端服务 / No running backend service detected >> ..\logs\system_stop.log
)

REM 停止占用3000端口的进程（前端）
echo 停止前端服务... / Stopping frontend service...
echo 停止前端服务... / Stopping frontend service... >> ..\logs\system_stop.log

set frontend_stopped=false
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000 ^| findstr LISTENING') do (
    echo 终止进程 / Terminating process: %%a
    echo 终止进程 / Terminating process: %%a >> ..\logs\system_stop.log
    taskkill /F /PID %%a >nul 2>nul
    set frontend_stopped=true
)

if "%frontend_stopped%"=="true" (
    echo [成功/SUCCESS] 前端服务已停止 / Frontend service stopped
    echo [成功/SUCCESS] 前端服务已停止 / Frontend service stopped >> ..\logs\system_stop.log
) else (
    echo [信息/INFO] 未检测到运行中的前端服务 / No running frontend service detected
    echo [信息/INFO] 未检测到运行中的前端服务 / No running frontend service detected >> ..\logs\system_stop.log
)

REM 停止可能的npm进程
echo 停止npm相关进程... / Stopping npm related processes...
echo 停止npm相关进程... / Stopping npm related processes... >> ..\logs\system_stop.log

taskkill /F /IM node.exe >nul 2>nul
if %errorlevel% equ 0 (
    echo [成功/SUCCESS] node进程已停止 / Node processes stopped
    echo [成功/SUCCESS] node进程已停止 / Node processes stopped >> ..\logs\system_stop.log
) else (
    echo [信息/INFO] 未检测到运行中的node进程 / No running node processes detected
    echo [信息/INFO] 未检测到运行中的node进程 / No running node processes detected >> ..\logs\system_stop.log
)

REM 停止可能的dotnet进程
echo 停止dotnet相关进程... / Stopping dotnet related processes...
echo 停止dotnet相关进程... / Stopping dotnet related processes... >> ..\logs\system_stop.log

wmic process where "commandline like '%%dotnet run%%'" call terminate >nul 2>nul

REM 再次检查端口，确保所有服务都已停止
echo 验证所有服务是否已停止... / Verifying all services are stopped...
echo 验证所有服务是否已停止... / Verifying all services are stopped... >> ..\logs\system_stop.log

set all_stopped=true

netstat -ano | findstr :5000 | findstr LISTENING >nul 2>nul
if %errorlevel% equ 0 (
    echo [警告/WARNING] 端口5000仍被占用，尝试强制终止... / Port 5000 is still in use, trying to force terminate...
    echo [警告/WARNING] 端口5000仍被占用，尝试强制终止... / Port 5000 is still in use, trying to force terminate... >> ..\logs\system_stop.log
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000 ^| findstr LISTENING') do (
        taskkill /F /PID %%a >nul 2>nul
    )
    set all_stopped=false
)

netstat -ano | findstr :3000 | findstr LISTENING >nul 2>nul
if %errorlevel% equ 0 (
    echo [警告/WARNING] 端口3000仍被占用，尝试强制终止... / Port 3000 is still in use, trying to force terminate...
    echo [警告/WARNING] 端口3000仍被占用，尝试强制终止... / Port 3000 is still in use, trying to force terminate... >> ..\logs\system_stop.log
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000 ^| findstr LISTENING') do (
        taskkill /F /PID %%a >nul 2>nul
    )
    set all_stopped=false
)



REM 最终检查
if "%all_stopped%"=="false" (
    echo 进行最终检查... / Performing final check...
    echo 进行最终检查... / Performing final check... >> ..\logs\system_stop.log
    timeout /t 3 /nobreak > nul
    
    set final_check_passed=true
    
    netstat -ano | findstr :5000 | findstr LISTENING >nul 2>nul
    if %errorlevel% equ 0 set final_check_passed=false
    
    netstat -ano | findstr :3000 | findstr LISTENING >nul 2>nul
    if %errorlevel% equ 0 set final_check_passed=false
    
    netstat -ano | findstr :3000 | findstr LISTENING >nul 2>nul
    if %errorlevel% equ 0 set final_check_passed=false
    
    if "%final_check_passed%"=="true" (
        echo [成功/SUCCESS] 所有服务已成功停止 / All services successfully stopped
        echo [成功/SUCCESS] 所有服务已成功停止 / All services successfully stopped >> ..\logs\system_stop.log
    ) else (
        echo [警告/WARNING] 某些服务可能仍在运行 / Some services might still be running
        echo [警告/WARNING] 某些服务可能仍在运行 / Some services might still be running >> ..\logs\system_stop.log
    )
) else (
    echo [成功/SUCCESS] 所有服务已成功停止 / All services successfully stopped
    echo [成功/SUCCESS] 所有服务已成功停止 / All services successfully stopped >> ..\logs\system_stop.log
)

echo.
echo ===================================================
echo    系统状态 / System Status
echo ===================================================
echo.
echo [信息/INFO] 后端API (端口5000): 已停止 / Stopped
echo [信息/INFO] 前端服务 (端口3000/3001): 已停止 / Stopped
echo.
echo 系统已完全停止 / System completely stopped
echo 系统已完全停止 / System completely stopped >> ..\logs\system_stop.log
echo.
echo 如需重新启动系统，请运行start_system.bat / To restart the system, run start_system.bat
echo.

timeout /t 5
exit 