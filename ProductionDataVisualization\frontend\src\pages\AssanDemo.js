import React, { useState } from 'react';
import { Layout, Card, Button, Typography, Space, Row, Col, Input, Form, Select, Switch } from 'antd';
import { 
  DashboardOutlined, 
  BarChartOutlined, 
  UserOutlined, 
  SettingOutlined,
  HeartOutlined,
  StarOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import AssanNavbar from '../components/layout/AssanNavbar';

const { Header, Content } = Layout;
const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const AssanDemo = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [darkMode, setDarkMode] = useState(false);

  const handleToggle = () => {
    setCollapsed(!collapsed);
  };

  const features = [
    {
      icon: <DashboardOutlined />,
      title: '现代仪表板',
      description: '直观的数据可视化界面，实时监控生产数据'
    },
    {
      icon: <BarChartOutlined />,
      title: '智能分析',
      description: '强大的数据分析工具，深入洞察业务趋势'
    },
    {
      icon: <UserOutlined />,
      title: '用户管理',
      description: '完善的权限控制系统，安全可靠'
    },
    {
      icon: <SettingOutlined />,
      title: '灵活配置',
      description: '高度可定制的系统设置，满足不同需求'
    }
  ];

  const stats = [
    { label: '活跃用户', value: '2,847', icon: <UserOutlined />, color: '#667eea' },
    { label: '数据处理', value: '98.5%', icon: <BarChartOutlined />, color: '#764ba2' },
    { label: '系统稳定性', value: '99.9%', icon: <ThunderboltOutlined />, color: '#667eea' },
    { label: '用户满意度', value: '4.9/5', icon: <StarOutlined />, color: '#764ba2' }
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <AssanNavbar collapsed={collapsed} onToggle={handleToggle} />
      
      <Layout style={{ marginTop: '72px' }}>
        <Content style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
          {/* 英雄区域 */}
          <div style={{ 
            padding: '80px 24px', 
            textAlign: 'center',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            position: 'relative',
            overflow: 'hidden'
          }}>
            {/* 背景装饰 */}
            <div style={{
              position: 'absolute',
              top: '10%',
              left: '10%',
              width: '200px',
              height: '200px',
              borderRadius: '50%',
              background: 'rgba(255, 255, 255, 0.1)',
              animation: 'float 6s ease-in-out infinite'
            }} />
            <div style={{
              position: 'absolute',
              top: '60%',
              right: '15%',
              width: '150px',
              height: '150px',
              borderRadius: '50%',
              background: 'rgba(255, 255, 255, 0.1)',
              animation: 'float 6s ease-in-out infinite 2s'
            }} />

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              style={{ position: 'relative', zIndex: 1 }}
            >
              <Title 
                level={1} 
                style={{ 
                  color: 'white', 
                  fontSize: '48px', 
                  fontWeight: '700',
                  marginBottom: '24px',
                  textShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}
              >
                Assan 风格设计系统
              </Title>
              <Paragraph 
                style={{ 
                  color: 'rgba(255, 255, 255, 0.9)', 
                  fontSize: '20px',
                  maxWidth: '600px',
                  margin: '0 auto 40px',
                  lineHeight: '1.6'
                }}
              >
                现代、简约、大气的生产数据可视化平台界面设计
              </Paragraph>
              <Space size="large">
                <Button 
                  type="primary" 
                  size="large"
                  style={{
                    background: 'rgba(255, 255, 255, 0.2)',
                    border: '2px solid rgba(255, 255, 255, 0.3)',
                    backdropFilter: 'blur(10px)',
                    color: 'white',
                    height: '56px',
                    padding: '0 32px',
                    borderRadius: '12px',
                    fontWeight: '600'
                  }}
                  icon={<HeartOutlined />}
                >
                  开始体验
                </Button>
                <Button 
                  size="large"
                  style={{
                    background: 'transparent',
                    border: '2px solid rgba(255, 255, 255, 0.5)',
                    color: 'white',
                    height: '56px',
                    padding: '0 32px',
                    borderRadius: '12px',
                    fontWeight: '600'
                  }}
                >
                  了解更多
                </Button>
              </Space>
            </motion.div>
          </div>

          {/* 统计数据区域 */}
          <div style={{ 
            padding: '80px 24px',
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(20px)'
          }}>
            <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
              <Row gutter={[32, 32]}>
                {stats.map((stat, index) => (
                  <Col xs={24} sm={12} lg={6} key={index}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1, duration: 0.6 }}
                    >
                      <Card
                        style={{
                          textAlign: 'center',
                          border: 'none',
                          borderRadius: '20px',
                          background: 'rgba(255, 255, 255, 0.8)',
                          backdropFilter: 'blur(10px)',
                          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                        }}
                        bodyStyle={{ padding: '32px 24px' }}
                      >
                        <div style={{
                          width: '64px',
                          height: '64px',
                          borderRadius: '50%',
                          background: `linear-gradient(135deg, ${stat.color}, ${stat.color}dd)`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          margin: '0 auto 16px',
                          fontSize: '24px',
                          color: 'white'
                        }}>
                          {stat.icon}
                        </div>
                        <Title level={2} style={{ margin: '0 0 8px', color: '#1a202c' }}>
                          {stat.value}
                        </Title>
                        <Text style={{ color: '#64748b', fontSize: '16px' }}>
                          {stat.label}
                        </Text>
                      </Card>
                    </motion.div>
                  </Col>
                ))}
              </Row>
            </div>
          </div>

          {/* 功能特性区域 */}
          <div style={{ 
            padding: '80px 24px',
            background: 'rgba(248, 250, 252, 0.95)'
          }}>
            <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
              <div style={{ textAlign: 'center', marginBottom: '64px' }}>
                <Title level={2} style={{ color: '#1a202c', marginBottom: '16px' }}>
                  核心功能
                </Title>
                <Text style={{ color: '#64748b', fontSize: '18px' }}>
                  为现代企业打造的全方位数据管理解决方案
                </Text>
              </div>

              <Row gutter={[32, 32]}>
                {features.map((feature, index) => (
                  <Col xs={24} sm={12} lg={6} key={index}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 + 0.3, duration: 0.6 }}
                      whileHover={{ y: -5 }}
                    >
                      <Card
                        style={{
                          height: '100%',
                          border: 'none',
                          borderRadius: '20px',
                          background: 'rgba(255, 255, 255, 0.9)',
                          backdropFilter: 'blur(10px)',
                          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
                          transition: 'all 0.3s ease'
                        }}
                        bodyStyle={{ padding: '32px 24px' }}
                      >
                        <div style={{
                          width: '56px',
                          height: '56px',
                          borderRadius: '12px',
                          background: 'linear-gradient(135deg, #667eea, #764ba2)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          marginBottom: '20px',
                          fontSize: '24px',
                          color: 'white'
                        }}>
                          {feature.icon}
                        </div>
                        <Title level={4} style={{ marginBottom: '12px', color: '#1a202c' }}>
                          {feature.title}
                        </Title>
                        <Text style={{ color: '#64748b', lineHeight: '1.6' }}>
                          {feature.description}
                        </Text>
                      </Card>
                    </motion.div>
                  </Col>
                ))}
              </Row>
            </div>
          </div>

          {/* 表单演示区域 */}
          <div style={{ 
            padding: '80px 24px',
            background: 'rgba(255, 255, 255, 0.95)'
          }}>
            <div style={{ maxWidth: '800px', margin: '0 auto' }}>
              <div style={{ textAlign: 'center', marginBottom: '48px' }}>
                <Title level={2} style={{ color: '#1a202c', marginBottom: '16px' }}>
                  组件演示
                </Title>
                <Text style={{ color: '#64748b', fontSize: '18px' }}>
                  体验 Assan 风格的表单组件
                </Text>
              </div>

              <Card
                style={{
                  borderRadius: '20px',
                  border: 'none',
                  background: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(20px)',
                  boxShadow: '0 16px 48px rgba(0, 0, 0, 0.1)'
                }}
                bodyStyle={{ padding: '48px' }}
              >
                <Form layout="vertical" size="large">
                  <Row gutter={24}>
                    <Col xs={24} sm={12}>
                      <Form.Item label="姓名">
                        <Input 
                          placeholder="请输入姓名" 
                          style={{
                            height: '56px',
                            borderRadius: '12px',
                            border: '2px solid #e2e8f0',
                            fontSize: '16px'
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={12}>
                      <Form.Item label="邮箱">
                        <Input 
                          placeholder="请输入邮箱" 
                          style={{
                            height: '56px',
                            borderRadius: '12px',
                            border: '2px solid #e2e8f0',
                            fontSize: '16px'
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Form.Item label="部门">
                    <Select 
                      placeholder="请选择部门"
                      style={{
                        height: '56px'
                      }}
                    >
                      <Option value="tech">技术部</Option>
                      <Option value="sales">销售部</Option>
                      <Option value="hr">人事部</Option>
                    </Select>
                  </Form.Item>

                  <Form.Item>
                    <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                      <Space>
                        <Text>深色模式</Text>
                        <Switch 
                          checked={darkMode} 
                          onChange={setDarkMode}
                          style={{
                            background: darkMode ? '#667eea' : '#ccc'
                          }}
                        />
                      </Space>
                      <Button 
                        type="primary" 
                        size="large"
                        style={{
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          border: 'none',
                          borderRadius: '12px',
                          height: '56px',
                          padding: '0 32px',
                          fontWeight: '600',
                          boxShadow: '0 4px 20px rgba(102, 126, 234, 0.3)'
                        }}
                      >
                        提交
                      </Button>
                    </Space>
                  </Form.Item>
                </Form>
              </Card>
            </div>
          </div>
        </Content>
      </Layout>

      <style jsx="true">{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
      `}</style>
    </Layout>
  );
};

export default AssanDemo;
