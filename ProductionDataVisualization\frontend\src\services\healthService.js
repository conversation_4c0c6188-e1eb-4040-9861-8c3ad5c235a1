import api from './api';

/**
 * 健康检查服务
 */
class HealthService {
  /**
   * 基础健康检查
   * @returns {Promise} 健康状态
   */
  async checkHealth() {
    try {
      const response = await api.get('/api/health');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { message: '健康检查失败' };
    }
  }

  /**
   * 详细健康检查
   * @returns {Promise} 详细健康状态
   */
  async checkDetailedHealth() {
    try {
      const response = await api.get('/api/health/detailed');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { message: '详细健康检查失败' };
    }
  }

  /**
   * 检查API连通性
   * @returns {Promise} 连通性状态
   */
  async checkConnectivity() {
    try {
      const startTime = Date.now();
      const response = await api.get('/api/health');
      const endTime = Date.now();
      
      return {
        isConnected: true,
        responseTime: endTime - startTime,
        status: response.data?.data?.Status || 'Unknown',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        isConnected: false,
        responseTime: null,
        status: 'Error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 持续监控健康状态
   * @param {Function} callback - 状态更新回调
   * @param {number} interval - 检查间隔（毫秒）
   * @returns {Function} 停止监控函数
   */
  startHealthMonitoring(callback, interval = 30000) {
    let isRunning = true;
    
    const checkHealth = async () => {
      if (!isRunning) return;
      
      try {
        const health = await this.checkConnectivity();
        callback(health);
      } catch (error) {
        callback({
          isConnected: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
      
      if (isRunning) {
        setTimeout(checkHealth, interval);
      }
    };
    
    // 立即执行一次
    checkHealth();
    
    // 返回停止函数
    return () => {
      isRunning = false;
    };
  }
}

// 创建单例实例
const healthService = new HealthService();

export default healthService;
