using Domain.DataAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 数据源实体配置
    /// </summary>
    public class DataSourceConfiguration : IEntityTypeConfiguration<DataSource>
    {
        public void Configure(EntityTypeBuilder<DataSource> builder)
        {
            builder.ToTable("DataSources");

            builder.HasKey(ds => ds.Id);

            builder.Property(ds => ds.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(ds => ds.Description)
                .HasMaxLength(500);

            builder.Property(ds => ds.SourceType)
                .IsRequired()
                .HasMaxLength(10);

            builder.Property(ds => ds.FilePath)
                .IsRequired()
                .HasMaxLength(500);

            builder.Property(ds => ds.ImportedAt)
                .IsRequired();

            builder.Property(ds => ds.ImportedBy)
                .IsRequired();

            builder.Property(ds => ds.TotalRows)
                .IsRequired();

            builder.Property(ds => ds.IsProcessed)
                .IsRequired();

            builder.Property(ds => ds.ProcessingError)
                .HasMaxLength(1000);

            builder.Property(ds => ds.CreatedAt)
                .IsRequired();

            builder.Property(ds => ds.ModifiedAt);

            builder.Property(ds => ds.CreatedBy)
                .HasMaxLength(50);

            builder.Property(ds => ds.ModifiedBy)
                .HasMaxLength(50);

            // 索引
            builder.HasIndex(ds => ds.Name);
            builder.HasIndex(ds => ds.ImportedAt);
            builder.HasIndex(ds => ds.ImportedBy);
        }
    }
} 