@echo off
title 测试表名修复

echo ========================================
echo   测试表名修复
echo ========================================
echo.

echo [INFO] 修复内容:
echo 1. 修复表名生成逻辑，移除中文字符
echo 2. 添加表名验证函数
echo 3. 改进分析API的错误处理
echo 4. 添加无效映射清理功能
echo.

echo [INFO] 启动后端服务...
cd backend\SqlServerAPI
start "Backend" cmd /k "dotnet run"

echo [INFO] 等待后端启动...
timeout /t 10 /nobreak

echo [INFO] 清理无效的表映射...
curl -s -X POST http://localhost:5000/api/data-import/cleanup-invalid-mappings
echo.
echo.

echo [INFO] 测试文件分析API...
curl -s -X POST http://localhost:5000/api/data-import/analyze ^
  -H "Content-Type: application/json" ^
  -d "{\"fileName\":\"注射水分配1电导率.csv\",\"sampleData\":[{\"VarName\":\"测试变量\",\"TimeString\":\"2024-08-07 06:01:26\",\"VarValue\":0.2002894,\"Validity\":1,\"Time_ms\":4551125099.4167}]}"
echo.
echo.

echo [INFO] 启动前端...
cd ..\..\frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 15 /nobreak

echo.
echo ========================================
echo   测试指南
echo ========================================
echo.
echo 1. 访问: http://localhost:3000/data-import
echo 2. 上传包含中文名称的数据文件
echo 3. 观察以下修复效果:
echo    - 文件分析不再失败
echo    - 生成的表名只包含ASCII字符
echo    - 重复文件检测正常工作
echo.
echo 4. 检查后端控制台:
echo    - 显示"生成表名: Data_xxx_yyyyMMdd_HHmmss"
echo    - 表名只包含字母、数字和下划线
echo    - 无"对象名无效"错误
echo.

pause
