using Microsoft.Data.Sqlite;
using System.Text.Json;

var builder = WebApplication.CreateBuilder(args);

// 添加CORS支持
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(builder =>
    {
        builder.AllowAnyOrigin()
               .AllowAnyMethod()
               .AllowAnyHeader();
    });
});

var app = builder.Build();
app.UseCors();

// SQLite数据库连接字符串
var connectionString = "Data Source=ProductionDataVisualization.db;";

// 初始化数据库
await InitializeDatabase();

async Task InitializeDatabase()
{
    using var connection = new SqliteConnection(connectionString);
    await connection.OpenAsync();

    // 创建用户表
    var createUserTable = new SqliteCommand(@"
        CREATE TABLE IF NOT EXISTS Users (
            Id TEXT PRIMARY KEY,
            Username TEXT UNIQUE NOT NULL,
            Email TEXT UNIQUE NOT NULL,
            Password TEXT NOT NULL,
            FullName TEXT NOT NULL,
            IsActive INTEGER DEFAULT 1,
            CreatedAt TEXT DEFAULT (datetime('now')),
            LastLoginTime TEXT NULL,
            Role TEXT DEFAULT 'user'
        )", connection);

    await createUserTable.ExecuteNonQueryAsync();

    // 创建数据导入相关表
    var createImportTasksTable = new SqliteCommand(@"
        CREATE TABLE IF NOT EXISTS ImportTasks (
            Id TEXT PRIMARY KEY,
            FileName TEXT NOT NULL,
            FileSize INTEGER NOT NULL,
            TotalRows INTEGER DEFAULT 0,
            ProcessedRows INTEGER DEFAULT 0,
            Status TEXT DEFAULT 'Pending',
            Progress REAL DEFAULT 0,
            CreatedBy TEXT NOT NULL,
            CreatedAt TEXT DEFAULT (datetime('now')),
            StartedAt TEXT NULL,
            CompletedAt TEXT NULL,
            ErrorMessage TEXT NULL,
            ConfigData TEXT NULL
        )", connection);
    await createImportTasksTable.ExecuteNonQueryAsync();

    var createFileTableMappingsTable = new SqliteCommand(@"
        CREATE TABLE IF NOT EXISTS FileTableMappings (
            Id INTEGER PRIMARY KEY AUTOINCREMENT,
            FileName TEXT NOT NULL UNIQUE,
            TableName TEXT NOT NULL UNIQUE,
            CreatedAt TEXT DEFAULT (datetime('now')),
            LastImportAt TEXT NULL,
            TotalRows INTEGER DEFAULT 0,
            IsActive INTEGER DEFAULT 1
        )", connection);
    await createFileTableMappingsTable.ExecuteNonQueryAsync();

    // 创建系统配置表
    var createSystemSettingsTable = new SqliteCommand(@"
        CREATE TABLE IF NOT EXISTS SystemSettings (
            Id INTEGER PRIMARY KEY AUTOINCREMENT,
            SettingKey TEXT NOT NULL UNIQUE,
            SettingValue TEXT NOT NULL,
            Description TEXT NULL,
            Category TEXT DEFAULT 'General',
            CreatedAt TEXT DEFAULT (datetime('now')),
            UpdatedAt TEXT DEFAULT (datetime('now'))
        )", connection);
    await createSystemSettingsTable.ExecuteNonQueryAsync();

    // 创建角色权限表
    var createRolesTable = new SqliteCommand(@"
        CREATE TABLE IF NOT EXISTS Roles (
            Id TEXT PRIMARY KEY,
            RoleName TEXT NOT NULL UNIQUE,
            Description TEXT NULL,
            IsActive INTEGER DEFAULT 1,
            CreatedAt TEXT DEFAULT (datetime('now'))
        )", connection);
    await createRolesTable.ExecuteNonQueryAsync();

    var createUserRolesTable = new SqliteCommand(@"
        CREATE TABLE IF NOT EXISTS UserRoles (
            UserId TEXT NOT NULL,
            RoleId TEXT NOT NULL,
            AssignedAt TEXT DEFAULT (datetime('now')),
            PRIMARY KEY (UserId, RoleId),
            FOREIGN KEY (UserId) REFERENCES Users(Id),
            FOREIGN KEY (RoleId) REFERENCES Roles(Id)
        )", connection);
    await createUserRolesTable.ExecuteNonQueryAsync();

    Console.WriteLine("✅ 所有系统表创建/检查完成");

    // 初始化默认角色
    var checkRoles = new SqliteCommand("SELECT COUNT(*) FROM Roles", connection);
    var rolesExist = Convert.ToInt32(await checkRoles.ExecuteScalarAsync()) > 0;

    if (!rolesExist)
    {
        var insertRoles = new SqliteCommand(@"
            INSERT INTO Roles (Id, RoleName, Description) VALUES
            (@AdminId, 'admin', '系统管理员'),
            (@UserId, 'user', '普通用户'),
            (@ManagerId, 'manager', '管理人员')", connection);

        insertRoles.Parameters.AddWithValue("@AdminId", Guid.NewGuid().ToString());
        insertRoles.Parameters.AddWithValue("@UserId", Guid.NewGuid().ToString());
        insertRoles.Parameters.AddWithValue("@ManagerId", Guid.NewGuid().ToString());

        await insertRoles.ExecuteNonQueryAsync();
        Console.WriteLine("✅ 默认角色创建成功");
    }

    // 初始化系统配置
    var checkSettings = new SqliteCommand("SELECT COUNT(*) FROM SystemSettings", connection);
    var settingsExist = Convert.ToInt32(await checkSettings.ExecuteScalarAsync()) > 0;

    if (!settingsExist)
    {
        var insertSettings = new SqliteCommand(@"
            INSERT INTO SystemSettings (SettingKey, SettingValue, Description, Category) VALUES
            ('system.name', '生产数据可视化系统', '系统名称', 'System'),
            ('system.version', '1.0.0', '系统版本', 'System'),
            ('data.max_file_size', '50', '最大文件大小(MB)', 'DataImport'),
            ('data.allowed_extensions', '.xlsx,.xls,.csv', '允许的文件扩展名', 'DataImport'),
            ('ui.page_size', '10', '默认分页大小', 'UI'),
            ('security.session_timeout', '30', '会话超时时间(分钟)', 'Security')", connection);

        await insertSettings.ExecuteNonQueryAsync();
        Console.WriteLine("✅ 默认系统配置创建成功");
    }

    // 检查是否有管理员用户
    var checkAdmin = new SqliteCommand("SELECT COUNT(*) FROM Users WHERE Username = 'admin'", connection);
    var adminExists = Convert.ToInt32(await checkAdmin.ExecuteScalarAsync()) > 0;

    if (!adminExists)
    {
        var insertAdmin = new SqliteCommand(@"
            INSERT INTO Users (Id, Username, Email, Password, FullName, Role)
            VALUES (@Id, @Username, @Email, @Password, @FullName, @Role)", connection);

        insertAdmin.Parameters.AddWithValue("@Id", Guid.NewGuid().ToString());
        insertAdmin.Parameters.AddWithValue("@Username", "admin");
        insertAdmin.Parameters.AddWithValue("@Email", "<EMAIL>");
        insertAdmin.Parameters.AddWithValue("@Password", "admin123");
        insertAdmin.Parameters.AddWithValue("@FullName", "系统管理员");
        insertAdmin.Parameters.AddWithValue("@Role", "admin");

        await insertAdmin.ExecuteNonQueryAsync();
        Console.WriteLine("✅ 默认管理员用户创建成功 (admin/admin123)");
    }
}

// 健康检查API
app.MapGet("/api/health", () =>
{
    return Results.Ok(new
    {
        status = "healthy",
        timestamp = DateTime.UtcNow,
        version = "1.0.0",
        database = "connected"
    });
});

// 数据库健康检查
app.MapGet("/api/health/database", async () =>
{
    try
    {
        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        var command = new SqliteCommand("SELECT COUNT(*) FROM Users", connection);
        var userCount = Convert.ToInt32(await command.ExecuteScalarAsync());

        return Results.Ok(new
        {
            type = "SQLite数据库",
            status = "Connected",
            database = "ProductionDataVisualization.db",
            userCount = userCount,
            message = "SQLite数据库连接正常",
            connectionString = "SQLite文件数据库"
        });
    }
    catch (Exception ex)
    {
        return Results.Json(new
        {
            type = "SQLite数据库",
            status = "Disconnected",
            message = $"数据库连接失败: {ex.Message}"
        }, statusCode: 500);
    }
});

// 获取用户列表
app.MapGet("/api/simple-auth/users", async () =>
{
    try
    {
        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        var command = new SqliteCommand(@"
            SELECT Id, Username, Email, FullName, Role, IsActive, CreatedAt, LastLoginTime
            FROM Users", connection);

        var users = new List<object>();
        using var reader = await command.ExecuteReaderAsync();

        while (await reader.ReadAsync())
        {
            users.Add(new
            {
                id = reader["Id"].ToString(),
                username = reader["Username"].ToString(),
                email = reader["Email"].ToString(),
                fullName = reader["FullName"].ToString(),
                role = reader["Role"].ToString(),
                isActive = Convert.ToInt32(reader["IsActive"]) == 1,
                createdAt = reader["CreatedAt"].ToString(),
                lastLoginTime = reader["LastLoginTime"]?.ToString()
            });
        }

        return Results.Ok(new { users = users });
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"获取用户列表失败: {ex.Message}" }, statusCode: 500);
    }
});

// 删除重复的登录路由，使用下面的兼容性版本

// 用户注册
app.MapPost("/api/simple-auth/register", async (HttpRequest request) =>
{
    try
    {
        var body = await new StreamReader(request.Body).ReadToEndAsync();
        var userData = JsonSerializer.Deserialize<JsonElement>(body);

        var username = userData.GetProperty("username").GetString();
        var email = userData.GetProperty("email").GetString();
        var password = userData.GetProperty("password").GetString();
        var fullName = userData.GetProperty("fullName").GetString();

        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        // 检查用户名是否已存在
        var checkCommand = new SqliteCommand(@"
            SELECT COUNT(*) FROM Users WHERE Username = @username OR Email = @email", connection);
        checkCommand.Parameters.AddWithValue("@username", username);
        checkCommand.Parameters.AddWithValue("@email", email);

        var exists = Convert.ToInt32(await checkCommand.ExecuteScalarAsync()) > 0;

        if (exists)
        {
            return Results.Json(new { message = "用户名或邮箱已存在" }, statusCode: 400);
        }

        // 创建新用户
        var insertCommand = new SqliteCommand(@"
            INSERT INTO Users (Id, Username, Email, Password, FullName, Role)
            VALUES (@Id, @Username, @Email, @Password, @FullName, @Role)", connection);

        insertCommand.Parameters.AddWithValue("@Id", Guid.NewGuid().ToString());
        insertCommand.Parameters.AddWithValue("@Username", username);
        insertCommand.Parameters.AddWithValue("@Email", email);
        insertCommand.Parameters.AddWithValue("@Password", password);
        insertCommand.Parameters.AddWithValue("@FullName", fullName);
        insertCommand.Parameters.AddWithValue("@Role", "user");

        await insertCommand.ExecuteNonQueryAsync();

        return Results.Ok(new { message = "用户注册成功" });
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"注册失败: {ex.Message}" }, statusCode: 500);
    }
});

// 添加用户管理API路径 - 支持 /api/users/ 路径
// 获取所有用户 - 用户管理界面
app.MapGet("/api/users", async () =>
{
    try
    {
        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        var command = new SqliteCommand(@"
            SELECT Id, Username, Email, FullName, Role, IsActive, CreatedAt, LastLoginTime
            FROM Users
            ORDER BY CreatedAt DESC", connection);

        using var reader = await command.ExecuteReaderAsync();
        var users = new List<object>();

        while (await reader.ReadAsync())
        {
            users.Add(new
            {
                id = reader["Id"].ToString(),
                username = reader["Username"].ToString(),
                email = reader["Email"].ToString(),
                fullName = reader["FullName"].ToString(),
                role = reader["Role"].ToString(),
                isActive = Convert.ToInt32(reader["IsActive"]) == 1,
                createdAt = reader["CreatedAt"].ToString(),
                lastLoginTime = reader["LastLoginTime"].ToString()
            });
        }

        return Results.Ok(new { items = users, total = users.Count });
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"获取用户列表失败: {ex.Message}" }, statusCode: 500);
    }
});

// 创建用户 - 用户管理界面
app.MapPost("/api/users", async (HttpRequest request) =>
{
    try
    {
        var body = await new StreamReader(request.Body).ReadToEndAsync();
        var userData = JsonSerializer.Deserialize<JsonElement>(body);

        var username = userData.GetProperty("username").GetString();
        var email = userData.GetProperty("email").GetString();
        var password = userData.GetProperty("password").GetString();
        var fullName = userData.GetProperty("fullName").GetString();

        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        // 检查用户名是否已存在
        var checkCommand = new SqliteCommand(@"
            SELECT COUNT(*) FROM Users WHERE Username = @username OR Email = @email", connection);
        checkCommand.Parameters.AddWithValue("@username", username);
        checkCommand.Parameters.AddWithValue("@email", email);

        var exists = Convert.ToInt32(await checkCommand.ExecuteScalarAsync()) > 0;

        if (exists)
        {
            return Results.Json(new { message = "用户名或邮箱已存在" }, statusCode: 400);
        }

        // 创建新用户
        var userId = Guid.NewGuid().ToString();
        var insertCommand = new SqliteCommand(@"
            INSERT INTO Users (Id, Username, Email, Password, FullName, Role)
            VALUES (@Id, @Username, @Email, @Password, @FullName, @Role)", connection);

        insertCommand.Parameters.AddWithValue("@Id", userId);
        insertCommand.Parameters.AddWithValue("@Username", username);
        insertCommand.Parameters.AddWithValue("@Email", email);
        insertCommand.Parameters.AddWithValue("@Password", password);
        insertCommand.Parameters.AddWithValue("@FullName", fullName);
        insertCommand.Parameters.AddWithValue("@Role", "user");

        await insertCommand.ExecuteNonQueryAsync();

        return Results.Ok(new {
            message = "用户创建成功",
            id = userId,
            username = username
        });
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"创建用户失败: {ex.Message}" }, statusCode: 500);
    }
});

// 更新用户 - 用户管理界面
app.MapPut("/api/users/{id}", async (string id, HttpRequest request) =>
{
    try
    {
        var body = await new StreamReader(request.Body).ReadToEndAsync();
        var userData = JsonSerializer.Deserialize<JsonElement>(body);

        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        var updateCommand = new SqliteCommand(@"
            UPDATE Users
            SET Email = @email, FullName = @fullName, IsActive = @isActive
            WHERE Id = @id", connection);

        updateCommand.Parameters.AddWithValue("@id", id);
        updateCommand.Parameters.AddWithValue("@email", userData.GetProperty("email").GetString());
        updateCommand.Parameters.AddWithValue("@fullName", userData.GetProperty("fullName").GetString());
        updateCommand.Parameters.AddWithValue("@isActive", userData.TryGetProperty("isActive", out var isActiveProp) ? (isActiveProp.GetBoolean() ? 1 : 0) : 1);

        // 如果提供了新密码，也更新密码
        if (userData.TryGetProperty("newPassword", out var newPasswordProp) && !string.IsNullOrEmpty(newPasswordProp.GetString()))
        {
            updateCommand.CommandText = @"
                UPDATE Users
                SET Email = @email, FullName = @fullName, IsActive = @isActive, Password = @password
                WHERE Id = @id";
            updateCommand.Parameters.AddWithValue("@password", newPasswordProp.GetString());
        }

        var rowsAffected = await updateCommand.ExecuteNonQueryAsync();

        if (rowsAffected > 0)
        {
            return Results.Ok(new { message = "用户更新成功" });
        }
        else
        {
            return Results.Json(new { message = "用户不存在" }, statusCode: 404);
        }
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"更新用户失败: {ex.Message}" }, statusCode: 500);
    }
});

// 删除用户 - 用户管理界面
app.MapDelete("/api/users/{id}", async (string id) =>
{
    try
    {
        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        var deleteCommand = new SqliteCommand("DELETE FROM Users WHERE Id = @id", connection);
        deleteCommand.Parameters.AddWithValue("@id", id);

        var rowsAffected = await deleteCommand.ExecuteNonQueryAsync();

        if (rowsAffected > 0)
        {
            return Results.Ok(new { message = "用户删除成功" });
        }
        else
        {
            return Results.Json(new { message = "用户不存在" }, statusCode: 404);
        }
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"删除用户失败: {ex.Message}" }, statusCode: 500);
    }
});

// 添加兼容性API路径 - 支持 /api/auth/ 路径
// 用户登录 - 兼容路径
app.MapPost("/api/auth/login", async (HttpRequest request) =>
{
    try
    {
        var body = await new StreamReader(request.Body).ReadToEndAsync();
        var loginData = JsonSerializer.Deserialize<JsonElement>(body);

        // 支持两种登录数据格式
        string username, password;

        if (loginData.TryGetProperty("usernameOrEmail", out var usernameOrEmailProp))
        {
            username = usernameOrEmailProp.GetString();
            password = loginData.GetProperty("password").GetString();
        }
        else
        {
            username = loginData.GetProperty("username").GetString();
            password = loginData.GetProperty("password").GetString();
        }

        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        var command = new SqliteCommand(@"
            SELECT Id, Username, Email, FullName, Role, IsActive
            FROM Users
            WHERE (Username = @username OR Email = @username) AND Password = @password", connection);

        command.Parameters.AddWithValue("@username", username);
        command.Parameters.AddWithValue("@password", password);

        using var reader = await command.ExecuteReaderAsync();

        if (await reader.ReadAsync())
        {
            var isActive = Convert.ToInt32(reader["IsActive"]) == 1;

            if (!isActive)
            {
                return Results.Json(new { message = "账户已被禁用" }, statusCode: 401);
            }

            return Results.Ok(new
            {
                message = "登录成功",
                userId = reader["Id"].ToString(),
                username = reader["Username"].ToString(),
                email = reader["Email"].ToString(),
                fullName = reader["FullName"].ToString(),
                token = $"token_{reader["Id"]}_{DateTime.UtcNow.Ticks}",
                roles = new[] { reader["Role"].ToString() },
                permissions = new[] { "read", "write" }
            });
        }
        else
        {
            return Results.Json(new { message = "用户名或密码错误" }, statusCode: 401);
        }
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"登录失败: {ex.Message}" }, statusCode: 500);
    }
});

// 用户注册 - 兼容路径
app.MapPost("/api/auth/register", async (HttpRequest request) =>
{
    try
    {
        var body = await new StreamReader(request.Body).ReadToEndAsync();
        var userData = JsonSerializer.Deserialize<JsonElement>(body);

        var username = userData.GetProperty("username").GetString();
        var email = userData.GetProperty("email").GetString();
        var password = userData.GetProperty("password").GetString();
        var fullName = userData.GetProperty("fullName").GetString();

        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        // 检查用户名是否已存在
        var checkCommand = new SqliteCommand(@"
            SELECT COUNT(*) FROM Users WHERE Username = @username OR Email = @email", connection);
        checkCommand.Parameters.AddWithValue("@username", username);
        checkCommand.Parameters.AddWithValue("@email", email);

        var exists = Convert.ToInt32(await checkCommand.ExecuteScalarAsync()) > 0;

        if (exists)
        {
            return Results.Json(new { message = "用户名或邮箱已存在" }, statusCode: 400);
        }

        // 创建新用户
        var insertCommand = new SqliteCommand(@"
            INSERT INTO Users (Id, Username, Email, Password, FullName, Role)
            VALUES (@Id, @Username, @Email, @Password, @FullName, @Role)", connection);

        insertCommand.Parameters.AddWithValue("@Id", Guid.NewGuid().ToString());
        insertCommand.Parameters.AddWithValue("@Username", username);
        insertCommand.Parameters.AddWithValue("@Email", email);
        insertCommand.Parameters.AddWithValue("@Password", password);
        insertCommand.Parameters.AddWithValue("@FullName", fullName);
        insertCommand.Parameters.AddWithValue("@Role", "user");

        await insertCommand.ExecuteNonQueryAsync();

        return Results.Ok(new { message = "用户注册成功" });
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"注册失败: {ex.Message}" }, statusCode: 500);
    }
});

// 系统配置API
app.MapGet("/api/system/settings", async () =>
{
    try
    {
        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        var command = new SqliteCommand("SELECT SettingKey, SettingValue, Description, Category FROM SystemSettings ORDER BY Category, SettingKey", connection);
        using var reader = await command.ExecuteReaderAsync();
        var settings = new List<object>();

        while (await reader.ReadAsync())
        {
            settings.Add(new
            {
                key = reader["SettingKey"].ToString(),
                value = reader["SettingValue"].ToString(),
                description = reader["Description"].ToString(),
                category = reader["Category"].ToString()
            });
        }

        return Results.Ok(new { settings = settings, total = settings.Count });
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"获取系统配置失败: {ex.Message}" }, statusCode: 500);
    }
});

// 数据导入任务API
app.MapGet("/api/import/tasks", async () =>
{
    try
    {
        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        var command = new SqliteCommand(@"
            SELECT Id, FileName, FileSize, TotalRows, ProcessedRows, Status, Progress,
                   CreatedBy, CreatedAt, StartedAt, CompletedAt, ErrorMessage
            FROM ImportTasks ORDER BY CreatedAt DESC", connection);

        using var reader = await command.ExecuteReaderAsync();
        var tasks = new List<object>();

        while (await reader.ReadAsync())
        {
            tasks.Add(new
            {
                id = reader["Id"].ToString(),
                fileName = reader["FileName"].ToString(),
                fileSize = Convert.ToInt64(reader["FileSize"]),
                totalRows = Convert.ToInt32(reader["TotalRows"]),
                processedRows = Convert.ToInt32(reader["ProcessedRows"]),
                status = reader["Status"].ToString(),
                progress = Convert.ToDouble(reader["Progress"]),
                createdBy = reader["CreatedBy"].ToString(),
                createdAt = reader["CreatedAt"].ToString(),
                startedAt = reader["StartedAt"].ToString(),
                completedAt = reader["CompletedAt"].ToString(),
                errorMessage = reader["ErrorMessage"].ToString()
            });
        }

        return Results.Ok(new { tasks = tasks, total = tasks.Count });
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"获取导入任务失败: {ex.Message}" }, statusCode: 500);
    }
});

// 文件表映射API
app.MapGet("/api/import/mappings", async () =>
{
    try
    {
        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        var command = new SqliteCommand(@"
            SELECT Id, FileName, TableName, CreatedAt, LastImportAt, TotalRows, IsActive
            FROM FileTableMappings WHERE IsActive = 1 ORDER BY CreatedAt DESC", connection);

        using var reader = await command.ExecuteReaderAsync();
        var mappings = new List<object>();

        while (await reader.ReadAsync())
        {
            mappings.Add(new
            {
                id = Convert.ToInt32(reader["Id"]),
                fileName = reader["FileName"].ToString(),
                tableName = reader["TableName"].ToString(),
                createdAt = reader["CreatedAt"].ToString(),
                lastImportAt = reader["LastImportAt"].ToString(),
                totalRows = Convert.ToInt32(reader["TotalRows"]),
                isActive = Convert.ToInt32(reader["IsActive"]) == 1
            });
        }

        return Results.Ok(new { mappings = mappings, total = mappings.Count });
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"获取文件映射失败: {ex.Message}" }, statusCode: 500);
    }
});

// 角色管理API
app.MapGet("/api/roles", async () =>
{
    try
    {
        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        var command = new SqliteCommand("SELECT Id, RoleName, Description, IsActive, CreatedAt FROM Roles WHERE IsActive = 1 ORDER BY RoleName", connection);
        using var reader = await command.ExecuteReaderAsync();
        var roles = new List<object>();

        while (await reader.ReadAsync())
        {
            roles.Add(new
            {
                id = reader["Id"].ToString(),
                roleName = reader["RoleName"].ToString(),
                description = reader["Description"].ToString(),
                isActive = Convert.ToInt32(reader["IsActive"]) == 1,
                createdAt = reader["CreatedAt"].ToString()
            });
        }

        return Results.Ok(new { roles = roles, total = roles.Count });
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"获取角色列表失败: {ex.Message}" }, statusCode: 500);
    }
});

// 数据导入API - 创建导入任务
app.MapPost("/api/data-import/tasks", async (HttpRequest request) =>
{
    try
    {
        var body = await new StreamReader(request.Body).ReadToEndAsync();
        var taskData = JsonSerializer.Deserialize<JsonElement>(body);

        var fileName = taskData.GetProperty("fileName").GetString();
        var fileSize = taskData.GetProperty("fileSize").GetInt64();
        var totalRows = taskData.GetProperty("totalRows").GetInt32();
        var createdBy = taskData.GetProperty("createdBy").GetString();

        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        var taskId = Guid.NewGuid().ToString();
        var insertCommand = new SqliteCommand(@"
            INSERT INTO ImportTasks (Id, FileName, FileSize, TotalRows, CreatedBy, Status)
            VALUES (@Id, @FileName, @FileSize, @TotalRows, @CreatedBy, 'Pending')", connection);

        insertCommand.Parameters.AddWithValue("@Id", taskId);
        insertCommand.Parameters.AddWithValue("@FileName", fileName);
        insertCommand.Parameters.AddWithValue("@FileSize", fileSize);
        insertCommand.Parameters.AddWithValue("@TotalRows", totalRows);
        insertCommand.Parameters.AddWithValue("@CreatedBy", createdBy);

        await insertCommand.ExecuteNonQueryAsync();

        return Results.Ok(new { taskId = taskId, message = "导入任务创建成功" });
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"创建导入任务失败: {ex.Message}" }, statusCode: 500);
    }
});

// 数据导入API - 保存导入数据到数据库
app.MapPost("/api/data-import/data", async (HttpRequest request) =>
{
    try
    {
        var body = await new StreamReader(request.Body).ReadToEndAsync();
        var importData = JsonSerializer.Deserialize<JsonElement>(body);

        var taskId = importData.GetProperty("taskId").GetString();
        var fileName = importData.GetProperty("fileName").GetString();
        var batchIndex = importData.GetProperty("batchIndex").GetInt32();
        var totalBatches = importData.GetProperty("totalBatches").GetInt32();
        var data = importData.GetProperty("data").EnumerateArray();

        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        using var transaction = connection.BeginTransaction();

        try
        {
            // 获取或创建数据表
            var tableName = await GetOrCreateDataTable(connection, transaction, fileName, data.FirstOrDefault());

            // 批量插入数据
            var insertedRows = 0;
            foreach (var row in data)
            {
                var columns = new List<string>();
                var values = new List<string>();
                var parameters = new List<SqliteParameter>();

                foreach (var property in row.EnumerateObject())
                {
                    columns.Add($"[{property.Name}]");
                    values.Add($"@{property.Name}");

                    var param = new SqliteParameter($"@{property.Name}", GetSqliteValue(property.Value));
                    parameters.Add(param);
                }

                var insertSql = $"INSERT INTO [{tableName}] ({string.Join(", ", columns)}) VALUES ({string.Join(", ", values)})";
                var insertCommand = new SqliteCommand(insertSql, connection, transaction);
                insertCommand.Parameters.AddRange(parameters.ToArray());

                await insertCommand.ExecuteNonQueryAsync();
                insertedRows++;
            }

            // 更新导入任务进度
            var updateTaskCommand = new SqliteCommand(@"
                UPDATE ImportTasks
                SET ProcessedRows = ProcessedRows + @ProcessedRows,
                    Progress = (CAST(ProcessedRows + @ProcessedRows AS REAL) / TotalRows) * 100,
                    Status = CASE WHEN (ProcessedRows + @ProcessedRows) >= TotalRows THEN 'Completed' ELSE 'Processing' END,
                    StartedAt = CASE WHEN StartedAt IS NULL THEN datetime('now') ELSE StartedAt END,
                    CompletedAt = CASE WHEN (ProcessedRows + @ProcessedRows) >= TotalRows THEN datetime('now') ELSE NULL END
                WHERE Id = @TaskId", connection, transaction);

            updateTaskCommand.Parameters.AddWithValue("@ProcessedRows", insertedRows);
            updateTaskCommand.Parameters.AddWithValue("@TaskId", taskId);
            await updateTaskCommand.ExecuteNonQueryAsync();

            // 更新文件表映射
            var updateMappingCommand = new SqliteCommand(@"
                UPDATE FileTableMappings
                SET LastImportAt = datetime('now'), TotalRows = TotalRows + @NewRows
                WHERE FileName = @FileName", connection, transaction);

            updateMappingCommand.Parameters.AddWithValue("@NewRows", insertedRows);
            updateMappingCommand.Parameters.AddWithValue("@FileName", fileName);
            await updateMappingCommand.ExecuteNonQueryAsync();

            transaction.Commit();

            return Results.Ok(new {
                message = "数据批次导入成功",
                insertedRows = insertedRows,
                batchIndex = batchIndex,
                totalBatches = totalBatches
            });
        }
        catch (Exception ex)
        {
            transaction.Rollback();
            throw;
        }
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"数据导入失败: {ex.Message}" }, statusCode: 500);
    }
});

Console.WriteLine("🚀 SQLite后端服务器启动成功!");
Console.WriteLine("📍 监听地址: http://localhost:5000");
Console.WriteLine("🔐 默认登录: admin / admin123");
Console.WriteLine("📊 数据库: SQLite (ProductionDataVisualization.db)");
Console.WriteLine("🔗 支持API路径:");
Console.WriteLine("   - /api/simple-auth/* (简化API)");
Console.WriteLine("   - /api/auth/* (兼容API)");
Console.WriteLine("   - /api/users/* (用户管理)");
Console.WriteLine("   - /api/system/* (系统配置)");
Console.WriteLine("   - /api/import/* (数据导入)");
Console.WriteLine("   - /api/roles/* (角色管理)");

app.Run("http://localhost:5000");
