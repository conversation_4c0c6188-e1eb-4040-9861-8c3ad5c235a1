@echo off
title SQLite后端服务器 - 持久运行

echo ==========================================
echo   SQLite后端服务器 - 持久运行模式
echo ==========================================
echo.

:start_backend
echo [%time%] 启动后端服务器...
cd /d "SimpleBackend\bin\Release\net8.0\win-x64\publish"

echo [%time%] 检查端口5000是否被占用...
netstat -ano | findstr :5000 | findstr LISTENING >nul
if %errorlevel% equ 0 (
    echo [%time%] 端口5000已被占用，尝试结束占用进程...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000 ^| findstr LISTENING') do (
        echo [%time%] 结束进程 %%a
        taskkill /PID %%a /F >nul 2>&1
    )
    timeout /t 3 /nobreak >nul
)

echo [%time%] 启动SimpleBackend.exe...
echo [%time%] 监听地址: http://localhost:5000
echo [%time%] 如果服务异常退出，将自动重启
echo.

SimpleBackend.exe

echo.
echo [%time%] 后端服务器意外退出，退出代码: %errorlevel%
echo [%time%] 等待5秒后自动重启...
timeout /t 5 /nobreak

goto start_backend
