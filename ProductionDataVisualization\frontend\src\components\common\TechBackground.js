import React, { useEffect, useRef, useState } from 'react';

const TechBackground = ({ 
  particleCount = 80, 
  lineDistance = 120, 
  particleSpeed = 0.3,
  showGrid = true,
  showHologram = true,
  showScanLines = true
}) => {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const particlesRef = useRef([]);
  const mouseRef = useRef({ x: 0, y: 0 });
  const [time, setTime] = useState(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    let width = window.innerWidth;
    let height = window.innerHeight;

    // 设置画布尺寸
    const resizeCanvas = () => {
      width = window.innerWidth;
      height = window.innerHeight;
      canvas.width = width;
      canvas.height = height;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // 鼠标跟踪
    const handleMouseMove = (e) => {
      mouseRef.current = { x: e.clientX, y: e.clientY };
    };
    window.addEventListener('mousemove', handleMouseMove);

    // 增强粒子类
    class TechParticle {
      constructor() {
        this.x = Math.random() * width;
        this.y = Math.random() * height;
        this.vx = (Math.random() - 0.5) * particleSpeed;
        this.vy = (Math.random() - 0.5) * particleSpeed;
        this.radius = Math.random() * 1.5 + 0.5;
        this.opacity = Math.random() * 0.6 + 0.2;
        this.pulseSpeed = Math.random() * 0.02 + 0.01;
        this.pulsePhase = Math.random() * Math.PI * 2;
        this.glowIntensity = Math.random() * 0.5 + 0.3;
      }

      update(time) {
        this.x += this.vx;
        this.y += this.vy;

        // 边界检测
        if (this.x < 0 || this.x > width) this.vx *= -1;
        if (this.y < 0 || this.y > height) this.vy *= -1;

        // 脉冲效果
        this.currentRadius = this.radius + Math.sin(time * this.pulseSpeed + this.pulsePhase) * 0.5;
        this.currentOpacity = this.opacity + Math.sin(time * this.pulseSpeed + this.pulsePhase) * 0.2;

        // 鼠标交互
        const dx = mouseRef.current.x - this.x;
        const dy = mouseRef.current.y - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < 100) {
          const force = (100 - distance) / 100;
          this.vx += (dx / distance) * force * 0.01;
          this.vy += (dy / distance) * force * 0.01;
          this.glowIntensity = Math.min(1, this.glowIntensity + force * 0.5);
        } else {
          this.glowIntensity = Math.max(0.3, this.glowIntensity - 0.01);
        }
      }

      draw(ctx) {
        // 发光效果
        const gradient = ctx.createRadialGradient(
          this.x, this.y, 0,
          this.x, this.y, this.currentRadius * 3
        );
        gradient.addColorStop(0, `rgba(100, 200, 255, ${this.currentOpacity * this.glowIntensity})`);
        gradient.addColorStop(0.5, `rgba(100, 200, 255, ${this.currentOpacity * 0.3})`);
        gradient.addColorStop(1, 'rgba(100, 200, 255, 0)');

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.currentRadius * 3, 0, Math.PI * 2);
        ctx.fill();

        // 核心粒子
        ctx.fillStyle = `rgba(255, 255, 255, ${this.currentOpacity})`;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.currentRadius, 0, Math.PI * 2);
        ctx.fill();
      }
    }

    // 初始化粒子
    particlesRef.current = [];
    for (let i = 0; i < particleCount; i++) {
      particlesRef.current.push(new TechParticle());
    }

    // 绘制网格
    const drawGrid = (ctx, time) => {
      if (!showGrid) return;
      
      const gridSize = 50;
      const offsetX = (time * 0.5) % gridSize;
      const offsetY = (time * 0.3) % gridSize;
      
      ctx.strokeStyle = `rgba(100, 200, 255, 0.1)`;
      ctx.lineWidth = 1;
      
      // 垂直线
      for (let x = -offsetX; x < width + gridSize; x += gridSize) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
      }
      
      // 水平线
      for (let y = -offsetY; y < height + gridSize; y += gridSize) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
      }
    };

    // 绘制扫描线
    const drawScanLines = (ctx, time) => {
      if (!showScanLines) return;
      
      const scanY = (time * 2) % (height + 100) - 50;
      const gradient = ctx.createLinearGradient(0, scanY - 50, 0, scanY + 50);
      gradient.addColorStop(0, 'rgba(0, 255, 255, 0)');
      gradient.addColorStop(0.5, 'rgba(0, 255, 255, 0.3)');
      gradient.addColorStop(1, 'rgba(0, 255, 255, 0)');
      
      ctx.fillStyle = gradient;
      ctx.fillRect(0, scanY - 50, width, 100);
    };

    // 绘制全息投影效果
    const drawHologram = (ctx, time) => {
      if (!showHologram) return;
      
      const centerX = width / 2;
      const centerY = height / 2;
      const radius = 200 + Math.sin(time * 0.001) * 50;
      
      // 外圈
      const gradient = ctx.createRadialGradient(
        centerX, centerY, radius - 20,
        centerX, centerY, radius + 20
      );
      gradient.addColorStop(0, 'rgba(0, 255, 255, 0)');
      gradient.addColorStop(0.5, 'rgba(0, 255, 255, 0.1)');
      gradient.addColorStop(1, 'rgba(0, 255, 255, 0)');
      
      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius + 20, 0, Math.PI * 2);
      ctx.fill();
      
      // 内圈脉冲
      const pulseRadius = 50 + Math.sin(time * 0.003) * 30;
      const pulseGradient = ctx.createRadialGradient(
        centerX, centerY, 0,
        centerX, centerY, pulseRadius
      );
      pulseGradient.addColorStop(0, 'rgba(100, 200, 255, 0.2)');
      pulseGradient.addColorStop(1, 'rgba(100, 200, 255, 0)');
      
      ctx.fillStyle = pulseGradient;
      ctx.beginPath();
      ctx.arc(centerX, centerY, pulseRadius, 0, Math.PI * 2);
      ctx.fill();
    };

    // 绘制连接线
    const drawConnections = (ctx) => {
      const particles = particlesRef.current;
      
      for (let i = 0; i < particles.length; i++) {
        for (let j = i + 1; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x;
          const dy = particles[i].y - particles[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < lineDistance) {
            const opacity = (1 - distance / lineDistance) * 0.3;
            ctx.strokeStyle = `rgba(100, 200, 255, ${opacity})`;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(particles[i].x, particles[i].y);
            ctx.lineTo(particles[j].x, particles[j].y);
            ctx.stroke();
          }
        }
      }
    };

    // 主动画循环
    const animate = (currentTime) => {
      setTime(currentTime);
      
      // 清空画布
      ctx.clearRect(0, 0, width, height);
      
      // 绘制背景效果
      drawGrid(ctx, currentTime);
      drawHologram(ctx, currentTime);
      
      // 更新和绘制粒子
      particlesRef.current.forEach(particle => {
        particle.update(currentTime);
        particle.draw(ctx);
      });
      
      // 绘制连接线
      drawConnections(ctx);
      
      // 绘制扫描线（在最上层）
      drawScanLines(ctx, currentTime);
      
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', resizeCanvas);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [particleCount, lineDistance, particleSpeed, showGrid, showHologram, showScanLines]);

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      zIndex: -1,
      background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%)',
      overflow: 'hidden'
    }}>
      <canvas
        ref={canvasRef}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%'
        }}
      />
      
      {/* 额外的光效层 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: `
          radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%)
        `,
        pointerEvents: 'none'
      }} />
    </div>
  );
};

export default TechBackground;
