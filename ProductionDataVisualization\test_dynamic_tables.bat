@echo off
title 测试动态表数据导入系统

echo ========================================
echo   测试动态表数据导入系统
echo ========================================
echo.

echo [INFO] 新功能特性:
echo - 每个文件创建独立的数据表
echo - 保持原始文件的列结构
echo - 重复文件检测和处理选择
echo - 覆盖或追加数据模式
echo.

echo [INFO] 启动后端服务...
cd backend\SqlServerAPI
start "Backend" cmd /k "dotnet run"

echo [INFO] 等待后端启动...
timeout /t 10 /nobreak

echo [INFO] 测试新API端点...
echo.
echo [TEST] 文件分析API:
curl -s -X POST http://localhost:5000/api/data-import/analyze ^
  -H "Content-Type: application/json" ^
  -d "{\"fileName\":\"test.csv\",\"sampleData\":[{\"VarName\":\"测试变量\",\"TimeString\":\"2024-08-07 06:01:26\",\"VarValue\":0.2002894,\"Validity\":1,\"Time_ms\":4551125099.4167}]}"
echo.
echo.

echo [INFO] 启动前端...
cd ..\..\frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 15 /nobreak

echo.
echo ========================================
echo   测试指南
echo ========================================
echo.
echo 1. 访问: http://localhost:3000/data-import
echo 2. 上传您的数据文件
echo 3. 观察以下新功能:
echo    - 文件分析和表结构检测
echo    - 重复文件检测对话框
echo    - 覆盖/追加选择
echo    - 数据按原始列结构存储
echo.
echo 4. 检查数据库:
echo    - 每个文件对应一个独立的表
echo    - 表名格式: Data_文件名_时间戳
echo    - 列结构与原始文件完全一致
echo.

pause
