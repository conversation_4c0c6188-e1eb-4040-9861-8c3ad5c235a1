@echo off
title 测试数据导入修复

echo ========================================
echo   测试数据导入修复
echo ========================================
echo.

echo [INFO] 检查后端API修复...
findstr /C:"app.MapPost.*data-import/tasks" backend\SqlServerAPI\Program.cs
if %errorlevel% equ 0 (
    echo [SUCCESS] 数据导入任务API已恢复
) else (
    echo [ERROR] 数据导入任务API未找到
)

echo.
findstr /C:"app.MapPost.*data-import/data" backend\SqlServerAPI\Program.cs
if %errorlevel% equ 0 (
    echo [SUCCESS] 数据保存API已添加
) else (
    echo [ERROR] 数据保存API未找到
)

echo.
echo [INFO] 检查前端修复...
findstr /C:"fetch.*data-import/tasks" frontend\src\pages\DataImportPage.js
if %errorlevel% equ 0 (
    echo [SUCCESS] 前端API调用已添加
) else (
    echo [ERROR] 前端API调用未找到
)

echo.
echo [INFO] 尝试启动后端...
cd backend\SqlServerAPI
start "Backend API" cmd /k "dotnet run"

echo [INFO] 等待5秒...
timeout /t 5 /nobreak

echo [INFO] 检查后端端口5000...
netstat -ano | findstr :5000

echo.
echo [INFO] 尝试启动前端...
cd ..\..\frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待5秒...
timeout /t 5 /nobreak

echo [INFO] 检查前端端口3000...
netstat -ano | findstr :3000

echo.
echo ========================================
echo   测试完成
echo ========================================
echo.
echo 如果看到端口5000和3000都被占用，说明系统启动成功
echo 请访问 http://localhost:3000/data-import 测试数据导入功能
echo.

pause
