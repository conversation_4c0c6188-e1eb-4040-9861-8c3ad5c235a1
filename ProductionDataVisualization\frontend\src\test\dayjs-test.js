// 测试dayjs功能
import dayjs from 'dayjs';

// 测试基本功能
console.log('dayjs基本测试:');
console.log('当前时间:', dayjs().format());

// 测试日期数组排序（替代min/max）
const dates = [
  dayjs('2024-01-01'),
  dayjs('2024-06-15'),
  dayjs('2024-03-10'),
  dayjs('2024-12-31')
];

console.log('原始日期数组:', dates.map(d => d.format()));

const sortedDates = dates.sort((a, b) => a.valueOf() - b.valueOf());
console.log('排序后日期数组:', sortedDates.map(d => d.format()));

const minDate = sortedDates[0];
const maxDate = sortedDates[sortedDates.length - 1];

console.log('最小日期:', minDate.format());
console.log('最大日期:', maxDate.format());

export { dayjs, minDate, maxDate };
