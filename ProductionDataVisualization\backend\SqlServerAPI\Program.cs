using Microsoft.Data.SqlClient;
using Microsoft.Data.Sqlite;
using System.Text.Json;
using Microsoft.AspNetCore.Server.IIS;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using System.Data;
using System.Text;
using System.Security.Cryptography;

var builder = WebApplication.CreateBuilder(args);

// 配置请求大小限制
builder.Services.Configure<IISServerOptions>(options =>
{
    options.MaxRequestBodySize = 500 * 1024 * 1024; // 500MB
});

builder.WebHost.ConfigureKestrel(options =>
{
    options.Limits.MaxRequestBodySize = 500 * 1024 * 1024; // 500MB
});

// 添加CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();
app.UseCors();

// SQLite数据库配置 - 使用本地文件数据库
var sqliteConnectionString = "Data Source=ProductionDataVisualization.db;";
var useSqlite = false; // 设置为false使用SQL Server，true使用SQLite

// SQL Server连接字符串配置 - 使用ProductionDataVisualizationDb数据库
var masterConnectionStrings = new[]
{
    "Server=localhost\\SQLEXPRESS;Database=master;Trusted_Connection=true;TrustServerCertificate=true;Connection Timeout=30;Command Timeout=300;Pooling=true;Max Pool Size=200;Min Pool Size=10;",
    "Server=.\\SQLEXPRESS;Database=master;Trusted_Connection=true;TrustServerCertificate=true;Connection Timeout=30;Command Timeout=300;Pooling=true;Max Pool Size=200;Min Pool Size=10;",
    "Server=.;Database=master;Trusted_Connection=true;TrustServerCertificate=true;Connection Timeout=30;Command Timeout=300;Pooling=true;Max Pool Size=200;Min Pool Size=10;",
    "Server=localhost;Database=master;Trusted_Connection=true;TrustServerCertificate=true;Connection Timeout=30;Command Timeout=300;Pooling=true;Max Pool Size=200;Min Pool Size=10;",
    "Server=(local);Database=master;Trusted_Connection=true;TrustServerCertificate=true;Connection Timeout=30;Command Timeout=300;Pooling=true;Max Pool Size=200;Min Pool Size=10;"
};

// 目标数据库连接字符串
var targetDatabaseName = "ProductionDataVisualizationDb";

string? workingConnectionString = null;

// 创建默认管理员用户
async Task EnsureDefaultAdmin(System.Data.Common.DbConnection connection)
{
    try
    {
        var checkAdminSql = useSqlite ?
            "SELECT COUNT(*) FROM Users WHERE Username = 'admin'" :
            "SELECT COUNT(*) FROM Users WHERE Username = 'admin'";

        var checkCommand = CreateCommand(checkAdminSql, connection);
        var adminExists = Convert.ToInt32(await checkCommand.ExecuteScalarAsync()) > 0;

        if (!adminExists)
        {
            var adminId = Guid.NewGuid().ToString();
            var hashedPassword = BCrypt.Net.BCrypt.HashPassword("admin123");

            var insertAdminSql = useSqlite ?
                "INSERT INTO Users (Id, Username, Email, Password, FullName, IsActive, Role) VALUES (@Id, @Username, @Email, @Password, @FullName, @IsActive, @Role)" :
                "INSERT INTO Users (Id, Username, Email, Password, FullName, IsActive, Role) VALUES (@Id, @Username, @Email, @Password, @FullName, @IsActive, @Role)";

            var insertCommand = CreateCommand(insertAdminSql, connection);

            if (useSqlite)
            {
                var cmd = (SqliteCommand)insertCommand;
                cmd.Parameters.AddWithValue("@Id", adminId);
                cmd.Parameters.AddWithValue("@Username", "admin");
                cmd.Parameters.AddWithValue("@Email", "<EMAIL>");
                cmd.Parameters.AddWithValue("@Password", hashedPassword);
                cmd.Parameters.AddWithValue("@FullName", "系统管理员");
                cmd.Parameters.AddWithValue("@IsActive", 1);
                cmd.Parameters.AddWithValue("@Role", "admin");
            }
            else
            {
                var cmd = (SqlCommand)insertCommand;
                cmd.Parameters.AddWithValue("@Id", adminId);
                cmd.Parameters.AddWithValue("@Username", "admin");
                cmd.Parameters.AddWithValue("@Email", "<EMAIL>");
                cmd.Parameters.AddWithValue("@Password", hashedPassword);
                cmd.Parameters.AddWithValue("@FullName", "系统管理员");
                cmd.Parameters.AddWithValue("@IsActive", true);
                cmd.Parameters.AddWithValue("@Role", "admin");
            }

            await insertCommand.ExecuteNonQueryAsync();
            Console.WriteLine("默认管理员用户创建成功 (admin/admin123)");
        }
        else
        {
            Console.WriteLine("管理员用户已存在");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"创建默认管理员失败: {ex.Message}");
    }
}

// 创建数据库连接的通用方法
System.Data.Common.DbConnection CreateConnection(string connectionString)
{
    if (useSqlite)
    {
        return new SqliteConnection(connectionString);
    }
    else
    {
        return new SqlConnection(connectionString);
    }
}

// 创建数据库命令的通用方法
System.Data.Common.DbCommand CreateCommand(string sql, System.Data.Common.DbConnection connection)
{
    if (useSqlite)
    {
        return new SqliteCommand(sql, (SqliteConnection)connection);
    }
    else
    {
        return new SqlCommand(sql, (SqlConnection)connection);
    }
}

// 测试连接并找到可用的连接字符串
async Task<string?> FindWorkingConnection()
{
    if (useSqlite)
    {
        try
        {
            Console.WriteLine("使用SQLite数据库");
            using var connection = new SqliteConnection(sqliteConnectionString);
            await connection.OpenAsync();

            var command = new SqliteCommand("SELECT 1", connection);
            await command.ExecuteScalarAsync();

            Console.WriteLine("✅ SQLite连接成功");
            return sqliteConnectionString;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ SQLite连接失败: {ex.Message}");
            Console.WriteLine("回退到SQL Server...");
        }
    }

    foreach (var connStr in masterConnectionStrings)
    {
        try
        {
            Console.WriteLine($"测试连接: {connStr.Split(';')[0]}");

            using var connection = new SqlConnection(connStr);
            await connection.OpenAsync();

            var command = new SqlCommand("SELECT @@VERSION, @@SERVERNAME, DB_NAME()", connection);
            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                Console.WriteLine($"✅ 连接成功!");
                Console.WriteLine($"   SQL Server版本: {reader.GetString(0).Substring(0, 80)}...");
                Console.WriteLine($"   服务器名称: {reader.GetString(1)}");
                Console.WriteLine($"   当前数据库: {reader.GetString(2)}");

                // 返回目标数据库的连接字符串
                return connStr.Replace("Database=master", "Database=ProductionDataVisualizationDb");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 连接失败: {ex.Message}");
        }
    }
    return null;
}

// 确保数据库和表存在
async Task<bool> EnsureDatabaseAndTable(string connectionString)
{
    try
    {
        if (useSqlite)
        {
            // SQLite - 直接连接并创建表
            using var connection = new SqliteConnection(connectionString);
            await connection.OpenAsync();

            var createTableCommand = new SqliteCommand(@"
                CREATE TABLE IF NOT EXISTS Users (
                    Id TEXT PRIMARY KEY,
                    Username TEXT UNIQUE NOT NULL,
                    Email TEXT UNIQUE NOT NULL,
                    Password TEXT NOT NULL,
                    FullName TEXT NOT NULL,
                    IsActive INTEGER DEFAULT 1,
                    CreatedAt TEXT DEFAULT (datetime('now')),
                    LastLoginTime TEXT NULL,
                    Role TEXT DEFAULT 'user'
                )", connection);

            await createTableCommand.ExecuteNonQueryAsync();
            Console.WriteLine("SQLite用户表创建/检查完成");

            // 创建默认管理员用户
            await EnsureDefaultAdmin(connection);
        }
        else
        {
            // SQL Server - 原有逻辑
            var masterConnStr = connectionString.Replace("ProductionDataVisualizationDb", "master");

            using var masterConnection = new SqlConnection(masterConnStr);
            await masterConnection.OpenAsync();

            var createDbCommand = new SqlCommand(@"
                IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'ProductionDataVisualizationDb')
                BEGIN
                    CREATE DATABASE ProductionDataVisualizationDb
                    PRINT '数据库 ProductionDataVisualizationDb 创建成功'
                END", masterConnection);

            await createDbCommand.ExecuteNonQueryAsync();
            Console.WriteLine("数据库创建/检查完成");

            // 连接到目标数据库创建表
            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            var createTableCommand = new SqlCommand(@"
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Users')
                BEGIN
                    CREATE TABLE Users (
                        Id NVARCHAR(50) PRIMARY KEY,
                        Username NVARCHAR(50) UNIQUE NOT NULL,
                        Email NVARCHAR(100) UNIQUE NOT NULL,
                        Password NVARCHAR(255) NOT NULL,
                        FullName NVARCHAR(100) NOT NULL,
                        IsActive BIT DEFAULT 1,
                        CreatedAt DATETIME2 DEFAULT GETDATE(),
                        LastLoginTime DATETIME2 NULL
                    )
                    PRINT '用户表创建成功'
                END", connection);

            await createTableCommand.ExecuteNonQueryAsync();
            Console.WriteLine("用户表创建/检查完成");

            // 检查并添加角色字段
            var checkRoleColumnCommand = new SqlCommand(@"
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                              WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'Role')
                BEGIN
                    ALTER TABLE Users ADD Role NVARCHAR(50) DEFAULT 'User'
                    PRINT '角色字段添加成功'
                END", connection);

            await checkRoleColumnCommand.ExecuteNonQueryAsync();
            Console.WriteLine("角色字段检查/添加完成");

            // 创建默认管理员用户
            await EnsureDefaultAdmin(connection);
        }
        
        return true;
    }
    catch (Exception ex)
    {
        Console.WriteLine($"数据库初始化失败: {ex.Message}");
        return false;
    }
}

// 数据库状态检查接口
app.MapGet("/api/health/database", async () =>
{
    try
    {
        if (workingConnectionString == null)
        {
            return Results.Json(new
            {
                type = "SQL Server数据库",
                status = "Disconnected",
                message = "数据库连接未初始化"
            }, statusCode: 500);
        }
        
        using var connection = CreateConnection(workingConnectionString);
        await connection.OpenAsync();

        var command = CreateCommand("SELECT COUNT(*) FROM Users", connection);
        var userCount = Convert.ToInt32(await command.ExecuteScalarAsync());

        return Results.Json(new
        {
            type = useSqlite ? "SQLite数据库" : "SQL Server数据库",
            status = "Connected",
            database = useSqlite ? "ProductionDataVisualization.db" : "ProductionDataVisualizationDb",
            userCount = userCount,
            message = useSqlite ? "SQLite数据库连接正常" : "SQL Server数据库连接正常",
            connectionString = useSqlite ? "SQLite文件数据库" : workingConnectionString.Split(';')[0]
        });
    }
    catch (Exception ex)
    {
        return Results.Json(new
        {
            type = "SQL Server数据库",
            status = "Error",
            message = $"数据库查询失败: {ex.Message}"
        }, statusCode: 500);
    }
});

// 获取用户列表接口
app.MapGet("/api/simple-auth/users", async () =>
{
    try
    {
        if (workingConnectionString == null)
        {
            return Results.Json(new { message = "数据库连接未初始化" }, statusCode: 500);
        }

        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        var command = new SqlCommand(@"
            SELECT Id, Username, Email, FullName, Role, IsActive, CreatedAt, LastLoginTime
            FROM Users
            ORDER BY CreatedAt DESC", connection);

        using var reader = await command.ExecuteReaderAsync();
        var users = new List<object>();

        while (await reader.ReadAsync())
        {
            var role = reader["Role"]?.ToString() ?? "User";
            var isAdmin = role == "Admin";

            // 根据角色设置权限
            var permissions = isAdmin ? new[] {
                "ViewUsers", "CreateUser", "EditUser", "DeleteUser",
                "ViewRoles", "CreateRole", "EditRole", "DeleteRole",
                "ViewPermissions", "AssignPermissions",
                "ViewData", "ImportData", "ExportData", "DeleteData",
                "ViewCharts", "CreateChart", "EditChart", "DeleteChart",
                "ViewDashboards", "CreateDashboard", "EditDashboard", "DeleteDashboard",
                "ViewSystemSettings", "EditSystemSettings"
            } : new[] {
                "ViewData", "ViewCharts", "ViewDashboards", "ViewUsers"
            };

            users.Add(new
            {
                id = reader["Id"].ToString(),
                username = reader["Username"].ToString(),
                email = reader["Email"].ToString(),
                fullName = reader["FullName"].ToString(),
                isActive = (bool)reader["IsActive"],
                createdAt = (DateTime)reader["CreatedAt"],
                lastLoginTime = reader.IsDBNull(reader.GetOrdinal("LastLoginTime")) ? null : (DateTime?)reader["LastLoginTime"],
                roles = new[] { role },
                permissions = permissions
            });
        }

        Console.WriteLine($"返回 {users.Count} 个用户 (从SQL Server)");

        return Results.Json(new
        {
            items = users,
            totalCount = users.Count,
            page = 1,
            pageSize = users.Count
        });
    }
    catch (Exception ex)
    {
        Console.WriteLine($"获取用户列表失败: {ex.Message}");
        return Results.Json(new { message = $"获取用户列表失败: {ex.Message}" }, statusCode: 500);
    }
});

// 用户注册接口
app.MapPost("/api/simple-auth/register", async (RegisterRequest request) =>
{
    try
    {
        if (workingConnectionString == null)
        {
            return Results.Json(new { message = "数据库连接未初始化" }, statusCode: 500);
        }

        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        // 检查用户名是否已存在
        var checkUsernameCommand = new SqlCommand(
            "SELECT COUNT(*) FROM Users WHERE LOWER(Username) = LOWER(@username)", connection);
        checkUsernameCommand.Parameters.AddWithValue("@username", request.Username);

        if ((int)await checkUsernameCommand.ExecuteScalarAsync() > 0)
        {
            return Results.Json(new { message = "用户名已存在" }, statusCode: 400);
        }

        // 检查邮箱是否已存在
        var checkEmailCommand = new SqlCommand(
            "SELECT COUNT(*) FROM Users WHERE LOWER(Email) = LOWER(@email)", connection);
        checkEmailCommand.Parameters.AddWithValue("@email", request.Email);

        if ((int)await checkEmailCommand.ExecuteScalarAsync() > 0)
        {
            return Results.Json(new { message = "邮箱已存在" }, statusCode: 400);
        }

        // 创建新用户
        var newUserId = DateTimeOffset.Now.ToUnixTimeMilliseconds().ToString();
        var insertCommand = new SqlCommand(@"
            INSERT INTO Users (Id, Username, Email, Password, FullName, Role, IsActive, CreatedAt)
            VALUES (@id, @username, @email, @password, @fullName, 'User', 1, GETDATE())", connection);

        insertCommand.Parameters.AddWithValue("@id", newUserId);
        insertCommand.Parameters.AddWithValue("@username", request.Username);
        insertCommand.Parameters.AddWithValue("@email", request.Email);
        insertCommand.Parameters.AddWithValue("@password", request.Password);
        insertCommand.Parameters.AddWithValue("@fullName", request.FullName);

        await insertCommand.ExecuteNonQueryAsync();

        Console.WriteLine($"用户注册成功: {request.Username} (存储到SQL Server数据库)");

        return Results.Json(new
        {
            message = "注册成功",
            userId = newUserId,
            username = request.Username
        });
    }
    catch (Exception ex)
    {
        Console.WriteLine($"注册失败: {ex.Message}");
        return Results.Json(new { message = $"注册失败: {ex.Message}" }, statusCode: 500);
    }
});

// 用户登录接口
app.MapPost("/api/auth/login", async (LoginRequest request) =>
{
    try
    {
        if (workingConnectionString == null)
        {
            return Results.Json(new { message = "数据库连接未初始化" }, statusCode: 500);
        }

        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        // 查找用户（支持用户名或邮箱登录）
        var command = new SqlCommand(@"
            SELECT Id, Username, Email, Password, FullName, Role, IsActive, CreatedAt, LastLoginTime
            FROM Users
            WHERE (LOWER(Username) = LOWER(@usernameOrEmail) OR LOWER(Email) = LOWER(@usernameOrEmail))
            AND IsActive = 1", connection);

        command.Parameters.AddWithValue("@usernameOrEmail", request.UsernameOrEmail);

        using var reader = await command.ExecuteReaderAsync();

        if (await reader.ReadAsync())
        {
            var storedPassword = reader["Password"].ToString();

            // 简单密码验证（实际应用中应该使用哈希验证）
            if (storedPassword == request.Password)
            {
                var userId = reader["Id"].ToString();
                var username = reader["Username"].ToString();
                var email = reader["Email"].ToString();
                var fullName = reader["FullName"].ToString();
                var role = reader["Role"]?.ToString() ?? "User";
                var isAdmin = role == "Admin";

                // 根据角色设置权限
                var permissions = isAdmin ? new[] {
                    "ViewUsers", "CreateUser", "EditUser", "DeleteUser",
                    "ViewRoles", "CreateRole", "EditRole", "DeleteRole",
                    "ViewPermissions", "AssignPermissions",
                    "ViewData", "ImportData", "ExportData", "DeleteData",
                    "ViewCharts", "CreateChart", "EditChart", "DeleteChart",
                    "ViewDashboards", "CreateDashboard", "EditDashboard", "DeleteDashboard",
                    "ViewSystemSettings", "EditSystemSettings"
                } : new[] {
                    "ViewData", "ViewCharts", "ViewDashboards", "ViewUsers"
                };

                // 更新最后登录时间
                reader.Close();
                var updateCommand = new SqlCommand(
                    "UPDATE Users SET LastLoginTime = GETDATE() WHERE Id = @id", connection);
                updateCommand.Parameters.AddWithValue("@id", userId);
                await updateCommand.ExecuteNonQueryAsync();

                Console.WriteLine($"用户登录成功: {username} (角色: {role}) (从SQL Server验证)");

                // 生成简单的token（实际应用中应该使用JWT）
                var token = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes($"{userId}:{username}:{DateTime.Now.Ticks}"));

                return Results.Json(new
                {
                    token = token,
                    userId = userId,
                    username = username,
                    email = email,
                    fullName = fullName,
                    roles = new[] { role },
                    permissions = permissions
                });
            }
            else
            {
                Console.WriteLine($"用户登录失败: {request.UsernameOrEmail} - 密码错误");
                return Results.Json(new { message = "用户名或密码错误" }, statusCode: 401);
            }
        }
        else
        {
            Console.WriteLine($"用户登录失败: {request.UsernameOrEmail} - 用户不存在");
            return Results.Json(new { message = "用户名或密码错误" }, statusCode: 401);
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"登录失败: {ex.Message}");
        return Results.Json(new { message = $"登录失败: {ex.Message}" }, statusCode: 500);
    }
});

// 用户管理API - 创建用户
app.MapPost("/api/users", async (CreateUserRequest request) =>
{
    try
    {
        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        // 检查用户名是否已存在
        var checkUsernameCommand = new SqlCommand(
            "SELECT COUNT(*) FROM Users WHERE LOWER(Username) = LOWER(@username)", connection);
        checkUsernameCommand.Parameters.AddWithValue("@username", request.Username);

        if ((int)await checkUsernameCommand.ExecuteScalarAsync() > 0)
        {
            return Results.Json(new { message = "用户名已存在" }, statusCode: 400);
        }

        // 检查邮箱是否已存在
        var checkEmailCommand = new SqlCommand(
            "SELECT COUNT(*) FROM Users WHERE LOWER(Email) = LOWER(@email)", connection);
        checkEmailCommand.Parameters.AddWithValue("@email", request.Email);

        if ((int)await checkEmailCommand.ExecuteScalarAsync() > 0)
        {
            return Results.Json(new { message = "邮箱已存在" }, statusCode: 400);
        }

        // 创建新用户
        var newUserId = Guid.NewGuid().ToString();
        var insertCommand = new SqlCommand(@"
            INSERT INTO Users (Id, Username, Email, Password, FullName, Role, IsActive, CreatedAt)
            VALUES (@id, @username, @email, @password, @fullName, 'User', 1, GETDATE())", connection);

        insertCommand.Parameters.AddWithValue("@id", newUserId);
        insertCommand.Parameters.AddWithValue("@username", request.Username);
        insertCommand.Parameters.AddWithValue("@email", request.Email);
        insertCommand.Parameters.AddWithValue("@password", request.Password); // 实际应用中应该加密
        insertCommand.Parameters.AddWithValue("@fullName", request.FullName);

        await insertCommand.ExecuteNonQueryAsync();

        Console.WriteLine($"用户创建成功: {request.Username}");
        return Results.Json(new { id = newUserId, message = "用户创建成功" }, statusCode: 201);
    }
    catch (Exception ex)
    {
        Console.WriteLine($"创建用户失败: {ex.Message}");
        return Results.Json(new { message = $"创建用户失败: {ex.Message}" }, statusCode: 500);
    }
});

// 用户管理API - 更新用户
app.MapPut("/api/users/{id}", async (string id, UpdateUserRequest request) =>
{
    try
    {
        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        // 检查用户是否存在
        var checkUserCommand = new SqlCommand("SELECT COUNT(*) FROM Users WHERE Id = @id", connection);
        checkUserCommand.Parameters.AddWithValue("@id", id);

        if ((int)await checkUserCommand.ExecuteScalarAsync() == 0)
        {
            return Results.Json(new { message = "用户不存在" }, statusCode: 404);
        }

        // 如果要更改邮箱，检查邮箱是否已被其他用户使用
        var checkEmailCommand = new SqlCommand(
            "SELECT COUNT(*) FROM Users WHERE LOWER(Email) = LOWER(@email) AND Id != @id", connection);
        checkEmailCommand.Parameters.AddWithValue("@email", request.Email);
        checkEmailCommand.Parameters.AddWithValue("@id", id);

        if ((int)await checkEmailCommand.ExecuteScalarAsync() > 0)
        {
            return Results.Json(new { message = "邮箱已被其他用户使用" }, statusCode: 400);
        }

        // 构建更新SQL
        var updateSql = "UPDATE Users SET Email = @email, FullName = @fullName, IsActive = @isActive";
        var updateCommand = new SqlCommand();
        updateCommand.Connection = connection;
        updateCommand.Parameters.AddWithValue("@email", request.Email);
        updateCommand.Parameters.AddWithValue("@fullName", request.FullName);
        updateCommand.Parameters.AddWithValue("@isActive", request.IsActive);
        updateCommand.Parameters.AddWithValue("@id", id);

        // 如果提供了新密码，则更新密码
        if (!string.IsNullOrEmpty(request.NewPassword))
        {
            updateSql += ", Password = @password";
            updateCommand.Parameters.AddWithValue("@password", request.NewPassword); // 实际应用中应该加密
        }

        updateSql += " WHERE Id = @id";
        updateCommand.CommandText = updateSql;

        await updateCommand.ExecuteNonQueryAsync();

        Console.WriteLine($"用户更新成功: {id}");
        return Results.Json(new { message = "用户更新成功" }, statusCode: 200);
    }
    catch (Exception ex)
    {
        Console.WriteLine($"更新用户失败: {ex.Message}");
        return Results.Json(new { message = $"更新用户失败: {ex.Message}" }, statusCode: 500);
    }
});

// 用户管理API - 删除用户
app.MapDelete("/api/users/{id}", async (string id) =>
{
    try
    {
        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        // 检查用户是否存在
        var checkUserCommand = new SqlCommand("SELECT Username FROM Users WHERE Id = @id", connection);
        checkUserCommand.Parameters.AddWithValue("@id", id);

        var username = await checkUserCommand.ExecuteScalarAsync() as string;
        if (username == null)
        {
            return Results.Json(new { message = "用户不存在" }, statusCode: 404);
        }

        // 删除用户
        var deleteCommand = new SqlCommand("DELETE FROM Users WHERE Id = @id", connection);
        deleteCommand.Parameters.AddWithValue("@id", id);

        await deleteCommand.ExecuteNonQueryAsync();

        Console.WriteLine($"用户删除成功: {username}");
        return Results.Json(new { message = "用户删除成功" }, statusCode: 200);
    }
    catch (Exception ex)
    {
        Console.WriteLine($"删除用户失败: {ex.Message}");
        return Results.Json(new { message = $"删除用户失败: {ex.Message}" }, statusCode: 500);
    }
});

// 启动应用
Console.WriteLine("启动SQL Server API服务器...");

// 查找可用的连接
workingConnectionString = await FindWorkingConnection();

if (workingConnectionString == null)
{
    Console.WriteLine("❌ 无法连接到SQL Server，服务器无法启动");
    Console.WriteLine("\n可能的解决方案:");
    Console.WriteLine("1. 确保SQL Server服务正在运行");
    Console.WriteLine("2. 检查SQL Server配置管理器中的网络配置");
    Console.WriteLine("3. 确保TCP/IP协议已启用");
    Console.WriteLine("4. 检查Windows防火墙设置");
    return;
}

// 初始化数据库和表
if (!await EnsureDatabaseAndTable(workingConnectionString))
{
    Console.WriteLine("❌ 数据库初始化失败，服务器无法启动");
    return;
}

// 数据导入相关API

// 创建数据导入表（增强版本，支持自动恢复）
async Task CreateDataImportTables()
{
    if (workingConnectionString == null) return;

    try
    {
        using var connection = CreateConnection(workingConnectionString);
        await connection.OpenAsync();

        Console.WriteLine("开始检查和创建数据导入表...");

        if (useSqlite)
        {
            // SQLite版本
            var createImportTasksCommand = CreateCommand(@"
                CREATE TABLE IF NOT EXISTS ImportTasks (
                    Id TEXT PRIMARY KEY,
                    FileName TEXT NOT NULL,
                    FileSize INTEGER NOT NULL,
                    TotalRows INTEGER NOT NULL DEFAULT 0,
                    ProcessedRows INTEGER NOT NULL DEFAULT 0,
                    Status TEXT NOT NULL DEFAULT 'Pending',
                    Progress REAL NOT NULL DEFAULT 0,
                    CreatedBy TEXT NOT NULL,
                    CreatedAt TEXT NOT NULL DEFAULT (datetime('now')),
                    StartedAt TEXT NULL,
                    CompletedAt TEXT NULL,
                    ErrorMessage TEXT NULL,
                    ConfigData TEXT NULL
                )", connection);

            await createImportTasksCommand.ExecuteNonQueryAsync();

            var createMappingsCommand = CreateCommand(@"
                CREATE TABLE IF NOT EXISTS FileTableMappings (
                    Id TEXT PRIMARY KEY,
                    FileName TEXT NOT NULL,
                    TableName TEXT NOT NULL,
                    ColumnDefinitions TEXT NOT NULL,
                    CreatedAt TEXT NOT NULL DEFAULT (datetime('now')),
                    UNIQUE(FileName, TableName)
                )", connection);

            await createMappingsCommand.ExecuteNonQueryAsync();

            var createIndexCommand = CreateCommand(@"
                CREATE INDEX IF NOT EXISTS IX_FileTableMappings_FileName
                ON FileTableMappings(FileName)", connection);

            await createIndexCommand.ExecuteNonQueryAsync();
        }
        else
        {
            // SQL Server版本
            var createTablesCommand = CreateCommand(@"
                -- 删除现有表（如果存在）以重建结构
                -- 只有在不存在时才创建导入任务表（保留历史记录）
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ImportTasks' AND xtype='U')
                BEGIN
                    CREATE TABLE ImportTasks (
                    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
                    FileName NVARCHAR(255) NOT NULL,
                    FileSize BIGINT NOT NULL,
                    TotalRows INT NOT NULL DEFAULT 0,
                    ProcessedRows INT NOT NULL DEFAULT 0,
                    Status NVARCHAR(50) NOT NULL DEFAULT 'Pending',
                    Progress DECIMAL(5,2) NOT NULL DEFAULT 0,
                    CreatedBy NVARCHAR(255) NOT NULL,
                    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
                    StartedAt DATETIME2 NULL,
                    CompletedAt DATETIME2 NULL,
                    ErrorMessage NVARCHAR(MAX) NULL,
                    ConfigData NVARCHAR(MAX) NULL
                    );
                END

                -- 只有在不存在时才创建文件表映射表
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FileTableMappings' AND xtype='U')
                BEGIN
                    CREATE TABLE FileTableMappings (
                    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
                    FileName NVARCHAR(255) NOT NULL,
                    TableName NVARCHAR(255) NOT NULL,
                    ColumnDefinitions NVARCHAR(MAX) NOT NULL, -- JSON格式的列定义
                    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
                    UNIQUE(FileName, TableName)
                    );

                    -- 创建索引
                    CREATE INDEX IX_FileTableMappings_FileName ON FileTableMappings(FileName);
                END

                -- 创建ImportTasks索引（如果不存在）
                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ImportTasks_CreatedBy')
                    CREATE INDEX IX_ImportTasks_CreatedBy ON ImportTasks(CreatedBy);
                ", connection);

            await createTablesCommand.ExecuteNonQueryAsync();
        }

        Console.WriteLine("数据导入表创建成功");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"创建数据导入表失败: {ex.Message}");
        throw; // 重新抛出异常，确保启动失败时能够被发现
    }
}

// 检查关键表是否存在
async Task<bool> CheckRequiredTablesExist()
{
    if (workingConnectionString == null) return false;

    try
    {
        using var connection = CreateConnection(workingConnectionString);
        await connection.OpenAsync();

        string checkSql;
        if (useSqlite)
        {
            checkSql = @"
                SELECT
                    CASE WHEN EXISTS (SELECT name FROM sqlite_master WHERE type='table' AND name='ImportTasks') THEN 1 ELSE 0 END as ImportTasksExists,
                    CASE WHEN EXISTS (SELECT name FROM sqlite_master WHERE type='table' AND name='FileTableMappings') THEN 1 ELSE 0 END as FileTableMappingsExists,
                    CASE WHEN EXISTS (SELECT name FROM sqlite_master WHERE type='table' AND name='Users') THEN 1 ELSE 0 END as UsersExists";
        }
        else
        {
            checkSql = @"
                SELECT
                    CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'ImportTasks') THEN 1 ELSE 0 END as ImportTasksExists,
                    CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'FileTableMappings') THEN 1 ELSE 0 END as FileTableMappingsExists,
                    CASE WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Users') THEN 1 ELSE 0 END as UsersExists";
        }

        var checkCommand = CreateCommand(checkSql, connection);

        using var reader = await checkCommand.ExecuteReaderAsync();
        if (await reader.ReadAsync())
        {
            var importTasksExists = Convert.ToInt32(reader["ImportTasksExists"]) == 1;
            var fileTableMappingsExists = Convert.ToInt32(reader["FileTableMappingsExists"]) == 1;
            var usersExists = Convert.ToInt32(reader["UsersExists"]) == 1;

            Console.WriteLine($"表存在性检查: ImportTasks={importTasksExists}, FileTableMappings={fileTableMappingsExists}, Users={usersExists}");

            return importTasksExists && fileTableMappingsExists && usersExists;
        }

        return false;
    }
    catch (Exception ex)
    {
        Console.WriteLine($"检查表存在性失败: {ex.Message}");
        return false;
    }
}

// 自动恢复数据库表结构
async Task<bool> AutoRecoverDatabaseTables()
{
    Console.WriteLine("检测到表结构缺失，开始自动恢复...");

    try
    {
        // 重新创建用户表
        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        var createUserTableCommand = new SqlCommand(@"
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
            BEGIN
                CREATE TABLE Users (
                    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
                    Username NVARCHAR(50) UNIQUE NOT NULL,
                    Email NVARCHAR(100) UNIQUE NOT NULL,
                    PasswordHash NVARCHAR(255) NOT NULL,
                    FullName NVARCHAR(100) NOT NULL,
                    Role NVARCHAR(20) NOT NULL DEFAULT 'User',
                    IsActive BIT NOT NULL DEFAULT 1,
                    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
                    LastLoginTime DATETIME2 NULL
                )
                PRINT '用户表创建成功'
            END", connection);

        await createUserTableCommand.ExecuteNonQueryAsync();
        Console.WriteLine("用户表恢复完成");

        // 重新创建数据导入表
        await CreateDataImportTables();
        Console.WriteLine("数据导入表恢复完成");

        // 验证恢复结果
        var tablesExist = await CheckRequiredTablesExist();
        if (tablesExist)
        {
            Console.WriteLine("✅ 数据库表结构自动恢复成功！");
            return true;
        }
        else
        {
            Console.WriteLine("❌ 数据库表结构恢复失败");
            return false;
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"自动恢复数据库表结构失败: {ex.Message}");
        return false;
    }
}

// 创建导入任务
app.MapPost("/api/data-import/tasks", async (HttpRequest request) =>
{
    try
    {
        if (workingConnectionString == null)
        {
            return Results.Json(new { message = "数据库连接未初始化" }, statusCode: 500);
        }

        var body = await new StreamReader(request.Body).ReadToEndAsync();
        var taskData = JsonSerializer.Deserialize<JsonElement>(body);

        var fileName = taskData.GetProperty("fileName").GetString();
        var fileSize = taskData.GetProperty("fileSize").GetInt64();
        var totalRows = taskData.GetProperty("totalRows").GetInt32();
        var createdBy = taskData.GetProperty("createdBy").GetString();

        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        var command = new SqlCommand(@"
            INSERT INTO ImportTasks (FileName, FileSize, TotalRows, CreatedBy, Status)
            OUTPUT INSERTED.Id
            VALUES (@fileName, @fileSize, @totalRows, @createdBy, 'Pending')", connection);

        command.Parameters.AddWithValue("@fileName", fileName);
        command.Parameters.AddWithValue("@fileSize", fileSize);
        command.Parameters.AddWithValue("@totalRows", totalRows);
        command.Parameters.AddWithValue("@createdBy", createdBy);

        var taskId = await command.ExecuteScalarAsync();

        return Results.Ok(new
        {
            taskId = taskId,
            message = "导入任务创建成功"
        });
    }
    catch (Exception ex)
    {
        Console.WriteLine($"创建导入任务失败: {ex.Message}");
        Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        return Results.Json(new { message = $"创建导入任务失败: {ex.Message}" }, statusCode: 500);
    }
});

// 获取导入任务列表
app.MapGet("/api/data-import/tasks", async () =>
{
    try
    {
        if (workingConnectionString == null)
        {
            return Results.Json(new { message = "数据库连接未初始化" }, statusCode: 500);
        }

        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        var command = new SqlCommand(@"
            SELECT Id, FileName, FileSize, TotalRows, ProcessedRows, Status, Progress,
                   CreatedAt, StartedAt, CompletedAt, ErrorMessage
            FROM ImportTasks
            ORDER BY CreatedAt DESC", connection);

        using var reader = await command.ExecuteReaderAsync();
        var tasks = new List<object>();

        while (await reader.ReadAsync())
        {
            tasks.Add(new
            {
                id = reader["Id"].ToString(),
                fileName = reader["FileName"].ToString(),
                fileSize = (long)reader["FileSize"],
                totalRows = (int)reader["TotalRows"],
                processedRows = (int)reader["ProcessedRows"],
                status = reader["Status"].ToString(),
                progress = (decimal)reader["Progress"],
                createTime = (DateTime)reader["CreatedAt"],
                startTime = reader.IsDBNull(reader.GetOrdinal("StartedAt")) ? null : (DateTime?)reader["StartedAt"],
                endTime = reader.IsDBNull(reader.GetOrdinal("CompletedAt")) ? null : (DateTime?)reader["CompletedAt"],
                errorMessage = reader.IsDBNull(reader.GetOrdinal("ErrorMessage")) ? null : reader["ErrorMessage"].ToString()
            });
        }

        return Results.Ok(new { items = tasks });
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"获取导入任务失败: {ex.Message}" }, statusCode: 500);
    }
});

// 删除导入任务
app.MapDelete("/api/data-import/tasks/{taskId}", async (string taskId) =>
{
    try
    {
        if (workingConnectionString == null)
        {
            return Results.Json(new { message = "数据库连接未初始化" }, statusCode: 500);
        }

        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        // 检查任务是否存在并获取状态
        var checkCommand = new SqlCommand(@"
            SELECT Status, FileName FROM ImportTasks WHERE Id = @taskId", connection);
        checkCommand.Parameters.AddWithValue("@taskId", taskId);

        using var reader = await checkCommand.ExecuteReaderAsync();
        if (!await reader.ReadAsync())
        {
            return Results.Json(new { message = "任务不存在" }, statusCode: 404);
        }

        var taskStatus = reader["Status"].ToString();
        var fileName = reader["FileName"].ToString();
        reader.Close();

        // 如果是处理中的任务，记录中断信息
        if (taskStatus == "Processing" || taskStatus == "Pending")
        {
            Console.WriteLine($"⚠️ 删除处理中的任务: {taskId} ({fileName})");

            // 更新任务状态为已取消，然后删除
            var cancelCommand = new SqlCommand(@"
                UPDATE ImportTasks
                SET Status = 'Cancelled',
                    CompletedAt = GETUTCDATE(),
                    ErrorMessage = '任务被用户删除'
                WHERE Id = @taskId", connection);
            cancelCommand.Parameters.AddWithValue("@taskId", taskId);
            await cancelCommand.ExecuteNonQueryAsync();
        }

        // 删除任务记录
        var deleteCommand = new SqlCommand(@"
            DELETE FROM ImportTasks WHERE Id = @taskId", connection);
        deleteCommand.Parameters.AddWithValue("@taskId", taskId);

        var deletedRows = await deleteCommand.ExecuteNonQueryAsync();

        if (deletedRows > 0)
        {
            var statusMessage = (taskStatus == "Processing" || taskStatus == "Pending")
                ? $"✅ 处理中的任务已删除并中断: {taskId} ({fileName})"
                : $"✅ 导入任务已删除: {taskId} ({fileName})";

            Console.WriteLine(statusMessage);
            return Results.Ok(new {
                message = "任务删除成功",
                taskId = taskId,
                wasProcessing = taskStatus == "Processing" || taskStatus == "Pending"
            });
        }
        else
        {
            return Results.Json(new { message = "删除任务失败" }, statusCode: 500);
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ 删除任务失败: {ex.Message}");
        return Results.Json(new { message = $"删除任务失败: {ex.Message}" }, statusCode: 500);
    }
});

// 检查文件是否已存在并分析结构
app.MapPost("/api/data-import/analyze", async (HttpRequest request) =>
{
    try
    {
        if (workingConnectionString == null)
        {
            return Results.Json(new { message = "数据库连接未初始化" }, statusCode: 500);
        }

        var body = await new StreamReader(request.Body).ReadToEndAsync();
        var analyzeData = JsonSerializer.Deserialize<JsonElement>(body);

        var fileName = analyzeData.GetProperty("fileName").GetString();
        var sampleData = analyzeData.GetProperty("sampleData").EnumerateArray().FirstOrDefault();

        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        // 检查文件是否已存在
        var checkCommand = new SqlCommand(@"
            SELECT TableName, ColumnDefinitions
            FROM FileTableMappings
            WHERE FileName = @fileName", connection);
        checkCommand.Parameters.AddWithValue("@fileName", fileName);

        using var reader = await checkCommand.ExecuteReaderAsync();

        if (await reader.ReadAsync())
        {
            // 文件已存在，检查结构是否匹配
            var existingTableName = reader["TableName"].ToString();
            var existingColumns = reader["ColumnDefinitions"].ToString();
            reader.Close();

            // 验证表名是否有效
            if (!IsValidTableName(existingTableName))
            {
                Console.WriteLine($"无效的表名: {existingTableName}，将重新生成");
                // 表名无效，当作新文件处理
                var newTableName = GenerateTableName(fileName);
                var columns = new List<object>();
                foreach (var property in sampleData.EnumerateObject())
                {
                    var columnType = GetSqlDataType(property.Value);
                    columns.Add(new
                    {
                        name = property.Name,
                        type = columnType,
                        nullable = true
                    });
                }

                return Results.Ok(new
                {
                    fileExists = false,
                    tableName = newTableName,
                    columns = columns,
                    message = $"表名无效，将创建新表 '{newTableName}'"
                });
            }

            // 检查是否有重复数据
            int existingRowCount = 0;
            try
            {
                var duplicateCheckCommand = new SqlCommand($@"
                    SELECT COUNT(*) FROM [{existingTableName}]", connection);
                existingRowCount = (int)await duplicateCheckCommand.ExecuteScalarAsync();
                Console.WriteLine($"表 [{existingTableName}] 现有行数: {existingRowCount}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查询表 [{existingTableName}] 行数失败: {ex.Message}");
                // 如果查询失败，可能表不存在或名称无效，设置为0
                existingRowCount = 0;
            }

            return Results.Ok(new
            {
                fileExists = true,
                tableName = existingTableName,
                existingColumns = existingColumns,
                existingRowCount = existingRowCount,
                message = $"文件 '{fileName}' 已存在，包含 {existingRowCount} 行数据"
            });
        }
        else
        {
            reader.Close();

            // 新文件，分析列结构
            var columns = new List<object>();
            foreach (var property in sampleData.EnumerateObject())
            {
                var columnType = GetSqlDataType(property.Value);
                columns.Add(new
                {
                    name = property.Name,
                    type = columnType,
                    nullable = true
                });
            }

            var tableName = GenerateTableName(fileName);

            return Results.Ok(new
            {
                fileExists = false,
                tableName = tableName,
                columns = columns,
                message = $"新文件 '{fileName}'，将创建表 '{tableName}'"
            });
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"文件分析失败: {ex.Message}");
        return Results.Json(new { message = $"文件分析失败: {ex.Message}" }, statusCode: 500);
    }
});

// 保存导入数据（支持动态表创建和自动恢复）
app.MapPost("/api/data-import/data", async (HttpRequest request) =>
{
    try
    {
        if (workingConnectionString == null)
        {
            return Results.Json(new { message = "数据库连接未初始化" }, statusCode: 500);
        }

        // 检查关键表是否存在，如果不存在则自动恢复
        var tablesExist = await CheckRequiredTablesExist();
        if (!tablesExist)
        {
            Console.WriteLine("⚠️ 检测到关键表缺失，尝试自动恢复...");
            var recovered = await AutoRecoverDatabaseTables();
            if (!recovered)
            {
                return Results.Json(new {
                    message = "数据库表结构缺失且自动恢复失败，请联系管理员重建数据库结构",
                    errorType = "DatabaseStructureMissing"
                }, statusCode: 500);
            }
        }

        var body = await new StreamReader(request.Body).ReadToEndAsync();
        var importData = JsonSerializer.Deserialize<JsonElement>(body);

        var taskId = importData.GetProperty("taskId").GetString();
        var fileName = importData.GetProperty("fileName").GetString();
        var data = importData.GetProperty("data").EnumerateArray();

        // 获取批次信息
        var batchInfo = importData.TryGetProperty("batchInfo", out var batchElement) ? batchElement : (JsonElement?)null;
        var isFirstBatch = batchInfo?.TryGetProperty("isFirstBatch", out var firstBatchElement) == true ? firstBatchElement.GetBoolean() : true;
        var batchIndex = batchInfo?.TryGetProperty("batchIndex", out var batchIndexElement) == true ? batchIndexElement.GetInt32() : 0;
        var batchSize = batchInfo?.TryGetProperty("batchSize", out var batchSizeElement) == true ? batchSizeElement.GetInt32() : data.Count();
        var totalBatches = batchInfo?.TryGetProperty("totalBatches", out var totalBatchesElement) == true ? totalBatchesElement.GetInt32() : 1;

        // 计算起始行号（保持数据顺序）- 使用实际批次大小
        var startRowNumber = batchIndex * batchSize; // 🎯 修复：使用实际批次大小而不是硬编码10000

        Console.WriteLine($"处理批次 {batchIndex + 1}/{totalBatches}，数据行数: {data.Count()}，起始行号: {startRowNumber}，自动覆盖重复数据");

        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        using var transaction = connection.BeginTransaction();

        try
        {
            // 获取或创建表
            var tableName = await GetOrCreateDataTable(connection, transaction, fileName, data.FirstOrDefault());



            int totalDataRows = data.Count();
            Console.WriteLine($"🚀 开始极速批量导入到表 [{tableName}]，总行数: {totalDataRows}");

            // 使用SqlBulkCopy进行极速批量导入（保持数据顺序 + 数据指纹去重）
            var (importedRows, duplicateRows) = await BulkInsertData(connection, transaction, tableName, data, startRowNumber);

            Console.WriteLine($"⚡ 极速导入完成，总数据 {totalDataRows} 行，导入 {importedRows} 行，跳过重复 {duplicateRows} 行");

            // 更新任务状态 - 累加处理的行数
            var updateCommand = new SqlCommand(@"
                UPDATE ImportTasks
                SET ProcessedRows = ProcessedRows + @newProcessedRows,
                    Progress = CASE
                        WHEN TotalRows > 0 THEN
                            CAST((ProcessedRows + @newProcessedRows) * 100.0 / TotalRows AS DECIMAL(5,2))
                        ELSE 100
                    END,
                    Status = CASE
                        WHEN (ProcessedRows + @newProcessedRows) >= TotalRows THEN 'Completed'
                        ELSE 'Processing'
                    END,
                    CompletedAt = CASE
                        WHEN (ProcessedRows + @newProcessedRows) >= TotalRows THEN GETUTCDATE()
                        ELSE CompletedAt
                    END
                WHERE Id = @taskId", connection, transaction);

            updateCommand.Parameters.AddWithValue("@taskId", taskId);
            updateCommand.Parameters.AddWithValue("@newProcessedRows", totalDataRows);

            await updateCommand.ExecuteNonQueryAsync();

            transaction.Commit();

            return Results.Ok(new
            {
                message = "数据导入成功",
                processedRows = totalDataRows
            });
        }
        catch (Exception ex)
        {
            transaction.Rollback();
            throw;
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"=== 数据导入失败 ===");
        Console.WriteLine($"错误类型: {ex.GetType().Name}");
        Console.WriteLine($"错误消息: {ex.Message}");
        Console.WriteLine($"内部异常: {ex.InnerException?.Message}");
        Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        Console.WriteLine($"==================");

        return Results.Json(new {
            message = $"数据导入失败: {ex.Message}",
            errorType = ex.GetType().Name,
            innerException = ex.InnerException?.Message
        }, statusCode: 500);
    }
});

// 清理无效表映射
app.MapPost("/api/data-import/cleanup-invalid-mappings", async () =>
{
    try
    {
        if (workingConnectionString == null)
        {
            return Results.Json(new { message = "数据库连接未初始化" }, statusCode: 500);
        }

        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        // 查找所有映射
        var selectCommand = new SqlCommand(@"
            SELECT Id, FileName, TableName FROM FileTableMappings", connection);

        var invalidMappings = new List<string>();
        using var reader = await selectCommand.ExecuteReaderAsync();
        var mappingsToDelete = new List<string>();

        while (await reader.ReadAsync())
        {
            var tableName = reader["TableName"].ToString();
            var id = reader["Id"].ToString();

            if (!IsValidTableName(tableName))
            {
                invalidMappings.Add(tableName);
                mappingsToDelete.Add(id);
            }
        }
        reader.Close();

        // 删除无效映射
        foreach (var id in mappingsToDelete)
        {
            var deleteCommand = new SqlCommand(@"
                DELETE FROM FileTableMappings WHERE Id = @id", connection);
            deleteCommand.Parameters.AddWithValue("@id", id);
            await deleteCommand.ExecuteNonQueryAsync();
        }

        return Results.Ok(new
        {
            message = $"清理完成，删除了 {mappingsToDelete.Count} 个无效映射",
            invalidMappings = invalidMappings
        });
    }
    catch (Exception ex)
    {
        Console.WriteLine($"清理无效映射失败: {ex.Message}");
        return Results.Json(new { message = $"清理失败: {ex.Message}" }, statusCode: 500);
    }
});

// 数据库健康检查API
app.MapGet("/api/database/health", async () =>
{
    try
    {
        if (workingConnectionString == null)
        {
            return Results.Json(new {
                status = "disconnected",
                message = "数据库连接未初始化"
            }, statusCode: 500);
        }

        var tablesExist = await CheckRequiredTablesExist();

        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        // 获取详细的表信息
        var tableInfoCommand = new SqlCommand(@"
            SELECT
                TABLE_NAME,
                (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = t.TABLE_NAME) as ColumnCount
            FROM INFORMATION_SCHEMA.TABLES t
            WHERE TABLE_TYPE = 'BASE TABLE'
              AND TABLE_NAME IN ('Users', 'ImportTasks', 'FileTableMappings')
            ORDER BY TABLE_NAME
        ", connection);

        var tables = new List<object>();
        using var reader = await tableInfoCommand.ExecuteReaderAsync();
        while (await reader.ReadAsync())
        {
            tables.Add(new
            {
                name = reader["TABLE_NAME"].ToString(),
                columnCount = (int)reader["ColumnCount"]
            });
        }

        return Results.Ok(new
        {
            status = tablesExist ? "healthy" : "missing_tables",
            message = tablesExist ? "数据库结构完整" : "部分关键表缺失",
            tables = tables,
            canAutoRecover = !tablesExist
        });
    }
    catch (Exception ex)
    {
        return Results.Json(new {
            status = "error",
            message = $"健康检查失败: {ex.Message}"
        }, statusCode: 500);
    }
});

// 手动恢复数据库表结构API
app.MapPost("/api/database/recover", async () =>
{
    try
    {
        if (workingConnectionString == null)
        {
            return Results.Json(new { message = "数据库连接未初始化" }, statusCode: 500);
        }

        Console.WriteLine("🔧 手动触发数据库表结构恢复...");
        var recovered = await AutoRecoverDatabaseTables();

        if (recovered)
        {
            return Results.Ok(new {
                message = "数据库表结构恢复成功",
                status = "recovered"
            });
        }
        else
        {
            return Results.Json(new {
                message = "数据库表结构恢复失败",
                status = "failed"
            }, statusCode: 500);
        }
    }
    catch (Exception ex)
    {
        return Results.Json(new {
            message = $"恢复失败: {ex.Message}",
            status = "error"
        }, statusCode: 500);
    }
});

// 获取导入的数据（表格格式）
app.MapGet("/api/data-import/data/{taskId}", async (string taskId) =>
{
    try
    {
        if (workingConnectionString == null)
        {
            return Results.Json(new { message = "数据库连接未初始化" }, statusCode: 500);
        }

        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        // 获取任务对应的表名
        var taskCommand = new SqlCommand(@"
            SELECT it.FileName, ftm.TableName
            FROM ImportTasks it
            LEFT JOIN FileTableMappings ftm ON it.FileName = ftm.FileName
            WHERE it.Id = @taskId", connection);
        taskCommand.Parameters.AddWithValue("@taskId", taskId);

        using var taskReader = await taskCommand.ExecuteReaderAsync();
        if (!await taskReader.ReadAsync())
        {
            return Results.Json(new { message = "任务不存在" }, statusCode: 404);
        }

        var tableName = taskReader["TableName"]?.ToString();
        taskReader.Close();

        if (string.IsNullOrEmpty(tableName))
        {
            return Results.Json(new { message = "未找到对应的数据表" }, statusCode: 404);
        }

        // 查询数据
        var dataCommand = new SqlCommand($@"
            SELECT * FROM [{tableName}]
            ORDER BY ImportedAt", connection);

        using var dataReader = await dataCommand.ExecuteReaderAsync();
        var rows = new List<object>();
        var columns = new List<string>();

        // 获取列名（排除系统字段）
        for (int i = 0; i < dataReader.FieldCount; i++)
        {
            var columnName = dataReader.GetName(i);
            if (columnName != "ImportedAt" && columnName != "RowId")
            {
                columns.Add(columnName);
            }
        }

        while (await dataReader.ReadAsync())
        {
            var row = new Dictionary<string, object>();
            foreach (var column in columns)
            {
                var value = dataReader[column];
                row[column] = value == DBNull.Value ? null : value;
            }
            rows.Add(row);
        }

        return Results.Ok(new {
            taskId = taskId,
            tableName = tableName,
            totalRows = rows.Count,
            data = rows
        });
    }
    catch (Exception ex)
    {
        Console.WriteLine($"获取导入数据失败: {ex.Message}");
        return Results.Json(new { message = $"获取导入数据失败: {ex.Message}" }, statusCode: 500);
    }
});

// 获取导入数据的列信息
app.MapGet("/api/data-import/columns/{taskId}", async (string taskId) =>
{
    try
    {
        if (workingConnectionString == null)
        {
            return Results.Json(new { message = "数据库连接未初始化" }, statusCode: 500);
        }

        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        var command = new SqlCommand(@"
            SELECT TOP 1 RowData
            FROM ImportedData
            WHERE TaskId = @taskId
            ORDER BY RowIndex", connection);

        command.Parameters.AddWithValue("@taskId", taskId);

        var result = await command.ExecuteScalarAsync();
        if (result == null)
        {
            return Results.Json(new { message = "未找到数据" }, statusCode: 404);
        }

        var rowData = result.ToString();
        var rowObject = JsonSerializer.Deserialize<JsonElement>(rowData);

        var columns = new List<object>();
        foreach (var property in rowObject.EnumerateObject())
        {
            columns.Add(new
            {
                key = property.Name,
                title = property.Name,
                dataIndex = property.Name
            });
        }

        return Results.Ok(new {
            taskId = taskId,
            columns = columns
        });
    }
    catch (Exception ex)
    {
        Console.WriteLine($"获取列信息失败: {ex.Message}");
        return Results.Json(new { message = $"获取列信息失败: {ex.Message}" }, statusCode: 500);
    }
});

// 测试数据导入API连接
app.MapGet("/api/data-import/test", () =>
{
    return Results.Ok(new
    {
        message = "数据导入API正常工作",
        timestamp = DateTime.UtcNow,
        database = workingConnectionString != null ? "connected" : "disconnected"
    });
});

// 健康检查接口
app.MapGet("/api/health", () =>
{
    return Results.Ok(new
    {
        status = "healthy",
        timestamp = DateTime.UtcNow,
        version = "1.0.0",
        database = workingConnectionString != null ? "connected" : "disconnected"
    });
});

// 初始化数据导入表
await CreateDataImportTables();

Console.WriteLine("🎉 SQL Server API服务器启动成功!");
Console.WriteLine($"监听地址: http://0.0.0.0:5000 (所有网络接口)");
Console.WriteLine($"本机访问: http://localhost:5000");
Console.WriteLine($"局域网访问: http://{System.Net.Dns.GetHostName()}:5000");
Console.WriteLine($"数据库: SQL Server - ProductionDataVisualizationDb");

app.Run("http://0.0.0.0:5000");

// 辅助方法
static string GenerateTableName(string fileName)
{
    // 从文件名生成表名，移除扩展名和特殊字符
    var tableName = Path.GetFileNameWithoutExtension(fileName);

    // 只移除特殊符号，保留中文、英文、数字
    tableName = System.Text.RegularExpressions.Regex.Replace(tableName, @"[^\w\u4e00-\u9fa5]", "");

    // 如果表名为空，使用默认名称
    if (string.IsNullOrEmpty(tableName))
    {
        tableName = "DefaultTable";
    }

    // 限制表名长度（SQL Server表名最大128字符）
    if (tableName.Length > 100)
    {
        tableName = tableName.Substring(0, 100);
    }

    // 使用简单的Data_前缀格式，不添加时间戳
    tableName = "Data_" + tableName;

    Console.WriteLine($"生成表名: {tableName}");
    return tableName;
}

static bool IsValidTableName(string tableName)
{
    // 检查表名是否符合SQL Server规范（支持中文）
    if (string.IsNullOrEmpty(tableName) || tableName.Length > 128)
        return false;

    // 表名必须以字母、中文字符或下划线开头
    if (!char.IsLetter(tableName[0]) && tableName[0] != '_' && !IsChinese(tableName[0]))
        return false;

    // 表名可以包含字母、数字、下划线和中文字符
    return System.Text.RegularExpressions.Regex.IsMatch(tableName, @"^[a-zA-Z_\u4e00-\u9fa5][\w\u4e00-\u9fa5]*$");
}

static bool IsChinese(char c)
{
    // 检查字符是否为中文字符
    return c >= 0x4e00 && c <= 0x9fa5;
}

static string GetSqlDataType(JsonElement element)
{
    return element.ValueKind switch
    {
        JsonValueKind.Number => element.TryGetInt32(out _) ? "INT" : "DECIMAL(18,6)",
        JsonValueKind.True or JsonValueKind.False => "BIT",
        JsonValueKind.String => "NVARCHAR(MAX)",
        _ => "NVARCHAR(MAX)"
    };
}



static async Task<string> GetOrCreateDataTable(SqlConnection connection, SqlTransaction transaction, string fileName, JsonElement sampleRow)
{
    // 总是生成新的表名，确保一致性
    var tableName = GenerateTableName(fileName);
    Console.WriteLine($"为文件 '{fileName}' 生成表名: {tableName}");

    // 检查是否已有映射
    var checkCommand = new SqlCommand(@"
        SELECT TableName FROM FileTableMappings WHERE FileName = @fileName", connection, transaction);
    checkCommand.Parameters.AddWithValue("@fileName", fileName);

    var existingTableName = await checkCommand.ExecuteScalarAsync() as string;
    if (!string.IsNullOrEmpty(existingTableName))
    {
        Console.WriteLine($"发现现有映射: {existingTableName}");

        // 如果现有表名与新生成的表名不同，删除旧映射
        if (existingTableName != tableName)
        {
            Console.WriteLine($"表名不一致，删除旧映射: {existingTableName} -> {tableName}");
            var deleteCommand = new SqlCommand(@"
                DELETE FROM FileTableMappings WHERE FileName = @fileName", connection, transaction);
            deleteCommand.Parameters.AddWithValue("@fileName", fileName);
            await deleteCommand.ExecuteNonQueryAsync();
        }
        else
        {
            // 表名一致，但需要检查表是否真的存在
            Console.WriteLine($"表名一致，检查表是否实际存在: {tableName}");

            var tableExistsCommand = new SqlCommand(@"
                SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_NAME = @tableName", connection, transaction);
            tableExistsCommand.Parameters.AddWithValue("@tableName", tableName);

            var tableExists = Convert.ToInt32(await tableExistsCommand.ExecuteScalarAsync()) > 0;

            if (tableExists)
            {
                Console.WriteLine($"✅ 表确实存在，使用现有表: {tableName}");
                return tableName;
            }
            else
            {
                Console.WriteLine($"❌ 表不存在但有映射记录，删除无效映射: {tableName}");
                var deleteInvalidMappingCommand = new SqlCommand(@"
                    DELETE FROM FileTableMappings WHERE FileName = @fileName", connection, transaction);
                deleteInvalidMappingCommand.Parameters.AddWithValue("@fileName", fileName);
                await deleteInvalidMappingCommand.ExecuteNonQueryAsync();

                // 继续创建新表
            }
        }
    }

    // 创建新表（使用已生成的表名）
    Console.WriteLine($"准备创建新表: {tableName}");

    // 再次检查表是否存在（防止并发创建）
    var finalTableExistsCommand = new SqlCommand(@"
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = @tableName", connection, transaction);
    finalTableExistsCommand.Parameters.AddWithValue("@tableName", tableName);

    var finalTableExists = Convert.ToInt32(await finalTableExistsCommand.ExecuteScalarAsync()) > 0;
    if (finalTableExists)
    {
        Console.WriteLine($"表已存在，直接使用: {tableName}");
        return tableName;
    }

    Console.WriteLine($"开始创建新表: {tableName}");
    var columns = new List<string>();
    var columnDefinitions = new List<object>();

    foreach (var property in sampleRow.EnumerateObject())
    {
        var sqlType = GetSqlDataType(property.Value);
        columns.Add($"[{property.Name}] {sqlType} NULL");

        columnDefinitions.Add(new
        {
            name = property.Name,
            type = sqlType,
            nullable = true
        });
    }

    // 🚀 极速优化：移除所有系统字段，只保留业务数据
    // 不添加任何系统字段，最大化导入性能

    var createTableSql = $@"
        CREATE TABLE [{tableName}] (
            {string.Join(",\n            ", columns)}
        )";

    // 🚀 临时移除唯一约束以恢复性能
    // CONSTRAINT UK_{tableName}_DataHash UNIQUE (DataHash)

    var createCommand = new SqlCommand(createTableSql, connection, transaction);
    await createCommand.ExecuteNonQueryAsync();
    Console.WriteLine($"✅ 表创建成功: {tableName} (包含数据指纹去重)");

    // 保存映射关系
    var mappingCommand = new SqlCommand(@"
        INSERT INTO FileTableMappings (FileName, TableName, ColumnDefinitions)
        VALUES (@fileName, @tableName, @columnDefinitions)", connection, transaction);

    mappingCommand.Parameters.AddWithValue("@fileName", fileName);
    mappingCommand.Parameters.AddWithValue("@tableName", tableName);
    mappingCommand.Parameters.AddWithValue("@columnDefinitions", JsonSerializer.Serialize(columnDefinitions));

    await mappingCommand.ExecuteNonQueryAsync();

    return tableName;
}

// 🚀 极速批量插入函数 - 使用SqlBulkCopy（保持数据顺序 + 数据指纹去重）
async Task<(int imported, int duplicates)> BulkInsertData(SqlConnection connection, SqlTransaction transaction, string tableName, IEnumerable<JsonElement> data, int startRowNumber = 0)
{
    var dataList = data.ToList();
    if (!dataList.Any()) return (0, 0);

    // 创建DataTable
    var dataTable = new DataTable();
    var firstRow = dataList.First();

    // 添加列定义
    foreach (var property in firstRow.EnumerateObject())
    {
        var columnType = GetClrType(property.Value);
        dataTable.Columns.Add(property.Name, columnType);
    }

    // 🚀 极速优化：不添加任何系统列，只保留业务数据

    Console.WriteLine($"🚀 极速导入模式: 总行数 {dataList.Count}, 纯业务数据");
    Console.WriteLine($"⚡ 准备批量插入 {dataList.Count} 行数据，列数: {dataTable.Columns.Count}");

    // 填充数据（跳过去重检测）
    var rowCount = 0;
    foreach (var rowData in dataList)
    {
        var dataRow = dataTable.NewRow();

        foreach (var property in rowData.EnumerateObject())
        {
            dataRow[property.Name] = GetClrValue(property.Value);
        }

        // 🚀 极速优化：不设置任何系统字段，只保留业务数据

        dataTable.Rows.Add(dataRow);
        rowCount++;
    }

    // 跳转到SqlBulkCopy部分
    goto SkipDeduplication;

    // 🔒 第1步：获取业务字段列表（排除系统字段和行索引字段）
    var businessColumns = firstRow.EnumerateObject()
        .Select(p => p.Name)
        .Where(name => !name.StartsWith("System")
                    && name != "SourceRowNumber"
                    && name != "ImportedAt"
                    && name != "RowId"
                    && name != "DataHash"
                    && name != "_rowIndex"  // 🎯 排除行索引，避免不同文件相同数据产生不同哈希
                    )
        .ToList();

    // Console.WriteLine($"🔍 业务字段列表: [{string.Join(", ", businessColumns)}]"); // 性能优化：减少日志

    // 🔒 第2步：简单顺序生成哈希值（避免并行冲突）
    var dataWithHashes = new List<(JsonElement data, string hash, int originalIndex)>();
    for (int i = 0; i < dataList.Count; i++)
    {
        var hash = GenerateDataHash(dataList[i], businessColumns);
        dataWithHashes.Add((dataList[i], hash, i));
    }

    // 🔍 第3步：批量检查现有哈希值
    var allHashes = dataWithHashes.Select(x => x.hash).ToList();
    var existingHashes = await GetExistingHashes(connection, transaction, tableName, allHashes);

    // 🎯 第4步：处理重复数据 - 覆盖模式
    var duplicateHashes = dataWithHashes.Where(x => existingHashes.Contains(x.hash)).Select(x => x.hash).ToList();
    var uniqueData = dataWithHashes.Where(x => !existingHashes.Contains(x.hash)).ToList();
    var duplicateCount = duplicateHashes.Count;

    Console.WriteLine($"🔒 数据覆盖处理: 总数据 {dataList.Count} 行, 重复覆盖 {duplicateCount} 行, 新增 {uniqueData.Count} 行");

    // 🗑️ 第5步：删除重复数据（覆盖模式）
    if (duplicateHashes.Any())
    {
        Console.WriteLine($"🗑️ 删除 {duplicateHashes.Count} 行重复数据进行覆盖...");
        await DeleteDuplicateData(connection, transaction, tableName, duplicateHashes);
        Console.WriteLine($"✅ 重复数据删除完成");
    }

    Console.WriteLine($"⚡ 准备批量插入 {dataList.Count} 行数据（覆盖模式），列数: {dataTable.Columns.Count}");

    // 填充数据（包含所有数据，重复数据已被删除，现在重新插入）
    var rowCount2 = 0;
    foreach (var (rowData, hash, originalIndex) in dataWithHashes)
    {
        var dataRow = dataTable.NewRow();

        foreach (var property in rowData.EnumerateObject())
        {
            dataRow[property.Name] = GetClrValue(property.Value);
        }

        // 设置系统字段（包含顺序信息和数据指纹）
        dataRow["SourceRowNumber"] = startRowNumber + originalIndex; // 🎯 记录原始行号
        dataRow["DataHash"] = hash; // 🔒 数据指纹
        dataRow["ImportedAt"] = DateTime.UtcNow;
        dataRow["RowId"] = Guid.NewGuid();

        dataTable.Rows.Add(dataRow);
        rowCount2++;

        // 每1000行输出一次进度
        if (rowCount % 1000 == 0)
        {
            Console.WriteLine($"⚡ 已准备 {rowCount}/{dataList.Count} 行数据");
        }
    }

    SkipDeduplication:
    // 🚀 稳定高性能：使用TableLock优化
    using var bulkCopy = new SqlBulkCopy(connection, SqlBulkCopyOptions.TableLock, transaction);
    bulkCopy.DestinationTableName = $"[{tableName}]";
    bulkCopy.BatchSize = 100000; // 🚀 稳定高性能批次大小
    bulkCopy.BulkCopyTimeout = 300; // 5分钟超时
    bulkCopy.NotifyAfter = 50000; // 每5万行通知一次

    // 映射列
    foreach (DataColumn column in dataTable.Columns)
    {
        bulkCopy.ColumnMappings.Add(column.ColumnName, column.ColumnName);
    }

    Console.WriteLine($"🚀 开始SqlBulkCopy极速插入...");
    var stopwatch = System.Diagnostics.Stopwatch.StartNew();

    await bulkCopy.WriteToServerAsync(dataTable);

    stopwatch.Stop();
    var rowsPerSecond = dataList.Count / Math.Max(stopwatch.Elapsed.TotalSeconds, 0.001);
    Console.WriteLine($"⚡ SqlBulkCopy完成！插入 {dataList.Count} 行数据，耗时 {stopwatch.Elapsed.TotalSeconds:F2}秒，速度 {rowsPerSecond:F0} 行/秒");
    Console.WriteLine($"🚀 极速导入: 总数据 {dataList.Count} 行, 纯业务数据, 无系统字段");

    return (dataList.Count, 0); // 临时返回值
}

// 获取CLR类型
static Type GetClrType(JsonElement element)
{
    return element.ValueKind switch
    {
        JsonValueKind.String => typeof(string),
        JsonValueKind.Number => typeof(decimal),
        JsonValueKind.True or JsonValueKind.False => typeof(bool),
        JsonValueKind.Null => typeof(string),
        _ => typeof(string)
    };
}

// 获取CLR值
static object GetClrValue(JsonElement element)
{
    return element.ValueKind switch
    {
        JsonValueKind.String => element.GetString() ?? "",
        JsonValueKind.Number => element.GetDecimal(),
        JsonValueKind.True => true,
        JsonValueKind.False => false,
        JsonValueKind.Null => DBNull.Value,
        _ => element.ToString()
    };
}

// 🔍 调试API：检查数据顺序
app.MapGet("/api/debug/check-order/{tableName}", async (string tableName) =>
{
    try
    {
        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        var query = $@"
            SELECT TOP 20
                SourceRowNumber,
                DataHash,
                ImportedAt,
                RowId
            FROM [{tableName}]
            ORDER BY SourceRowNumber";

        var command = new SqlCommand(query, connection);
        var results = new List<object>();

        using var reader = await command.ExecuteReaderAsync();
        while (await reader.ReadAsync())
        {
            results.Add(new
            {
                SourceRowNumber = reader["SourceRowNumber"],
                DataHash = reader["DataHash"],
                ImportedAt = reader["ImportedAt"],
                RowId = reader["RowId"]
            });
        }

        return Results.Ok(new {
            tableName = tableName,
            totalRows = results.Count,
            data = results,
            message = "数据顺序检查完成"
        });
    }
    catch (Exception ex)
    {
        return Results.Json(new { error = ex.Message }, statusCode: 500);
    }
});

// 🚨 紧急恢复API：执行SQL脚本
app.MapPost("/api/emergency/execute-sql", async (HttpContext context) =>
{
    try
    {
        // 读取请求体中的SQL脚本
        using var reader = new StreamReader(context.Request.Body);
        var sqlScript = await reader.ReadToEndAsync();

        if (string.IsNullOrEmpty(sqlScript))
        {
            return Results.BadRequest(new { error = "SQL脚本不能为空" });
        }

        // 执行SQL脚本
        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        // 分割SQL脚本（按GO语句分割）
        var sqlBatches = sqlScript.Split(new[] { "GO", "go" }, StringSplitOptions.RemoveEmptyEntries);
        var results = new List<string>();

        foreach (var batch in sqlBatches)
        {
            if (string.IsNullOrWhiteSpace(batch)) continue;

            try
            {
                using var command = new SqlCommand(batch, connection);
                command.CommandTimeout = 60; // 设置超时时间

                // 执行非查询SQL
                var rowsAffected = await command.ExecuteNonQueryAsync();
                results.Add($"执行成功: 影响 {rowsAffected} 行");
            }
            catch (Exception ex)
            {
                results.Add($"执行失败: {ex.Message}");
            }
        }

        return Results.Ok(new {
            success = true,
            message = "SQL脚本执行完成",
            results = results
        });
    }
    catch (Exception ex)
    {
        return Results.Json(new { error = ex.Message }, statusCode: 500);
    }
});

// 🔒 调试API：检查重复数据统计
app.MapGet("/api/debug/duplicate-stats/{tableName}", async (string tableName) =>
{
    try
    {
        using var connection = new SqlConnection(workingConnectionString);
        await connection.OpenAsync();

        // 检查重复哈希值
        var duplicateQuery = $@"
            SELECT
                DataHash,
                COUNT(*) as Count,
                MIN(ImportedAt) as FirstImported,
                MAX(ImportedAt) as LastImported
            FROM [{tableName}]
            GROUP BY DataHash
            HAVING COUNT(*) > 1
            ORDER BY COUNT(*) DESC";

        var duplicateCommand = new SqlCommand(duplicateQuery, connection);
        var duplicates = new List<object>();

        using var duplicateReader = await duplicateCommand.ExecuteReaderAsync();
        while (await duplicateReader.ReadAsync())
        {
            duplicates.Add(new
            {
                DataHash = duplicateReader["DataHash"],
                Count = duplicateReader["Count"],
                FirstImported = duplicateReader["FirstImported"],
                LastImported = duplicateReader["LastImported"]
            });
        }

        // 获取总体统计
        var statsQuery = $@"
            SELECT
                COUNT(*) as TotalRows,
                COUNT(DISTINCT DataHash) as UniqueRows,
                COUNT(*) - COUNT(DISTINCT DataHash) as DuplicateRows
            FROM [{tableName}]";

        var statsCommand = new SqlConnection(workingConnectionString);
        await statsCommand.OpenAsync();
        var statsCmd = new SqlCommand(statsQuery, statsCommand);

        using var statsReader = await statsCmd.ExecuteReaderAsync();
        var stats = new { TotalRows = 0, UniqueRows = 0, DuplicateRows = 0 };
        if (await statsReader.ReadAsync())
        {
            stats = new
            {
                TotalRows = Convert.ToInt32(statsReader["TotalRows"]),
                UniqueRows = Convert.ToInt32(statsReader["UniqueRows"]),
                DuplicateRows = Convert.ToInt32(statsReader["DuplicateRows"])
            };
        }

        return Results.Ok(new {
            tableName = tableName,
            statistics = stats,
            duplicateDetails = duplicates,
            message = "重复数据统计完成"
        });
    }
    catch (Exception ex)
    {
        return Results.Json(new { error = ex.Message }, statusCode: 500);
    }
});

app.Run();

// 🔒 数据指纹生成函数
string GenerateDataHash(JsonElement row, List<string> businessColumns)
{
    try
    {
        // 提取业务数据字段值
        var values = new List<string>();
        foreach (var column in businessColumns.OrderBy(x => x)) // 排序确保一致性
        {
            if (row.TryGetProperty(column, out var prop))
            {
                var value = prop.ValueKind == JsonValueKind.Null ? "" : prop.ToString();
                values.Add(value?.Trim() ?? ""); // 去除空白字符
            }
            else
            {
                values.Add(""); // 缺失字段用空字符串
            }
        }

        // 组合所有值
        var combined = string.Join("|", values);

        // 🔍 调试日志：仅在需要时启用
        // Console.WriteLine($"🔍 哈希计算 - 业务字段: [{string.Join(", ", businessColumns)}]");
        // Console.WriteLine($"🔍 哈希计算 - 组合值: {combined}");

        // 🚀 性能优化：使用更快的哈希算法
        var hashString = Convert.ToHexString(MD5.HashData(Encoding.UTF8.GetBytes(combined)));

        // Console.WriteLine($"🔍 哈希计算 - 结果: {hashString}");
        return hashString;
    }
    catch (Exception ex)
    {
        Console.WriteLine($"⚠️ 生成数据哈希失败: {ex.Message}");
        Console.WriteLine($"⚠️ 异常详情: {ex}");
        // 如果哈希生成失败，使用GUID作为备用方案
        var fallbackHash = Guid.NewGuid().ToString("N").ToUpper();
        Console.WriteLine($"⚠️ 使用备用哈希: {fallbackHash}");
        return fallbackHash;
    }
}

// 🔍 批量检查现有哈希值
async Task<HashSet<string>> GetExistingHashes(SqlConnection connection, SqlTransaction transaction, string tableName, List<string> hashes)
{
    var existingHashes = new HashSet<string>();

    if (hashes.Count == 0) return existingHashes;

    try
    {
        // 简化查询：使用小批次避免性能问题
        const int batchSize = 500; // 使用较小批次
        for (int i = 0; i < hashes.Count; i += batchSize)
        {
            var batch = hashes.Skip(i).Take(batchSize).ToList();
            var parameters = string.Join(",", batch.Select((h, idx) => $"@hash{i + idx}"));
            var query = $"SELECT DataHash FROM [{tableName}] WHERE DataHash IN ({parameters})";

            using var command = new SqlCommand(query, connection, transaction);
            for (int j = 0; j < batch.Count; j++)
            {
                command.Parameters.AddWithValue($"@hash{i + j}", batch[j]);
            }

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                existingHashes.Add(reader.GetString(0));
            }
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"⚠️ 查询现有哈希值失败: {ex.Message}");
    }

    return existingHashes;
}

// 🗑️ 删除重复数据函数（用于覆盖模式）
async Task DeleteDuplicateData(SqlConnection connection, SqlTransaction transaction, string tableName, List<string> hashesToDelete)
{
    if (!hashesToDelete.Any()) return;

    try
    {
        // 简化删除：使用参数化查询，分批删除
        const int batchSize = 500;
        var deletedCount = 0;

        for (int i = 0; i < hashesToDelete.Count; i += batchSize)
        {
            var batch = hashesToDelete.Skip(i).Take(batchSize).ToList();
            var parameters = string.Join(",", batch.Select((h, idx) => $"@hash{i + idx}"));
            var deleteQuery = $"DELETE FROM [{tableName}] WHERE DataHash IN ({parameters})";

            using var command = new SqlCommand(deleteQuery, connection, transaction);
            for (int j = 0; j < batch.Count; j++)
            {
                command.Parameters.AddWithValue($"@hash{i + j}", batch[j]);
            }

            var affected = await command.ExecuteNonQueryAsync();
            deletedCount += affected;
        }

        Console.WriteLine($"🗑️ 删除重复数据: {deletedCount} 行");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"⚠️ 删除重复数据失败: {ex.Message}");
        throw; // 重新抛出异常，让事务回滚
    }
}

// 请求模型
public record RegisterRequest(string Username, string Email, string Password, string FullName);
public record LoginRequest(string UsernameOrEmail, string Password);
public record CreateUserRequest(string Username, string Email, string Password, string FullName);
public record UpdateUserRequest(string Email, string FullName, bool IsActive, string? NewPassword);
