using System.Security.Claims;
using Domain.UserAggregate;

namespace API.Auth
{
    /// <summary>
    /// JWT服务接口
    /// </summary>
    public interface IJwtService
    {
        /// <summary>
        /// 生成JWT令牌
        /// </summary>
        /// <param name="user">用户</param>
        /// <param name="roles">角色列表</param>
        /// <param name="permissions">权限列表</param>
        /// <returns>JWT令牌</returns>
        string GenerateToken(User user, IEnumerable<string> roles, IEnumerable<string> permissions);

        /// <summary>
        /// 验证令牌并返回声明
        /// </summary>
        /// <param name="token">JWT令牌</param>
        /// <returns>声明主体</returns>
        ClaimsPrincipal? ValidateToken(string token);
    }
} 