import React, { useState } from 'react';
import { 
  Typography, 
  Row, 
  Col, 
  Switch, 
  Button, 
  Input, 
  Select, 
  message,
  Divider,
  Space,
  Tag
} from 'antd';
import {
  SettingOutlined,
  SecurityScanOutlined,
  NotificationOutlined,
  DatabaseOutlined,
  UserOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import CleanAssanLayout from '../components/layout/CleanAssanLayout';
import CleanCard from '../components/common/CleanCard';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const CleanSystemSettings = () => {
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({
    // 系统设置
    systemName: 'Assan Pro 生产数据可视化系统',
    systemVersion: '1.0.0',
    maintenanceMode: false,
    autoBackup: true,
    backupInterval: '24',
    
    // 安全设置
    sessionTimeout: '30',
    passwordPolicy: 'medium',
    twoFactorAuth: false,
    loginAttempts: '5',
    
    // 通知设置
    emailNotifications: true,
    systemAlerts: true,
    maintenanceNotifications: true,
    reportNotifications: false,
    
    // 数据设置
    dataRetention: '365',
    autoCleanup: true,
    compressionEnabled: true,
    realtimeSync: true
  });

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      // 模拟保存API调用
      await new Promise(resolve => setTimeout(resolve, 1500));
      message.success('设置保存成功');
    } catch (error) {
      message.error('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    // 重置为默认设置
    message.info('设置已重置为默认值');
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 }
    }
  };

  const SettingItem = ({ icon, title, description, children, status }) => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'flex-start', 
      justifyContent: 'space-between',
      padding: '20px 0',
      borderBottom: '1px solid #f3f4f6'
    }}>
      <div style={{ display: 'flex', alignItems: 'flex-start', gap: '16px', flex: 1 }}>
        <div style={{
          width: '40px',
          height: '40px',
          borderRadius: '10px',
          background: '#f8fafc',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#6366f1',
          fontSize: '18px'
        }}>
          {icon}
        </div>
        <div style={{ flex: 1 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '4px' }}>
            <Text style={{ fontWeight: '600', fontSize: '16px', color: '#111827' }}>
              {title}
            </Text>
            {status && (
              <Tag color={status === 'enabled' ? 'green' : status === 'warning' ? 'orange' : 'default'}>
                {status === 'enabled' ? '已启用' : status === 'warning' ? '警告' : '已禁用'}
              </Tag>
            )}
          </div>
          <Text style={{ color: '#6b7280', fontSize: '14px' }}>
            {description}
          </Text>
        </div>
      </div>
      <div style={{ marginLeft: '16px' }}>
        {children}
      </div>
    </div>
  );

  return (
    <CleanAssanLayout>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* 页面标题 */}
        <motion.div variants={itemVariants} style={{ marginBottom: '24px' }}>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'flex-start',
            marginBottom: '8px'
          }}>
            <div>
              <Title level={1} style={{ 
                margin: 0, 
                fontSize: '32px',
                fontWeight: '700',
                color: '#111827',
                lineHeight: '1.2'
              }}>
                系统设置
              </Title>
              <Text style={{ 
                fontSize: '16px',
                color: '#6b7280',
                display: 'block',
                marginTop: '8px'
              }}>
                配置系统参数和安全选项
              </Text>
            </div>
            <Space>
              <Button 
                icon={<ReloadOutlined />}
                onClick={handleReset}
                style={{
                  borderRadius: '8px',
                  height: '44px',
                  fontWeight: '500'
                }}
              >
                重置
              </Button>
              <Button 
                type="primary" 
                icon={<SaveOutlined />}
                onClick={handleSave}
                loading={loading}
                style={{
                  background: '#6366f1',
                  borderColor: '#6366f1',
                  borderRadius: '8px',
                  height: '44px',
                  padding: '0 24px',
                  fontWeight: '600',
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                }}
              >
                保存设置
              </Button>
            </Space>
          </div>
        </motion.div>

        <Row gutter={[24, 24]}>
          {/* 系统设置 */}
          <Col xs={24} lg={12}>
            <motion.div variants={itemVariants}>
              <CleanCard 
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <SettingOutlined style={{ color: '#6366f1' }} />
                    <span>系统设置</span>
                  </div>
                }
              >
                <SettingItem
                  icon={<DatabaseOutlined />}
                  title="系统名称"
                  description="显示在页面标题和导航栏中的系统名称"
                >
                  <Input
                    value={settings.systemName}
                    onChange={(e) => handleSettingChange('systemName', e.target.value)}
                    style={{ width: '200px' }}
                  />
                </SettingItem>

                <SettingItem
                  icon={<SettingOutlined />}
                  title="维护模式"
                  description="启用后将阻止普通用户访问系统"
                  status={settings.maintenanceMode ? 'warning' : 'enabled'}
                >
                  <Switch
                    checked={settings.maintenanceMode}
                    onChange={(checked) => handleSettingChange('maintenanceMode', checked)}
                  />
                </SettingItem>

                <SettingItem
                  icon={<DatabaseOutlined />}
                  title="自动备份"
                  description="定期自动备份系统数据"
                  status={settings.autoBackup ? 'enabled' : 'disabled'}
                >
                  <Switch
                    checked={settings.autoBackup}
                    onChange={(checked) => handleSettingChange('autoBackup', checked)}
                  />
                </SettingItem>

                <SettingItem
                  icon={<DatabaseOutlined />}
                  title="备份间隔"
                  description="自动备份的时间间隔（小时）"
                >
                  <Select
                    value={settings.backupInterval}
                    onChange={(value) => handleSettingChange('backupInterval', value)}
                    style={{ width: '120px' }}
                  >
                    <Option value="6">6小时</Option>
                    <Option value="12">12小时</Option>
                    <Option value="24">24小时</Option>
                    <Option value="72">3天</Option>
                  </Select>
                </SettingItem>
              </CleanCard>
            </motion.div>
          </Col>

          {/* 安全设置 */}
          <Col xs={24} lg={12}>
            <motion.div variants={itemVariants}>
              <CleanCard 
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <SecurityScanOutlined style={{ color: '#10b981' }} />
                    <span>安全设置</span>
                  </div>
                }
              >
                <SettingItem
                  icon={<UserOutlined />}
                  title="会话超时"
                  description="用户无操作后自动登出的时间（分钟）"
                >
                  <Select
                    value={settings.sessionTimeout}
                    onChange={(value) => handleSettingChange('sessionTimeout', value)}
                    style={{ width: '120px' }}
                  >
                    <Option value="15">15分钟</Option>
                    <Option value="30">30分钟</Option>
                    <Option value="60">1小时</Option>
                    <Option value="120">2小时</Option>
                  </Select>
                </SettingItem>

                <SettingItem
                  icon={<SecurityScanOutlined />}
                  title="密码策略"
                  description="设置用户密码的复杂度要求"
                >
                  <Select
                    value={settings.passwordPolicy}
                    onChange={(value) => handleSettingChange('passwordPolicy', value)}
                    style={{ width: '120px' }}
                  >
                    <Option value="low">简单</Option>
                    <Option value="medium">中等</Option>
                    <Option value="high">复杂</Option>
                  </Select>
                </SettingItem>

                <SettingItem
                  icon={<SecurityScanOutlined />}
                  title="双因素认证"
                  description="启用双因素认证增强账户安全性"
                  status={settings.twoFactorAuth ? 'enabled' : 'disabled'}
                >
                  <Switch
                    checked={settings.twoFactorAuth}
                    onChange={(checked) => handleSettingChange('twoFactorAuth', checked)}
                  />
                </SettingItem>

                <SettingItem
                  icon={<SecurityScanOutlined />}
                  title="登录尝试限制"
                  description="连续登录失败后锁定账户的次数"
                >
                  <Select
                    value={settings.loginAttempts}
                    onChange={(value) => handleSettingChange('loginAttempts', value)}
                    style={{ width: '120px' }}
                  >
                    <Option value="3">3次</Option>
                    <Option value="5">5次</Option>
                    <Option value="10">10次</Option>
                  </Select>
                </SettingItem>
              </CleanCard>
            </motion.div>
          </Col>

          {/* 通知设置 */}
          <Col xs={24} lg={12}>
            <motion.div variants={itemVariants}>
              <CleanCard 
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <NotificationOutlined style={{ color: '#f59e0b' }} />
                    <span>通知设置</span>
                  </div>
                }
              >
                <SettingItem
                  icon={<NotificationOutlined />}
                  title="邮件通知"
                  description="通过邮件接收系统通知"
                  status={settings.emailNotifications ? 'enabled' : 'disabled'}
                >
                  <Switch
                    checked={settings.emailNotifications}
                    onChange={(checked) => handleSettingChange('emailNotifications', checked)}
                  />
                </SettingItem>

                <SettingItem
                  icon={<NotificationOutlined />}
                  title="系统警报"
                  description="接收系统异常和错误警报"
                  status={settings.systemAlerts ? 'enabled' : 'disabled'}
                >
                  <Switch
                    checked={settings.systemAlerts}
                    onChange={(checked) => handleSettingChange('systemAlerts', checked)}
                  />
                </SettingItem>

                <SettingItem
                  icon={<NotificationOutlined />}
                  title="维护通知"
                  description="接收系统维护和更新通知"
                  status={settings.maintenanceNotifications ? 'enabled' : 'disabled'}
                >
                  <Switch
                    checked={settings.maintenanceNotifications}
                    onChange={(checked) => handleSettingChange('maintenanceNotifications', checked)}
                  />
                </SettingItem>

                <SettingItem
                  icon={<NotificationOutlined />}
                  title="报告通知"
                  description="接收定期报告生成通知"
                  status={settings.reportNotifications ? 'enabled' : 'disabled'}
                >
                  <Switch
                    checked={settings.reportNotifications}
                    onChange={(checked) => handleSettingChange('reportNotifications', checked)}
                  />
                </SettingItem>
              </CleanCard>
            </motion.div>
          </Col>

          {/* 数据设置 */}
          <Col xs={24} lg={12}>
            <motion.div variants={itemVariants}>
              <CleanCard 
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <DatabaseOutlined style={{ color: '#ef4444' }} />
                    <span>数据设置</span>
                  </div>
                }
              >
                <SettingItem
                  icon={<DatabaseOutlined />}
                  title="数据保留期"
                  description="系统数据的保留天数"
                >
                  <Select
                    value={settings.dataRetention}
                    onChange={(value) => handleSettingChange('dataRetention', value)}
                    style={{ width: '120px' }}
                  >
                    <Option value="90">90天</Option>
                    <Option value="180">180天</Option>
                    <Option value="365">1年</Option>
                    <Option value="730">2年</Option>
                  </Select>
                </SettingItem>

                <SettingItem
                  icon={<DatabaseOutlined />}
                  title="自动清理"
                  description="自动清理过期的数据和日志"
                  status={settings.autoCleanup ? 'enabled' : 'disabled'}
                >
                  <Switch
                    checked={settings.autoCleanup}
                    onChange={(checked) => handleSettingChange('autoCleanup', checked)}
                  />
                </SettingItem>

                <SettingItem
                  icon={<DatabaseOutlined />}
                  title="数据压缩"
                  description="启用数据压缩以节省存储空间"
                  status={settings.compressionEnabled ? 'enabled' : 'disabled'}
                >
                  <Switch
                    checked={settings.compressionEnabled}
                    onChange={(checked) => handleSettingChange('compressionEnabled', checked)}
                  />
                </SettingItem>

                <SettingItem
                  icon={<DatabaseOutlined />}
                  title="实时同步"
                  description="启用数据的实时同步功能"
                  status={settings.realtimeSync ? 'enabled' : 'disabled'}
                >
                  <Switch
                    checked={settings.realtimeSync}
                    onChange={(checked) => handleSettingChange('realtimeSync', checked)}
                  />
                </SettingItem>
              </CleanCard>
            </motion.div>
          </Col>
        </Row>
      </motion.div>
    </CleanAssanLayout>
  );
};

export default CleanSystemSettings;
