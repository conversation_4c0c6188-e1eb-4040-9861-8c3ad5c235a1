import React, { useMemo } from 'react';
import Base<PERSON>hart from './BaseChart';
import { generateBarChartOption, detectAnomalies, highlightAnomalies } from '../../utils/chartUtils';

const BarChart = ({
  data,
  title = '柱状图',
  barWidth = '60%',
  borderRadius = [2, 2, 0, 0],
  xAxisRotate = 0,
  yAxisFormatter,
  enableAnomalyDetection = false,
  anomalyMethod = 'zscore',
  anomalyThreshold,
  height = 400,
  loading = false,
  error = null,
  onBarClick,
  ...props
}) => {
  // 生成图表配置
  const chartOption = useMemo(() => {
    if (!data || loading || error) return null;

    const config = {
      barWidth,
      borderRadius,
      xAxisRotate,
      yAxisFormatter
    };

    let option = generateBarChartOption(data, config);

    // 异常检测和高亮
    if (enableAnomalyDetection && data.series && data.series.length > 0) {
      const firstSeriesData = data.series[0].data;
      if (Array.isArray(firstSeriesData)) {
        const anomalies = detectAnomalies(firstSeriesData, anomalyMethod, anomalyThreshold);
        if (anomalies.length > 0) {
          option = highlightAnomalies(option, anomalies);
        }
      }
    }

    return option;
  }, [data, barWidth, borderRadius, xAxisRotate, yAxisFormatter, 
      enableAnomalyDetection, anomalyMethod, anomalyThreshold, loading, error]);

  // 事件处理
  const chartEvents = useMemo(() => {
    const events = {};

    if (onBarClick) {
      events.click = (params) => {
        onBarClick(params);
      };
    }

    return events;
  }, [onBarClick]);

  return (
    <BaseChart
      title={title}
      option={chartOption}
      height={height}
      loading={loading}
      error={error}
      onEvents={chartEvents}
      {...props}
    />
  );
};

export default BarChart;
