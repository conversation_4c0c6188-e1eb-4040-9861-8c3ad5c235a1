
> production-data-visualization-frontend@0.1.0 start
> react-scripts start

(node:12184) [DEP_WEBPACK_DEV_SERVER_ON_AFTER_SETUP_MIDDLEWARE] DeprecationWarning: 'onAfterSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:12184) [DEP_WEBPACK_DEV_SERVER_ON_BEFORE_SETUP_MIDDLEWARE] DeprecationWarning: 'onBeforeSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
Starting the development server...

Compiled successfully!

You can now view production-data-visualization-frontend in the browser.

  Local:            http://localhost:3000
  On Your Network:  http://**************:3000

Note that the development build is not optimized.
To create a production build, use npm run build.

webpack compiled [1m[32msuccessfully[39m[22m
