import React from 'react';
import { Form, Input, Button, Select, DatePicker, Switch, InputNumber } from 'antd';
import { motion } from 'framer-motion';

const { Option } = Select;
const { TextArea } = Input;

// 增强版输入框组件
export const EnhancedInput = ({ 
  placeholder, 
  prefix, 
  suffix, 
  size = 'large',
  style = {},
  className = '',
  ...props 
}) => {
  const enhancedStyle = {
    height: size === 'large' ? '56px' : size === 'middle' ? '40px' : '32px',
    borderRadius: '12px',
    border: '2px solid #e2e8f0',
    background: 'rgba(255, 255, 255, 0.8)',
    backdropFilter: 'blur(10px)',
    fontSize: '16px',
    transition: 'all 0.3s ease',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
    ...style
  };

  return (
    <Input
      placeholder={placeholder}
      prefix={prefix}
      suffix={suffix}
      size={size}
      style={enhancedStyle}
      className={`enhanced-input ${className}`}
      {...props}
    />
  );
};

// 增强版密码输入框
export const EnhancedPasswordInput = ({ 
  placeholder = '请输入密码',
  prefix,
  size = 'large',
  style = {},
  className = '',
  ...props 
}) => {
  const enhancedStyle = {
    height: size === 'large' ? '56px' : size === 'middle' ? '40px' : '32px',
    borderRadius: '12px',
    border: '2px solid #e2e8f0',
    background: 'rgba(255, 255, 255, 0.8)',
    backdropFilter: 'blur(10px)',
    fontSize: '16px',
    transition: 'all 0.3s ease',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
    ...style
  };

  return (
    <Input.Password
      placeholder={placeholder}
      prefix={prefix}
      size={size}
      style={enhancedStyle}
      className={`enhanced-input ${className}`}
      {...props}
    />
  );
};

// 增强版选择框
export const EnhancedSelect = ({ 
  placeholder,
  options = [],
  size = 'large',
  style = {},
  className = '',
  children,
  ...props 
}) => {
  const enhancedStyle = {
    height: size === 'large' ? '56px' : size === 'middle' ? '40px' : '32px',
    borderRadius: '12px',
    ...style
  };

  return (
    <Select
      placeholder={placeholder}
      size={size}
      style={enhancedStyle}
      className={`enhanced-select ${className}`}
      dropdownStyle={{
        borderRadius: '12px',
        boxShadow: '0 16px 32px rgba(0, 0, 0, 0.1)',
        border: '1px solid rgba(255, 255, 255, 0.8)',
        backdropFilter: 'blur(20px)'
      }}
      {...props}
    >
      {children || options.map(option => (
        <Option key={option.value} value={option.value}>
          {option.label}
        </Option>
      ))}
    </Select>
  );
};

// 增强版文本域
export const EnhancedTextArea = ({ 
  placeholder,
  rows = 4,
  style = {},
  className = '',
  ...props 
}) => {
  const enhancedStyle = {
    borderRadius: '12px',
    border: '2px solid #e2e8f0',
    background: 'rgba(255, 255, 255, 0.8)',
    backdropFilter: 'blur(10px)',
    fontSize: '16px',
    transition: 'all 0.3s ease',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
    ...style
  };

  return (
    <TextArea
      placeholder={placeholder}
      rows={rows}
      style={enhancedStyle}
      className={`enhanced-textarea ${className}`}
      {...props}
    />
  );
};

// 增强版按钮
export const EnhancedButton = ({ 
  type = 'primary',
  size = 'large',
  children,
  icon,
  loading = false,
  style = {},
  className = '',
  variant = 'default', // default, gradient, outline
  ...props 
}) => {
  const getButtonStyle = () => {
    const baseStyle = {
      height: size === 'large' ? '56px' : size === 'middle' ? '40px' : '32px',
      borderRadius: '12px',
      fontWeight: '600',
      fontSize: size === 'large' ? '16px' : '14px',
      transition: 'all 0.3s ease',
      border: 'none',
      ...style
    };

    if (type === 'primary') {
      if (variant === 'gradient') {
        return {
          ...baseStyle,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          boxShadow: '0 4px 20px rgba(102, 126, 234, 0.3)',
        };
      } else if (variant === 'outline') {
        return {
          ...baseStyle,
          background: 'transparent',
          border: '2px solid #667eea',
          color: '#667eea',
        };
      } else {
        return {
          ...baseStyle,
          background: '#667eea',
          color: 'white',
          boxShadow: '0 4px 16px rgba(102, 126, 234, 0.3)',
        };
      }
    }

    return {
      ...baseStyle,
      background: 'rgba(255, 255, 255, 0.8)',
      border: '2px solid #e2e8f0',
      color: '#4a5568',
    };
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -2 }}
      whileTap={{ scale: 0.98 }}
    >
      <Button
        type={type}
        size={size}
        icon={icon}
        loading={loading}
        style={getButtonStyle()}
        className={`enhanced-button ${className}`}
        {...props}
      >
        {children}
      </Button>
    </motion.div>
  );
};

// 增强版表单项
export const EnhancedFormItem = ({ 
  label,
  name,
  rules = [],
  children,
  style = {},
  className = '',
  ...props 
}) => {
  const enhancedStyle = {
    marginBottom: '24px',
    ...style
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6 }}
    >
      <Form.Item
        label={label}
        name={name}
        rules={rules}
        style={enhancedStyle}
        className={`enhanced-form-item ${className}`}
        {...props}
      >
        {children}
      </Form.Item>
    </motion.div>
  );
};

// 增强版表单
export const EnhancedForm = ({ 
  children,
  onFinish,
  form,
  layout = 'vertical',
  style = {},
  className = '',
  ...props 
}) => {
  const enhancedStyle = {
    ...style
  };

  return (
    <Form
      form={form}
      layout={layout}
      onFinish={onFinish}
      style={enhancedStyle}
      className={`enhanced-form ${className}`}
      {...props}
    >
      {children}

      <style jsx="true">{`
        .enhanced-form .ant-form-item-label > label {
          color: #374151;
          font-weight: 600;
          font-size: 14px;
        }

        .enhanced-input:hover,
        .enhanced-input:focus {
          border-color: #667eea !important;
          box-shadow: 
            0 0 0 3px rgba(102, 126, 234, 0.15) !important,
            0 4px 20px rgba(102, 126, 234, 0.1) !important;
          background: rgba(255, 255, 255, 0.95) !important;
          transform: translateY(-1px);
        }

        .enhanced-select .ant-select-selector {
          border: 2px solid #e2e8f0 !important;
          background: rgba(255, 255, 255, 0.8) !important;
          backdrop-filter: blur(10px) !important;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
          transition: all 0.3s ease !important;
        }

        .enhanced-select:hover .ant-select-selector,
        .enhanced-select.ant-select-focused .ant-select-selector {
          border-color: #667eea !important;
          box-shadow: 
            0 0 0 3px rgba(102, 126, 234, 0.15) !important,
            0 4px 20px rgba(102, 126, 234, 0.1) !important;
          background: rgba(255, 255, 255, 0.95) !important;
        }

        .enhanced-textarea:hover,
        .enhanced-textarea:focus {
          border-color: #667eea !important;
          box-shadow: 
            0 0 0 3px rgba(102, 126, 234, 0.15) !important,
            0 4px 20px rgba(102, 126, 234, 0.1) !important;
          background: rgba(255, 255, 255, 0.95) !important;
        }

        .enhanced-button:hover {
          transform: translateY(-2px) scale(1.02) !important;
          box-shadow: 
            0 8px 25px rgba(102, 126, 234, 0.4) !important;
        }

        .enhanced-button:active {
          transform: translateY(0) scale(0.98) !important;
        }

        .enhanced-form-item .ant-form-item-explain-error {
          color: #ef4444;
          font-size: 13px;
          margin-top: 4px;
        }

        .enhanced-form-item .ant-form-item-has-error .enhanced-input,
        .enhanced-form-item .ant-form-item-has-error .enhanced-select .ant-select-selector,
        .enhanced-form-item .ant-form-item-has-error .enhanced-textarea {
          border-color: #ef4444 !important;
          box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.15) !important;
        }
      `}</style>
    </Form>
  );
};

// 默认导出所有组件
export default {
  EnhancedInput,
  EnhancedPasswordInput,
  EnhancedSelect,
  EnhancedTextArea,
  EnhancedButton,
  EnhancedFormItem,
  EnhancedForm
};
