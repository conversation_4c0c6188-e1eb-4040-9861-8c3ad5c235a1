# PowerShell IP自动检测脚本
param(
    [switch]$StartService = $false
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "  IP地址自动检测和配置工具 v2.0" -ForegroundColor Green
Write-Host "  Auto IP Detection and Configuration" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "[INFO] 正在检测当前服务器的网络配置..." -ForegroundColor Cyan

# 获取本机IP地址
try {
    $networkAdapters = Get-NetIPAddress -AddressFamily IPv4 | Where-Object { 
        $_.IPAddress -ne "127.0.0.1" -and 
        $_.PrefixOrigin -eq "Dhcp" -or $_.PrefixOrigin -eq "Manual" 
    }
    
    $localIP = $networkAdapters | Select-Object -First 1 | Select-Object -ExpandProperty IPAddress
    
    if (-not $localIP) {
        # 备用方法
        $ipConfig = ipconfig | Select-String "IPv4" | Select-Object -First 1
        if ($ipConfig) {
            $localIP = ($ipConfig -split ":")[1].Trim()
        }
    }
    
    if (-not $localIP) {
        throw "无法检测到本机IP地址"
    }
    
    Write-Host "[SUCCESS] 检测到本机IP地址: $localIP" -ForegroundColor Green
    
} catch {
    Write-Host "[ERROR] 无法检测到本机IP地址: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""
Write-Host "[INFO] 网络接口详细信息:" -ForegroundColor Cyan
Get-NetIPAddress -AddressFamily IPv4 | Where-Object { $_.IPAddress -ne "127.0.0.1" } | 
    Select-Object IPAddress, InterfaceAlias, PrefixOrigin | Format-Table -AutoSize

Write-Host "[INFO] 正在生成自适应配置文件..." -ForegroundColor Cyan

# 更新 .env 文件
$envContent = @"
HOST=0.0.0.0
PORT=3000
BROWSER=none
# REACT_APP_API_URL=auto  # 让系统自动检测
CHOKIDAR_USEPOLLING=true
REACT_APP_AUTO_DETECT_API=true
REACT_APP_DETECTED_IP=$localIP
"@

$envContent | Out-File -FilePath "..\..\frontend\.env" -Encoding UTF8
Write-Host "[SUCCESS] .env 文件已更新（自动检测模式）" -ForegroundColor Green

# 更新 .env.production 文件
$envProductionContent = @"
# 生产环境配置 - 自动生成
# 生成时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
# 检测到的IP: $localIP

REACT_APP_API_URL=http://$localIP`:5000
REACT_APP_ENVIRONMENT=production
REACT_APP_VERSION=1.0.0
REACT_APP_ENABLE_DEBUG=false
REACT_APP_LOG_LEVEL=info
REACT_APP_CACHE_ENABLED=true
REACT_APP_CACHE_EXPIRE_TIME=600000
REACT_APP_MAX_FILE_SIZE=104857600
REACT_APP_SUPPORTED_FILE_TYPES=.txt,.csv,.xls,.xlsx
"@

$envProductionContent | Out-File -FilePath "..\..\frontend\.env.production" -Encoding UTF8
Write-Host "[SUCCESS] .env.production 文件已更新" -ForegroundColor Green

Write-Host ""
Write-Host "[INFO] 配置文件内容预览:" -ForegroundColor Cyan
Write-Host ""
Write-Host "=== .env 文件 ===" -ForegroundColor Yellow
Get-Content "..\..\frontend\.env"
Write-Host ""
Write-Host "=== .env.production 文件 ===" -ForegroundColor Yellow
Get-Content "..\..\frontend\.env.production"
Write-Host ""

# 生成启动脚本
Write-Host "[INFO] 生成自适应启动脚本..." -ForegroundColor Cyan

$startupScript = @"
@echo off
title 自适应前端服务器
echo 正在启动自适应前端服务器...
echo 检测到的IP地址: $localIP
echo 前端访问地址: http://$localIP`:3000
echo API访问地址: http://$localIP`:5000
echo.
cd frontend
set HOST=0.0.0.0
set PORT=3000
set REACT_APP_API_URL=http://$localIP`:5000
npm start
"@

$startupScript | Out-File -FilePath "..\..\start-adaptive.bat" -Encoding ASCII
Write-Host "[SUCCESS] 自适应启动脚本已生成: start-adaptive.bat" -ForegroundColor Green

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "  配置完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 自适应网络配置:" -ForegroundColor Cyan
Write-Host "  本机IP: $localIP" -ForegroundColor White
Write-Host "  前端地址: http://$localIP`:3000" -ForegroundColor White
Write-Host "  API地址: http://$localIP`:5000" -ForegroundColor White
Write-Host ""
Write-Host "📋 使用方法:" -ForegroundColor Cyan
Write-Host "  1. 运行 start-adaptive.bat 启动前端服务" -ForegroundColor White
Write-Host "  2. 或者手动启动: cd frontend && npm start" -ForegroundColor White
Write-Host "  3. 系统会自动使用检测到的IP地址" -ForegroundColor White
Write-Host ""
Write-Host "🔄 IP地址变化时:" -ForegroundColor Cyan
Write-Host "  重新运行此脚本即可自动更新配置" -ForegroundColor White
Write-Host ""
Write-Host "✅ 现在其他电脑可以通过以下地址访问:" -ForegroundColor Green
Write-Host "  http://$localIP`:3000" -ForegroundColor Yellow
Write-Host ""

# 询问是否立即启动服务
if (-not $StartService) {
    $startNow = Read-Host "是否立即启动前端服务? (Y/N)"
    if ($startNow -eq "Y" -or $startNow -eq "y") {
        $StartService = $true
    }
}

if ($StartService) {
    Write-Host ""
    Write-Host "[INFO] 正在启动前端服务..." -ForegroundColor Cyan
    Start-Process -FilePath "..\..\start-adaptive.bat" -WindowStyle Normal
    Write-Host "[SUCCESS] 前端服务器启动命令已执行" -ForegroundColor Green
}

Write-Host ""
Read-Host "按任意键退出"
