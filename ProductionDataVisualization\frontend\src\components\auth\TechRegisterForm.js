import React, { useState, useRef, useEffect } from 'react';
import { Form, Input, Button, Alert, Typography } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, UserAddOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { motion, useAnimation } from 'framer-motion';
import authService from '../../services/authService';
import { useNavigate, Link } from 'react-router-dom';

const { Title, Text } = Typography;

const TechRegisterForm = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const formRef = useRef(null);
  const controls = useAnimation();

  // 鼠标跟踪效果
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e) => {
      if (formRef.current) {
        const rect = formRef.current.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    };

    const formElement = formRef.current;
    if (formElement) {
      formElement.addEventListener('mousemove', handleMouseMove);
      return () => formElement.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  const onFinish = async (values) => {
    if (values.password !== values.confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }
    
    setLoading(true);
    setError('');
    setSuccess('');
    
    try {
      await authService.register(
        values.username, 
        values.email, 
        values.password, 
        values.fullName
      );
      
      setSuccess('注册成功！即将跳转到登录页面...');
      
      // 成功动画
      await controls.start({
        scale: [1, 1.05, 1],
        opacity: [1, 0.8, 0],
        transition: { duration: 0.6 }
      });
      
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (err) {
      setError(err.message || '注册失败，请稍后再试');
      
      // 错误震动动画
      controls.start({
        x: [0, -10, 10, -10, 10, 0],
        transition: { duration: 0.5 }
      });
    } finally {
      setLoading(false);
    }
  };

  // 样式定义
  const containerStyle = {
    width: '100%',
    maxWidth: '420px',
    padding: '40px',
    borderRadius: '20px',
    background: `
      linear-gradient(145deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(248, 250, 252, 0.8) 100%
      )
    `,
    backdropFilter: 'blur(20px)',
    border: '1px solid rgba(255, 255, 255, 0.6)',
    boxShadow: `
      0 20px 40px rgba(0, 0, 0, 0.1),
      0 8px 16px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      0 0 0 1px rgba(255, 255, 255, 0.2)
    `,
    position: 'relative',
    overflow: 'hidden'
  };

  const headerStyle = {
    textAlign: 'center',
    marginBottom: '32px',
    position: 'relative'
  };

  const logoStyle = {
    width: '80px',
    height: '80px',
    borderRadius: '50%',
    background: `
      linear-gradient(135deg,
        #52c41a 0%,
        #1890ff 100%
      )
    `,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    margin: '0 auto 20px',
    boxShadow: `
      0 8px 24px rgba(82, 196, 26, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3)
    `,
    position: 'relative',
    overflow: 'hidden'
  };

  const inputStyle = {
    height: '50px',
    fontSize: '16px',
    borderRadius: '12px',
    border: '1px solid rgba(203, 213, 225, 0.6)',
    background: 'rgba(255, 255, 255, 0.7)',
    backdropFilter: 'blur(10px)',
    color: '#334155',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
    transition: 'all 0.3s ease'
  };

  const buttonStyle = {
    height: '50px',
    fontSize: '16px',
    fontWeight: '600',
    borderRadius: '12px',
    background: `
      linear-gradient(135deg,
        #52c41a 0%,
        #1890ff 100%
      )
    `,
    border: 'none',
    boxShadow: `
      0 4px 15px rgba(82, 196, 26, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2)
    `,
    position: 'relative',
    overflow: 'hidden'
  };

  return (
    <motion.div
      ref={formRef}
      animate={controls}
      style={containerStyle}
      className="tech-register-form"
    >
      {/* 鼠标跟随光效 */}
      <div
        style={{
          position: 'absolute',
          top: mousePosition.y - 100,
          left: mousePosition.x - 100,
          width: '200px',
          height: '200px',
          background: 'radial-gradient(circle, rgba(82, 196, 26, 0.1) 0%, transparent 70%)',
          borderRadius: '50%',
          pointerEvents: 'none',
          transition: 'all 0.3s ease'
        }}
      />

      {/* 表单头部 */}
      <div style={headerStyle}>
        <motion.div
          style={logoStyle}
          whileHover={{ scale: 1.1, rotate: 5 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          {/* Logo内部光效 */}
          <div style={{
            position: 'absolute',
            top: '20%',
            left: '20%',
            width: '60%',
            height: '60%',
            background: 'radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%)',
            borderRadius: '50%'
          }} />
          <UserAddOutlined style={{ fontSize: '36px', color: '#fff', zIndex: 2 }} />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
        >
          <Title level={2} style={{ 
            color: '#fff', 
            fontSize: '28px', 
            fontWeight: '300',
            marginBottom: '8px',
            textShadow: '0 0 20px rgba(255, 255, 255, 0.5)'
          }}>
            创建账户
          </Title>
          <Text style={{ 
            color: 'rgba(255, 255, 255, 0.7)', 
            fontSize: '16px',
            textShadow: '0 0 10px rgba(255, 255, 255, 0.3)'
          }}>
            加入我们，开始您的数据之旅
          </Text>
        </motion.div>
      </div>

      {/* 错误提示 */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          style={{ marginBottom: '24px' }}
        >
          <Alert 
            message={error} 
            type="error" 
            showIcon 
            style={{
              borderRadius: '12px',
              border: '1px solid rgba(255, 107, 107, 0.3)',
              background: 'rgba(255, 107, 107, 0.1)',
              backdropFilter: 'blur(10px)',
              color: '#fff'
            }}
          />
        </motion.div>
      )}

      {/* 成功提示 */}
      {success && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          style={{ marginBottom: '24px' }}
        >
          <Alert 
            message={success} 
            type="success" 
            showIcon 
            style={{
              borderRadius: '12px',
              border: '1px solid rgba(82, 196, 26, 0.3)',
              background: 'rgba(82, 196, 26, 0.1)',
              backdropFilter: 'blur(10px)',
              color: '#fff'
            }}
          />
        </motion.div>
      )}

      {/* 注册表单 */}
      <Form
        name="register"
        form={form}
        onFinish={onFinish}
        size="large"
        layout="vertical"
      >
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名!' },
              { min: 3, message: '用户名至少3个字符' },
              { max: 20, message: '用户名最多20个字符' }
            ]}
            style={{ marginBottom: '16px' }}
          >
            <Input 
              prefix={<UserOutlined style={{ color: 'rgba(255, 255, 255, 0.6)' }} />} 
              placeholder="用户名" 
              style={inputStyle}
              className="tech-input"
            />
          </Form.Item>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          <Form.Item
            name="email"
            rules={[
              { required: true, message: '请输入邮箱!' },
              { type: 'email', message: '请输入有效的邮箱地址!' }
            ]}
            style={{ marginBottom: '16px' }}
          >
            <Input 
              prefix={<MailOutlined style={{ color: 'rgba(255, 255, 255, 0.6)' }} />} 
              placeholder="邮箱" 
              style={inputStyle}
              className="tech-input"
            />
          </Form.Item>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5, duration: 0.6 }}
        >
          <Form.Item
            name="fullName"
            rules={[{ required: true, message: '请输入姓名!' }]}
            style={{ marginBottom: '16px' }}
          >
            <Input 
              prefix={<UserAddOutlined style={{ color: 'rgba(255, 255, 255, 0.6)' }} />} 
              placeholder="姓名" 
              style={inputStyle}
              className="tech-input"
            />
          </Form.Item>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6, duration: 0.6 }}
        >
          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码!' },
              { min: 6, message: '密码至少6个字符' }
            ]}
            style={{ marginBottom: '16px' }}
          >
            <Input.Password
              prefix={<LockOutlined style={{ color: 'rgba(255, 255, 255, 0.6)' }} />}
              placeholder="密码"
              style={inputStyle}
              className="tech-input"
            />
          </Form.Item>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.7, duration: 0.6 }}
        >
          <Form.Item
            name="confirmPassword"
            rules={[
              { required: true, message: '请确认密码!' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致!'));
                },
              }),
            ]}
            style={{ marginBottom: '24px' }}
          >
            <Input.Password
              prefix={<LockOutlined style={{ color: 'rgba(255, 255, 255, 0.6)' }} />}
              placeholder="确认密码"
              style={inputStyle}
              className="tech-input"
            />
          </Form.Item>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.8, duration: 0.6 }}
        >
          <Button
            type="primary"
            htmlType="submit"
            block
            loading={loading}
            style={buttonStyle}
            className="tech-button"
            icon={<ArrowRightOutlined />}
          >
            <span style={{ position: 'relative', zIndex: 2 }}>注册</span>
          </Button>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.0, duration: 0.6 }}
          style={{ 
            textAlign: 'center', 
            marginTop: '24px',
            color: 'rgba(255, 255, 255, 0.7)'
          }}
        >
          <Text style={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            已有账号？
            <Link 
              to="/login" 
              style={{ 
                color: 'rgba(82, 196, 26, 0.9)',
                marginLeft: '8px',
                textDecoration: 'none',
                textShadow: '0 0 10px rgba(82, 196, 26, 0.5)'
              }}
            >
              立即登录
            </Link>
          </Text>
        </motion.div>
      </Form>

      {/* CSS样式 */}
      <style jsx="true">{`
        .tech-input:focus,
        .tech-input:hover {
          border-color: rgba(82, 196, 26, 0.6) !important;
          box-shadow: 0 0 20px rgba(82, 196, 26, 0.3) !important;
          background: rgba(255, 255, 255, 0.1) !important;
        }
        
        .tech-button:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(82, 196, 26, 0.6) !important;
        }
        
        .tech-button:active {
          transform: translateY(0);
        }
        
        .tech-register-form::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
          animation: shimmer 3s infinite;
          pointer-events: none;
        }
        
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </motion.div>
  );
};

export default TechRegisterForm;
