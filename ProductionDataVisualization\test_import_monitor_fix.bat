@echo off
title 测试导入监控显示修复

echo ========================================
echo   测试导入监控显示修复
echo ========================================
echo.

echo [INFO] 修复内容:
echo 1. 添加了从后端加载导入任务列表的功能
echo 2. 组件加载时自动获取已有的导入任务
echo 3. 导入成功后自动刷新任务列表
echo 4. 添加了手动刷新按钮
echo 5. 修复了数据格式转换问题
echo.

echo [INFO] 后端已启动在 http://localhost:5000
echo.

echo [INFO] 启动前端...
cd frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 20 /nobreak

echo.
echo ========================================
echo   测试指南
echo ========================================
echo.
echo 1. 访问: http://localhost:3000/data-import
echo 2. 点击"导入监控"步骤
echo 3. 观察修复效果:
echo    - 导入任务列表显示已有的导入记录
echo    - 统计数字正确显示（不再是0）
echo    - 可以看到之前导入的文件信息
echo.
echo 4. 测试刷新功能:
echo    - 点击"刷新任务列表"按钮
echo    - 验证数据是否正确更新
echo.
echo 5. 测试新导入:
echo    - 上传新文件并完成导入
echo    - 验证任务列表是否自动更新
echo    - 检查进度和状态是否正确显示
echo.
echo 6. 检查浏览器控制台:
echo    - 查看"加载的导入任务:"日志
echo    - 确认API调用成功
echo    - 验证数据格式转换正确
echo.

pause
