[2025-07-16 11:04:33 INF] Now listening on: http://localhost:5000 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-16 11:04:33 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-16 11:04:33 INF] Hosting environment: Production {"SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-16 11:04:33 INF] Content root path: C:\Users\<USER>\Documents\Trae_Files\Report\ProductionDataVisualization\backend\src\API {"SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-16 11:04:54 INF] HTTP OPTIONS /api/auth/me responded 204 in 16.0312 ms {"RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE42M4SDBPP:00000001","ConnectionId":"0HNE42M4SDBPP","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-16 11:04:54 INF] HTTP OPTIONS /api/auth/me responded 204 in 16.0523 ms {"RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE42M4SDBPO:00000001","ConnectionId":"0HNE42M4SDBPO","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-16 11:04:54 WRN] HTTP GET /api/auth/me responded 404 in 312.3969 ms {"RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE42M4SDBPP:00000002","ConnectionId":"0HNE42M4SDBPP","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-16 11:04:54 WRN] HTTP GET /api/auth/me responded 404 in 7.5344 ms {"RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE42M4SDBPP:00000003","ConnectionId":"0HNE42M4SDBPP","Application":"ProductionDataVisualization","Environment":"Production"}
