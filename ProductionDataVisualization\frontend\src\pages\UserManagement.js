import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Modal, 
  message, 
  Typography, 
  Row, 
  Col, 
  Divider 
} from 'antd';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import MainLayout from '../components/layout/MainLayout';
import UserTable from '../components/user/UserTable';
import UserForm from '../components/user/UserForm';
import userService from '../services/userService';
import roleService from '../services/roleService';
import { useNavigate } from 'react-router-dom';
import { checkPermission } from '../utils/auth';

const { Title } = Typography;

// 用户管理页面样式
const USER_MANAGEMENT_STYLES = {
  title: {
    fontSize: '28px',
    fontWeight: 600,
    color: '#262626',
    lineHeight: 1.2,
    letterSpacing: '-0.3px',
    margin: '0 0 20px 0'
  },
  button: {
    fontSize: '14px',
    fontWeight: 500,
    letterSpacing: '0.3px'
  },
  divider: {
    margin: '16px 0'
  },
  modalTitle: {
    fontSize: '20px',
    fontWeight: 600,
    color: '#262626',
    lineHeight: 1.2
  }
};

const UserManagement = () => {
  const navigate = useNavigate();
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [currentUser, setCurrentUser] = useState(null);
  const [isEdit, setIsEdit] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条记录`
  });

  // 检查用户是否有权限，并加载数据
  useEffect(() => {
    console.log("UserManagement - 组件挂载，检查权限并加载数据");
    
    // 检查用户是否有查看用户的权限
    const hasViewUsersPermission = checkPermission('ViewUsers');
    console.log("UserManagement - 权限检查结果:", hasViewUsersPermission);
    
    if (!hasViewUsersPermission) {
      console.log("UserManagement - 用户没有查看用户的权限，重定向到首页");
      message.error('您没有权限访问此页面');
      navigate('/');
      return;
    }
    
    // 加载数据
    fetchUsers();
    fetchRoles();
  }, [navigate]);

  // 加载用户数据
  const fetchUsers = async (page = 1, pageSize = 10) => {
    try {
      console.log("UserManagement - 开始加载用户数据");
      setLoading(true);
      
      // 始终使用模拟数据，避免API请求
      if (true || process.env.NODE_ENV === 'development') {
        console.log("UserManagement - 使用模拟用户数据");
        // 模拟数据
        const mockUsers = [
          { 
            id: '1', 
            username: 'admin', 
            email: '<EMAIL>', 
            fullName: '管理员',
            roles: ['Admin'],
            isActive: true,
            createdAt: '2023-01-01T00:00:00Z'
          },
          { 
            id: '2', 
            username: 'manager', 
            email: '<EMAIL>', 
            fullName: '经理',
            roles: ['Manager'],
            isActive: true,
            createdAt: '2023-01-02T00:00:00Z'
          },
          { 
            id: '3', 
            username: 'user', 
            email: '<EMAIL>', 
            fullName: '普通用户',
            roles: ['User'],
            isActive: true,
            createdAt: '2023-01-03T00:00:00Z'
          }
        ];
        
        setUsers(mockUsers);
        setPagination({
          ...pagination,
          current: page,
          pageSize: pageSize,
          total: mockUsers.length
        });
        
        setTimeout(() => {
          setLoading(false);
        }, 500);
        
        return;
      }
      
      const data = await userService.getAllUsers(page, pageSize);
      setUsers(data.items);
      setPagination({
        ...pagination,
        current: page,
        pageSize: pageSize,
        total: data.totalCount
      });
    } catch (error) {
      console.error("获取用户列表失败:", error);
      message.error('获取用户列表失败：' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 加载角色数据
  const fetchRoles = async () => {
    try {
      console.log("UserManagement - 开始加载角色数据");
      
      // 始终使用模拟数据，避免API请求
      if (true || process.env.NODE_ENV === 'development') {
        console.log("UserManagement - 使用模拟角色数据");
        // 模拟角色数据
        const mockRoles = [
          { id: '1', name: 'Admin', description: '管理员' },
          { id: '2', name: 'Manager', description: '经理' },
          { id: '3', name: 'User', description: '普通用户' }
        ];
        
        setRoles(mockRoles);
        return;
      }
      
      const data = await roleService.getAllRoles();
      setRoles(data);
    } catch (error) {
      console.error("获取角色列表失败:", error);
      message.error('获取角色列表失败：' + (error.message || '未知错误'));
    }
  };

  // 表格变化处理
  const handleTableChange = (pagination) => {
    fetchUsers(pagination.current, pagination.pageSize);
  };

  // 添加用户
  const handleAddUser = () => {
    setModalTitle('添加用户');
    setCurrentUser(null);
    setIsEdit(false);
    setModalVisible(true);
  };

  // 编辑用户
  const handleEditUser = (user) => {
    setModalTitle('编辑用户');
    setCurrentUser(user);
    setIsEdit(true);
    setModalVisible(true);
  };

  // 删除用户
  const handleDeleteUser = async (userId) => {
    try {
      if (process.env.NODE_ENV === 'development') {
        // 模拟删除成功
        message.success('用户删除成功');
        // 从当前列表中移除该用户
        setUsers(users.filter(user => user.id !== userId));
        return;
      }
      
      await userService.deleteUser(userId);
      message.success('用户删除成功');
      fetchUsers(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error('删除用户失败：' + (error.message || '未知错误'));
    }
  };

  // 分配角色
  const handleAssignRoles = (user) => {
    setModalTitle('分配角色');
    setCurrentUser(user);
    // 这里可以实现分配角色的逻辑
  };

  // 提交表单
  const handleFormSubmit = async (values) => {
    try {
      if (process.env.NODE_ENV === 'development') {
        // 模拟成功
        message.success(isEdit ? '用户更新成功' : '用户创建成功');
        setModalVisible(false);
        
        if (isEdit) {
          // 更新当前列表中的用户
          setUsers(users.map(user => 
            user.id === currentUser.id ? {...user, ...values} : user
          ));
        } else {
          // 添加新用户到列表
          const newUser = {
            id: Date.now().toString(),
            ...values,
            createdAt: new Date().toISOString(),
            isActive: true
          };
          setUsers([...users, newUser]);
        }
        
        return;
      }
      
      if (isEdit) {
        await userService.updateUser(currentUser.id, values);
        message.success('用户更新成功');
      } else {
        await userService.createUser(values);
        message.success('用户创建成功');
      }
      setModalVisible(false);
      fetchUsers(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error((isEdit ? '更新' : '创建') + '用户失败：' + (error.message || '未知错误'));
    }
  };

  // 检查用户是否有特定权限
  const hasPermission = (permission) => {
    return checkPermission(permission);
  };

  return (
    <MainLayout>
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={3} style={USER_MANAGEMENT_STYLES.title}>用户管理</Title>
          </Col>
          <Col>
            <Space>
              {hasPermission('CreateUser') && (
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />} 
                  onClick={handleAddUser}
                  style={USER_MANAGEMENT_STYLES.button}
                >
                  添加用户
                </Button>
              )}
              <Button 
                icon={<ReloadOutlined />} 
                onClick={() => fetchUsers(pagination.current, pagination.pageSize)}
                style={USER_MANAGEMENT_STYLES.button}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
        
        <Divider style={USER_MANAGEMENT_STYLES.divider} />
        
        <UserTable 
          users={users}
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          onEdit={handleEditUser}
          onDelete={handleDeleteUser}
          onAssignRoles={handleAssignRoles}
          checkPermission={hasPermission}
        />
      </Card>
      
      <Modal
        title={<span style={USER_MANAGEMENT_STYLES.modalTitle}>{modalTitle}</span>}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        destroyOnClose
      >
        <UserForm 
          user={currentUser}
          roles={roles}
          onFinish={handleFormSubmit}
          loading={loading}
          isEdit={isEdit}
        />
      </Modal>
    </MainLayout>
  );
};

export default UserManagement; 