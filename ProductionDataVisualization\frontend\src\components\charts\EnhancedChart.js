import React from 'react';
import { motion } from 'framer-motion';
import EnhancedCard from '../common/EnhancedCard';

// 增强版图表容器
export const EnhancedChartContainer = ({ 
  title,
  children,
  height = 400,
  loading = false,
  style = {},
  className = '',
  ...props 
}) => {
  return (
    <EnhancedCard
      title={title}
      style={{ height: height + 100, ...style }}
      className={`enhanced-chart-container ${className}`}
      {...props}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6 }}
        style={{ 
          height: height,
          position: 'relative',
          background: 'linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8))',
          borderRadius: '16px',
          padding: '20px',
          border: '1px solid rgba(255, 255, 255, 0.6)',
          boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.8)'
        }}
      >
        {loading ? (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            background: 'linear-gradient(45deg, #667eea, #764ba2)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontSize: '18px',
            fontWeight: '600'
          }}>
            加载中...
          </div>
        ) : (
          children
        )}
        
        {/* 图表装饰效果 */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'linear-gradient(45deg, transparent 30%, rgba(102, 126, 234, 0.05) 50%, transparent 70%)',
          animation: 'chartShimmer 3s infinite',
          pointerEvents: 'none',
          borderRadius: '16px'
        }} />
      </motion.div>

      <style jsx="true">{`
        @keyframes chartShimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }

        .enhanced-chart-container .ant-card-body {
          padding: 24px;
        }

        .enhanced-chart-container .ant-card-head-title {
          font-size: 18px;
          font-weight: 600;
          background: linear-gradient(135deg, #667eea, #764ba2);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      `}</style>
    </EnhancedCard>
  );
};

// 增强版统计卡片
export const EnhancedStatCard = ({ 
  title,
  value,
  suffix = '',
  prefix,
  trend,
  icon,
  color = '#667eea',
  style = {},
  className = '',
  ...props 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      whileHover={{ y: -5, scale: 1.02 }}
      className={`enhanced-stat-card ${className}`}
      style={style}
    >
      <EnhancedCard hoverable {...props}>
        <div style={{ position: 'relative' }}>
          {/* 背景装饰 */}
          <div style={{
            position: 'absolute',
            top: -10,
            right: -10,
            width: '80px',
            height: '80px',
            borderRadius: '50%',
            background: `linear-gradient(135deg, ${color}20, ${color}10)`,
            zIndex: 0
          }} />
          
          <div style={{ position: 'relative', zIndex: 1 }}>
            {/* 图标区域 */}
            {icon && (
              <div style={{
                width: '56px',
                height: '56px',
                borderRadius: '14px',
                background: `linear-gradient(135deg, ${color}, ${color}dd)`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '16px',
                fontSize: '24px',
                color: 'white',
                boxShadow: `0 8px 24px ${color}40`
              }}>
                {icon}
              </div>
            )}

            {/* 标题 */}
            <div style={{
              fontSize: '14px',
              color: '#64748b',
              marginBottom: '8px',
              fontWeight: '500'
            }}>
              {title}
            </div>

            {/* 数值 */}
            <div style={{
              fontSize: '32px',
              fontWeight: '700',
              color: '#1a202c',
              marginBottom: '8px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              {prefix}
              <span style={{ 
                background: `linear-gradient(135deg, ${color}, ${color}dd)`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>
                {value}
              </span>
              <span style={{ fontSize: '18px', color: '#64748b' }}>
                {suffix}
              </span>
            </div>

            {/* 趋势 */}
            {trend && (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
                fontSize: '14px',
                fontWeight: '500',
                color: trend.isUp ? '#22c55e' : '#ef4444'
              }}>
                <span>{trend.isUp ? '↗' : '↘'}</span>
                <span>{trend.value}%</span>
                <span style={{ color: '#64748b', marginLeft: '4px' }}>
                  {trend.period || '较上月'}
                </span>
              </div>
            )}
          </div>
        </div>
      </EnhancedCard>
    </motion.div>
  );
};

// 增强版进度卡片
export const EnhancedProgressCard = ({ 
  title,
  percentage,
  color = '#667eea',
  showPercentage = true,
  height = 8,
  style = {},
  className = '',
  ...props 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.6 }}
      className={`enhanced-progress-card ${className}`}
      style={style}
    >
      <div style={{ marginBottom: '12px' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '8px'
        }}>
          <span style={{
            fontSize: '16px',
            fontWeight: '500',
            color: '#1a202c'
          }}>
            {title}
          </span>
          {showPercentage && (
            <span style={{
              fontSize: '16px',
              fontWeight: '600',
              background: `linear-gradient(135deg, ${color}, ${color}dd)`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              {percentage}%
            </span>
          )}
        </div>
        
        {/* 进度条背景 */}
        <div style={{
          width: '100%',
          height: `${height}px`,
          borderRadius: `${height / 2}px`,
          background: 'rgba(226, 232, 240, 0.8)',
          overflow: 'hidden',
          position: 'relative'
        }}>
          {/* 进度条 */}
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${percentage}%` }}
            transition={{ duration: 1.2, ease: "easeOut" }}
            style={{
              height: '100%',
              background: `linear-gradient(90deg, ${color}, ${color}dd)`,
              borderRadius: `${height / 2}px`,
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            {/* 进度条光效 */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent)',
              animation: 'progressShine 2s infinite'
            }} />
          </motion.div>
        </div>
      </div>

      <style jsx="true">{`
        @keyframes progressShine {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </motion.div>
  );
};

// 增强版数据展示网格
export const EnhancedDataGrid = ({ 
  data = [],
  columns = 3,
  gap = 16,
  style = {},
  className = '',
  children,
  ...props 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6, staggerChildren: 0.1 }}
      style={{
        display: 'grid',
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap: `${gap}px`,
        ...style
      }}
      className={`enhanced-data-grid ${className}`}
      {...props}
    >
      {children || data.map((item, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1, duration: 0.6 }}
        >
          {item}
        </motion.div>
      ))}
    </motion.div>
  );
};

// 默认导出
export default {
  EnhancedChartContainer,
  EnhancedStatCard,
  EnhancedProgressCard,
  EnhancedDataGrid
};
