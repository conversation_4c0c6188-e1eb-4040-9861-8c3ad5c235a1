using System;
using System.Threading;
using System.Threading.Tasks;
using Domain.Common;
using Domain.DataAggregate;
using Domain.UserAggregate;
using Domain.VisualizationAggregate;
using Infrastructure.Persistence.Extensions;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistence
{
    /// <summary>
    /// 应用程序数据库上下文
    /// </summary>
    public class ApplicationDbContext : DbContext, IUnitOfWork
    {
        // 用户领域
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }

        // 数据领域
        public DbSet<DataSource> DataSources { get; set; }
        public DbSet<DataCategory> DataCategories { get; set; }
        public DbSet<DataPoint> DataPoints { get; set; }
        public DbSet<Threshold> Thresholds { get; set; }

        // 可视化领域
        public DbSet<Chart> Charts { get; set; }
        public DbSet<Dashboard> Dashboards { get; set; }
        public DbSet<ChartDataCategory> ChartDataCategories { get; set; }
        public DbSet<DashboardChart> DashboardCharts { get; set; }

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 应用所有实体配置
            modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);

            // 配置性能优化索引
            modelBuilder.ConfigurePerformanceIndexes();

            // 配置审计字段索引
            modelBuilder.ConfigureAuditIndexes();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            // 自动设置实体的创建/修改时间
            foreach (var entry in ChangeTracker.Entries<Entity>())
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedAt = DateTime.UtcNow;
                        break;
                    case EntityState.Modified:
                        entry.Entity.ModifiedAt = DateTime.UtcNow;
                        break;
                }
            }

            return await base.SaveChangesAsync(cancellationToken);
        }

        public async Task BeginTransactionAsync()
        {
            await Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            await Database.CommitTransactionAsync();
        }

        public async Task RollbackTransactionAsync()
        {
            await Database.RollbackTransactionAsync();
        }
    }
} 