# 🚀 快速启动指南

## ⚡ 5分钟快速部署

### 第一步：网络配置 (1分钟)
```cmd
# 运行自动IP检测工具
tools\network\simple-ip-detect.bat
```

### 第二步：启动系统 (2分钟)
```cmd
# 启动完整系统
scripts\start_system.bat
```

### 第三步：访问系统 (1分钟)
- 打开浏览器访问: http://[检测到的IP]:3000
- 使用默认账户登录: admin / admin123

### 第四步：验证功能 (1分钟)
- 测试登录功能
- 检查用户管理页面
- 验证数据显示

## 🔧 常用命令

### 网络配置
```cmd
# PowerShell版本 (推荐)
powershell -ExecutionPolicy Bypass -File tools\network\Auto-DetectIP.ps1

# 简化版本
tools\network\simple-ip-detect.bat
```

### 系统控制
```cmd
# 启动系统
scripts\start_system.bat

# 停止系统
scripts\stop_system.bat

# 自适应启动 (IP变化后使用)
start-adaptive.bat
```

### 测试工具
```cmd
# 网络诊断 (浏览器访问)
tools\testing\network-config-detector.html

# 登录测试
tools\testing\test-login-live.html

# 移动端测试
tools\testing\mobile-permissions-test.html
```

## 🚨 常见问题快速解决

### 问题1: 其他电脑无法访问
```cmd
# 解决方案
tools\network\simple-ip-detect.bat
# 然后重启前端服务
```

### 问题2: 用户管理显示不正确
```cmd
# 检查API配置
tools\testing\network-config-detector.html
# 重新配置网络
tools\network\Auto-DetectIP.ps1
```

### 问题3: 数据库连接失败
```cmd
# 检查SQL Server服务
services.msc
# 查找 "SQL Server" 服务并启动
```

### 问题4: 端口被占用
```cmd
# 检查端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :5000
# 结束占用进程或更改端口
```

## 📋 默认配置

### 端口配置
- **前端**: 3000
- **后端API**: 5000
- **数据库**: 1433 (SQL Server默认)

### 默认用户
- **管理员**: admin / admin123
- **测试用户**: testuser / test123

### 文件路径
- **配置文件**: `frontend\.env.production`
- **日志文件**: `logs\backend.log`
- **数据库**: SQL Server本地实例

## 🎯 快速检查清单

### 部署前检查
- [ ] SQL Server已安装并运行
- [ ] Node.js已安装 (16.0+)
- [ ] .NET 6.0已安装
- [ ] 防火墙端口已开放 (3000, 5000)

### 部署后验证
- [ ] 前端页面可以访问
- [ ] 登录功能正常
- [ ] 用户管理页面显示正确用户数量
- [ ] 其他电脑可以访问系统

### 故障排除
- [ ] 检查日志文件 (`logs\` 目录)
- [ ] 运行网络诊断工具
- [ ] 验证数据库连接
- [ ] 确认服务运行状态

## 📞 紧急联系

如果遇到无法解决的问题：

1. **收集信息**:
   - 错误截图
   - 日志文件内容
   - 系统环境信息

2. **运行诊断**:
   ```cmd
   tools\testing\network-config-detector.html
   ```

3. **联系技术支持**:
   - 提供收集的信息
   - 描述问题复现步骤

---

**提示**: 保存此文档到桌面，以便快速查阅！
