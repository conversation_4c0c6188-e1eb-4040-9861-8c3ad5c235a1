@echo off
title Adaptive Frontend Server
echo ========================================
echo   Adaptive Frontend Server
echo ========================================
echo.
echo [INFO] Starting adaptive frontend server...
echo [INFO] This script uses auto-detected IP configuration
echo.

REM Check if network configuration exists
if not exist "frontend\.env.production" (
    echo [WARNING] No network configuration found!
    echo [INFO] Running auto-detection...
    call tools\network\simple-ip-detect.bat
)

echo [INFO] Loading configuration...
cd frontend
set HOST=0.0.0.0
set PORT=3000

REM Try to read IP from configuration file
for /f "tokens=2 delims==" %%a in ('findstr "REACT_APP_API_URL" .env.production 2^>nul') do (
    set "API_URL=%%a"
)

if defined API_URL (
    echo [SUCCESS] Using configured API URL: %API_URL%
    set REACT_APP_API_URL=%API_URL%
) else (
    echo [WARNING] No API URL configured, using localhost
    set REACT_APP_API_URL=http://localhost:5000
)

echo.
echo [INFO] Starting React development server...
echo [INFO] Frontend will be available at: http://0.0.0.0:3000
echo [INFO] API endpoint: %REACT_APP_API_URL%
echo.

npm start