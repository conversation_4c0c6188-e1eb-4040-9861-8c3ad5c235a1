import React from 'react';
import { Card } from 'antd';
import { motion } from 'framer-motion';

const CleanCard = ({ 
  children, 
  title, 
  extra,
  hoverable = true,
  className = '',
  style = {},
  bodyStyle = {},
  headStyle = {},
  ...props 
}) => {
  const cardVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.4, ease: "easeOut" }
    },
    hover: hoverable ? { 
      y: -4,
      transition: { duration: 0.2, ease: "easeOut" }
    } : {}
  };

  const cleanStyle = {
    background: '#ffffff',
    borderRadius: '12px',
    border: '1px solid #e5e7eb',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
    overflow: 'hidden',
    ...style
  };

  const cleanBodyStyle = {
    padding: '24px',
    ...bodyStyle
  };

  const cleanHeadStyle = {
    background: '#ffffff',
    borderBottom: '1px solid #f3f4f6',
    padding: '20px 24px',
    ...headStyle
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="initial"
      animate="animate"
      whileHover="hover"
      className={`clean-card-wrapper ${className}`}
    >
      <Card
        title={title}
        extra={extra}
        style={cleanStyle}
        bodyStyle={cleanBodyStyle}
        headStyle={cleanHeadStyle}
        {...props}
      >
        {children}
      </Card>

      <style jsx="true">{`
        .clean-card-wrapper {
          position: relative;
        }

        .clean-card-wrapper .ant-card {
          transition: all 0.2s ease;
        }

        .clean-card-wrapper:hover .ant-card {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
          border-color: #d1d5db !important;
        }

        .clean-card-wrapper .ant-card-head-title {
          font-weight: 600;
          font-size: 18px;
          color: #111827;
          line-height: 1.4;
        }

        .clean-card-wrapper .ant-card-body {
          color: #374151;
          line-height: 1.6;
        }

        .clean-card-wrapper .ant-card-extra {
          color: #6b7280;
        }

        @media (max-width: 768px) {
          .clean-card-wrapper .ant-card-body {
            padding: 20px 16px;
          }
          
          .clean-card-wrapper .ant-card-head {
            padding: 16px;
          }
        }
      `}</style>
    </motion.div>
  );
};

export default CleanCard;
