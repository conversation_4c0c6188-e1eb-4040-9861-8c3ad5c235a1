import api from './api';
import cacheManager from '../utils/cache';

const DATA_IMPORT_ENDPOINT = '/api/data-import';

/**
 * 数据导入服务
 */
class DataImportService {
  /**
   * 上传文件
   * @param {File} file - 要上传的文件
   * @param {Function} onProgress - 进度回调函数
   * @returns {Promise} 上传结果
   */
  async uploadFile(file, onProgress = null) {
    const formData = new FormData();
    formData.append('file', file);
    
    try {
      const response = await api.post(`${DATA_IMPORT_ENDPOINT}/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            onProgress(percentCompleted);
          }
        },
        timeout: 30000, // 30秒超时
      });
      
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { message: '文件上传失败' };
    }
  }

  /**
   * 获取导入历史
   * @param {number} page - 页码
   * @param {number} pageSize - 每页大小
   * @returns {Promise} 导入历史列表
   */
  async getImportHistory(page = 1, pageSize = 10) {
    try {
      const response = await api.get(`${DATA_IMPORT_ENDPOINT}/history`, {
        params: { page, pageSize }
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { message: '获取导入历史失败' };
    }
  }

  /**
   * 获取导入状态
   * @param {string} importId - 导入ID
   * @returns {Promise} 导入状态
   */
  async getImportStatus(importId) {
    try {
      const response = await api.get(`${DATA_IMPORT_ENDPOINT}/status/${importId}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { message: '获取导入状态失败' };
    }
  }

  /**
   * 取消导入
   * @param {string} importId - 导入ID
   * @returns {Promise} 取消结果
   */
  async cancelImport(importId) {
    try {
      const response = await api.post(`${DATA_IMPORT_ENDPOINT}/cancel/${importId}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { message: '取消导入失败' };
    }
  }

  /**
   * 验证文件格式
   * @param {File} file - 要验证的文件
   * @returns {Object} 验证结果
   */
  validateFile(file) {
    const allowedTypes = [
      'text/plain',
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    const allowedExtensions = ['.txt', '.csv', '.xls', '.xlsx'];
    const maxSize = 100 * 1024 * 1024; // 100MB
    
    const result = {
      isValid: true,
      errors: []
    };
    
    // 检查文件类型
    if (!allowedTypes.includes(file.type)) {
      const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
      if (!allowedExtensions.includes(extension)) {
        result.isValid = false;
        result.errors.push('不支持的文件格式，请上传 TXT、CSV、XLS 或 XLSX 文件');
      }
    }
    
    // 检查文件大小
    if (file.size > maxSize) {
      result.isValid = false;
      result.errors.push('文件大小不能超过 100MB');
    }
    
    // 检查文件名
    if (!file.name || file.name.trim() === '') {
      result.isValid = false;
      result.errors.push('文件名不能为空');
    }
    
    return result;
  }

  /**
   * 获取支持的文件格式信息
   * @returns {Object} 文件格式信息
   */
  getSupportedFormats() {
    return {
      formats: [
        {
          name: 'TXT',
          extension: '.txt',
          description: '纯文本文件，支持逗号、制表符等分隔符',
          maxSize: '100MB'
        },
        {
          name: 'CSV',
          extension: '.csv',
          description: '逗号分隔值文件',
          maxSize: '100MB'
        },
        {
          name: 'Excel',
          extension: '.xls/.xlsx',
          description: 'Microsoft Excel 文件',
          maxSize: '100MB'
        }
      ],
      requirements: [
        '文件第一行应为列标题',
        '数据列应包含时间戳、数值等必要字段',
        '时间格式建议使用 YYYY-MM-DD HH:mm:ss',
        '数值字段应为数字格式'
      ]
    };
  }

  /**
   * 清除导入相关缓存
   */
  clearCache() {
    // 清除导入历史缓存
    const cacheKeys = cacheManager.getStats().memory.keys.filter(key => 
      key.includes('data-import') || key.includes('import-history')
    );
    
    cacheKeys.forEach(key => {
      cacheManager.delete(key);
    });
  }
}

// 创建单例实例
const dataImportService = new DataImportService();

export default dataImportService;
