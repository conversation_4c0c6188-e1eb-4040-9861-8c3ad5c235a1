using System.ComponentModel.DataAnnotations;

namespace API.Auth.Models
{
    /// <summary>
    /// 用户登录请求
    /// </summary>
    public class LoginRequest
    {
        /// <summary>
        /// 用户名或邮箱
        /// </summary>
        [Required(ErrorMessage = "用户名或邮箱不能为空")]
        public string UsernameOrEmail { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        [Required(ErrorMessage = "密码不能为空")]
        public string Password { get; set; } = string.Empty;
    }
} 