# 测试完整系统功能

Write-Host "=== 测试完整系统功能 ===" -ForegroundColor Yellow

# 1. 测试健康检查
Write-Host "`n1. 测试系统健康检查" -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/health/database' -Method GET
    Write-Host "数据库健康检查成功!" -ForegroundColor Green
    $response | ConvertTo-Json
} catch {
    Write-Host "数据库健康检查失败!" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

# 2. 测试用户管理
Write-Host "`n2. 测试用户管理API" -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/users' -Method GET
    Write-Host "用户列表获取成功!" -ForegroundColor Green
    Write-Host "用户数量: $($response.total)"
} catch {
    Write-Host "用户列表获取失败!" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

# 3. 测试系统配置
Write-Host "`n3. 测试系统配置API" -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/system/settings' -Method GET
    Write-Host "系统配置获取成功!" -ForegroundColor Green
    $response | ConvertTo-Json
} catch {
    Write-Host "系统配置获取失败!" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

# 4. 测试角色管理
Write-Host "`n4. 测试角色管理API" -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/roles' -Method GET
    Write-Host "角色列表获取成功!" -ForegroundColor Green
    $response | ConvertTo-Json
} catch {
    Write-Host "角色列表获取失败!" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

# 5. 测试数据导入任务
Write-Host "`n5. 测试数据导入任务API" -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/import/tasks' -Method GET
    Write-Host "导入任务列表获取成功!" -ForegroundColor Green
    Write-Host "任务数量: $($response.total)"
} catch {
    Write-Host "导入任务列表获取失败!" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

# 6. 测试文件映射
Write-Host "`n6. 测试文件映射API" -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/import/mappings' -Method GET
    Write-Host "文件映射列表获取成功!" -ForegroundColor Green
    Write-Host "映射数量: $($response.total)"
} catch {
    Write-Host "文件映射列表获取失败!" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

Write-Host "`n=== 系统功能测试完成 ===" -ForegroundColor Yellow
