using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using API.Auth;
using Domain.UserAggregate;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace API.Controllers
{
    /// <summary>
    /// 用户管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")]
    public class UsersController : ControllerBase
    {
        private readonly IUserRepository _userRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IPasswordService _passwordService;
        private readonly ILogger<UsersController> _logger;

        public UsersController(
            IUserRepository userRepository,
            IRoleRepository roleRepository,
            IPasswordService passwordService,
            ILogger<UsersController> logger)
        {
            _userRepository = userRepository;
            _roleRepository = roleRepository;
            _passwordService = passwordService;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有用户
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetUsers()
        {
            try
            {
                var users = await _userRepository.GetAllAsync();
                var userDtos = users.Select(u => new
                {
                    Id = u.Id,
                    Username = u.Username,
                    Email = u.Email,
                    FullName = u.FullName,
                    IsActive = u.IsActive,
                    LastLoginTime = u.LastLoginTime,
                    CreatedAt = u.CreatedAt,
                    ModifiedAt = u.ModifiedAt
                });

                return Ok(userDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户列表失败");
                return StatusCode(500, new { message = "获取用户列表失败，请稍后再试" });
            }
        }

        /// <summary>
        /// 获取用户详情
        /// </summary>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(Guid id)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(id);
                if (user == null)
                {
                    return NotFound(new { message = "用户不存在" });
                }

                var roles = await _roleRepository.GetUserRolesAsync(user.Id);
                var roleNames = roles.Select(r => r.Name).ToList();

                var userDto = new
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    FullName = user.FullName,
                    IsActive = user.IsActive,
                    LastLoginTime = user.LastLoginTime,
                    Roles = roleNames,
                    CreatedAt = user.CreatedAt,
                    ModifiedAt = user.ModifiedAt
                };

                return Ok(userDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户详情失败");
                return StatusCode(500, new { message = "获取用户详情失败，请稍后再试" });
            }
        }

        /// <summary>
        /// 创建用户
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> CreateUser([FromBody] CreateUserRequest request)
        {
            try
            {
                // 检查用户名是否已存在
                var existingUserByUsername = await _userRepository.FindByUsernameAsync(request.Username);
                if (existingUserByUsername != null)
                {
                    return BadRequest(new { message = "用户名已存在" });
                }

                // 检查邮箱是否已存在
                var existingUserByEmail = await _userRepository.FindByEmailAsync(request.Email);
                if (existingUserByEmail != null)
                {
                    return BadRequest(new { message = "邮箱已存在" });
                }

                // 创建用户
                var user = new User(
                    Guid.NewGuid(),
                    request.Username,
                    request.Email,
                    _passwordService.HashPassword(request.Password),
                    request.FullName
                );

                // 保存用户
                await _userRepository.AddAsync(user);

                // 分配角色
                if (request.RoleIds != null && request.RoleIds.Any())
                {
                    foreach (var roleId in request.RoleIds)
                    {
                        var role = await _roleRepository.GetByIdAsync(roleId);
                        if (role != null)
                        {
                            user.AddRole(role);
                        }
                    }
                }

                await _userRepository.UnitOfWork.SaveChangesAsync();

                _logger.LogInformation("用户创建成功: {Username}", user.Username);

                return CreatedAtAction(nameof(GetUser), new { id = user.Id }, new { id = user.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建用户失败");
                return StatusCode(500, new { message = "创建用户失败，请稍后再试" });
            }
        }

        /// <summary>
        /// 更新用户
        /// </summary>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUser(Guid id, [FromBody] UpdateUserRequest request)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(id);
                if (user == null)
                {
                    return NotFound(new { message = "用户不存在" });
                }

                // 如果要更改邮箱，检查邮箱是否已被其他用户使用
                if (request.Email != user.Email)
                {
                    var existingUserByEmail = await _userRepository.FindByEmailAsync(request.Email);
                    if (existingUserByEmail != null && existingUserByEmail.Id != id)
                    {
                        return BadRequest(new { message = "邮箱已被其他用户使用" });
                    }
                }

                // 更新用户信息
                user.UpdateProfile(request.Email, request.FullName);
                user.SetActive(request.IsActive);

                // 如果提供了新密码，则更新密码
                if (!string.IsNullOrEmpty(request.NewPassword))
                {
                    user.ChangePassword(_passwordService.HashPassword(request.NewPassword));
                }

                await _userRepository.UpdateAsync(user);
                await _userRepository.UnitOfWork.SaveChangesAsync();

                _logger.LogInformation("用户更新成功: {Username}", user.Username);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户失败");
                return StatusCode(500, new { message = "更新用户失败，请稍后再试" });
            }
        }

        /// <summary>
        /// 删除用户
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUser(Guid id)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(id);
                if (user == null)
                {
                    return NotFound(new { message = "用户不存在" });
                }

                await _userRepository.DeleteAsync(user);
                await _userRepository.UnitOfWork.SaveChangesAsync();

                _logger.LogInformation("用户删除成功: {Username}", user.Username);

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除用户失败");
                return StatusCode(500, new { message = "删除用户失败，请稍后再试" });
            }
        }
    }

    /// <summary>
    /// 创建用户请求
    /// </summary>
    public class CreateUserRequest
    {
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public List<Guid>? RoleIds { get; set; }
    }

    /// <summary>
    /// 更新用户请求
    /// </summary>
    public class UpdateUserRequest
    {
        public string Email { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public string? NewPassword { get; set; }
    }
} 