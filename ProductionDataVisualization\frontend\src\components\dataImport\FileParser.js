import React, { useState, useEffect } from 'react';
import { Card, Table, Alert, Spin, Button, Space, Typography, Tabs, Select, InputNumber, Switch } from 'antd';
import { FileTextOutlined, SettingOutlined, CheckCircleOutlined, ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
// import * as XLSX from 'xlsx'; // 临时注释，等待安装

const { Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

const FileParser = ({ file, onParseComplete, onParseError }) => {
  const [loading, setLoading] = useState(false);
  const [parsedData, setParsedData] = useState(null);
  const [parseConfig, setParseConfig] = useState({
    delimiter: ',',
    hasHeader: true,
    encoding: 'UTF-8',
    skipRows: 0,
    maxRows: 1000, // 预览行数限制
    dateFormat: 'YYYY-MM-DD',
    numberFormat: 'auto'
  });
  const [parseErrors, setParseErrors] = useState([]);
  const [dataTypes, setDataTypes] = useState({});

  // 文件解析器映射
  const parsers = {
    csv: parseCSV,
    txt: parseTXT,
    // xlsx: parseXLSX, // 临时禁用
    // xls: parseXLSX   // 临时禁用
  };

  // CSV解析器
  async function parseCSV(file, config) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const text = e.target.result;
          const lines = text.split('\n').filter(line => line.trim());
          
          // 跳过指定行数
          const dataLines = lines.slice(config.skipRows);
          
          let headers = [];
          let rows = [];
          
          if (config.hasHeader && dataLines.length > 0) {
            headers = dataLines[0].split(config.delimiter).map(h => h.trim().replace(/"/g, ''));
            rows = dataLines.slice(1);
          } else {
            // 自动生成列名
            const firstRow = dataLines[0] ? dataLines[0].split(config.delimiter) : [];
            headers = firstRow.map((_, index) => `Column_${index + 1}`);
            rows = dataLines;
          }
          
          // 解析数据行 - 区分预览和完整数据
          const allData = rows.map((row, index) => {
            const values = row.split(config.delimiter).map(v => v.trim().replace(/"/g, ''));
            const rowData = { _rowIndex: index + 1 };
            
            headers.forEach((header, colIndex) => {
              rowData[header] = values[colIndex] || '';
            });
            
            return rowData;
          });
          
          // 创建预览数据（限制行数）
          const previewData = allData.slice(0, config.maxRows);

          resolve({
            headers,
            data: allData, // 完整数据用于导入
            previewData: previewData, // 预览数据用于显示
            totalRows: allData.length, // 实际总行数
            previewRows: previewData.length, // 预览行数
            totalColumns: headers.length,
            fileSize: file.size,
            fileName: file.name,
            parseConfig: config
          });
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = reject;
      reader.readAsText(file, config.encoding);
    });
  }

  // TXT解析器
  async function parseTXT(file, config) {
    // TXT文件通常使用制表符或空格分隔
    const txtConfig = { ...config, delimiter: config.delimiter === ',' ? '\t' : config.delimiter };
    return parseCSV(file, txtConfig);
  }

  // Excel解析器 (临时禁用，等待xlsx库安装)
  async function parseXLSX(file, config) {
    return new Promise((resolve, reject) => {
      reject(new Error('Excel文件解析功能暂时不可用，请安装xlsx依赖包。目前支持CSV和TXT文件。'));
    });
  }

  // 数据类型检测
  const detectDataTypes = (data, headers) => {
    const types = {};
    
    headers.forEach(header => {
      const values = data.map(row => row[header]).filter(v => v !== '' && v != null);
      
      if (values.length === 0) {
        types[header] = 'string';
        return;
      }
      
      // 检测数字类型
      const numericValues = values.filter(v => !isNaN(Number(v)));
      if (numericValues.length / values.length > 0.8) {
        // 检测是否为整数
        const integerValues = numericValues.filter(v => Number.isInteger(Number(v)));
        types[header] = integerValues.length === numericValues.length ? 'integer' : 'number';
        return;
      }
      
      // 检测日期类型
      const dateValues = values.filter(v => !isNaN(Date.parse(v)));
      if (dateValues.length / values.length > 0.8) {
        types[header] = 'date';
        return;
      }
      
      // 检测布尔类型
      const booleanValues = values.filter(v => 
        ['true', 'false', '1', '0', 'yes', 'no', 'y', 'n'].includes(v.toString().toLowerCase())
      );
      if (booleanValues.length / values.length > 0.8) {
        types[header] = 'boolean';
        return;
      }
      
      types[header] = 'string';
    });
    
    return types;
  };

  // 数据验证
  const validateData = (data, headers, types) => {
    const errors = [];
    
    data.forEach((row, rowIndex) => {
      headers.forEach(header => {
        const value = row[header];
        const type = types[header];
        
        if (value === '' || value == null) return;
        
        switch (type) {
          case 'number':
          case 'integer':
            if (isNaN(Number(value))) {
              errors.push({
                row: rowIndex + 1,
                column: header,
                value,
                error: `期望${type === 'integer' ? '整数' : '数字'}类型`
              });
            }
            break;
          case 'date':
            if (isNaN(Date.parse(value))) {
              errors.push({
                row: rowIndex + 1,
                column: header,
                value,
                error: '期望日期类型'
              });
            }
            break;
        }
      });
    });
    
    return errors;
  };

  // 执行文件解析
  const parseFile = async () => {
    if (!file) return;
    
    setLoading(true);
    setParseErrors([]);
    
    try {
      const fileExt = file.name.split('.').pop().toLowerCase();
      const parser = parsers[fileExt];
      
      if (!parser) {
        throw new Error(`不支持的文件格式: ${fileExt}`);
      }
      
      const result = await parser(file, parseConfig);
      
      // 检测数据类型
      const detectedTypes = detectDataTypes(result.data, result.headers);
      setDataTypes(detectedTypes);
      
      // 验证数据
      const validationErrors = validateData(result.data, result.headers, detectedTypes);
      setParseErrors(validationErrors);
      
      setParsedData(result);
      
      if (onParseComplete) {
        onParseComplete({
          ...result,
          dataTypes: detectedTypes,
          validationErrors
        });
      }
      
    } catch (error) {
      console.error('Parse error:', error);
      setParseErrors([{ error: error.message }]);
      if (onParseError) {
        onParseError(error);
      }
    } finally {
      setLoading(false);
    }
  };

  // 文件变化时自动解析
  useEffect(() => {
    if (file) {
      parseFile();
    }
  }, [file, parseConfig]);

  // 生成表格列配置
  const generateColumns = () => {
    if (!parsedData) return [];
    
    return parsedData.headers.map(header => ({
      title: (
        <Space>
          <Text strong>{header}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ({dataTypes[header] || 'string'})
          </Text>
        </Space>
      ),
      dataIndex: header,
      key: header,
      width: 150,
      ellipsis: true,
      render: (text, record) => {
        const hasError = parseErrors.some(error => 
          error.row === record._rowIndex && error.column === header
        );
        
        return (
          <span style={{ color: hasError ? '#ff4d4f' : undefined }}>
            {text}
          </span>
        );
      }
    }));
  };

  return (
    <div className="file-parser">
      <Card>
        <Tabs defaultActiveKey="preview">
          <TabPane tab="数据预览" key="preview">
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              {/* 解析状态 */}
              {loading && (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <Spin size="large" />
                  <div style={{ marginTop: '10px' }}>
                    <Text>正在解析文件...</Text>
                  </div>
                </div>
              )}
              
              {/* 解析错误 */}
              {parseErrors.length > 0 && (
                <Alert
                  message={`发现 ${parseErrors.length} 个数据问题`}
                  type="warning"
                  showIcon
                  description={
                    <div style={{ maxHeight: '200px', overflow: 'auto' }}>
                      {parseErrors.slice(0, 10).map((error, index) => (
                        <div key={index}>
                          {error.row && error.column ? 
                            `第${error.row}行，列"${error.column}": ${error.error} (值: "${error.value}")` :
                            error.error
                          }
                        </div>
                      ))}
                      {parseErrors.length > 10 && (
                        <Text type="secondary">... 还有 {parseErrors.length - 10} 个问题</Text>
                      )}
                    </div>
                  }
                />
              )}
              
              {/* 数据统计 */}
              {parsedData && (
                <Card size="small">
                  <Space>
                    <Text><FileTextOutlined /> 文件: {file?.name}</Text>
                    <Text strong>总行数: {parsedData.totalRows}</Text>
                    <Text type="secondary">预览行数: {parsedData.previewRows}</Text>
                    <Text>列数: {parsedData.headers.length}</Text>
                    {parsedData.totalRows > parsedData.previewRows && (
                      <Text type="warning">
                        <InfoCircleOutlined /> 仅显示前{parsedData.previewRows}行，导入时将处理全部{parsedData.totalRows}行
                      </Text>
                    )}
                    {parseErrors.length === 0 ? (
                      <Text type="success"><CheckCircleOutlined /> 数据格式正确</Text>
                    ) : (
                      <Text type="warning"><ExclamationCircleOutlined /> 发现数据问题</Text>
                    )}
                  </Space>
                </Card>
              )}
              
              {/* 数据表格 */}
              {parsedData && (
                <Table
                  columns={generateColumns()}
                  dataSource={parsedData.previewData || parsedData.data} // 使用预览数据
                  rowKey="_rowIndex"
                  scroll={{ x: 'max-content', y: 400 }}
                  pagination={{
                    pageSize: 50,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 行数据`
                  }}
                  size="small"
                />
              )}
            </Space>
          </TabPane>
          
          <TabPane tab="解析配置" key="config">
            <Card title={<><SettingOutlined /> 解析配置</>}>
              <Space direction="vertical" style={{ width: '100%' }} size="middle">
                <div>
                  <Text strong>分隔符:</Text>
                  <Select
                    value={parseConfig.delimiter}
                    onChange={(value) => setParseConfig(prev => ({ ...prev, delimiter: value }))}
                    style={{ width: 120, marginLeft: 10 }}
                  >
                    <Option value=",">逗号 (,)</Option>
                    <Option value="\t">制表符 (\t)</Option>
                    <Option value=";">分号 (;)</Option>
                    <Option value="|">竖线 (|)</Option>
                  </Select>
                </div>
                
                <div>
                  <Text strong>包含标题行:</Text>
                  <Switch
                    checked={parseConfig.hasHeader}
                    onChange={(checked) => setParseConfig(prev => ({ ...prev, hasHeader: checked }))}
                    style={{ marginLeft: 10 }}
                  />
                </div>
                
                <div>
                  <Text strong>跳过行数:</Text>
                  <InputNumber
                    min={0}
                    max={100}
                    value={parseConfig.skipRows}
                    onChange={(value) => setParseConfig(prev => ({ ...prev, skipRows: value || 0 }))}
                    style={{ marginLeft: 10 }}
                  />
                </div>
                
                <div>
                  <Text strong>预览行数:</Text>
                  <InputNumber
                    min={100}
                    max={10000}
                    value={parseConfig.maxRows}
                    onChange={(value) => setParseConfig(prev => ({ ...prev, maxRows: value || 1000 }))}
                    style={{ marginLeft: 10 }}
                  />
                </div>
                
                <div>
                  <Text strong>编码格式:</Text>
                  <Select
                    value={parseConfig.encoding}
                    onChange={(value) => setParseConfig(prev => ({ ...prev, encoding: value }))}
                    style={{ width: 120, marginLeft: 10 }}
                  >
                    <Option value="UTF-8">UTF-8</Option>
                    <Option value="GBK">GBK</Option>
                    <Option value="GB2312">GB2312</Option>
                  </Select>
                </div>
                
                <Button type="primary" onClick={parseFile} loading={loading}>
                  重新解析
                </Button>
              </Space>
            </Card>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default FileParser;
