[2025-07-13 23:39:42 INF] Now listening on: http://localhost:5000 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:39:42 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:39:42 INF] Hosting environment: Production {"SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:39:42 INF] Content root path: C:\Users\<USER>\Documents\Trae_Files\Report\ProductionDataVisualization\backend\src\API {"SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:40:20 INF] HTTP GET / responded 200 in 106.9702 ms {"RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE24E9JHUJE:00000001","ConnectionId":"0HNE24E9JHUJE","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:40:20 WRN] HTTP GET /favicon.ico responded 404 in 2.8939 ms {"RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE24E9JHUJE:00000002","ConnectionId":"0HNE24E9JHUJE","Application":"ProductionDataVisualization","Environment":"Production"}
