import axios from 'axios';
import cacheManager from '../utils/cache';

// 预定义的服务器配置（便于快速部署和备用）
const SERVER_CONFIGS = {
  // 开发环境
  'localhost': 'http://localhost:5000',
  '127.0.0.1': 'http://127.0.0.1:5000',

  // 常见的内网IP段（可以根据实际情况添加）
  '***********': 'http://***********:5000',
  '*************': 'http://*************:5000',
  '*************': 'http://*************:5000',

  // 可以添加更多预定义配置
};

// 动态获取API基础URL - 智能自适应版本
const getApiBaseURL = () => {
  console.log('🔍 开始智能检测API基础URL...');

  // 优先级1: 环境变量配置（最高优先级，但只在明确配置时使用）
  if (process.env.REACT_APP_API_URL &&
      process.env.REACT_APP_API_URL !== 'http://localhost:5000' &&
      !process.env.REACT_APP_API_URL.includes('localhost')) {
    console.log('✅ 使用环境变量API URL:', process.env.REACT_APP_API_URL);
    return process.env.REACT_APP_API_URL;
  }

  // 优先级2: 智能检测当前访问的主机名和IP
  const currentHost = window.location.hostname;
  const currentPort = window.location.port;
  const apiPort = 5000;
  const protocol = window.location.protocol;

  console.log('📍 当前访问信息:', {
    hostname: currentHost,
    port: currentPort,
    origin: window.location.origin,
    href: window.location.href,
    protocol: protocol
  });

  // 优先级3: 预定义服务器配置
  if (SERVER_CONFIGS[currentHost]) {
    console.log('✅ 使用预定义服务器配置:', SERVER_CONFIGS[currentHost]);
    return SERVER_CONFIGS[currentHost];
  }

  // 优先级4: 智能检测 - 开发环境
  if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
    const apiUrl = 'http://localhost:5000';
    console.log('🔧 检测到开发环境，使用:', apiUrl);
    return apiUrl;
  }

  // 优先级5: 智能检测 - IP地址访问（自适应）
  const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
  if (ipRegex.test(currentHost)) {
    // 使用相同的协议和IP，但使用API端口
    const apiUrl = `${protocol}//${currentHost}:${apiPort}`;
    console.log('🌐 检测到IP地址访问，自适应使用:', apiUrl);
    return apiUrl;
  }

  // 优先级6: 智能检测 - 域名访问
  if (currentHost && currentHost !== 'localhost') {
    const apiUrl = `${protocol}//${currentHost}:${apiPort}`;
    console.log('🏷️ 检测到域名访问，使用:', apiUrl);
    return apiUrl;
  }

  // 备用方案: 使用当前主机名
  const fallbackUrl = `http://${currentHost}:${apiPort}`;
  console.log('⚠️ 使用备用方案:', fallbackUrl);
  return fallbackUrl;
};

console.log('API Base URL:', getApiBaseURL());

// 创建axios实例
const api = axios.create({
  baseURL: getApiBaseURL(),
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 请求超时时间：10秒
});

// 性能监控
let performanceStats = {
  totalRequests: 0,
  slowRequests: 0,
  errorRequests: 0,
  averageResponseTime: 0
};

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加请求开始时间
    config.metadata = { startTime: new Date() };

    // 从localStorage获取token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 性能统计
    performanceStats.totalRequests++;

    return config;
  },
  (error) => {
    console.error('请求拦截器错误:', error);
    performanceStats.errorRequests++;
    return Promise.reject(error);
  }
);

// 全局变量，用于存储401错误状态
let isHandling401Error = false;

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 计算响应时间
    const endTime = new Date();
    const duration = endTime - response.config.metadata.startTime;

    // 更新平均响应时间
    performanceStats.averageResponseTime =
      (performanceStats.averageResponseTime * (performanceStats.totalRequests - 1) + duration) / performanceStats.totalRequests;

    // 检测慢请求
    if (duration > 3000) { // 超过3秒
      performanceStats.slowRequests++;
      console.warn(`慢请求检测: ${response.config.method?.toUpperCase()} ${response.config.url} 耗时 ${duration}ms`);
    }

    // 记录性能日志
    console.debug(`API请求: ${response.config.method?.toUpperCase()} ${response.config.url} ${response.status} ${duration}ms`);

    return response;
  },
  (error) => {
    // 性能统计
    performanceStats.errorRequests++;

    // 计算错误请求的响应时间
    if (error.config?.metadata?.startTime) {
      const endTime = new Date();
      const duration = endTime - error.config.metadata.startTime;
      console.error(`API错误: ${error.config.method?.toUpperCase()} ${error.config.url} ${error.response?.status || 'NETWORK_ERROR'} ${duration}ms`);
    }

    // 处理网络错误
    if (!error.response) {
      console.error('网络错误或服务器未响应:', error.message);
      return Promise.reject({
        message: '无法连接到服务器，请检查网络连接或稍后再试'
      });
    }

    // 处理401错误（未授权）
    if (error.response && error.response.status === 401) {
      console.warn('未授权访问，清除凭据');

      // 避免重复处理401错误
      if (!isHandling401Error) {
        isHandling401Error = true;

        // 清除认证信息
        localStorage.removeItem('token');
        localStorage.removeItem('user');

        // 清除缓存
        cacheManager.clear();

        // 使用自定义事件通知应用程序处理认证失败
        const authErrorEvent = new CustomEvent('authError', {
          detail: { message: '您的登录已过期，请重新登录' }
        });
        window.dispatchEvent(authErrorEvent);

        // 5秒后重置状态，避免长时间锁定
        setTimeout(() => {
          isHandling401Error = false;
        }, 5000);
      }
    }

    // 处理其他错误
    const errorMessage = error.response?.data?.message || error.message || '未知错误';
    console.error(`API错误 (${error.response?.status || 'unknown'}):`, errorMessage);

    return Promise.reject(error.response?.data || { message: errorMessage });
  }
);

// 缓存GET请求的包装函数
export const cachedGet = async (url, options = {}) => {
  const {
    cache = true,
    cacheTime = 5 * 60 * 1000, // 5分钟
    ...axiosOptions
  } = options;

  if (cache) {
    const cacheKey = `api_${url}_${JSON.stringify(axiosOptions)}`;
    const cachedData = cacheManager.get(cacheKey);

    if (cachedData) {
      console.debug(`缓存命中: ${url}`);
      return { data: cachedData };
    }

    try {
      const response = await api.get(url, axiosOptions);
      cacheManager.set(cacheKey, response.data, { expireTime: cacheTime });
      return response;
    } catch (error) {
      throw error;
    }
  }

  return api.get(url, axiosOptions);
};

// 获取性能统计
export const getPerformanceStats = () => ({
  ...performanceStats,
  errorRate: performanceStats.totalRequests > 0
    ? (performanceStats.errorRequests / performanceStats.totalRequests * 100).toFixed(2) + '%'
    : '0%',
  slowRequestRate: performanceStats.totalRequests > 0
    ? (performanceStats.slowRequests / performanceStats.totalRequests * 100).toFixed(2) + '%'
    : '0%'
});

// 重置性能统计
export const resetPerformanceStats = () => {
  performanceStats = {
    totalRequests: 0,
    slowRequests: 0,
    errorRequests: 0,
    averageResponseTime: 0
  };
};

export default api;