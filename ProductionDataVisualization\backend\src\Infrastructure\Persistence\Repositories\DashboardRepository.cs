using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Domain.Common;
using Domain.VisualizationAggregate;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// 仪表盘仓储实现
    /// </summary>
    public class DashboardRepository : BaseRepository<Dashboard>, IDashboardRepository
    {
        public DashboardRepository(ApplicationDbContext dbContext) : base(dbContext)
        {
        }

        public override async Task<Dashboard> GetByIdAsync(Guid id)
        {
            return await DbContext.Dashboards
                .Include(d => d.DashboardCharts)
                    .ThenInclude(dc => dc.Chart)
                        .ThenInclude(c => c.ChartDataCategories)
                .FirstOrDefaultAsync(d => d.Id == id);
        }

        public override async Task<IEnumerable<Dashboard>> GetAllAsync()
        {
            return await DbContext.Dashboards
                .Include(d => d.DashboardCharts)
                    .ThenInclude(dc => dc.Chart)
                .ToListAsync();
        }

        public async Task<IEnumerable<Dashboard>> GetByCreatedByAsync(Guid userId)
        {
            return await DbContext.Dashboards
                .Where(d => d.CreatedBy == userId)
                .Include(d => d.DashboardCharts)
                    .ThenInclude(dc => dc.Chart)
                .OrderByDescending(d => d.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Dashboard>> GetByChartIdAsync(Guid chartId)
        {
            return await DbContext.Dashboards
                .Include(d => d.DashboardCharts)
                .Where(d => d.DashboardCharts.Any(dc => dc.ChartId == chartId))
                .ToListAsync();
        }

        public async Task<IEnumerable<Dashboard>> GetPublicAsync()
        {
            return await DbContext.Dashboards
                .Where(d => d.IsPublic)
                .Include(d => d.DashboardCharts)
                .ThenInclude(dc => dc.Chart)
                .ToListAsync();
        }
    }
} 