/**
 * 前端缓存管理工具
 */

// 缓存配置
const CACHE_CONFIG = {
  // 默认过期时间（毫秒）
  DEFAULT_EXPIRE_TIME: 5 * 60 * 1000, // 5分钟
  // 最大缓存条目数
  MAX_CACHE_SIZE: 100,
  // 缓存键前缀
  CACHE_PREFIX: 'pdv_cache_'
};

/**
 * 内存缓存类
 */
class MemoryCache {
  constructor() {
    this.cache = new Map();
    this.timers = new Map();
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {number} expireTime - 过期时间（毫秒）
   */
  set(key, value, expireTime = CACHE_CONFIG.DEFAULT_EXPIRE_TIME) {
    // 检查缓存大小限制
    if (this.cache.size >= CACHE_CONFIG.MAX_CACHE_SIZE) {
      this.clearOldest();
    }

    // 清除旧的定时器
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
    }

    // 设置缓存值
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      expireTime
    });

    // 设置过期定时器
    const timer = setTimeout(() => {
      this.delete(key);
    }, expireTime);

    this.timers.set(key, timer);
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {any} 缓存值或null
   */
  get(key) {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > item.expireTime) {
      this.delete(key);
      return null;
    }

    return item.value;
  }

  /**
   * 删除缓存
   * @param {string} key - 缓存键
   */
  delete(key) {
    this.cache.delete(key);
    
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
      this.timers.delete(key);
    }
  }

  /**
   * 清除所有缓存
   */
  clear() {
    this.cache.clear();
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();
  }

  /**
   * 清除最旧的缓存项
   */
  clearOldest() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, item] of this.cache.entries()) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.delete(oldestKey);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: CACHE_CONFIG.MAX_CACHE_SIZE,
      keys: Array.from(this.cache.keys())
    };
  }
}

/**
 * LocalStorage缓存类
 */
class LocalStorageCache {
  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {number} expireTime - 过期时间（毫秒）
   */
  set(key, value, expireTime = CACHE_CONFIG.DEFAULT_EXPIRE_TIME) {
    try {
      const item = {
        value,
        timestamp: Date.now(),
        expireTime
      };
      
      localStorage.setItem(
        CACHE_CONFIG.CACHE_PREFIX + key, 
        JSON.stringify(item)
      );
    } catch (error) {
      console.warn('LocalStorage缓存设置失败:', error);
    }
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {any} 缓存值或null
   */
  get(key) {
    try {
      const itemStr = localStorage.getItem(CACHE_CONFIG.CACHE_PREFIX + key);
      
      if (!itemStr) {
        return null;
      }

      const item = JSON.parse(itemStr);
      
      // 检查是否过期
      if (Date.now() - item.timestamp > item.expireTime) {
        this.delete(key);
        return null;
      }

      return item.value;
    } catch (error) {
      console.warn('LocalStorage缓存获取失败:', error);
      return null;
    }
  }

  /**
   * 删除缓存
   * @param {string} key - 缓存键
   */
  delete(key) {
    try {
      localStorage.removeItem(CACHE_CONFIG.CACHE_PREFIX + key);
    } catch (error) {
      console.warn('LocalStorage缓存删除失败:', error);
    }
  }

  /**
   * 清除所有缓存
   */
  clear() {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(CACHE_CONFIG.CACHE_PREFIX)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn('LocalStorage缓存清除失败:', error);
    }
  }
}

// 创建缓存实例
const memoryCache = new MemoryCache();
const localStorageCache = new LocalStorageCache();

/**
 * 缓存管理器
 */
export const cacheManager = {
  // 内存缓存
  memory: memoryCache,
  
  // 本地存储缓存
  localStorage: localStorageCache,
  
  /**
   * 智能缓存（自动选择存储方式）
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {Object} options - 选项
   */
  set(key, value, options = {}) {
    const { 
      expireTime = CACHE_CONFIG.DEFAULT_EXPIRE_TIME,
      persistent = false 
    } = options;

    if (persistent) {
      localStorageCache.set(key, value, expireTime);
    } else {
      memoryCache.set(key, value, expireTime);
    }
  },

  /**
   * 智能获取缓存
   * @param {string} key - 缓存键
   * @param {boolean} persistent - 是否从持久化存储获取
   * @returns {any} 缓存值或null
   */
  get(key, persistent = false) {
    if (persistent) {
      return localStorageCache.get(key);
    } else {
      return memoryCache.get(key) || localStorageCache.get(key);
    }
  },

  /**
   * 删除缓存
   * @param {string} key - 缓存键
   */
  delete(key) {
    memoryCache.delete(key);
    localStorageCache.delete(key);
  },

  /**
   * 清除所有缓存
   */
  clear() {
    memoryCache.clear();
    localStorageCache.clear();
  },

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      memory: memoryCache.getStats(),
      localStorage: {
        keys: Object.keys(localStorage).filter(key => 
          key.startsWith(CACHE_CONFIG.CACHE_PREFIX)
        )
      }
    };
  }
};

export default cacheManager;
