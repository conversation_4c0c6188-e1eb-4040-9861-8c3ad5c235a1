@echo off
title 前端测试启动器

echo ========================================
echo   前端编译测试
echo ========================================
echo.

cd frontend

echo [INFO] 检查依赖...
if not exist "node_modules" (
    echo [INFO] 安装依赖...
    npm install
)

echo.
echo [INFO] 检查编译...
npm run build

if %errorlevel% equ 0 (
    echo [SUCCESS] 编译成功！
    echo [INFO] 启动开发服务器...
    npm start
) else (
    echo [ERROR] 编译失败，请检查错误信息
    pause
)

pause
