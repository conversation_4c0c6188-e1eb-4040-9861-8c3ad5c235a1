@echo off
title .NET PATH Verification

echo ==========================================
echo   .NET PATH Configuration Verification
echo ==========================================
echo.

echo [1] Checking current session PATH...
echo Current PATH contains:
echo %PATH% | findstr /i "dotnet"
if %errorlevel% equ 0 (
    echo [SUCCESS] .NET path found in current session PATH
) else (
    echo [INFO] .NET path not found in current session PATH
    echo [INFO] This is normal after just configuring PATH
)

echo.
echo [2] Testing direct .NET execution...
if exist "C:\Program Files\dotnet\dotnet.exe" (
    echo [SUCCESS] .NET executable found at: C:\Program Files\dotnet\dotnet.exe
    echo Version:
    "C:\Program Files\dotnet\dotnet.exe" --version
) else (
    echo [ERROR] .NET executable not found
)

echo.
echo [3] Testing dotnet command (after PATH refresh)...
REM Refresh PATH for this session
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SystemPath=%%b"
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "UserPath=%%b"
set "PATH=%SystemPath%;%UserPath%"

echo Testing dotnet command with refreshed PATH...
dotnet --version
if %errorlevel% equ 0 (
    echo [SUCCESS] dotnet command works with refreshed PATH!
) else (
    echo [INFO] dotnet command still needs system restart to work
)

echo.
echo ==========================================
echo   Configuration Status
echo ==========================================
echo.
echo System PATH Configuration: COMPLETED
echo - You have successfully added C:\Program Files\dotnet to system PATH
echo - This is visible in your environment variables screenshot
echo.
echo Current Session Status:
if %errorlevel% equ 0 (
    echo - dotnet command is working in current session
    echo - Configuration is fully active
) else (
    echo - dotnet command needs session refresh
    echo - This is normal behavior after PATH changes
)

echo.
echo ==========================================
echo   Next Steps
echo ==========================================
echo.
echo To activate the PATH changes:
echo.
echo Option 1 (Recommended): 
echo   - Close ALL command prompt windows
echo   - Open a NEW command prompt
echo   - Test: dotnet --version
echo.
echo Option 2 (Most thorough):
echo   - Restart your computer
echo   - Test: dotnet --version
echo.
echo Option 3 (Quick test):
echo   - Log out and log back in
echo   - Test: dotnet --version
echo.

echo Your .NET SDK is fully installed and PATH is correctly configured!
echo.

pause
