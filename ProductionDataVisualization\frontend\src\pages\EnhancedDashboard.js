import React, { useState, useEffect } from 'react';
import { Typo<PERSON>, Row, Col, Statistic, Progress, Space, Button, Avatar } from 'antd';
import {
  DashboardOutlined,
  BarChartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  UserOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import EnhancedAssanLayout from '../components/layout/EnhancedAssanLayout';
import EnhancedCard from '../components/common/EnhancedCard';
import authService from '../services/authService';

const { Title, Text } = Typography;

const EnhancedDashboard = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const currentUser = authService.getUser();
    setUser(currentUser);
    
    // 模拟数据加载
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // 统计数据
  const stats = [
    {
      title: '总产量',
      value: 2847,
      suffix: '件',
      precision: 0,
      valueStyle: { color: '#667eea' },
      prefix: <BarChartOutlined />,
      trend: { value: 12.5, isUp: true }
    },
    {
      title: '生产效率',
      value: 98.5,
      suffix: '%',
      precision: 1,
      valueStyle: { color: '#764ba2' },
      prefix: <LineChartOutlined />,
      trend: { value: 3.2, isUp: true }
    },
    {
      title: '质量合格率',
      value: 99.2,
      suffix: '%',
      precision: 1,
      valueStyle: { color: '#667eea' },
      prefix: <CheckCircleOutlined />,
      trend: { value: 0.8, isUp: true }
    },
    {
      title: '设备利用率',
      value: 87.3,
      suffix: '%',
      precision: 1,
      valueStyle: { color: '#764ba2' },
      prefix: <DashboardOutlined />,
      trend: { value: 2.1, isUp: false }
    }
  ];

  // 快速操作
  const quickActions = [
    { icon: <BarChartOutlined />, title: '生产报告', color: '#667eea' },
    { icon: <TeamOutlined />, title: '人员管理', color: '#764ba2' },
    { icon: <PieChartOutlined />, title: '数据分析', color: '#667eea' },
    { icon: <WarningOutlined />, title: '异常监控', color: '#764ba2' }
  ];

  // 最近活动
  const recentActivities = [
    { time: '10:30', action: '生产线A完成日产量目标', type: 'success' },
    { time: '09:15', action: '设备维护计划已更新', type: 'info' },
    { time: '08:45', action: '质量检测报告已生成', type: 'success' },
    { time: '08:20', action: '新员工培训已安排', type: 'warning' }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <EnhancedAssanLayout>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        style={{ padding: '0 24px' }}
      >
        {/* 欢迎区域 */}
        <motion.div variants={itemVariants}>
          <EnhancedCard 
            style={{ marginBottom: '24px' }}
            bodyStyle={{ padding: '32px' }}
          >
            <Row align="middle" justify="space-between">
              <Col>
                <Space size="large" align="center">
                  <Avatar 
                    size={64} 
                    icon={<UserOutlined />}
                    style={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      boxShadow: '0 8px 24px rgba(102, 126, 234, 0.3)'
                    }}
                  />
                  <div>
                    <Title level={2} style={{ margin: 0, color: '#1a202c' }}>
                      欢迎回来，{user?.username || '管理员'}！
                    </Title>
                    <Text style={{ color: '#64748b', fontSize: '16px' }}>
                      今天是 {new Date().toLocaleDateString('zh-CN', { 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric',
                        weekday: 'long'
                      })}
                    </Text>
                  </div>
                </Space>
              </Col>
              <Col>
                <Space>
                  <Button 
                    type="primary" 
                    size="large"
                    style={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      border: 'none',
                      borderRadius: '12px',
                      height: '48px',
                      padding: '0 24px',
                      fontWeight: '600',
                      boxShadow: '0 4px 16px rgba(102, 126, 234, 0.3)'
                    }}
                  >
                    查看详细报告
                  </Button>
                </Space>
              </Col>
            </Row>
          </EnhancedCard>
        </motion.div>

        {/* 统计卡片 */}
        <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
          {stats.map((stat, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <motion.div variants={itemVariants}>
                <EnhancedCard hoverable>
                  <Statistic
                    title={stat.title}
                    value={stat.value}
                    suffix={stat.suffix}
                    precision={stat.precision}
                    valueStyle={stat.valueStyle}
                    prefix={stat.prefix}
                  />
                  <div style={{ marginTop: '12px', display: 'flex', alignItems: 'center' }}>
                    {stat.trend.isUp ? (
                      <ArrowUpOutlined style={{ color: '#22c55e', marginRight: '4px' }} />
                    ) : (
                      <ArrowDownOutlined style={{ color: '#ef4444', marginRight: '4px' }} />
                    )}
                    <Text 
                      style={{ 
                        color: stat.trend.isUp ? '#22c55e' : '#ef4444',
                        fontSize: '14px',
                        fontWeight: '500'
                      }}
                    >
                      {stat.trend.value}% 较上月
                    </Text>
                  </div>
                </EnhancedCard>
              </motion.div>
            </Col>
          ))}
        </Row>

        <Row gutter={[24, 24]}>
          {/* 快速操作 */}
          <Col xs={24} lg={8}>
            <motion.div variants={itemVariants}>
              <EnhancedCard title="快速操作" style={{ height: '400px' }}>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  {quickActions.map((action, index) => (
                    <motion.div
                      key={index}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div
                        style={{
                          padding: '16px',
                          borderRadius: '12px',
                          background: `linear-gradient(135deg, ${action.color}15, ${action.color}05)`,
                          border: `1px solid ${action.color}20`,
                          cursor: 'pointer',
                          transition: 'all 0.3s ease'
                        }}
                      >
                        <Space>
                          <div
                            style={{
                              width: '40px',
                              height: '40px',
                              borderRadius: '10px',
                              background: `linear-gradient(135deg, ${action.color}, ${action.color}dd)`,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'white',
                              fontSize: '18px'
                            }}
                          >
                            {action.icon}
                          </div>
                          <Text style={{ fontSize: '16px', fontWeight: '500', color: '#1a202c' }}>
                            {action.title}
                          </Text>
                        </Space>
                      </div>
                    </motion.div>
                  ))}
                </Space>
              </EnhancedCard>
            </motion.div>
          </Col>

          {/* 生产进度 */}
          <Col xs={24} lg={8}>
            <motion.div variants={itemVariants}>
              <EnhancedCard title="今日生产进度" style={{ height: '400px' }}>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <Text>生产线 A</Text>
                      <Text strong>85%</Text>
                    </div>
                    <Progress 
                      percent={85} 
                      strokeColor={{
                        '0%': '#667eea',
                        '100%': '#764ba2',
                      }}
                      strokeWidth={8}
                      style={{ marginBottom: '16px' }}
                    />
                  </div>
                  
                  <div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <Text>生产线 B</Text>
                      <Text strong>92%</Text>
                    </div>
                    <Progress 
                      percent={92} 
                      strokeColor={{
                        '0%': '#667eea',
                        '100%': '#764ba2',
                      }}
                      strokeWidth={8}
                      style={{ marginBottom: '16px' }}
                    />
                  </div>
                  
                  <div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <Text>生产线 C</Text>
                      <Text strong>78%</Text>
                    </div>
                    <Progress 
                      percent={78} 
                      strokeColor={{
                        '0%': '#667eea',
                        '100%': '#764ba2',
                      }}
                      strokeWidth={8}
                    />
                  </div>

                  <div style={{
                    marginTop: '24px',
                    padding: '16px',
                    borderRadius: '12px',
                    background: 'linear-gradient(135deg, #22c55e15, #22c55e05)',
                    border: '1px solid #22c55e20'
                  }}>
                    <Space>
                      <TrophyOutlined style={{ color: '#22c55e', fontSize: '20px' }} />
                      <div>
                        <Text strong style={{ color: '#1a202c' }}>目标完成度</Text>
                        <br />
                        <Text style={{ color: '#64748b' }}>今日整体进度良好</Text>
                      </div>
                    </Space>
                  </div>
                </Space>
              </EnhancedCard>
            </motion.div>
          </Col>

          {/* 最近活动 */}
          <Col xs={24} lg={8}>
            <motion.div variants={itemVariants}>
              <EnhancedCard title="最近活动" style={{ height: '400px' }}>
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  {recentActivities.map((activity, index) => (
                    <div key={index} style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
                      <div style={{
                        width: '8px',
                        height: '8px',
                        borderRadius: '50%',
                        background: activity.type === 'success' ? '#22c55e' : 
                                   activity.type === 'warning' ? '#f59e0b' : '#667eea',
                        marginTop: '6px',
                        flexShrink: 0
                      }} />
                      <div style={{ flex: 1 }}>
                        <Text style={{ fontSize: '14px', color: '#1a202c' }}>
                          {activity.action}
                        </Text>
                        <br />
                        <Text style={{ fontSize: '12px', color: '#64748b' }}>
                          {activity.time}
                        </Text>
                      </div>
                    </div>
                  ))}
                </Space>
              </EnhancedCard>
            </motion.div>
          </Col>
        </Row>
      </motion.div>
    </EnhancedAssanLayout>
  );
};

export default EnhancedDashboard;
