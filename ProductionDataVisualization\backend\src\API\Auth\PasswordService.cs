namespace API.Auth
{
    /// <summary>
    /// 密码服务实现
    /// </summary>
    public class PasswordService : IPasswordService
    {
        /// <summary>
        /// 哈希密码
        /// </summary>
        public string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        /// <summary>
        /// 验证密码
        /// </summary>
        public bool VerifyPassword(string password, string passwordHash)
        {
            return BCrypt.Net.BCrypt.Verify(password, passwordHash);
        }
    }
} 