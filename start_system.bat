@echo off
title 生产数据可视化系统启动

echo ==========================================
echo   生产数据可视化系统启动
echo ==========================================
echo.

echo [1] 检查后端状态...
curl -s http://localhost:5000/api/health >nul 2>&1
if %errorlevel% equ 0 (
    echo    后端API: ✓ 正在运行
) else (
    echo    后端API: ✗ 未运行，正在启动...
    echo.
    echo    启动SQLite后端...
    cd /d "SimpleBackend\bin\Release\net8.0\win-x64\publish"
    start /B SimpleBackend.exe
    cd /d "%~dp0"
    
    echo    等待后端启动...
    timeout /t 8 /nobreak >nul
    
    curl -s http://localhost:5000/api/health >nul 2>&1
    if %errorlevel% equ 0 (
        echo    后端API: ✓ 启动成功
    ) else (
        echo    后端API: ✗ 启动失败
    )
)

echo.
echo [2] 检查前端状态...
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo    前端应用: ✓ 正在运行
) else (
    echo    前端应用: ✗ 未运行，正在启动...
    echo.
    echo    启动React前端...
    cd /d "ProductionDataVisualization\frontend"
    start /B npm start
    cd /d "%~dp0"
    
    echo    等待前端启动...
    timeout /t 15 /nobreak >nul
    
    curl -s http://localhost:3000 >nul 2>&1
    if %errorlevel% equ 0 (
        echo    前端应用: ✓ 启动成功
    ) else (
        echo    前端应用: ✗ 启动失败
    )
)

echo.
echo [3] 系统状态检查...
echo.

echo 测试后端API...
curl -s http://localhost:5000/api/health
echo.
echo.

echo 测试数据库连接...
curl -s http://localhost:5000/api/health/database
echo.
echo.

echo 测试用户API...
curl -s http://localhost:5000/api/simple-auth/users
echo.
echo.

echo ==========================================
echo   系统启动完成
echo ==========================================
echo.
echo 访问地址:
echo   前端应用: http://localhost:3000
echo   后端API:  http://localhost:5000
echo.
echo 默认登录:
echo   用户名: admin
echo   密码: admin123
echo.
echo 正在打开浏览器...
start http://localhost:3000
echo.

pause
