@echo off
title Node.js Complete Setup and Test

echo ==========================================
echo   Node.js Installation Test and Setup
echo ==========================================
echo.

REM Test 1: Check if Node.js files exist
echo [Test 1] Checking Node.js installation files...
if exist "D:\Programs\nodejs\node.exe" (
    echo [SUCCESS] node.exe found
) else (
    echo [ERROR] node.exe not found
    goto :error
)

if exist "D:\Programs\nodejs\npm.cmd" (
    echo [SUCCESS] npm.cmd found
) else (
    echo [ERROR] npm.cmd not found
    goto :error
)

echo.

REM Test 2: Test direct execution
echo [Test 2] Testing direct execution...
echo Node.js version:
"D:\Programs\nodejs\node.exe" --version
echo.
echo npm version:
"D:\Programs\nodejs\npm.cmd" --version
echo.

REM Test 3: Add to current session PATH
echo [Test 3] Adding to current session PATH...
set "PATH=D:\Programs\nodejs;%PATH%"
echo [INFO] Added D:\Programs\nodejs to current session PATH
echo.

REM Test 4: Test commands in current session
echo [Test 4] Testing commands in current session...
echo Testing 'node --version':
node --version
if %errorlevel% equ 0 (
    echo [SUCCESS] node command works in current session
) else (
    echo [WARNING] node command failed in current session
)

echo.
echo Testing 'npm --version':
npm --version
if %errorlevel% equ 0 (
    echo [SUCCESS] npm command works in current session
) else (
    echo [WARNING] npm command failed in current session
)

echo.

REM Test 5: Configure user PATH permanently
echo [Test 5] Configuring user PATH permanently...
echo [INFO] Adding Node.js to user PATH environment variable...

REM Use PowerShell to add to user PATH
powershell -Command "& {$userPath = [Environment]::GetEnvironmentVariable('PATH', 'User'); if ($userPath -eq $null) {$userPath = ''}; if ($userPath -notlike '*D:\Programs\nodejs*') {$newPath = if ($userPath -eq '') {'D:\Programs\nodejs'} else {$userPath + ';D:\Programs\nodejs'}; [Environment]::SetEnvironmentVariable('PATH', $newPath, 'User'); Write-Host '[SUCCESS] Added to user PATH'} else {Write-Host '[INFO] Already in user PATH'}}"

echo.

REM Test 6: Verify PATH configuration
echo [Test 6] Verifying PATH configuration...
powershell -Command "& {$userPath = [Environment]::GetEnvironmentVariable('PATH', 'User'); if ($userPath -like '*D:\Programs\nodejs*') {Write-Host '[SUCCESS] Node.js found in user PATH'} else {Write-Host '[WARNING] Node.js not found in user PATH'}}"

echo.

REM Test 7: Test project dependencies
echo [Test 7] Testing project dependencies installation...
if exist "ProductionDataVisualization\frontend\package.json" (
    echo [INFO] Found frontend package.json
    echo [INFO] Testing npm install in frontend directory...
    cd ProductionDataVisualization\frontend
    npm --version > nul 2>&1
    if %errorlevel% equ 0 (
        echo [SUCCESS] npm is available for frontend project
    ) else (
        echo [WARNING] npm not available for frontend project
    )
    cd ..\..
) else (
    echo [INFO] Frontend package.json not found, skipping frontend test
)

echo.

REM Summary
echo ==========================================
echo   Setup Summary
echo ==========================================
echo.
echo Installation Status:
echo - Node.js executable: FOUND
echo - npm executable: FOUND
echo - Direct execution: WORKING
echo - Current session PATH: CONFIGURED
echo - User PATH: CONFIGURED
echo.
echo Next Steps:
echo 1. Close this command prompt
echo 2. Open a NEW command prompt
echo 3. Test with: node --version
echo 4. Test with: npm --version
echo 5. Navigate to project and run: npm install
echo.
echo If commands still don't work in new prompt:
echo - Restart your computer
echo - Or manually add D:\Programs\nodejs to system PATH
echo.

goto :end

:error
echo.
echo [ERROR] Node.js installation not found or incomplete
echo Please reinstall Node.js to D:\Programs\nodejs\
echo.

:end
echo Press any key to exit...
pause > nul
