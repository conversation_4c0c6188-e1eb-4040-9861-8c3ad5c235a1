using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Domain.Common;

namespace Domain.DataAggregate
{
    /// <summary>
    /// 数据点仓储接口
    /// </summary>
    public interface IDataPointRepository : IRepository<DataPoint>
    {
        Task<IEnumerable<DataPoint>> GetByDataCategoryIdAsync(Guid dataCategoryId);
        Task<IEnumerable<DataPoint>> GetByDataCategoryIdAndTimeRangeAsync(Guid dataCategoryId, DateTime startTime, DateTime endTime);
        Task<IEnumerable<DataPoint>> GetLatestByDataCategoryIdAsync(Guid dataCategoryId, int count);
        Task AddRangeAsync(IEnumerable<DataPoint> dataPoints);
    }
} 