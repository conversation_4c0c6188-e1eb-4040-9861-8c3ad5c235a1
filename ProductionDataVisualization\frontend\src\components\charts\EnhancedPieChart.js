import React, { useEffect, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import { Spin } from 'antd';
import PropTypes from 'prop-types';

/**
 * EnhancedPieChart - 增强的饼图组件
 * 
 * 特性：
 * - 现代简约浅色主题配色
 * - 动画加载效果
 * - 响应式设计
 * - 自定义配置选项
 * - 支持环形图和普通饼图
 */
const EnhancedPieChart = ({
  data,
  title,
  subtitle,
  loading = false,
  height = '300px',
  darkMode = false,
  animation = true,
  showLegend = true,
  legendPosition = 'right',
  doughnut = false,
  radius = ['50%', '70%'],
  centerPosition = ['50%', '50%'],
  customOptions = {}
}) => {
  const [chartOptions, setChartOptions] = useState({});
  
  // 现代简约黑白灰主题配色
  const themeColors = {
    light: {
      primary: '#333333',
      secondary: '#666666',
      tertiary: '#999999',
      quaternary: '#555555',
      accent: '#777777',
      background: '#FFFFFF',
      textPrimary: '#333333',
      textSecondary: '#666666',
      borderColor: '#E0E0E0',
      // 饼图配色方案 - 黑白灰色系
      pieColors: [
        '#333333', // 深灰色
        '#666666', // 中灰色
        '#999999', // 浅灰色
        '#555555', // 中深灰色
        '#777777', // 中浅灰色
        '#444444', // 深灰色变体
        '#888888', // 浅灰色变体
        '#AAAAAA', // 更浅灰色
        '#222222', // 近黑色
        '#CCCCCC'  // 极浅灰色
      ],
      // 渐变色
      gradients: {
        main: ['#333333', 'rgba(51, 51, 51, 0.5)'],
        secondary: ['#666666', 'rgba(102, 102, 102, 0.5)'],
        tertiary: ['#999999', 'rgba(153, 153, 153, 0.5)'],
        quaternary: ['#555555', 'rgba(85, 85, 85, 0.5)'],
        accent: ['#777777', 'rgba(119, 119, 119, 0.5)'],
        dark: ['#222222', 'rgba(34, 34, 34, 0.5)'],
        light: ['#AAAAAA', 'rgba(170, 170, 170, 0.5)']
      }
    },
    dark: {
      primary: '#CCCCCC',
      secondary: '#999999',
      tertiary: '#777777',
      quaternary: '#AAAAAA',
      accent: '#888888',
      background: '#222222',
      textPrimary: '#FFFFFF',
      textSecondary: '#CCCCCC',
      borderColor: 'rgba(153, 153, 153, 0.2)',
      // 饼图配色方案 - 黑白灰色系
      pieColors: [
        '#CCCCCC', // 浅灰色
        '#999999', // 中灰色
        '#777777', // 深灰色
        '#AAAAAA', // 浅灰色变体
        '#888888', // 中灰色变体
        '#DDDDDD', // 更浅灰色
        '#666666', // 更深灰色
        '#BBBBBB', // 中浅灰色
        '#555555', // 深灰色变体
        '#EEEEEE'  // 近白色
      ],
      // 渐变色
      gradients: {
        main: ['#CCCCCC', 'rgba(204, 204, 204, 0.5)'],
        secondary: ['#999999', 'rgba(153, 153, 153, 0.5)'],
        tertiary: ['#777777', 'rgba(119, 119, 119, 0.5)'],
        quaternary: ['#AAAAAA', 'rgba(170, 170, 170, 0.5)'],
        accent: ['#888888', 'rgba(136, 136, 136, 0.5)'],
        dark: ['#666666', 'rgba(102, 102, 102, 0.5)'],
        light: ['#EEEEEE', 'rgba(238, 238, 238, 0.5)']
      }
    }
  };
  
  // 选择主题
  const theme = darkMode ? themeColors.dark : themeColors.light;
  
  // 生成图表配置
  useEffect(() => {
    // 基础配置
    const baseOptions = {
      backgroundColor: theme.background,
      title: {
        text: title,
        subtext: subtitle,
        left: 'center',
        top: 0,
        textStyle: {
          color: theme.textPrimary,
          fontWeight: 500,
          fontFamily: 'Inter, sans-serif'
        },
        subtextStyle: {
          color: theme.textSecondary,
          fontFamily: 'Inter, sans-serif'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: 'rgba(226, 232, 240, 0.8)',
        textStyle: {
          color: theme.textPrimary
        },
        extraCssText: 'box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05); border-radius: 8px;'
      },
      legend: {
        show: showLegend,
        top: legendPosition === 'top' ? '5%' : 'auto',
        bottom: legendPosition === 'bottom' ? '5%' : 'auto',
        left: legendPosition === 'left' ? '5%' : 'auto',
        right: legendPosition === 'right' ? '5%' : 'auto',
        orient: ['left', 'right'].includes(legendPosition) ? 'vertical' : 'horizontal',
        textStyle: {
          color: theme.textSecondary,
          fontFamily: 'Inter, sans-serif'
        },
        itemGap: 20,
        icon: 'circle',
        itemWidth: 10,
        itemHeight: 10
      },
      series: [{
        name: title || '数据',
        type: 'pie',
        radius: doughnut ? radius : '70%',
        center: centerPosition,
        data: data || [],
        itemStyle: {
          borderWidth: 2,
          borderColor: theme.background,
          borderRadius: 4,
          // 使用渐变色
          color: function(params) {
            // 获取渐变类型
            const gradientTypes = Object.keys(theme.gradients);
            const gradientType = gradientTypes[params.dataIndex % gradientTypes.length];
            const gradientColors = theme.gradients[gradientType];
            
            return {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: gradientColors[0]
              }, {
                offset: 1,
                color: gradientColors[1]
              }]
            };
          }
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(51, 51, 51, 0.3)',
            borderWidth: 3,
            borderColor: 'rgba(51, 51, 51, 0.5)'
          },
          label: {
            show: true,
            fontWeight: 'bold',
            fontSize: 14,
            color: theme.textPrimary
          }
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{d}%',
          color: theme.textPrimary,
          fontSize: 12,
          fontWeight: 'normal'
        },
        labelLine: {
          length: 10,
          length2: 10,
          lineStyle: {
            color: 'rgba(204, 204, 204, 0.6)'
          }
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function(idx) {
          return animation ? idx * 100 : 0;
        }
      }]
    };
    
    // 合并自定义选项
    const finalOptions = {
      ...baseOptions,
      ...customOptions
    };
    
    setChartOptions(finalOptions);
  }, [data, title, subtitle, darkMode, animation, showLegend, legendPosition, doughnut, radius, centerPosition, customOptions, theme]);
  
  return (
    <div style={{ position: 'relative', height }}>
      {loading ? (
        <div style={{ 
          position: 'absolute', 
          top: 0, 
          left: 0, 
          right: 0, 
          bottom: 0, 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          background: 'rgba(255, 255, 255, 0.7)',
          borderRadius: '8px',
          zIndex: 10
        }}>
          <Spin size="large" />
        </div>
      ) : null}
      <ReactECharts
        option={chartOptions}
        style={{ height: '100%', width: '100%' }}
        opts={{ renderer: 'canvas' }}
      />
    </div>
  );
};

EnhancedPieChart.propTypes = {
  data: PropTypes.array,
  title: PropTypes.string,
  subtitle: PropTypes.string,
  loading: PropTypes.bool,
  height: PropTypes.string,
  darkMode: PropTypes.bool,
  animation: PropTypes.bool,
  showLegend: PropTypes.bool,
  legendPosition: PropTypes.oneOf(['top', 'bottom', 'left', 'right']),
  doughnut: PropTypes.bool,
  radius: PropTypes.array,
  centerPosition: PropTypes.array,
  customOptions: PropTypes.object
};

export default EnhancedPieChart; 