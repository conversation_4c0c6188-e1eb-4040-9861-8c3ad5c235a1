@echo off
title PATH Configuration Test

echo ==========================================
echo   PATH Configuration Test
echo ==========================================
echo.

echo [1] Current session PATH:
echo %PATH%
echo.

echo [2] Checking if Node.js path is in current PATH:
echo %PATH% | findstr /i "nodejs"
if %errorlevel% equ 0 (
    echo [SUCCESS] Node.js path found in current PATH
) else (
    echo [WARNING] Node.js path NOT found in current PATH
)
echo.

echo [3] Testing direct Node.js execution:
"D:\Programs\nodejs\node.exe" --version
if %errorlevel% equ 0 (
    echo [SUCCESS] Direct Node.js execution works
) else (
    echo [ERROR] Direct Node.js execution failed
)
echo.

echo [4] Testing node command (should work if PATH is correct):
node --version
if %errorlevel% equ 0 (
    echo [SUCCESS] node command works - PATH is correctly configured!
) else (
    echo [INFO] node command failed - PA<PERSON> needs refresh
    echo [INFO] This is normal if you just configured PATH
)
echo.

echo [5] Testing npm command:
npm --version
if %errorlevel% equ 0 (
    echo [SUCCESS] npm command works - PATH is correctly configured!
) else (
    echo [INFO] npm command failed - PATH needs refresh
)
echo.

echo ==========================================
echo   Recommendation
echo ==========================================
echo.
if %errorlevel% equ 0 (
    echo Your PATH configuration is working perfectly!
    echo Node.js and npm are ready to use.
) else (
    echo Your PATH is configured in system settings, but this
    echo command session is using the old PATH.
    echo.
    echo Please do ONE of the following:
    echo 1. Close this window and open a NEW command prompt
    echo 2. Restart your computer
    echo 3. Log out and log back in
    echo.
    echo Then test again with: node --version
)
echo.

pause
