using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Domain.Common;

namespace Domain.DataAggregate
{
    /// <summary>
    /// 数据源仓储接口
    /// </summary>
    public interface IDataSourceRepository : IRepository<DataSource>
    {
        Task<DataSource?> GetByNameAsync(string name);
        Task<IEnumerable<DataSource>> GetByImportedByAsync(Guid userId);
        Task<IEnumerable<DataSource>> GetRecentAsync(int count);
        Task<IEnumerable<DataSource>> GetBySourceTypeAsync(string sourceType);
    }
} 