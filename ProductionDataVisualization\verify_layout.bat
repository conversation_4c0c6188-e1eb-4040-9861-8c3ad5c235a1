@echo off
title 验证布局修复

echo ========================================
echo   验证布局修复
echo ========================================
echo.

echo [INFO] 检查修改的文件...

echo [INFO] 检查 DataImportPage.js...
findstr /C:"CleanAssanLayout" frontend\src\pages\DataImportPage.js
if %errorlevel% equ 0 (
    echo [SUCCESS] DataImportPage.js 已正确导入布局
) else (
    echo [ERROR] DataImportPage.js 布局导入失败
)

echo.
echo [INFO] 检查 DataVisualizationPage.js...
findstr /C:"CleanAssanLayout" frontend\src\pages\DataVisualizationPage.js
if %errorlevel% equ 0 (
    echo [SUCCESS] DataVisualizationPage.js 已正确导入布局
) else (
    echo [ERROR] DataVisualizationPage.js 布局导入失败
)

echo.
echo [INFO] 检查布局组件是否存在...
if exist "frontend\src\components\layout\CleanAssanLayout.js" (
    echo [SUCCESS] CleanAssanLayout.js 存在
) else (
    echo [ERROR] CleanAssanLayout.js 不存在
)

echo.
echo [INFO] 尝试编译检查...
cd frontend
npm run build --silent

if %errorlevel% equ 0 (
    echo [SUCCESS] 编译成功，布局修复完成
) else (
    echo [ERROR] 编译失败，请检查错误
)

echo.
echo ========================================
echo   验证完成
echo ========================================

pause
