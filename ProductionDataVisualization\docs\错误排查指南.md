# 生产数据可视化系统 - 错误排查指南

## 常见启动问题

### 1. 无法访问网站（显示ERR_CONNECTION_REFUSED）

**可能原因：**
- 前端React服务未正常启动
- 端口3000被其他程序占用
- Node.js/npm未正确安装或版本不兼容

**解决方法：**
1. 检查Node.js和npm是否正确安装：
   ```bash
   node -v
   npm -v
   ```
   
2. 检查前端依赖是否安装：
   ```bash
   cd ProductionDataVisualization\frontend
   npm install
   ```
   
3. 检查端口3000是否被占用：
   ```bash
   netstat -ano | findstr :3000
   ```
   
4. 尝试手动启动前端服务：
   ```bash
   cd ProductionDataVisualization\frontend
   npm start
   ```
   
5. 检查前端日志查找错误信息

### 2. 后端API无法连接

**可能原因：**
- 后端.NET服务未正常启动
- 端口5000被其他程序占用
- .NET SDK未正确安装或版本不兼容

**解决方法：**
1. 检查.NET SDK是否正确安装：
   ```bash
   dotnet --version
   ```
   
2. 检查端口5000是否被占用：
   ```bash
   netstat -ano | findstr :5000
   ```
   
3. 尝试手动启动后端服务：
   ```bash
   cd ProductionDataVisualization\backend\src\API
   dotnet run --urls=http://localhost:5000
   ```
   
4. 检查后端日志查找错误信息

### 3. 数据库连接失败

**可能原因：**
- SQL Server服务未启动
- 数据库连接字符串错误
- 数据库不存在或权限不足

**解决方法：**
1. 检查SQL Server服务是否运行：
   ```bash
   net start | findstr /C:"SQL Server (MSSQLSERVER)"
   ```
   
2. 尝试启动SQL Server服务：
   ```bash
   net start MSSQLSERVER
   ```
   
3. 检查连接字符串是否正确（appsettings.json文件）

## 启动脚本执行问题

### 1. start.bat脚本无法执行

**可能原因：**
- 脚本权限问题
- 路径中包含中文或特殊字符
- 脚本被杀毒软件阻止

**解决方法：**
1. 右键点击脚本，选择"以管理员身份运行"
2. 尝试使用`快速启动.bat`脚本
3. 如果脚本内容显示乱码，请确保使用UTF-8编码保存脚本

### 2. PowerShell脚本无法执行

**可能原因：**
- PowerShell执行策略限制
- 脚本未签名
- npm无法在PowerShell中执行

**解决方法：**
1. 使用我们提供的解决PowerShell限制脚本：
   ```bash
   解决PowerShell限制.bat
   ```
   
2. 或者手动以管理员身份打开PowerShell，执行以下命令：
   ```powershell
   Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned
   ```
   
3. 如果仍然无法执行PowerShell脚本，请尝试使用纯CMD模式启动：
   ```bash
   cmd启动.bat
   ```

## 运行时问题

### 1. 前端显示空白或报错

**可能原因：**
- JavaScript错误
- React组件加载失败
- API连接问题

**解决方法：**
1. 打开浏览器控制台（F12）查看错误信息
2. 检查后端API是否正常运行
3. 刷新页面或清除浏览器缓存

### 2. 前端无法连接后端API

**可能原因：**
- CORS设置不正确
- 后端API URL配置错误
- API服务未运行

**解决方法：**
1. 确认`.env`文件中API URL配置正确：
   ```
   REACT_APP_API_URL=http://localhost:5000
   ```
   
2. 检查后端API CORS设置是否正确
3. 使用浏览器访问`http://localhost:5000/health`测试API是否可用

### 3. 性能问题

**可能原因：**
- 硬件资源不足
- 数据量过大
- 代码优化不足

**解决方法：**
1. 确保机器有足够内存和CPU资源
2. 减少一次性加载的数据量
3. 使用分页和懒加载技术

## 如何获取帮助

如果以上步骤无法解决您的问题，请：

1. 运行诊断脚本获取系统信息：
   ```bash
   cd ProductionDataVisualization\scripts
   diagnose.bat
   ```
   
2. 将诊断结果和错误截图提供给技术支持团队

3. 联系系统管理员寻求进一步帮助 