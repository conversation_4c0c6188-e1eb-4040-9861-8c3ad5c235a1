import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Alert, 
  Button, 
  Space, 
  Typography, 
  Tabs, 
  Form, 
  Select, 
  Input, 
  InputNumber,
  Switch,
  Progress,
  Tag,
  Modal,
  Tooltip
} from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  SettingOutlined,
  ClearOutlined,
  BugOutlined,
  FilterOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

const DataValidator = ({ data, headers, dataTypes, onValidationComplete, onDataClean }) => {
  const [validationRules, setValidationRules] = useState({});
  const [validationResults, setValidationResults] = useState([]);
  const [cleaningRules, setCleaningRules] = useState({});
  const [cleanedData, setCleanedData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('validation');

  // 默认验证规则
  const defaultRules = {
    required: false,
    minLength: null,
    maxLength: null,
    pattern: '',
    minValue: null,
    maxValue: null,
    allowedValues: [],
    dateFormat: 'YYYY-MM-DD',
    customRule: ''
  };

  // 初始化验证规则
  useEffect(() => {
    if (headers && headers.length > 0) {
      const initialRules = {};
      headers.forEach(header => {
        initialRules[header] = { ...defaultRules };
      });
      setValidationRules(initialRules);
    }
  }, [headers]);

  // 数据验证函数
  const validateData = async () => {
    setLoading(true);
    const results = [];
    
    try {
      data.forEach((row, rowIndex) => {
        headers.forEach(header => {
          const value = row[header];
          const rules = validationRules[header];
          const dataType = dataTypes[header];
          const errors = [];

          // 必填验证
          if (rules.required && (value === '' || value == null || value === undefined)) {
            errors.push('字段不能为空');
          }

          // 如果值为空且非必填，跳过其他验证
          if (!rules.required && (value === '' || value == null || value === undefined)) {
            return;
          }

          // 字符串长度验证
          if (dataType === 'string') {
            if (rules.minLength && value.length < rules.minLength) {
              errors.push(`长度不能少于${rules.minLength}个字符`);
            }
            if (rules.maxLength && value.length > rules.maxLength) {
              errors.push(`长度不能超过${rules.maxLength}个字符`);
            }
          }

          // 数值范围验证
          if ((dataType === 'number' || dataType === 'integer') && !isNaN(Number(value))) {
            const numValue = Number(value);
            if (rules.minValue !== null && numValue < rules.minValue) {
              errors.push(`值不能小于${rules.minValue}`);
            }
            if (rules.maxValue !== null && numValue > rules.maxValue) {
              errors.push(`值不能大于${rules.maxValue}`);
            }
          }

          // 正则表达式验证
          if (rules.pattern && rules.pattern.trim()) {
            try {
              const regex = new RegExp(rules.pattern);
              if (!regex.test(value)) {
                errors.push('格式不符合要求');
              }
            } catch (e) {
              errors.push('正则表达式格式错误');
            }
          }

          // 允许值验证
          if (rules.allowedValues && rules.allowedValues.length > 0) {
            if (!rules.allowedValues.includes(value)) {
              errors.push(`值必须是: ${rules.allowedValues.join(', ')}`);
            }
          }

          // 日期格式验证
          if (dataType === 'date') {
            if (isNaN(Date.parse(value))) {
              errors.push('日期格式不正确');
            }
          }

          // 自定义规则验证
          if (rules.customRule && rules.customRule.trim()) {
            try {
              // 简单的自定义规则执行（安全考虑，这里只是示例）
              const customFunction = new Function('value', 'row', 'rowIndex', rules.customRule);
              const customResult = customFunction(value, row, rowIndex);
              if (!customResult) {
                errors.push('不符合自定义规则');
              }
            } catch (e) {
              errors.push('自定义规则执行错误');
            }
          }

          // 记录错误
          if (errors.length > 0) {
            results.push({
              row: rowIndex + 1,
              column: header,
              value,
              errors,
              severity: rules.required && (value === '' || value == null) ? 'error' : 'warning'
            });
          }
        });
      });

      setValidationResults(results);
      
      if (onValidationComplete) {
        onValidationComplete(results);
      }

    } catch (error) {
      console.error('Validation error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 数据清洗函数
  const cleanData = () => {
    setLoading(true);
    
    try {
      const cleaned = data.map((row, rowIndex) => {
        const cleanedRow = { ...row };
        
        headers.forEach(header => {
          let value = cleanedRow[header];
          const rules = cleaningRules[header] || {};
          
          // 去除空白字符
          if (rules.trimWhitespace && typeof value === 'string') {
            value = value.trim();
          }
          
          // 转换大小写
          if (rules.caseTransform && typeof value === 'string') {
            switch (rules.caseTransform) {
              case 'upper':
                value = value.toUpperCase();
                break;
              case 'lower':
                value = value.toLowerCase();
                break;
              case 'title':
                value = value.replace(/\w\S*/g, (txt) => 
                  txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
                );
                break;
            }
          }
          
          // 替换值
          if (rules.replaceValue && rules.replaceValue.from && rules.replaceValue.to !== undefined) {
            if (value === rules.replaceValue.from) {
              value = rules.replaceValue.to;
            }
          }
          
          // 数值格式化
          if ((dataTypes[header] === 'number' || dataTypes[header] === 'integer') && !isNaN(Number(value))) {
            if (rules.numberFormat) {
              const num = Number(value);
              if (rules.numberFormat.decimals !== undefined) {
                value = num.toFixed(rules.numberFormat.decimals);
              }
            }
          }
          
          // 日期格式化
          if (dataTypes[header] === 'date' && !isNaN(Date.parse(value))) {
            if (rules.dateFormat) {
              const date = new Date(value);
              // 简单的日期格式化（实际项目中建议使用moment.js或date-fns）
              if (rules.dateFormat === 'YYYY-MM-DD') {
                value = date.toISOString().split('T')[0];
              }
            }
          }
          
          cleanedRow[header] = value;
        });
        
        return cleanedRow;
      });
      
      setCleanedData(cleaned);
      
      if (onDataClean) {
        onDataClean(cleaned);
      }
      
    } catch (error) {
      console.error('Data cleaning error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 更新验证规则
  const updateValidationRule = (header, field, value) => {
    setValidationRules(prev => ({
      ...prev,
      [header]: {
        ...prev[header],
        [field]: value
      }
    }));
  };

  // 更新清洗规则
  const updateCleaningRule = (header, field, value) => {
    setCleaningRules(prev => ({
      ...prev,
      [header]: {
        ...prev[header],
        [field]: value
      }
    }));
  };

  // 生成验证结果表格列
  const validationColumns = [
    {
      title: '行号',
      dataIndex: 'row',
      key: 'row',
      width: 80,
      sorter: (a, b) => a.row - b.row
    },
    {
      title: '列名',
      dataIndex: 'column',
      key: 'column',
      width: 120,
      filters: headers.map(h => ({ text: h, value: h })),
      onFilter: (value, record) => record.column === value
    },
    {
      title: '值',
      dataIndex: 'value',
      key: 'value',
      width: 150,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <Text code>{text}</Text>
        </Tooltip>
      )
    },
    {
      title: '错误信息',
      dataIndex: 'errors',
      key: 'errors',
      render: (errors, record) => (
        <Space direction="vertical" size="small">
          {errors.map((error, index) => (
            <Tag 
              key={index} 
              color={record.severity === 'error' ? 'red' : 'orange'}
              icon={record.severity === 'error' ? <CloseCircleOutlined /> : <ExclamationCircleOutlined />}
            >
              {error}
            </Tag>
          ))}
        </Space>
      )
    }
  ];

  // 统计信息
  const getValidationStats = () => {
    const errors = validationResults.filter(r => r.severity === 'error').length;
    const warnings = validationResults.filter(r => r.severity === 'warning').length;
    const totalRows = data ? data.length : 0;
    const validRows = totalRows - new Set(validationResults.map(r => r.row)).size;
    
    return { errors, warnings, totalRows, validRows };
  };

  const stats = getValidationStats();

  return (
    <div className="data-validator">
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={<><BugOutlined />数据验证</>} key="validation">
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              {/* 验证统计 */}
              <Card size="small">
                <Space>
                  <Text>总行数: {stats.totalRows}</Text>
                  <Text type="success">有效行数: {stats.validRows}</Text>
                  <Text type="danger">错误: {stats.errors}</Text>
                  <Text type="warning">警告: {stats.warnings}</Text>
                  <Progress 
                    percent={stats.totalRows > 0 ? Math.round((stats.validRows / stats.totalRows) * 100) : 0}
                    size="small"
                    status={stats.errors > 0 ? 'exception' : 'success'}
                  />
                </Space>
              </Card>

              {/* 验证操作 */}
              <Space>
                <Button type="primary" onClick={validateData} loading={loading}>
                  执行验证
                </Button>
                <Button onClick={() => setValidationResults([])}>
                  清除结果
                </Button>
              </Space>

              {/* 验证结果 */}
              {validationResults.length > 0 && (
                <Table
                  columns={validationColumns}
                  dataSource={validationResults}
                  rowKey={(record, index) => `${record.row}-${record.column}-${index}`}
                  pagination={{
                    pageSize: 50,
                    showSizeChanger: true,
                    showTotal: (total) => `共 ${total} 个问题`
                  }}
                  size="small"
                  scroll={{ y: 400 }}
                />
              )}
            </Space>
          </TabPane>

          <TabPane tab={<><SettingOutlined />验证规则</>} key="rules">
            <div style={{ maxHeight: '600px', overflow: 'auto' }}>
              {headers.map(header => (
                <Card key={header} size="small" style={{ marginBottom: 16 }}>
                  <Title level={5}>{header} ({dataTypes[header]})</Title>
                  <Form layout="inline" size="small">
                    <Form.Item label="必填">
                      <Switch
                        checked={validationRules[header]?.required}
                        onChange={(checked) => updateValidationRule(header, 'required', checked)}
                      />
                    </Form.Item>
                    
                    {dataTypes[header] === 'string' && (
                      <>
                        <Form.Item label="最小长度">
                          <InputNumber
                            min={0}
                            value={validationRules[header]?.minLength}
                            onChange={(value) => updateValidationRule(header, 'minLength', value)}
                          />
                        </Form.Item>
                        <Form.Item label="最大长度">
                          <InputNumber
                            min={0}
                            value={validationRules[header]?.maxLength}
                            onChange={(value) => updateValidationRule(header, 'maxLength', value)}
                          />
                        </Form.Item>
                      </>
                    )}
                    
                    {(dataTypes[header] === 'number' || dataTypes[header] === 'integer') && (
                      <>
                        <Form.Item label="最小值">
                          <InputNumber
                            value={validationRules[header]?.minValue}
                            onChange={(value) => updateValidationRule(header, 'minValue', value)}
                          />
                        </Form.Item>
                        <Form.Item label="最大值">
                          <InputNumber
                            value={validationRules[header]?.maxValue}
                            onChange={(value) => updateValidationRule(header, 'maxValue', value)}
                          />
                        </Form.Item>
                      </>
                    )}
                    
                    <Form.Item label="正则表达式">
                      <Input
                        placeholder="如: ^[A-Za-z0-9]+$"
                        value={validationRules[header]?.pattern}
                        onChange={(e) => updateValidationRule(header, 'pattern', e.target.value)}
                      />
                    </Form.Item>
                  </Form>
                </Card>
              ))}
            </div>
          </TabPane>

          <TabPane tab={<><ClearOutlined />数据清洗</>} key="cleaning">
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              {/* 清洗操作 */}
              <Space>
                <Button type="primary" onClick={cleanData} loading={loading}>
                  执行清洗
                </Button>
                <Button onClick={() => setCleanedData(null)}>
                  重置数据
                </Button>
              </Space>

              {/* 清洗规则配置 */}
              <div style={{ maxHeight: '400px', overflow: 'auto' }}>
                {headers.map(header => (
                  <Card key={header} size="small" style={{ marginBottom: 16 }}>
                    <Title level={5}>{header}</Title>
                    <Form layout="inline" size="small">
                      <Form.Item label="去除空白">
                        <Switch
                          checked={cleaningRules[header]?.trimWhitespace}
                          onChange={(checked) => updateCleaningRule(header, 'trimWhitespace', checked)}
                        />
                      </Form.Item>
                      
                      {dataTypes[header] === 'string' && (
                        <Form.Item label="大小写转换">
                          <Select
                            style={{ width: 120 }}
                            value={cleaningRules[header]?.caseTransform}
                            onChange={(value) => updateCleaningRule(header, 'caseTransform', value)}
                            allowClear
                          >
                            <Option value="upper">大写</Option>
                            <Option value="lower">小写</Option>
                            <Option value="title">标题格式</Option>
                          </Select>
                        </Form.Item>
                      )}
                    </Form>
                  </Card>
                ))}
              </div>

              {/* 清洗结果预览 */}
              {cleanedData && (
                <Alert
                  message="数据清洗完成"
                  description={`已处理 ${cleanedData.length} 行数据`}
                  type="success"
                  showIcon
                />
              )}
            </Space>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default DataValidator;
