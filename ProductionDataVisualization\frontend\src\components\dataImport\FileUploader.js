import React, { useState } from 'react';
import { Upload, Button, Progress, Alert, Card, Typography, Space, Tag, List, Modal } from 'antd';
import {
  InboxOutlined,
  FileTextOutlined,
  FileExcelOutlined,
  DeleteOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';

const { Dragger } = Upload;
const { Title, Text } = Typography;

const FileUploader = ({ 
  onFileUpload, 
  onFileRemove, 
  maxFileSize = 100, // MB
  supportedFormats = ['txt', 'csv', 'xlsx', 'xls'],
  multiple = true,
  showPreview = true 
}) => {
  const [fileList, setFileList] = useState([]);
  const [uploadProgress, setUploadProgress] = useState({});
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState(null);
  const [validationErrors, setValidationErrors] = useState([]);

  // 文件类型图标映射
  const getFileIcon = (fileName) => {
    const ext = fileName.split('.').pop().toLowerCase();
    switch (ext) {
      case 'xlsx':
      case 'xls':
        return <FileExcelOutlined style={{ color: '#52c41a' }} />;
      case 'csv':
      case 'txt':
        return <FileTextOutlined style={{ color: '#1890ff' }} />;
      default:
        return <FileTextOutlined style={{ color: '#666' }} />;
    }
  };

  // 文件大小格式化
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 文件验证
  const validateFile = (file) => {
    const errors = [];
    
    // 检查文件大小
    if (file.size > maxFileSize * 1024 * 1024) {
      errors.push(`文件大小超过限制 (${maxFileSize}MB)`);
    }
    
    // 检查文件格式
    const ext = file.name.split('.').pop().toLowerCase();
    if (!supportedFormats.includes(ext)) {
      errors.push(`不支持的文件格式，支持格式: ${supportedFormats.join(', ')}`);
    }
    
    // 检查文件名
    if (file.name.length > 100) {
      errors.push('文件名过长');
    }
    
    return errors;
  };

  // 文件上传前处理
  const beforeUpload = (file) => {
    const errors = validateFile(file);
    
    if (errors.length > 0) {
      setValidationErrors(prev => [...prev, { file: file.name, errors }]);
      return false;
    }
    
    // 清除之前的错误
    setValidationErrors(prev => prev.filter(item => item.file !== file.name));
    
    return true;
  };

  // 自定义上传处理
  const customRequest = async ({ file, onProgress, onSuccess, onError }) => {
    try {
      setUploadProgress(prev => ({ ...prev, [file.uid]: 0 }));
      
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const currentProgress = prev[file.uid] || 0;
          if (currentProgress >= 100) {
            clearInterval(progressInterval);
            return prev;
          }
          const newProgress = Math.min(currentProgress + Math.random() * 30, 100);
          onProgress({ percent: newProgress });
          return { ...prev, [file.uid]: newProgress };
        });
      }, 200);

      // 调用父组件的上传处理函数
      if (onFileUpload) {
        await onFileUpload(file, (progress) => {
          setUploadProgress(prev => ({ ...prev, [file.uid]: progress }));
          onProgress({ percent: progress });
        });
      }

      setTimeout(() => {
        clearInterval(progressInterval);
        setUploadProgress(prev => ({ ...prev, [file.uid]: 100 }));
        onSuccess(file);
      }, 1000);

    } catch (error) {
      console.error('Upload error:', error);
      onError(error);
    }
  };

  // 文件列表变化处理
  const handleChange = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };

  // 文件移除处理
  const handleRemove = (file) => {
    if (onFileRemove) {
      onFileRemove(file);
    }
    setUploadProgress(prev => {
      const newProgress = { ...prev };
      delete newProgress[file.uid];
      return newProgress;
    });
  };

  // 文件预览
  const handlePreview = (file) => {
    setPreviewFile(file);
    setPreviewVisible(true);
  };

  // 拖拽上传属性
  const uploadProps = {
    name: 'file',
    multiple,
    fileList,
    beforeUpload,
    customRequest,
    onChange: handleChange,
    onRemove: handleRemove,
    onPreview: showPreview ? handlePreview : undefined,
    showUploadList: {
      showPreviewIcon: showPreview,
      showRemoveIcon: true,
      showDownloadIcon: false,
    },
  };

  return (
    <div className="file-uploader">
      <Card>
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          <div>
            <Title level={4}>文件上传</Title>
            <Text type="secondary">
              支持格式: {supportedFormats.join(', ')} | 最大文件大小: {maxFileSize}MB
            </Text>
          </div>

          <Dragger {...uploadProps} style={{ padding: '20px' }}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持单个或批量上传。严禁上传公司数据或其他敏感文件。
            </p>
          </Dragger>

          {/* 验证错误显示 */}
          <AnimatePresence>
            {validationErrors.length > 0 && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
              >
                <Alert
                  message="文件验证错误"
                  type="error"
                  showIcon
                  description={
                    <List
                      size="small"
                      dataSource={validationErrors}
                      renderItem={item => (
                        <List.Item>
                          <Text strong>{item.file}:</Text>
                          <ul style={{ margin: 0, paddingLeft: '20px' }}>
                            {item.errors.map((error, index) => (
                              <li key={index}>{error}</li>
                            ))}
                          </ul>
                        </List.Item>
                      )}
                    />
                  }
                  closable
                  onClose={() => setValidationErrors([])}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* 文件列表 */}
          {fileList.length > 0 && (
            <Card size="small" title="上传文件列表">
              <List
                dataSource={fileList}
                renderItem={file => (
                  <List.Item
                    actions={[
                      showPreview && (
                        <Button 
                          type="link" 
                          icon={<EyeOutlined />} 
                          onClick={() => handlePreview(file)}
                        >
                          预览
                        </Button>
                      ),
                      <Button 
                        type="link" 
                        danger 
                        icon={<DeleteOutlined />} 
                        onClick={() => handleRemove(file)}
                      >
                        删除
                      </Button>
                    ].filter(Boolean)}
                  >
                    <List.Item.Meta
                      avatar={getFileIcon(file.name)}
                      title={
                        <Space>
                          <Text>{file.name}</Text>
                          {file.status === 'done' && (
                            <Tag color="success" icon={<CheckCircleOutlined />}>
                              上传完成
                            </Tag>
                          )}
                          {file.status === 'error' && (
                            <Tag color="error" icon={<ExclamationCircleOutlined />}>
                              上传失败
                            </Tag>
                          )}
                        </Space>
                      }
                      description={
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Text type="secondary">{formatFileSize(file.size)}</Text>
                          {uploadProgress[file.uid] !== undefined && uploadProgress[file.uid] < 100 && (
                            <Progress 
                              percent={Math.round(uploadProgress[file.uid])} 
                              size="small"
                              status={file.status === 'error' ? 'exception' : 'active'}
                            />
                          )}
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          )}
        </Space>
      </Card>

      {/* 文件预览模态框 */}
      <Modal
        title="文件预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width={800}
      >
        {previewFile && (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Card size="small">
              <Space>
                {getFileIcon(previewFile.name)}
                <div>
                  <Text strong>{previewFile.name}</Text>
                  <br />
                  <Text type="secondary">{formatFileSize(previewFile.size)}</Text>
                </div>
              </Space>
            </Card>
            <Alert
              message="预览功能"
              description="文件预览功能正在开发中，将支持CSV、TXT文件的内容预览。"
              type="info"
              showIcon
            />
          </Space>
        )}
      </Modal>
    </div>
  );
};

export default FileUploader;
