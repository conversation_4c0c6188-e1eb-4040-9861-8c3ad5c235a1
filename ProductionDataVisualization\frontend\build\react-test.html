<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>React渲染测试</title>
  <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <!-- 使用jwt-decode的UMD版本 -->
  <script src="https://unpkg.com/jwt-decode@4.0.0/build/jwt-decode.umd.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f0f2f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    button {
      background-color: #1890ff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin: 10px 5px;
    }
    button:hover {
      background-color: #40a9ff;
    }
    .success {
      color: green;
      padding: 10px;
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
      border-radius: 4px;
      margin: 10px 0;
    }
    .error {
      color: red;
      padding: 10px;
      background-color: #fff2f0;
      border: 1px solid #ffccc7;
      border-radius: 4px;
      margin: 10px 0;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow: auto;
      max-height: 300px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>React渲染测试</h1>
    <p>这个页面用于测试React渲染，特别是对象和数组的渲染。</p>
    
    <div id="root"></div>
  </div>

  <script type="text/babel">
    // 测试组件
    function RenderTest() {
      const [result, setResult] = React.useState(null);
      const [error, setError] = React.useState(null);
      
      // 测试1：渲染字符串和数字
      const testPrimitives = () => {
        setResult('这是一个字符串，数字是：' + 42);
        setError(null);
      };
      
      // 测试2：渲染数组
      const testArray = () => {
        try {
          const arr = ['项目1', '项目2', '项目3'];
          setResult(
            <div>
              <p>数组渲染正确方式：</p>
              <ul>
                {arr.map((item, index) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </div>
          );
          setError(null);
        } catch (err) {
          setError('数组渲染失败：' + err.message);
        }
      };
      
      // 测试3：渲染对象（错误方式）
      const testObjectWrong = () => {
        try {
          const obj = { name: '测试', value: 123 };
          // 这会导致错误
          setResult(<div>错误渲染：{obj}</div>);
          setError(null);
        } catch (err) {
          setError('对象渲染失败（预期错误）：' + err.message);
          setResult(null);
        }
      };
      
      // 测试4：渲染对象（正确方式）
      const testObjectCorrect = () => {
        try {
          const obj = { name: '测试', value: 123 };
          setResult(
            <div>
              <p>对象渲染正确方式：</p>
              <p>名称：{obj.name}</p>
              <p>值：{obj.value}</p>
              <p>JSON字符串：{JSON.stringify(obj)}</p>
            </div>
          );
          setError(null);
        } catch (err) {
          setError('对象渲染失败：' + err.message);
        }
      };
      
      // 测试5：渲染null和undefined
      const testNullUndefined = () => {
        try {
          const nullValue = null;
          const undefinedValue = undefined;
          setResult(
            <div>
              <p>null渲染为：'{nullValue}'</p>
              <p>undefined渲染为：'{undefinedValue}'</p>
              <p>条件渲染null：{nullValue && '这不会显示'}</p>
              <p>条件渲染undefined：{undefinedValue && '这不会显示'}</p>
              <p>空字符串：{''}（看不见）</p>
            </div>
          );
          setError(null);
        } catch (err) {
          setError('null/undefined渲染失败：' + err.message);
        }
      };
      
      // 测试6：JWT解码测试
      const testJwtDecode = () => {
        try {
          const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkFkbWluIFVzZXIiLCJpYXQiOjE1MTYyMzkwMjIsImV4cCI6MTkxNjIzOTAyMn0.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
          
          // 使用jwtDecode函数
          const decoded = jwtDecode(testToken);
          
          setResult(
            <div>
              <p>JWT解码成功：</p>
              <p>名称：{decoded.name}</p>
              <p>过期时间：{new Date(decoded.exp * 1000).toLocaleString()}</p>
              <pre>{JSON.stringify(decoded, null, 2)}</pre>
            </div>
          );
          setError(null);
        } catch (err) {
          setError('JWT解码失败：' + err.message);
        }
      };
      
      return (
        <div>
          <div>
            <button onClick={testPrimitives}>测试1：基本类型</button>
            <button onClick={testArray}>测试2：数组</button>
            <button onClick={testObjectWrong}>测试3：对象（错误方式）</button>
            <button onClick={testObjectCorrect}>测试4：对象（正确方式）</button>
            <button onClick={testNullUndefined}>测试5：null和undefined</button>
            <button onClick={testJwtDecode}>测试6：JWT解码</button>
          </div>
          
          {error && (
            <div className="error">
              <h3>错误</h3>
              <p>{error}</p>
            </div>
          )}
          
          {result && (
            <div className="success">
              <h3>结果</h3>
              {result}
            </div>
          )}
        </div>
      );
    }

    // 确保DOM已完全加载后再渲染
    document.addEventListener('DOMContentLoaded', function() {
      const rootElement = document.getElementById('root');
      if (rootElement) {
        const root = ReactDOM.createRoot(rootElement);
        root.render(<RenderTest />);
      } else {
        console.error('找不到ID为root的DOM元素');
      }
    });
  </script>
</body>
</html> 