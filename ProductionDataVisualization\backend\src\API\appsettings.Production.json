{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Server=.;Database=ProductionDataVisualizationDb;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "JwtSettings": {"SecretKey": "your-production-secret-key-should-be-very-secure-and-at-least-32-characters-long", "Issuer": "ProductionDataVisualization", "Audience": "ProductionDataVisualization-Users", "ExpirationMinutes": 30}, "RateLimiting": {"WindowMinutes": 1, "MaxRequests": 100}, "Performance": {"EnableDetailedLogging": false, "SlowRequestThreshold": 3000, "EnableMetrics": true}, "Features": {"EnableSwagger": false, "EnableDetailedErrors": false, "EnableCors": true}}