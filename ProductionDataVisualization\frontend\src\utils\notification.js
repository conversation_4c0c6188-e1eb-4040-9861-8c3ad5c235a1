import { notification, message } from 'antd';
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  InfoCircleOutlined, 
  CloseCircleOutlined 
} from '@ant-design/icons';

/**
 * 通知类型
 */
export const NotificationTypes = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

/**
 * 通知管理器
 */
class NotificationManager {
  constructor() {
    this.defaultConfig = {
      placement: 'topRight',
      duration: 4.5,
      maxCount: 5
    };
    
    // 配置全局通知样式
    notification.config(this.defaultConfig);
  }

  /**
   * 显示成功通知
   * @param {string} title - 标题
   * @param {string} description - 描述
   * @param {Object} options - 额外选项
   */
  success(title, description = '', options = {}) {
    notification.success({
      message: title,
      description,
      icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      ...options
    });
  }

  /**
   * 显示错误通知
   * @param {string} title - 标题
   * @param {string} description - 描述
   * @param {Object} options - 额外选项
   */
  error(title, description = '', options = {}) {
    notification.error({
      message: title,
      description,
      icon: <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
      duration: 0, // 错误通知不自动关闭
      ...options
    });
  }

  /**
   * 显示警告通知
   * @param {string} title - 标题
   * @param {string} description - 描述
   * @param {Object} options - 额外选项
   */
  warning(title, description = '', options = {}) {
    notification.warning({
      message: title,
      description,
      icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
      ...options
    });
  }

  /**
   * 显示信息通知
   * @param {string} title - 标题
   * @param {string} description - 描述
   * @param {Object} options - 额外选项
   */
  info(title, description = '', options = {}) {
    notification.info({
      message: title,
      description,
      icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />,
      ...options
    });
  }

  /**
   * 显示加载通知
   * @param {string} title - 标题
   * @param {string} description - 描述
   * @param {Object} options - 额外选项
   * @returns {Function} 关闭函数
   */
  loading(title, description = '', options = {}) {
    const key = `loading_${Date.now()}`;
    
    notification.open({
      key,
      message: title,
      description,
      icon: <div className="ant-spin ant-spin-sm">
        <span className="ant-spin-dot ant-spin-dot-spin">
          <i className="ant-spin-dot-item"></i>
          <i className="ant-spin-dot-item"></i>
          <i className="ant-spin-dot-item"></i>
          <i className="ant-spin-dot-item"></i>
        </span>
      </div>,
      duration: 0,
      ...options
    });

    return () => notification.close(key);
  }

  /**
   * 显示进度通知
   * @param {string} title - 标题
   * @param {number} percent - 进度百分比
   * @param {Object} options - 额外选项
   * @returns {Function} 更新函数
   */
  progress(title, percent = 0, options = {}) {
    const key = `progress_${Date.now()}`;
    
    const updateProgress = (newPercent, newTitle = title) => {
      notification.open({
        key,
        message: newTitle,
        description: (
          <div>
            <div style={{ marginBottom: '8px' }}>
              进度: {Math.round(newPercent)}%
            </div>
            <div style={{ 
              width: '100%', 
              height: '6px', 
              backgroundColor: '#f0f0f0', 
              borderRadius: '3px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${newPercent}%`,
                height: '100%',
                backgroundColor: '#1890ff',
                transition: 'width 0.3s ease'
              }} />
            </div>
          </div>
        ),
        duration: 0,
        ...options
      });
    };

    updateProgress(percent);

    return {
      update: updateProgress,
      close: () => notification.close(key),
      complete: (successTitle = '完成') => {
        notification.close(key);
        this.success(successTitle);
      }
    };
  }

  /**
   * 关闭所有通知
   */
  closeAll() {
    notification.destroy();
  }

  /**
   * 关闭指定通知
   * @param {string} key - 通知键
   */
  close(key) {
    notification.close(key);
  }

  /**
   * 更新配置
   * @param {Object} config - 新配置
   */
  config(config) {
    this.defaultConfig = { ...this.defaultConfig, ...config };
    notification.config(this.defaultConfig);
  }
}

/**
 * 消息管理器
 */
class MessageManager {
  constructor() {
    // 配置全局消息样式
    message.config({
      duration: 3,
      maxCount: 3,
      top: 100
    });
  }

  /**
   * 显示成功消息
   * @param {string} content - 消息内容
   * @param {number} duration - 持续时间
   */
  success(content, duration = 3) {
    message.success(content, duration);
  }

  /**
   * 显示错误消息
   * @param {string} content - 消息内容
   * @param {number} duration - 持续时间
   */
  error(content, duration = 5) {
    message.error(content, duration);
  }

  /**
   * 显示警告消息
   * @param {string} content - 消息内容
   * @param {number} duration - 持续时间
   */
  warning(content, duration = 4) {
    message.warning(content, duration);
  }

  /**
   * 显示信息消息
   * @param {string} content - 消息内容
   * @param {number} duration - 持续时间
   */
  info(content, duration = 3) {
    message.info(content, duration);
  }

  /**
   * 显示加载消息
   * @param {string} content - 消息内容
   * @param {number} duration - 持续时间，0表示不自动关闭
   * @returns {Function} 关闭函数
   */
  loading(content, duration = 0) {
    return message.loading(content, duration);
  }

  /**
   * 销毁所有消息
   */
  destroy() {
    message.destroy();
  }
}

// 创建全局实例
const notificationManager = new NotificationManager();
const messageManager = new MessageManager();

/**
 * 便捷的通知方法
 */
export const notify = {
  success: (title, description, options) => notificationManager.success(title, description, options),
  error: (title, description, options) => notificationManager.error(title, description, options),
  warning: (title, description, options) => notificationManager.warning(title, description, options),
  info: (title, description, options) => notificationManager.info(title, description, options),
  loading: (title, description, options) => notificationManager.loading(title, description, options),
  progress: (title, percent, options) => notificationManager.progress(title, percent, options),
  closeAll: () => notificationManager.closeAll(),
  close: (key) => notificationManager.close(key),
  config: (config) => notificationManager.config(config)
};

/**
 * 便捷的消息方法
 */
export const msg = {
  success: (content, duration) => messageManager.success(content, duration),
  error: (content, duration) => messageManager.error(content, duration),
  warning: (content, duration) => messageManager.warning(content, duration),
  info: (content, duration) => messageManager.info(content, duration),
  loading: (content, duration) => messageManager.loading(content, duration),
  destroy: () => messageManager.destroy()
};

/**
 * API错误处理通知
 * @param {Object} error - 错误对象
 * @param {string} defaultMessage - 默认错误消息
 */
export const handleApiError = (error, defaultMessage = '操作失败') => {
  const errorMessage = error?.message || error?.data?.message || defaultMessage;
  const errorDetails = error?.data?.details || error?.response?.data?.details;
  
  if (errorDetails) {
    notify.error('操作失败', errorDetails);
  } else {
    msg.error(errorMessage);
  }
};

/**
 * API成功处理通知
 * @param {string} message - 成功消息
 * @param {string} description - 详细描述
 */
export const handleApiSuccess = (message = '操作成功', description = '') => {
  if (description) {
    notify.success(message, description);
  } else {
    msg.success(message);
  }
};

export default {
  notify,
  msg,
  handleApiError,
  handleApiSuccess,
  NotificationTypes
};
