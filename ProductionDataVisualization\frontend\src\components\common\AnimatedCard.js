import React, { useState } from 'react';
import { Card } from 'antd';
import { motion } from 'framer-motion';
import PropTypes from 'prop-types';

/**
 * AnimatedCard - 具有动画效果的卡片组件
 * 
 * 特性：
 * - 悬停时的提升效果
 * - 进入视图时的动画
 * - 可自定义动画类型和延迟
 * - 支持所有Ant Design Card的属性
 */
const AnimatedCard = ({ 
  children, 
  hoverEffect = true,
  animationType = 'fade-up',
  animationDelay = 0,
  className = '',
  style = {},
  ...cardProps 
}) => {
  const [isHovered, setIsHovered] = useState(false);
  
  // 动画变体配置
  const animationVariants = {
    hidden: {
      opacity: 0,
      y: animationType.includes('up') ? 20 : 
         animationType.includes('down') ? -20 : 0,
      x: animationType.includes('left') ? 20 : 
         animationType.includes('right') ? -20 : 0,
      scale: animationType.includes('zoom') ? 0.95 : 1,
    },
    visible: {
      opacity: 1,
      y: 0,
      x: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.1, 0.25, 1],
        delay: animationDelay,
      }
    }
  };
  
  // 悬停效果配置
  const hoverVariants = {
    idle: {
      y: 0,
      boxShadow: 'var(--shadow-md)',
      transition: {
        duration: 0.3,
        ease: [0.25, 0.1, 0.25, 1],
      }
    },
    hover: {
      y: -8,
      boxShadow: 'var(--shadow-lg)',
      transition: {
        duration: 0.3,
        ease: [0.25, 0.1, 0.25, 1],
      }
    }
  };
  
  // 卡片样式
  const cardStyle = {
    overflow: 'hidden',
    borderRadius: 'var(--radius-md)',
    border: '1px solid var(--border-color)',
    background: 'var(--bg-base)',
    ...style
  };
  
  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={animationVariants}
      className={`animated-card ${className}`}
      style={{ width: '100%' }}
    >
      <motion.div
        variants={hoverEffect ? hoverVariants : {}}
        initial="idle"
        animate={isHovered ? 'hover' : 'idle'}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <Card
          {...cardProps}
          className={`animated-card-inner ${cardProps.className || ''}`}
          style={cardStyle}
        >
          {children}
        </Card>
      </motion.div>
    </motion.div>
  );
};

AnimatedCard.propTypes = {
  children: PropTypes.node,
  hoverEffect: PropTypes.bool,
  animationType: PropTypes.oneOf([
    'fade', 'fade-up', 'fade-down', 'fade-left', 'fade-right', 
    'zoom', 'zoom-up', 'zoom-down'
  ]),
  animationDelay: PropTypes.number,
  className: PropTypes.string,
  style: PropTypes.object,
};

export default AnimatedCard; 