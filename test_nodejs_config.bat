@echo off
chcp 65001 > nul
title Node.js 安装测试和环境配置

echo ========================================
echo   Node.js 安装测试和环境配置
echo ========================================
echo.

echo [1] 检查Node.js安装路径...
if exist "D:\Programs\nodejs\node.exe" (
    echo [成功] Node.js已安装在: D:\Programs\nodejs\
) else (
    echo [错误] 未找到Node.js安装文件
    pause
    exit /b 1
)

echo.
echo [2] 测试Node.js直接运行...
"D:\Programs\nodejs\node.exe" --version
if %errorlevel% equ 0 (
    echo [成功] Node.js可以正常运行
) else (
    echo [错误] Node.js运行失败
)

echo.
echo [3] 测试npm直接运行...
"D:\Programs\nodejs\npm.cmd" --version
if %errorlevel% equ 0 (
    echo [成功] npm可以正常运行
) else (
    echo [错误] npm运行失败
)

echo.
echo [4] 检查当前PATH环境变量...
echo 当前PATH包含以下路径:
echo %PATH% | findstr /i "nodejs"
if %errorlevel% equ 0 (
    echo [成功] PATH中已包含Node.js路径
) else (
    echo [警告] PATH中未包含Node.js路径
)

echo.
echo [5] 临时添加Node.js到PATH (当前会话)...
set PATH=D:\Programs\nodejs;%PATH%
echo [信息] 已临时添加Node.js到PATH

echo.
echo [6] 测试临时PATH配置...
node --version
if %errorlevel% equ 0 (
    echo [成功] 临时PATH配置有效，node命令可用
) else (
    echo [错误] 临时PATH配置失败
)

npm --version
if %errorlevel% equ 0 (
    echo [成功] npm命令可用
) else (
    echo [错误] npm命令不可用
)

echo.
echo [7] 永久添加到系统PATH...
echo [信息] 正在永久添加Node.js到系统PATH...

REM 使用PowerShell添加到系统PATH
powershell -Command "& {$oldPath = [Environment]::GetEnvironmentVariable('PATH', 'Machine'); if ($oldPath -notlike '*D:\Programs\nodejs*') { $newPath = $oldPath + ';D:\Programs\nodejs'; [Environment]::SetEnvironmentVariable('PATH', $newPath, 'Machine'); Write-Host '[成功] 已添加到系统PATH' } else { Write-Host '[信息] PATH中已存在Node.js路径' }}"

echo.
echo [8] 验证系统PATH更新...
echo [信息] 请重新打开命令提示符窗口以使PATH更改生效
echo [信息] 或者运行以下命令刷新环境变量:
echo   refreshenv
echo.

echo ========================================
echo   配置完成！
echo ========================================
echo.
echo 测试结果:
echo - Node.js安装路径: D:\Programs\nodejs\
echo - 直接运行测试: 已完成
echo - PATH配置: 已更新
echo.
echo 下一步:
echo 1. 重新打开命令提示符
echo 2. 运行 'node --version' 验证
echo 3. 运行 'npm --version' 验证
echo.

pause
