using Domain.VisualizationAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 图表实体配置
    /// </summary>
    public class ChartConfiguration : IEntityTypeConfiguration<Chart>
    {
        public void Configure(EntityTypeBuilder<Chart> builder)
        {
            builder.ToTable("Charts");

            builder.HasKey(c => c.Id);

            builder.Property(c => c.Title)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(c => c.Description)
                .HasMaxLength(500);

            builder.Property(c => c.ChartType)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(c => c.CreatedBy)
                .IsRequired();

            builder.Property(c => c.IsPublic)
                .IsRequired();

            builder.Property(c => c.ConfigJson)
                .HasColumnType("nvarchar(max)");

            builder.Property(c => c.CreatedAt)
                .IsRequired();

            builder.Property(c => c.ModifiedAt);

            builder.Property(c => c.CreatedBy)
                .HasMaxLength(50);

            builder.Property(c => c.ModifiedBy)
                .HasMaxLength(50);

            // 索引
            builder.HasIndex(c => c.CreatedBy);
            builder.HasIndex(c => c.ChartType);
            builder.HasIndex(c => c.IsPublic);
        }
    }
} 