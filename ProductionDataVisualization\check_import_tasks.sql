-- 检查导入任务表和数据
USE ProductionDataVisualizationDb;

-- 1. 检查ImportTasks表是否存在
SELECT '检查ImportTasks表:' AS Status;
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME = 'ImportTasks';

-- 2. 如果表存在，查看表结构
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'ImportTasks')
BEGIN
    SELECT '表结构:' AS Status;
    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ImportTasks'
    ORDER BY ORDINAL_POSITION;
    
    -- 3. 查看所有导入任务记录
    SELECT '导入任务记录:' AS Status;
    SELECT Id, FileName, FileSize, TotalRows, ProcessedRows, Status, Progress,
           CreatedAt, StartedAt, CompletedAt, ErrorMessage
    FROM ImportTasks
    ORDER BY CreatedAt DESC;
    
    -- 4. 统计任务状态
    SELECT '任务状态统计:' AS Status;
    SELECT Status, COUNT(*) as Count
    FROM ImportTasks
    GROUP BY Status;
END
ELSE
BEGIN
    PRINT 'ImportTasks表不存在！';
END

-- 5. 检查FileTableMappings表
SELECT '文件表映射:' AS Status;
SELECT Id, FileName, TableName, CreatedAt 
FROM FileTableMappings 
ORDER BY CreatedAt DESC;

-- 6. 检查所有Data_开头的表
SELECT '数据表列表:' AS Status;
SELECT TABLE_NAME, 
       (SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = t.TABLE_NAME) as ColumnCount
FROM INFORMATION_SCHEMA.TABLES t
WHERE TABLE_TYPE = 'BASE TABLE' 
  AND TABLE_NAME LIKE 'Data_%'
ORDER BY TABLE_NAME;
