import React, { useState, useMemo } from 'react';
import { 
  Table, 
  Card, 
  Input, 
  Button, 
  Space, 
  Tag, 
  Tooltip, 
  Dropdown, 
  Menu,
  Alert
} from 'antd';
import { 
  SearchOutlined, 
  DownloadOutlined, 
  FilterOutlined,
  ExclamationCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { detectAnomalies, formatNumber, PHARMA_COLORS } from '../../utils/chartUtils';

const { Search } = Input;

const DataTable = ({
  data = [],
  columns = [],
  title = '数据表格',
  loading = false,
  error = null,
  enableAnomalyDetection = false,
  anomalyMethod = 'zscore',
  anomalyThreshold,
  showSearch = true,
  showExport = true,
  showFilter = true,
  pageSize = 10,
  height,
  onRowClick,
  onExport,
  ...props
}) => {
  const [searchText, setSearchText] = useState('');
  const [filteredData, setFilteredData] = useState(data);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 异常检测
  const anomaliesMap = useMemo(() => {
    if (!enableAnomalyDetection || !data.length) return new Map();

    const anomaliesMap = new Map();
    
    columns.forEach(column => {
      if (column.dataIndex && column.type === 'number') {
        const values = data.map(row => row[column.dataIndex]).filter(val => 
          typeof val === 'number' && !isNaN(val)
        );
        
        if (values.length > 0) {
          const anomalies = detectAnomalies(values, anomalyMethod, anomalyThreshold);
          anomalies.forEach(anomaly => {
            const rowIndex = data.findIndex(row => row[column.dataIndex] === anomaly.value);
            if (rowIndex !== -1) {
              if (!anomaliesMap.has(rowIndex)) {
                anomaliesMap.set(rowIndex, []);
              }
              anomaliesMap.get(rowIndex).push({
                column: column.dataIndex,
                ...anomaly
              });
            }
          });
        }
      }
    });

    return anomaliesMap;
  }, [data, columns, enableAnomalyDetection, anomalyMethod, anomalyThreshold]);

  // 处理列配置，添加异常高亮
  const enhancedColumns = useMemo(() => {
    return columns.map(column => {
      const enhancedColumn = { ...column };

      // 添加搜索功能
      if (showSearch && column.searchable !== false) {
        enhancedColumn.filterDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
          <div style={{ padding: 8 }}>
            <Input
              placeholder={`搜索 ${column.title}`}
              value={selectedKeys[0]}
              onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
              onPressEnter={() => confirm()}
              style={{ marginBottom: 8, display: 'block' }}
            />
            <Space>
              <Button
                type="primary"
                onClick={() => confirm()}
                icon={<SearchOutlined />}
                size="small"
                style={{ width: 90 }}
              >
                搜索
              </Button>
              <Button onClick={() => clearFilters()} size="small" style={{ width: 90 }}>
                重置
              </Button>
            </Space>
          </div>
        );
        enhancedColumn.filterIcon = filtered => (
          <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
        );
        enhancedColumn.onFilter = (value, record) =>
          record[column.dataIndex]?.toString().toLowerCase().includes(value.toLowerCase());
      }

      // 自定义渲染，添加异常高亮
      const originalRender = enhancedColumn.render;
      enhancedColumn.render = (text, record, index) => {
        let renderedText = text;
        
        // 格式化数值
        if (column.type === 'number' && typeof text === 'number') {
          renderedText = formatNumber(text, column.format, column.precision);
        }
        
        // 应用原始渲染函数
        if (originalRender) {
          renderedText = originalRender(text, record, index);
        }

        // 检查是否为异常数据
        const rowAnomalies = anomaliesMap.get(index);
        const columnAnomaly = rowAnomalies?.find(a => a.column === column.dataIndex);

        if (columnAnomaly) {
          const severityColor = {
            high: PHARMA_COLORS.danger,
            medium: PHARMA_COLORS.warning,
            low: PHARMA_COLORS.info
          }[columnAnomaly.severity] || PHARMA_COLORS.warning;

          return (
            <Tooltip 
              title={`异常数据 (${anomalyMethod.toUpperCase()}): ${columnAnomaly.zScore ? 
                `Z分数: ${columnAnomaly.zScore.toFixed(2)}` : '超出正常范围'}`}
            >
              <Tag 
                color={severityColor} 
                icon={<ExclamationCircleOutlined />}
                style={{ margin: 0 }}
              >
                {renderedText}
              </Tag>
            </Tooltip>
          );
        }

        return renderedText;
      };

      return enhancedColumn;
    });
  }, [columns, anomaliesMap, showSearch, anomalyMethod]);

  // 搜索功能
  const handleSearch = (value) => {
    setSearchText(value);
    if (!value) {
      setFilteredData(data);
      return;
    }

    const filtered = data.filter(row =>
      Object.values(row).some(val =>
        val?.toString().toLowerCase().includes(value.toLowerCase())
      )
    );
    setFilteredData(filtered);
  };

  // 导出功能
  const handleExport = () => {
    if (onExport) {
      onExport(filteredData);
    } else {
      // 默认导出为CSV
      const csvContent = [
        columns.map(col => col.title).join(','),
        ...filteredData.map(row =>
          columns.map(col => row[col.dataIndex] || '').join(',')
        )
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `${title}_${new Date().getTime()}.csv`;
      link.click();
    }
  };

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: record.disabled,
    }),
  };

  // 工具栏
  const renderToolbar = () => (
    <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <Space>
        {showSearch && (
          <Search
            placeholder="搜索数据..."
            allowClear
            onSearch={handleSearch}
            style={{ width: 300 }}
          />
        )}
        {showFilter && (
          <Dropdown
            overlay={
              <Menu>
                <Menu.Item key="all">显示全部</Menu.Item>
                <Menu.Item key="anomalies">仅显示异常</Menu.Item>
                <Menu.Item key="normal">仅显示正常</Menu.Item>
              </Menu>
            }
          >
            <Button icon={<FilterOutlined />}>
              筛选
            </Button>
          </Dropdown>
        )}
      </Space>
      
      <Space>
        {anomaliesMap.size > 0 && (
          <Alert
            message={`检测到 ${anomaliesMap.size} 行异常数据`}
            type="warning"
            icon={<WarningOutlined />}
            showIcon
            style={{ marginRight: 8 }}
          />
        )}
        {showExport && (
          <Button 
            icon={<DownloadOutlined />} 
            onClick={handleExport}
            disabled={filteredData.length === 0}
          >
            导出数据
          </Button>
        )}
      </Space>
    </div>
  );

  if (error) {
    return (
      <Card title={title}>
        <Alert
          message="数据加载失败"
          description={error}
          type="error"
          showIcon
        />
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card title={title} {...props}>
        {renderToolbar()}
        <Table
          columns={enhancedColumns}
          dataSource={searchText ? filteredData : data}
          loading={loading}
          rowSelection={rowSelection}
          rowKey={(record, index) => record.id || index}
          pagination={{
            pageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`,
          }}
          scroll={{ y: height }}
          onRow={(record, index) => ({
            onClick: () => onRowClick && onRowClick(record, index),
            style: {
              backgroundColor: anomaliesMap.has(index) ? '#fff2f0' : undefined,
              cursor: onRowClick ? 'pointer' : 'default'
            }
          })}
          size="small"
        />
      </Card>
    </motion.div>
  );
};

export default DataTable;
