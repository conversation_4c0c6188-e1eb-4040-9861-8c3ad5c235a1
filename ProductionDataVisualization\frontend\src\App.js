import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { ConfigProvider, theme, message } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import './App.css';
import './styles/assan.css';
import { LazyDashboard, LazyLogin, LazyRegister, LazyUserManagement, LazyAssanLogin, LazyAssanRegister } from './utils/lazyLoad';
import Debug from './pages/Debug';
import SystemStatus from './pages/SystemStatus';
import AssanDemo from './pages/AssanDemo';
import StyleComparison from './pages/StyleComparison';
import EnhancedDashboard from './pages/EnhancedDashboard';
import EnhancedUserManagement from './pages/EnhancedUserManagement';
import CleanDashboard from './pages/CleanDashboard';

import SplitScreenLogin from './pages/SplitScreenLogin';
import SimpleLogin from './pages/SimpleLogin';
import SimpleRegister from './pages/SimpleRegister';
import CleanUserManagement from './pages/CleanUserManagement';
import CleanSystemSettings from './pages/CleanSystemSettings';
import StyleShowcase from './pages/StyleShowcase';
import DataImportPage from './pages/DataImportPage';
import DataVisualizationPage from './pages/DataVisualizationPage';
import authService from './services/authService';
import ErrorBoundary from './components/common/ErrorBoundary';
import { AppProvider, useApp } from './contexts/AppContext';
import { antdThemeConfig } from './styles/assanTheme';

// 全局样式对象 - 使用现代简约黑白灰主题
const globalStyles = {
  // 标题样式
  '.heading-1, h1.ant-typography, h1': {
    fontSize: '32px',
    fontWeight: 700,
    color: '#333333',
    lineHeight: 1.2,
    letterSpacing: '-0.5px',
  },
  '.heading-2, h2.ant-typography, h2, .ant-typography h2': {
    fontSize: '28px',
    fontWeight: 600,
    color: '#333333',
    lineHeight: 1.2,
    letterSpacing: '-0.3px',
  },
  '.heading-3, h3.ant-typography, h3': {
    fontSize: '20px',
    fontWeight: 600,
    color: '#333333',
    lineHeight: 1.2,
  },
  '.heading-4, h4.ant-typography, h4': {
    fontSize: '18px',
    fontWeight: 500,
    color: '#333333',
    lineHeight: 1.2,
  },
  // 正文样式
  '.text-body, .ant-typography p': {
    fontSize: '16px',
    fontWeight: 400,
    color: '#666666',
    lineHeight: 1.5,
  },
  '.text-body-small': {
    fontSize: '14px',
    fontWeight: 400,
    color: '#666666',
    lineHeight: 1.5,
  },
  // 表格样式
  '.ant-table-thead > tr > th, .table-header': {
    fontSize: '14px',
    fontWeight: 600,
    color: '#333333',
    backgroundColor: '#E0E0E0',
  },
  '.ant-table-tbody > tr > td, .table-cell': {
    fontSize: '14px',
    fontWeight: 400,
    color: '#666666',
  },
  // 按钮样式
  '.ant-btn, .button-text': {
    fontSize: '14px',
    fontWeight: 500,
    letterSpacing: '0.3px',
  },
  '.ant-btn-primary': {
    backgroundColor: '#333333',
    borderColor: '#333333',
    color: '#FFFFFF',
    boxShadow: '0 0 10px rgba(51, 51, 51, 0.3)',
  },
  '.ant-btn-primary:hover, .ant-btn-primary:focus': {
    backgroundColor: '#555555',
    borderColor: '#555555',
    color: '#FFFFFF',
    boxShadow: '0 0 15px rgba(51, 51, 51, 0.4)',
  },
  '.ant-btn-default': {
    borderColor: '#E0E0E0',
    color: '#666666',
    background: 'transparent',
  },
  '.ant-btn-default:hover, .ant-btn-default:focus': {
    borderColor: '#333333',
    color: '#333333',
    boxShadow: '0 0 8px rgba(51, 51, 51, 0.2)',
  },
  // 菜单样式
  '.ant-menu-item, .menu-item': {
    fontSize: '16px',
    fontWeight: 500,
    color: '#666666',
  },
  '.ant-menu-item-selected, .menu-item-active': {
    fontWeight: 600,
    color: '#333333',
  },
  '.ant-menu.ant-menu-light': {
    backgroundColor: '#FFFFFF',
  },
  '.ant-menu-light .ant-menu-item-selected': {
    backgroundColor: 'rgba(51, 51, 51, 0.1)',
    color: '#333333',
    boxShadow: '0 0 10px rgba(51, 51, 51, 0.2)',
  },
  // 标签样式
  '.ant-tag': {
    fontSize: '12px',
    fontWeight: 500,
  },
  // 表单样式
  '.ant-form-item-label > label': {
    color: '#666666',
  },
  '.ant-input': {
    borderColor: '#E0E0E0',
    background: '#FFFFFF',
    color: '#333333',
  },
  '.ant-input:hover': {
    borderColor: '#333333',
  },
  '.ant-input:focus, .ant-input-focused': {
    borderColor: '#333333',
    boxShadow: '0 0 0 2px rgba(51, 51, 51, 0.2)',
    background: '#FFFFFF',
  },
  // 卡片样式
  '.ant-card': {
    borderRadius: '8px',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
    background: '#FFFFFF',
    border: '1px solid #E0E0E0',
  },
  '.ant-card-head': {
    borderBottom: '1px solid #E0E0E0',
    color: '#333333',
  },
  // 分页样式
  '.ant-pagination-item-active': {
    borderColor: '#333333',
    background: 'rgba(51, 51, 51, 0.1)',
  },
  '.ant-pagination-item-active a': {
    color: '#333333',
  },
  // 下拉菜单
  '.ant-dropdown-menu': {
    borderRadius: '8px',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
    background: '#FFFFFF',
    border: '1px solid #E0E0E0',
  },
  // 模态框
  '.ant-modal-content': {
    borderRadius: '12px',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
    background: '#FFFFFF',
    border: '1px solid #E0E0E0',
  },
  '.ant-modal-header': {
    borderBottom: '1px solid #E0E0E0',
    background: 'transparent',
  },
  // 通知提示
  '.ant-notification-notice': {
    borderRadius: '8px',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
    background: '#FFFFFF',
    border: '1px solid #E0E0E0',
  },
  // 全局背景色
  'body': {
    backgroundColor: '#F5F5F5',
    color: '#333333',
  },
  // 滚动条样式
  '::-webkit-scrollbar': {
    width: '6px',
    height: '6px',
  },
  '::-webkit-scrollbar-track': {
    background: '#E0E0E0',
  },
  '::-webkit-scrollbar-thumb': {
    background: 'rgba(51, 51, 51, 0.3)',
    borderRadius: '3px',
  },
  '::-webkit-scrollbar-thumb:hover': {
    background: '#333333',
  },
};

// 认证错误处理组件
const AuthErrorHandler = () => {
  const navigate = useNavigate();
  
  useEffect(() => {
    // 监听认证错误事件
    const handleAuthError = (event) => {
      console.log('收到认证错误事件:', event.detail);
      message.error(event.detail.message || '您的登录已过期，请重新登录');
      navigate('/login');
    };
    
    window.addEventListener('authError', handleAuthError);
    
    return () => {
      window.removeEventListener('authError', handleAuthError);
    };
  }, [navigate]);
  
  return null;
};

// 全局样式注入组件
const GlobalStylesInjector = () => {
  useEffect(() => {
    // 创建样式元素
    const styleElement = document.createElement('style');
    styleElement.type = 'text/css';
    
    // 将样式对象转换为CSS字符串
    let cssText = '';
    for (const selector in globalStyles) {
      cssText += `${selector} {\n`;
      for (const property in globalStyles[selector]) {
        cssText += `  ${property}: ${globalStyles[selector][property]} !important;\n`;
      }
      cssText += '}\n';
    }
    
    // 添加字体导入
    cssText = `@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\n\n` + cssText;
    
    // 添加全局字体设置
    cssText += `body, .ant-typography, .ant-table, .ant-btn, .ant-menu, .ant-form, .ant-modal { font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important; }\n`;
    
    // 设置样式内容
    styleElement.textContent = cssText;
    
    // 添加到文档头部
    document.head.appendChild(styleElement);
    
    console.log('全局样式已应用');
    
    // 清理函数
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);
  
  return null;
};

// 路由守卫组件 - 智能认证检查
const ProtectedRoute = ({ children }) => {
  const [isChecking, setIsChecking] = useState(true);
  const [isAuth, setIsAuth] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // 智能认证检查 - 考虑登录成功的保护窗口
    const checkAuth = () => {
      try {
        console.log('ProtectedRoute - 检查认证状态');

        // 检查是否在登录成功的保护窗口内（5秒内）
        const loginSuccessTime = sessionStorage.getItem('loginSuccessTime');
        const now = Date.now();
        const isInProtectionWindow = loginSuccessTime && (now - parseInt(loginSuccessTime)) < 5000;

        if (isInProtectionWindow) {
          console.log('ProtectedRoute - 在登录保护窗口内，跳过强制清除');
          // 检查认证状态
          const authenticated = authService.isAuthenticated();
          console.log('ProtectedRoute - 认证检查结果:', authenticated);
          setIsAuth(Boolean(authenticated));
        } else {
          console.log('ProtectedRoute - 不在保护窗口内，检查是否需要强制清除');

          // 检查是否是首次访问（没有访问标记）
          const hasVisited = sessionStorage.getItem('hasVisited');

          if (!hasVisited) {
            console.log('ProtectedRoute - 首次访问，强制清除认证缓存');

            // 清除所有认证相关的缓存
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            localStorage.removeItem('authToken');
            localStorage.removeItem('currentUser');
            localStorage.removeItem('registeredUsers');
            localStorage.removeItem('userSession');

            // 标记已访问
            sessionStorage.setItem('hasVisited', 'true');

            console.log('ProtectedRoute - 认证缓存已清除，要求重新登录');
            setIsAuth(false);
          } else {
            console.log('ProtectedRoute - 非首次访问，正常检查认证状态');
            // 正常检查认证状态
            const authenticated = authService.isAuthenticated();
            console.log('ProtectedRoute - 认证检查结果:', authenticated);
            setIsAuth(Boolean(authenticated));
          }
        }
      } catch (error) {
        console.error('认证检查失败:', error);
        setError(error.message || '认证检查过程中发生错误');
        setIsAuth(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkAuth();
  }, []);
  
  // 如果正在检查认证状态，显示加载中
  if (isChecking) {
    return <div className="auth-checking">检查认证状态...</div>;
  }
  
  // 如果发生错误，显示错误信息
  if (error) {
    return (
      <div className="auth-error" style={{ padding: 20, color: 'red' }}>
        <h3>认证错误</h3>
        <p>{error}</p>
        <button onClick={() => window.location.href = '/login'}>
          返回登录页面
        </button>
      </div>
    );
  }
  
  // 如果未认证，重定向到登录页面
  if (!isAuth) {
    return <Navigate to="/login" replace />;
  }
  
  // 已认证，渲染子组件
  return children;
};

// 主应用组件（内部）
const AppContent = () => {
  const { state } = useApp();

  // 应用启动时智能清除认证缓存
  useEffect(() => {
    console.log('应用启动 - 检查是否需要清除认证缓存');

    // 检查是否在登录保护窗口内
    const loginSuccessTime = sessionStorage.getItem('loginSuccessTime');
    const now = Date.now();
    const isInProtectionWindow = loginSuccessTime && (now - parseInt(loginSuccessTime)) < 5000;

    if (!isInProtectionWindow) {
      console.log('应用启动 - 不在保护窗口内，清除认证缓存');
      authService.forceLogout();
    } else {
      console.log('应用启动 - 在登录保护窗口内，跳过清除');
    }
  }, []);

  // 使用Assan主题配置
  const customTheme = antdThemeConfig;

  return (
    <ConfigProvider locale={zhCN} theme={customTheme}>
      <Router>
        <GlobalStylesInjector />
        <AuthErrorHandler />
        <Routes>
          {/* 调试路由 - 不需要认证 */}
          <Route path="/debug" element={<Debug />} />
          <Route path="/demo" element={<AssanDemo />} />
          <Route path="/comparison" element={<StyleComparison />} />
          <Route path="/showcase" element={<StyleShowcase />} />

          {/* 公开路由 - 简化登录风格（默认） */}
          <Route path="/login" element={<SimpleLogin />} />
          <Route path="/register" element={<SimpleRegister />} />

          {/* 其他登录风格 */}
          <Route path="/login-enhanced" element={<LazyAssanLogin />} />
          <Route path="/login-split" element={<SplitScreenLogin />} />

          {/* 原始登录注册页面（备用） */}
          <Route path="/login-original" element={<LazyLogin />} />
          <Route path="/register-original" element={<LazyRegister />} />

          {/* 根路径直接重定向到登录页面 - 强制要求登录 */}
          <Route path="/" element={<Navigate to="/login" replace />} />

          {/* 仪表板路由 - 受保护 */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <CleanDashboard />
            </ProtectedRoute>
          } />

          {/* 增强版路由 */}
          <Route path="/dashboard-enhanced" element={
            <ProtectedRoute>
              <EnhancedDashboard />
            </ProtectedRoute>
          } />

          <Route path="/users" element={
            <ProtectedRoute>
              <CleanUserManagement />
            </ProtectedRoute>
          } />

          <Route path="/settings" element={
            <ProtectedRoute>
              <CleanSystemSettings />
            </ProtectedRoute>
          } />

          {/* 增强版路由（备用） */}
          <Route path="/users-enhanced" element={
            <ProtectedRoute>
              <EnhancedUserManagement />
            </ProtectedRoute>
          } />

          {/* 原始版本路由（备用） */}
          <Route path="/dashboard-original" element={
            <ProtectedRoute>
              <LazyDashboard />
            </ProtectedRoute>
          } />
          <Route path="/users-original" element={
            <ProtectedRoute>
              <LazyUserManagement />
            </ProtectedRoute>
          } />

          <Route path="/system-status" element={
            <ProtectedRoute>
              <SystemStatus />
            </ProtectedRoute>
          } />

          <Route path="/data-import" element={
            <ProtectedRoute>
              <DataImportPage />
            </ProtectedRoute>
          } />

          <Route path="/data-visualization" element={
            <ProtectedRoute>
              <DataVisualizationPage />
            </ProtectedRoute>
          } />

          {/* 默认重定向到首页 */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
    </ConfigProvider>
  );
};

// 主应用组件（外部包装）
function App() {
  return (
    <ErrorBoundary>
      <AppProvider>
        <AppContent />
      </AppProvider>
    </ErrorBoundary>
  );
}

export default App; 