# 测试工具

本目录包含项目的测试和诊断工具。

## 🔧 工具列表

### 功能测试工具
- `test-login-live.html` - 登录功能实时测试
- `mobile-permissions-test.html` - 移动端权限测试

### 网络诊断工具
- `network-config-detector.html` - 网络配置检测器（可视化）

## 📋 使用方法

### 登录功能测试
访问 `test-login-live.html` 测试登录功能：
- 测试用户登录
- 验证JWT令牌
- 检查API连接

### 移动端测试
访问 `mobile-permissions-test.html` 测试移动端功能：
- 检查移动端权限
- 测试响应式设计
- 验证移动端API调用

### 网络配置检测
访问 `network-config-detector.html` 进行网络诊断：
- 自动检测网络配置
- 测试API连接
- 生成配置建议

## 🎯 注意事项

这些工具主要用于开发和调试阶段，生产环境中可以移除。
