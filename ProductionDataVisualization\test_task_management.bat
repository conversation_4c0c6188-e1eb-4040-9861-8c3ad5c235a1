@echo off
title 测试任务管理功能

echo ========================================
echo   测试任务管理功能
echo ========================================
echo.

echo [INFO] 新增功能:
echo ✅ 任务删除功能 - 可以删除已完成、失败或取消的任务
echo ✅ 真实数据量显示 - 显示实际导入的数据行数
echo ✅ 累加式数据计数 - 批量导入时正确累加行数
echo ✅ 删除任务API - DELETE /api/data-import/tasks/{taskId}
echo.

echo [INFO] 修复内容:
echo 1. 添加删除按钮到任务操作列
echo 2. 修复ProcessedRows累加逻辑
echo 3. 实现删除确认对话框
echo 4. 添加删除成功/失败提示
echo 5. 自动刷新任务列表
echo.

echo [INFO] 后端状态:
echo - 服务已启动: http://localhost:5000
echo - 删除API: 已添加
echo - 数据计数: 已修复
echo.

echo [INFO] 启动前端...
cd frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 20 /nobreak

echo.
echo ========================================
echo   测试步骤
echo ========================================
echo.
echo 1. 查看任务列表:
echo    - 访问: http://localhost:3000/data-import
echo    - 切换到"导入监控"页面
echo    - 查看现有的导入任务
echo.
echo 2. 测试删除功能:
echo    - 找到已完成、失败或取消的任务
echo    - 点击"删除"按钮
echo    - 确认删除对话框
echo    - 验证任务从列表中消失
echo.
echo 3. 测试数据量显示:
echo    - 导入一个新的数据文件
echo    - 观察数据量列显示的数字
echo    - 验证显示的是实际导入的行数
echo    - 不应该显示固定的1000
echo.
echo 4. 测试批量导入计数:
echo    - 导入一个大文件（超过1000行）
echo    - 观察后端日志中的进度显示
echo    - 验证最终的ProcessedRows是正确的总数
echo.
echo 5. 验证API功能:
echo    - 测试删除API: DELETE http://localhost:5000/api/data-import/tasks/{taskId}
echo    - 检查返回的响应状态
echo    - 验证数据库中的记录确实被删除
echo.

echo ========================================
echo   预期结果
echo ========================================
echo.
echo ✅ 任务列表显示删除按钮（仅对已完成/失败/取消的任务）
echo ✅ 删除功能正常工作，有确认对话框
echo ✅ 删除后任务从列表中消失
echo ✅ 数据量显示真实的导入行数
echo ✅ 批量导入时正确累加行数
echo ✅ 删除API返回正确的状态码
echo ✅ 用户体验友好，操作流畅
echo.

echo [INFO] 测试要点:
echo - 删除按钮只对非处理中的任务显示
echo - 删除前有确认对话框防止误操作
echo - 数据量不再显示误导性的1000
echo - 大文件导入时行数计算正确
echo.

pause
