import React, { useMemo } from 'react';
import BaseChart from './BaseChart';
import { generatePieChartOption } from '../../utils/chartUtils';

const PieChart = ({
  data,
  title = '饼图',
  radius = ['40%', '70%'],
  center = ['50%', '50%'],
  seriesName = '数据分布',
  showLabel = true,
  showLabelLine = true,
  labelFormatter = '{b}: {c} ({d}%)',
  height = 400,
  loading = false,
  error = null,
  onSectorClick,
  ...props
}) => {
  // 生成图表配置
  const chartOption = useMemo(() => {
    if (!data || loading || error) return null;

    const config = {
      radius,
      center,
      seriesName,
      showLabel,
      showLabelLine,
      labelFormatter
    };

    return generatePieChartOption(data, config);
  }, [data, radius, center, seriesName, showLabel, showLabelLine, 
      labelFormatter, loading, error]);

  // 事件处理
  const chartEvents = useMemo(() => {
    const events = {};

    if (onSectorClick) {
      events.click = (params) => {
        onSectorClick(params);
      };
    }

    return events;
  }, [onSectorClick]);

  return (
    <BaseChart
      title={title}
      option={chartOption}
      height={height}
      loading={loading}
      error={error}
      onEvents={chartEvents}
      {...props}
    />
  );
};

export default PieChart;
