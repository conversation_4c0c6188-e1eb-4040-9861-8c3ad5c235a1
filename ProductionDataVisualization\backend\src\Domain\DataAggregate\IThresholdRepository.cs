using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Domain.Common;

namespace Domain.DataAggregate
{
    /// <summary>
    /// 阈值仓储接口
    /// </summary>
    public interface IThresholdRepository : IRepository<Threshold>
    {
        Task<IEnumerable<Threshold>> GetByDataCategoryIdAsync(Guid dataCategoryId);
        Task<IEnumerable<Threshold>> GetActiveAsync();
    }
}