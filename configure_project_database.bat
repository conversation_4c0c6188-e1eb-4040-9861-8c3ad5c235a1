@echo off
title Project Database Configuration

echo ==========================================
echo   Project Database Configuration
echo ==========================================
echo.

echo [1] Testing current SQL Server connection...
echo.

REM Set the path to sqlcmd
set SQLCMD_PATH=C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\SQLCMD.EXE

REM Test the connection that worked
"%SQLCMD_PATH%" -S "localhost\SQLEXPRESS" -E -Q "SELECT @@VERSION" -W
if %errorlevel% equ 0 (
    echo [SUCCESS] SQL Server connection verified
    set SQL_INSTANCE=localhost\SQLEXPRESS
) else (
    echo [ERROR] SQL Server connection failed
    goto :error
)

echo.
echo [2] Creating project database...
echo.

REM Create the project database
"%SQLCMD_PATH%" -S "%SQL_INSTANCE%" -E -Q "IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'ProductionDataVisualizationDb') CREATE DATABASE ProductionDataVisualizationDb"
if %errorlevel% equ 0 (
    echo [SUCCESS] Project database created/verified
) else (
    echo [ERROR] Database creation failed
    goto :error
)

echo.
echo [3] Testing project database connection...
echo.

REM Test connection to project database
"%SQLCMD_PATH%" -S "%SQL_INSTANCE%" -E -d "ProductionDataVisualizationDb" -Q "SELECT DB_NAME() as DatabaseName, GETDATE() as CurrentTime"
if %errorlevel% equ 0 (
    echo [SUCCESS] Project database connection working
) else (
    echo [ERROR] Project database connection failed
    goto :error
)

echo.
echo [4] Testing C# API with database...
echo.

REM Test the C# API
echo Starting C# API test...
cd ProductionDataVisualization\backend\SqlServerAPI

REM Set PATH for dotnet
set "PATH=C:\Program Files\dotnet;%PATH%"

echo Testing dotnet build...
dotnet build --configuration Release --verbosity quiet
if %errorlevel% equ 0 (
    echo [SUCCESS] C# API builds successfully
) else (
    echo [WARNING] C# API build had issues
)

echo.
echo [5] Starting API server test...
echo.

REM Start the API server in background for testing
echo Starting API server for connection test...
start /b dotnet run --urls=http://localhost:5000 > ..\..\..\logs\api_test.log 2>&1

REM Wait for server to start
echo Waiting for API server to start...
timeout /t 15 /nobreak > nul

REM Test API health endpoint
echo Testing API health endpoint...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/health' -TimeoutSec 10; Write-Host '[SUCCESS] API Health Check:'; Write-Host $response.status; Write-Host $response.database } catch { Write-Host '[WARNING] API health check failed:' $_.Exception.Message }"

echo.
echo Testing database health endpoint...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/health/database' -TimeoutSec 10; Write-Host '[SUCCESS] Database Health Check:'; Write-Host $response.status; Write-Host $response.message } catch { Write-Host '[WARNING] Database health check failed:' $_.Exception.Message }"

REM Stop the test server
echo.
echo Stopping test server...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000 ^| findstr LISTENING') do (
    taskkill /F /PID %%a >nul 2>nul
)

cd ..\..\..

echo.
echo ==========================================
echo   Configuration Summary
echo ==========================================
echo.
echo [SUCCESS] Database Configuration Complete!
echo.
echo Database Details:
echo - SQL Server Instance: %SQL_INSTANCE%
echo - Project Database: ProductionDataVisualizationDb
echo - Authentication: Windows Authentication
echo - Connection Status: Verified
echo.
echo Connection String for project:
echo Server=%SQL_INSTANCE%;Database=ProductionDataVisualizationDb;Trusted_Connection=true;TrustServerCertificate=true;
echo.
echo Project Status:
echo - C# API: Ready
echo - Database: Configured
echo - Connection: Tested
echo.
echo Next Steps:
echo 1. The project is ready to run
echo 2. Use scripts\start_system.bat to start the full system
echo 3. Access the application at http://localhost:3000
echo.
goto :end

:error
echo.
echo ==========================================
echo   Configuration Error
echo ==========================================
echo.
echo Database configuration failed.
echo.
echo Please check:
echo 1. SQL Server is running
echo 2. SQL Server Express instance is accessible
echo 3. Windows Authentication is enabled
echo 4. No firewall blocking connections
echo.
echo Manual steps:
echo 1. Open SQL Server Management Studio
echo 2. Connect to localhost\SQLEXPRESS
echo 3. Create database: ProductionDataVisualizationDb
echo 4. Test connection manually
echo.

:end
echo.
echo Press any key to exit...
pause > nul
