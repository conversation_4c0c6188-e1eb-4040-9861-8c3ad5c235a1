@echo off
title SQL Server 2022 Configuration and Test

echo ==========================================
echo   SQL Server 2022 Configuration and Test
echo ==========================================
echo.

echo [1] Checking SQL Server installation...
echo.

REM Check if SQL Server service exists
sc query "MSSQL$SQLEXPRESS" >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] SQL Server Express service found
    echo Service status:
    sc query "MSSQL$SQLEXPRESS" | findstr "STATE"
) else (
    echo [WARNING] SQL Server Express service not found
    echo Checking for default instance...
    sc query "MSSQLSERVER" >nul 2>&1
    if %errorlevel% equ 0 (
        echo [SUCCESS] SQL Server default instance found
        sc query "MSSQLSERVER" | findstr "STATE"
    ) else (
        echo [ERROR] No SQL Server service found
        goto :error
    )
)

echo.
echo [2] Starting SQL Server service...
echo.

REM Start SQL Server Express service
net start "MSSQL$SQLEXPRESS" 2>nul
if %errorlevel% equ 0 (
    echo [SUCCESS] SQL Server Express started
) else (
    echo [INFO] SQL Server Express may already be running or using different name
    REM Try default instance
    net start "MSSQLSERVER" 2>nul
    if %errorlevel% equ 0 (
        echo [SUCCESS] SQL Server default instance started
    ) else (
        echo [INFO] SQL Server may already be running
    )
)

echo.
echo [3] Checking SQL Server Browser service...
echo.

REM Start SQL Server Browser (helps with named instances)
net start "SQLBrowser" 2>nul
if %errorlevel% equ 0 (
    echo [SUCCESS] SQL Server Browser started
) else (
    echo [INFO] SQL Server Browser may already be running
)

echo.
echo [4] Testing SQL Server connection...
echo.

REM Test connection using sqlcmd
echo Testing connection to localhost\SQLEXPRESS...
sqlcmd -S "localhost\SQLEXPRESS" -E -Q "SELECT @@VERSION" -W
if %errorlevel% equ 0 (
    echo [SUCCESS] Connection to SQLEXPRESS successful!
    set SQL_INSTANCE=localhost\SQLEXPRESS
    goto :connection_success
)

echo Testing connection to localhost...
sqlcmd -S "localhost" -E -Q "SELECT @@VERSION" -W
if %errorlevel% equ 0 (
    echo [SUCCESS] Connection to localhost successful!
    set SQL_INSTANCE=localhost
    goto :connection_success
)

echo Testing connection to (local)...
sqlcmd -S "(local)" -E -Q "SELECT @@VERSION" -W
if %errorlevel% equ 0 (
    echo [SUCCESS] Connection to (local) successful!
    set SQL_INSTANCE=(local)
    goto :connection_success
)

echo [WARNING] Direct sqlcmd connection failed
echo This might be normal - checking alternative methods...
goto :check_config

:connection_success
echo.
echo [5] Creating test database...
echo.

REM Create a test database for the project
sqlcmd -S "%SQL_INSTANCE%" -E -Q "IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'ProductionDataDB') CREATE DATABASE ProductionDataDB" -W
if %errorlevel% equ 0 (
    echo [SUCCESS] Test database created/verified
) else (
    echo [WARNING] Database creation failed - may need manual setup
)

echo.
echo [6] Testing database operations...
echo.

REM Test basic database operations
sqlcmd -S "%SQL_INSTANCE%" -E -d "ProductionDataDB" -Q "SELECT DB_NAME() as CurrentDatabase, GETDATE() as CurrentTime" -W
if %errorlevel% equ 0 (
    echo [SUCCESS] Database operations working
) else (
    echo [WARNING] Database operations failed
)

goto :success

:check_config
echo.
echo [INFO] Checking SQL Server configuration...
echo.

REM Check if SQL Server Configuration Manager is available
if exist "C:\Windows\SysWOW64\SQLServerManager16.msc" (
    echo [INFO] SQL Server Configuration Manager found
    echo You can open it with: C:\Windows\SysWOW64\SQLServerManager16.msc
) else (
    echo [INFO] SQL Server Configuration Manager path may vary
)

echo.
echo [INFO] Common connection strings for your setup:
echo.
echo For .NET applications:
echo   Server=localhost\SQLEXPRESS;Database=ProductionDataDB;Trusted_Connection=true;
echo   Server=(local);Database=ProductionDataDB;Trusted_Connection=true;
echo   Server=localhost;Database=ProductionDataDB;Trusted_Connection=true;
echo.

goto :manual_config

:error
echo.
echo ==========================================
echo   SQL Server Installation Issue
echo ==========================================
echo.
echo SQL Server service was not found.
echo Please check:
echo 1. SQL Server was installed correctly
echo 2. Installation completed successfully
echo 3. Services are properly registered
echo.
goto :end

:manual_config
echo.
echo ==========================================
echo   Manual Configuration Required
echo ==========================================
echo.
echo SQL Server is installed but may need configuration:
echo.
echo 1. Enable TCP/IP protocol:
echo    - Open SQL Server Configuration Manager
echo    - Go to SQL Server Network Configuration
echo    - Enable TCP/IP protocol
echo    - Restart SQL Server service
echo.
echo 2. Enable SQL Server Browser:
echo    - Start SQL Server Browser service
echo    - Set to automatic startup
echo.
echo 3. Configure Windows Firewall:
echo    - Allow SQL Server through firewall
echo    - Default port: 1433
echo.
goto :end

:success
echo.
echo ==========================================
echo   SQL Server Configuration Complete
echo ==========================================
echo.
echo [SUCCESS] SQL Server 2022 Express is configured and working!
echo.
echo Connection Details:
echo - Server Instance: %SQL_INSTANCE%
echo - Authentication: Windows Authentication
echo - Test Database: ProductionDataDB
echo.
echo Connection String for applications:
echo Server=%SQL_INSTANCE%;Database=ProductionDataDB;Trusted_Connection=true;
echo.
echo Next Steps:
echo 1. Update project configuration files with connection string
echo 2. Test project database connectivity
echo 3. Run project startup scripts
echo.

:end
echo.
echo Press any key to exit...
pause > nul
