using System.Collections.Concurrent;
using System.Net;
using API.Models;

namespace API.Middleware
{
    /// <summary>
    /// API限流中间件
    /// </summary>
    public class RateLimitingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<RateLimitingMiddleware> _logger;
        private readonly RateLimitOptions _options;
        
        // 存储客户端请求记录
        private static readonly ConcurrentDictionary<string, ClientRequestInfo> _clients = new();
        
        public RateLimitingMiddleware(
            RequestDelegate next,
            ILogger<RateLimitingMiddleware> logger,
            RateLimitOptions options)
        {
            _next = next;
            _logger = logger;
            _options = options;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // 获取客户端标识
            var clientId = GetClientIdentifier(context);
            
            // 检查是否需要限流
            if (ShouldRateLimit(context.Request.Path))
            {
                if (!IsRequestAllowed(clientId))
                {
                    await HandleRateLimitExceeded(context, clientId);
                    return;
                }
            }

            await _next(context);
        }

        /// <summary>
        /// 获取客户端标识
        /// </summary>
        private string GetClientIdentifier(HttpContext context)
        {
            // 优先使用用户ID（如果已认证）
            var userId = context.User?.FindFirst("sub")?.Value;
            if (!string.IsNullOrEmpty(userId))
            {
                return $"user:{userId}";
            }

            // 使用IP地址
            var ipAddress = context.Connection.RemoteIpAddress?.ToString();
            if (!string.IsNullOrEmpty(ipAddress))
            {
                return $"ip:{ipAddress}";
            }

            // 使用User-Agent作为后备
            var userAgent = context.Request.Headers["User-Agent"].ToString();
            return $"ua:{userAgent.GetHashCode()}";
        }

        /// <summary>
        /// 检查路径是否需要限流
        /// </summary>
        private bool ShouldRateLimit(PathString path)
        {
            // 登录接口特殊限制
            if (path.StartsWithSegments("/api/auth/login"))
                return true;

            // 注册接口特殊限制
            if (path.StartsWithSegments("/api/auth/register"))
                return true;

            // API接口通用限制
            if (path.StartsWithSegments("/api"))
                return true;

            return false;
        }

        /// <summary>
        /// 检查请求是否被允许
        /// </summary>
        private bool IsRequestAllowed(string clientId)
        {
            var now = DateTime.UtcNow;
            
            _clients.AddOrUpdate(clientId, 
                new ClientRequestInfo { LastRequestTime = now, RequestCount = 1 },
                (key, existing) =>
                {
                    // 如果超过时间窗口，重置计数
                    if (now - existing.LastRequestTime > TimeSpan.FromMinutes(_options.WindowMinutes))
                    {
                        existing.RequestCount = 1;
                        existing.LastRequestTime = now;
                    }
                    else
                    {
                        existing.RequestCount++;
                        existing.LastRequestTime = now;
                    }
                    return existing;
                });

            var clientInfo = _clients[clientId];
            return clientInfo.RequestCount <= _options.MaxRequests;
        }

        /// <summary>
        /// 处理限流超出
        /// </summary>
        private async Task HandleRateLimitExceeded(HttpContext context, string clientId)
        {
            _logger.LogWarning("客户端 {ClientId} 触发限流，路径: {Path}", clientId, context.Request.Path);

            context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
            context.Response.ContentType = "application/json";

            var response = ApiResponse<object>.Fail("请求过于频繁，请稍后再试");
            response.RequestId = context.TraceIdentifier;

            var json = System.Text.Json.JsonSerializer.Serialize(response, new System.Text.Json.JsonSerializerOptions
            {
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            });

            await context.Response.WriteAsync(json);
        }

        /// <summary>
        /// 清理过期的客户端记录（定期调用）
        /// </summary>
        public static void CleanupExpiredClients(int windowMinutes)
        {
            var cutoff = DateTime.UtcNow.AddMinutes(-windowMinutes * 2);
            var expiredKeys = _clients
                .Where(kvp => kvp.Value.LastRequestTime < cutoff)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                _clients.TryRemove(key, out _);
            }
        }
    }

    /// <summary>
    /// 客户端请求信息
    /// </summary>
    public class ClientRequestInfo
    {
        public DateTime LastRequestTime { get; set; }
        public int RequestCount { get; set; }
    }

    /// <summary>
    /// 限流配置选项
    /// </summary>
    public class RateLimitOptions
    {
        /// <summary>
        /// 时间窗口（分钟）
        /// </summary>
        public int WindowMinutes { get; set; } = 1;

        /// <summary>
        /// 最大请求数
        /// </summary>
        public int MaxRequests { get; set; } = 100;
    }

    /// <summary>
    /// 限流中间件扩展
    /// </summary>
    public static class RateLimitingMiddlewareExtensions
    {
        public static IApplicationBuilder UseRateLimiting(
            this IApplicationBuilder builder, 
            RateLimitOptions? options = null)
        {
            options ??= new RateLimitOptions();
            return builder.UseMiddleware<RateLimitingMiddleware>(options);
        }
    }

    /// <summary>
    /// 限流清理后台服务
    /// </summary>
    public class RateLimitCleanupService : BackgroundService
    {
        private readonly ILogger<RateLimitCleanupService> _logger;
        private readonly int _windowMinutes;

        public RateLimitCleanupService(ILogger<RateLimitCleanupService> logger)
        {
            _logger = logger;
            _windowMinutes = 1; // 默认1分钟窗口
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    RateLimitingMiddleware.CleanupExpiredClients(_windowMinutes);
                    _logger.LogDebug("限流记录清理完成");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "限流记录清理失败");
                }

                // 每5分钟清理一次
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
        }
    }
}
