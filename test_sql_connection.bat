@echo off
title SQL Server Connection Test

echo ==========================================
echo   SQL Server 2022 Connection Test
echo ==========================================
echo.

REM Set the path to sqlcmd
set SQLCMD_PATH=C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\SQLCMD.EXE

echo [1] Checking SQL Server service status...
sc query "MSSQL$SQLEXPRESS" | findstr "STATE"
echo.

echo [2] Testing SQL Server connection...
echo.

echo Testing connection to localhost\SQLEXPRESS...
"%SQLCMD_PATH%" -S "localhost\SQLEXPRESS" -E -Q "SELECT @@VERSION" -W
if %errorlevel% equ 0 (
    echo [SUCCESS] Connection successful!
    set SQL_INSTANCE=localhost\SQLEXPRESS
    goto :test_database
) else (
    echo [INFO] SQLEXPRESS connection failed, trying localhost...
)

echo Testing connection to localhost...
"%SQLCMD_PATH%" -S "localhost" -E -Q "SELECT @@VERSION" -W
if %errorlevel% equ 0 (
    echo [SUCCESS] Connection successful!
    set SQL_INSTANCE=localhost
    goto :test_database
) else (
    echo [WARNING] localhost connection failed
    goto :connection_failed
)

:test_database
echo.
echo [3] Creating test database...
"%SQLCMD_PATH%" -S "%SQL_INSTANCE%" -E -Q "IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'ProductionDataDB') CREATE DATABASE ProductionDataDB"
if %errorlevel% equ 0 (
    echo [SUCCESS] Database created/verified
) else (
    echo [WARNING] Database creation failed
)

echo.
echo [4] Testing database operations...
"%SQLCMD_PATH%" -S "%SQL_INSTANCE%" -E -d "ProductionDataDB" -Q "SELECT DB_NAME() as CurrentDatabase, GETDATE() as CurrentTime"
if %errorlevel% equ 0 (
    echo [SUCCESS] Database operations working
) else (
    echo [WARNING] Database operations failed
)

echo.
echo ==========================================
echo   SQL Server Configuration Summary
echo ==========================================
echo.
echo [SUCCESS] SQL Server 2022 Express is working!
echo.
echo Connection Details:
echo - Server Instance: %SQL_INSTANCE%
echo - Authentication: Windows Authentication
echo - Database: ProductionDataDB
echo.
echo Connection String for .NET applications:
echo Server=%SQL_INSTANCE%;Database=ProductionDataDB;Trusted_Connection=true;TrustServerCertificate=true;
echo.
goto :end

:connection_failed
echo.
echo ==========================================
echo   Connection Configuration Needed
echo ==========================================
echo.
echo SQL Server is installed but connection failed.
echo.
echo Possible solutions:
echo 1. Enable TCP/IP protocol in SQL Server Configuration Manager
echo 2. Start SQL Server Browser service
echo 3. Check Windows Firewall settings
echo 4. Verify SQL Server is running
echo.
echo Manual connection test:
echo Try opening SQL Server Management Studio (if installed)
echo Or use the connection string in your application
echo.

:end
echo.
echo Press any key to continue...
pause > nul
