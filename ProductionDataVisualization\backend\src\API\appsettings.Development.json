{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=ProductionDataVisualizationDb;Trusted_Connection=True;MultipleActiveResultSets=true"}, "JwtSettings": {"SecretKey": "your-super-secret-key-for-development-only-min-32-chars", "Issuer": "ProductionDataVisualization", "Audience": "ProductionDataVisualization-Users", "ExpirationMinutes": 60}, "RateLimiting": {"WindowMinutes": 1, "MaxRequests": 1000}, "Performance": {"EnableDetailedLogging": true, "SlowRequestThreshold": 2000, "EnableMetrics": true}, "Features": {"EnableSwagger": true, "EnableDetailedErrors": true, "EnableCors": true}}