import React, { useState, useEffect } from 'react';
import { Typo<PERSON>, Row, Col, Statistic, Button, Avatar, Space, Progress, Tag } from 'antd';
import {
  DashboardOutlined,
  BarChartOutlined,
  LineChartOutlined,
  UserOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  TrophyOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import CleanAssanLayout from '../components/layout/CleanAssanLayout';
import CleanCard from '../components/common/CleanCard';
import authService from '../services/authService';

const { Title, Text, Paragraph } = Typography;

const CleanDashboard = () => {
  const [user, setUser] = useState(null);

  useEffect(() => {
    const currentUser = authService.getUser();
    setUser(currentUser);
  }, []);

  // 统计数据
  const stats = [
    {
      title: '总产量',
      value: 2847,
      suffix: '件',
      icon: <BarChartOutlined />,
      trend: { value: 12.5, isUp: true },
      color: '#6366f1'
    },
    {
      title: '生产效率',
      value: 98.5,
      suffix: '%',
      icon: <LineChartOutlined />,
      trend: { value: 3.2, isUp: true },
      color: '#10b981'
    },
    {
      title: '质量合格率',
      value: 99.2,
      suffix: '%',
      icon: <CheckCircleOutlined />,
      trend: { value: 0.8, isUp: true },
      color: '#f59e0b'
    },
    {
      title: '设备利用率',
      value: 87.3,
      suffix: '%',
      icon: <DashboardOutlined />,
      trend: { value: 2.1, isUp: false },
      color: '#ef4444'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 }
    }
  };

  return (
    <CleanAssanLayout>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* 页面标题区域 */}
        <motion.div variants={itemVariants} style={{ marginBottom: '32px' }}>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'flex-start',
            marginBottom: '8px'
          }}>
            <div>
              <Title level={1} style={{ 
                margin: 0, 
                fontSize: '32px',
                fontWeight: '700',
                color: '#111827',
                lineHeight: '1.2'
              }}>
                欢迎回来，{user?.username || '管理员'}
              </Title>
              <Paragraph style={{ 
                margin: '8px 0 0 0',
                fontSize: '16px',
                color: '#6b7280'
              }}>
                这是您的生产数据概览，今天是 {new Date().toLocaleDateString('zh-CN', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric',
                  weekday: 'long'
                })}
              </Paragraph>
            </div>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              size="large"
              style={{
                background: '#6366f1',
                borderColor: '#6366f1',
                borderRadius: '8px',
                height: '44px',
                padding: '0 24px',
                fontWeight: '500',
                boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
              }}
            >
              新建报告
            </Button>
          </div>
        </motion.div>

        {/* 统计卡片 */}
        <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
          {stats.map((stat, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <motion.div variants={itemVariants}>
                <CleanCard hoverable>
                  <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
                    <div style={{ flex: 1 }}>
                      <div style={{
                        width: '48px',
                        height: '48px',
                        borderRadius: '12px',
                        background: `${stat.color}15`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginBottom: '16px',
                        fontSize: '20px',
                        color: stat.color
                      }}>
                        {stat.icon}
                      </div>
                      
                      <Text style={{ 
                        fontSize: '14px', 
                        color: '#6b7280',
                        fontWeight: '500',
                        display: 'block',
                        marginBottom: '4px'
                      }}>
                        {stat.title}
                      </Text>
                      
                      <div style={{ display: 'flex', alignItems: 'baseline', gap: '4px', marginBottom: '8px' }}>
                        <span style={{
                          fontSize: '28px',
                          fontWeight: '700',
                          color: '#111827',
                          lineHeight: '1'
                        }}>
                          {stat.value}
                        </span>
                        <span style={{ 
                          fontSize: '16px', 
                          color: '#6b7280',
                          fontWeight: '500'
                        }}>
                          {stat.suffix}
                        </span>
                      </div>

                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px'
                      }}>
                        {stat.trend.isUp ? (
                          <ArrowUpOutlined style={{ color: '#10b981', fontSize: '12px' }} />
                        ) : (
                          <ArrowDownOutlined style={{ color: '#ef4444', fontSize: '12px' }} />
                        )}
                        <Text style={{ 
                          color: stat.trend.isUp ? '#10b981' : '#ef4444',
                          fontSize: '14px',
                          fontWeight: '500'
                        }}>
                          {stat.trend.value}%
                        </Text>
                        <Text style={{ color: '#6b7280', fontSize: '14px' }}>
                          较上月
                        </Text>
                      </div>
                    </div>
                  </div>
                </CleanCard>
              </motion.div>
            </Col>
          ))}
        </Row>

        <Row gutter={[24, 24]}>
          {/* 生产进度 */}
          <Col xs={24} lg={8}>
            <motion.div variants={itemVariants}>
              <CleanCard title="今日生产进度">
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <Text style={{ fontWeight: '500', color: '#374151' }}>生产线 A</Text>
                      <Text style={{ fontWeight: '600', color: '#111827' }}>85%</Text>
                    </div>
                    <Progress 
                      percent={85} 
                      strokeColor="#6366f1"
                      strokeWidth={8}
                      style={{ marginBottom: '16px' }}
                    />
                  </div>
                  
                  <div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <Text style={{ fontWeight: '500', color: '#374151' }}>生产线 B</Text>
                      <Text style={{ fontWeight: '600', color: '#111827' }}>92%</Text>
                    </div>
                    <Progress 
                      percent={92} 
                      strokeColor="#10b981"
                      strokeWidth={8}
                      style={{ marginBottom: '16px' }}
                    />
                  </div>
                  
                  <div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <Text style={{ fontWeight: '500', color: '#374151' }}>生产线 C</Text>
                      <Text style={{ fontWeight: '600', color: '#111827' }}>78%</Text>
                    </div>
                    <Progress 
                      percent={78} 
                      strokeColor="#f59e0b"
                      strokeWidth={8}
                    />
                  </div>
                </Space>
              </CleanCard>
            </motion.div>
          </Col>

          {/* 团队状态 */}
          <Col xs={24} lg={8}>
            <motion.div variants={itemVariants}>
              <CleanCard title="团队状态">
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      <Avatar.Group size="small" maxCount={3}>
                        <Avatar style={{ background: '#6366f1' }}>A</Avatar>
                        <Avatar style={{ background: '#10b981' }}>B</Avatar>
                        <Avatar style={{ background: '#f59e0b' }}>C</Avatar>
                        <Avatar style={{ background: '#ef4444' }}>D</Avatar>
                      </Avatar.Group>
                      <Text style={{ fontWeight: '500', color: '#374151' }}>在线员工</Text>
                    </div>
                    <Tag color="green">24人</Tag>
                  </div>
                  
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      <TrophyOutlined style={{ color: '#f59e0b', fontSize: '16px' }} />
                      <Text style={{ fontWeight: '500', color: '#374151' }}>今日最佳</Text>
                    </div>
                    <Text style={{ fontWeight: '600', color: '#111827' }}>张三 - 生产线A</Text>
                  </div>
                  
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      <TeamOutlined style={{ color: '#6366f1', fontSize: '16px' }} />
                      <Text style={{ fontWeight: '500', color: '#374151' }}>班次安排</Text>
                    </div>
                    <Tag color="blue">早班 8人</Tag>
                  </div>
                </Space>
              </CleanCard>
            </motion.div>
          </Col>

          {/* 最近活动 */}
          <Col xs={24} lg={8}>
            <motion.div variants={itemVariants}>
              <CleanCard title="最近活动">
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  {[
                    { time: '10:30', action: '生产线A完成日产量目标', type: 'success' },
                    { time: '09:15', action: '设备维护计划已更新', type: 'info' },
                    { time: '08:45', action: '质量检测报告已生成', type: 'success' },
                    { time: '08:20', action: '新员工培训已安排', type: 'warning' }
                  ].map((activity, index) => (
                    <div key={index} style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
                      <div style={{
                        width: '8px',
                        height: '8px',
                        borderRadius: '50%',
                        background: activity.type === 'success' ? '#10b981' : 
                                   activity.type === 'warning' ? '#f59e0b' : '#6366f1',
                        marginTop: '6px',
                        flexShrink: 0
                      }} />
                      <div style={{ flex: 1 }}>
                        <Text style={{ fontSize: '14px', color: '#374151', fontWeight: '500' }}>
                          {activity.action}
                        </Text>
                        <br />
                        <Text style={{ fontSize: '12px', color: '#9ca3af' }}>
                          {activity.time}
                        </Text>
                      </div>
                    </div>
                  ))}
                </Space>
              </CleanCard>
            </motion.div>
          </Col>
        </Row>
      </motion.div>
    </CleanAssanLayout>
  );
};

export default CleanDashboard;
