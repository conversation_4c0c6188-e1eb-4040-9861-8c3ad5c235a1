@echo off
title 测试最终修复

echo ========================================
echo   测试最终修复 - 表名一致性问题
echo ========================================
echo.

echo [INFO] 修复内容:
echo 1. 修复了GetOrCreateDataTable函数中的表名验证
echo 2. 确保分析和导入使用相同的表名
echo 3. 自动删除无效的表映射
echo 4. 完整的表名验证和重新生成逻辑
echo.

echo [INFO] 后端已启动在 http://localhost:5000
echo.

echo [INFO] 启动前端...
cd frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 15 /nobreak

echo.
echo ========================================
echo   测试指南
echo ========================================
echo.
echo 1. 访问: http://localhost:3000/data-import
echo 2. 上传包含中文字符的数据文件
echo 3. 观察修复效果:
echo    - 文件分析成功
echo    - 生成规范的表名
echo    - 数据导入成功
echo    - 无500错误
echo.
echo 4. 检查后端控制台日志:
echo    - "无效的表名: xxx，删除映射并重新创建"
echo    - "使用现有有效表名: xxx" 或 "生成表名: xxx"
echo    - "开始导入数据到表 [Data_xxx_yyyyMMdd_HHmmss]"
echo    - "数据导入完成，共导入 X 行"
echo.
echo 5. 验证数据:
echo    - 导入完成后查看数据预览
echo    - 确认所有数据都被正确导入
echo    - 表名只包含ASCII字符
echo.

pause
