@echo off
title 数据导入系统就绪测试

echo ========================================
echo   数据导入系统就绪测试
echo ========================================
echo.

echo [INFO] 系统状态:
echo - 后端服务: 运行中 (http://localhost:5000)
echo - 前端服务: 准备启动 (http://localhost:3000)
echo - 数据库: SQL Server 连接正常
echo.

echo [INFO] 新功能:
echo - 表名格式: Data_原始文件名
echo - 支持中文字符表名
echo - 分批处理大数据文件
echo - 重复文件检测和处理
echo.

echo [INFO] 启动前端...
cd frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 15 /nobreak

echo.
echo ========================================
echo   测试说明
echo ========================================
echo.
echo 1. 前端地址: http://localhost:3000/data-import
echo 2. 后端地址: http://localhost:5000
echo.
echo 3. 测试步骤:
echo    a) 上传数据文件（支持中文文件名）
echo    b) 观察文件分析结果
echo    c) 确认表名格式: Data_文件名
echo    d) 完成数据导入
echo    e) 查看数据预览
echo.
echo 4. 预期结果:
echo    - 文件分析成功
echo    - 表名保留中文字符
echo    - 数据完整导入
echo    - 无500错误
echo.
echo 5. 如果遇到问题:
echo    - 检查后端控制台日志
echo    - 检查浏览器控制台错误
echo    - 确认文件格式正确
echo.

pause
