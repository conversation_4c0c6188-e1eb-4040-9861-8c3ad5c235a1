using Microsoft.Data.Sqlite;
using System.Text.Json;

var builder = WebApplication.CreateBuilder(args);

// 添加CORS支持
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(builder =>
    {
        builder.AllowAnyOrigin()
               .AllowAnyMethod()
               .AllowAnyHeader();
    });
});

var app = builder.Build();
app.UseCors();

// SQLite数据库连接字符串
var connectionString = "Data Source=ProductionDataVisualization.db;";

// 初始化数据库
await InitializeDatabase();

async Task InitializeDatabase()
{
    using var connection = new SqliteConnection(connectionString);
    await connection.OpenAsync();
    
    // 创建用户表
    var createUserTable = new SqliteCommand(@"
        CREATE TABLE IF NOT EXISTS Users (
            Id TEXT PRIMARY KEY,
            Username TEXT UNIQUE NOT NULL,
            Email TEXT UNIQUE NOT NULL,
            Password TEXT NOT NULL,
            FullName TEXT NOT NULL,
            IsActive INTEGER DEFAULT 1,
            CreatedAt TEXT DEFAULT (datetime('now')),
            LastLoginTime TEXT NULL,
            Role TEXT DEFAULT 'user'
        )", connection);
    
    await createUserTable.ExecuteNonQueryAsync();
    
    // 检查是否有管理员用户
    var checkAdmin = new SqliteCommand("SELECT COUNT(*) FROM Users WHERE Username = 'admin'", connection);
    var adminExists = Convert.ToInt32(await checkAdmin.ExecuteScalarAsync()) > 0;
    
    if (!adminExists)
    {
        var insertAdmin = new SqliteCommand(@"
            INSERT INTO Users (Id, Username, Email, Password, FullName, Role) 
            VALUES (@Id, @Username, @Email, @Password, @FullName, @Role)", connection);
        
        insertAdmin.Parameters.AddWithValue("@Id", Guid.NewGuid().ToString());
        insertAdmin.Parameters.AddWithValue("@Username", "admin");
        insertAdmin.Parameters.AddWithValue("@Email", "<EMAIL>");
        insertAdmin.Parameters.AddWithValue("@Password", "admin123");
        insertAdmin.Parameters.AddWithValue("@FullName", "系统管理员");
        insertAdmin.Parameters.AddWithValue("@Role", "admin");
        
        await insertAdmin.ExecuteNonQueryAsync();
        Console.WriteLine("默认管理员用户创建成功 (admin/admin123)");
    }
}

// 健康检查API
app.MapGet("/api/health", () =>
{
    return Results.Ok(new
    {
        status = "healthy",
        timestamp = DateTime.UtcNow,
        version = "1.0.0",
        database = "connected"
    });
});

// 数据库健康检查
app.MapGet("/api/health/database", async () =>
{
    try
    {
        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();
        
        var command = new SqliteCommand("SELECT COUNT(*) FROM Users", connection);
        var userCount = Convert.ToInt32(await command.ExecuteScalarAsync());
        
        return Results.Ok(new
        {
            type = "SQLite数据库",
            status = "Connected",
            database = "ProductionDataVisualization.db",
            userCount = userCount,
            message = "SQLite数据库连接正常",
            connectionString = "SQLite文件数据库"
        });
    }
    catch (Exception ex)
    {
        return Results.Json(new
        {
            type = "SQLite数据库",
            status = "Disconnected",
            message = $"数据库连接失败: {ex.Message}"
        }, statusCode: 500);
    }
});

// 获取用户列表
app.MapGet("/api/simple-auth/users", async () =>
{
    try
    {
        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        var command = new SqliteCommand(@"
            SELECT Id, Username, Email, FullName, Role, IsActive, CreatedAt, LastLoginTime
            FROM Users", connection);

        var users = new List<object>();
        using var reader = await command.ExecuteReaderAsync();
        
        while (await reader.ReadAsync())
        {
            users.Add(new
            {
                id = reader["Id"].ToString(),
                username = reader["Username"].ToString(),
                email = reader["Email"].ToString(),
                fullName = reader["FullName"].ToString(),
                role = reader["Role"].ToString(),
                isActive = Convert.ToInt32(reader["IsActive"]) == 1,
                createdAt = reader["CreatedAt"].ToString(),
                lastLoginTime = reader["LastLoginTime"]?.ToString()
            });
        }

        return Results.Ok(new { users = users });
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"获取用户列表失败: {ex.Message}" }, statusCode: 500);
    }
});

// 用户登录
app.MapPost("/api/auth/login", async (HttpRequest request) =>
{
    try
    {
        var body = await new StreamReader(request.Body).ReadToEndAsync();
        var loginData = JsonSerializer.Deserialize<JsonElement>(body);
        
        var username = loginData.GetProperty("username").GetString();
        var password = loginData.GetProperty("password").GetString();

        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        var command = new SqliteCommand(@"
            SELECT Id, Username, Email, FullName, Role, IsActive 
            FROM Users 
            WHERE (Username = @username OR Email = @username) AND Password = @password", connection);
        
        command.Parameters.AddWithValue("@username", username);
        command.Parameters.AddWithValue("@password", password);

        using var reader = await command.ExecuteReaderAsync();
        
        if (await reader.ReadAsync())
        {
            var isActive = Convert.ToInt32(reader["IsActive"]) == 1;
            
            if (!isActive)
            {
                return Results.Json(new { message = "账户已被禁用" }, statusCode: 401);
            }

            // 更新最后登录时间
            var updateCommand = new SqliteCommand(@"
                UPDATE Users SET LastLoginTime = datetime('now') WHERE Id = @id", connection);
            updateCommand.Parameters.AddWithValue("@id", reader["Id"].ToString());
            await updateCommand.ExecuteNonQueryAsync();

            return Results.Ok(new
            {
                message = "登录成功",
                user = new
                {
                    id = reader["Id"].ToString(),
                    username = reader["Username"].ToString(),
                    email = reader["Email"].ToString(),
                    fullName = reader["FullName"].ToString(),
                    role = reader["Role"].ToString()
                }
            });
        }
        else
        {
            return Results.Json(new { message = "用户名或密码错误" }, statusCode: 401);
        }
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"登录失败: {ex.Message}" }, statusCode: 500);
    }
});

// 用户注册
app.MapPost("/api/simple-auth/register", async (HttpRequest request) =>
{
    try
    {
        var body = await new StreamReader(request.Body).ReadToEndAsync();
        var userData = JsonSerializer.Deserialize<JsonElement>(body);
        
        var username = userData.GetProperty("username").GetString();
        var email = userData.GetProperty("email").GetString();
        var password = userData.GetProperty("password").GetString();
        var fullName = userData.GetProperty("fullName").GetString();

        using var connection = new SqliteConnection(connectionString);
        await connection.OpenAsync();

        // 检查用户名是否已存在
        var checkCommand = new SqliteCommand(@"
            SELECT COUNT(*) FROM Users WHERE Username = @username OR Email = @email", connection);
        checkCommand.Parameters.AddWithValue("@username", username);
        checkCommand.Parameters.AddWithValue("@email", email);
        
        var exists = Convert.ToInt32(await checkCommand.ExecuteScalarAsync()) > 0;
        
        if (exists)
        {
            return Results.Json(new { message = "用户名或邮箱已存在" }, statusCode: 400);
        }

        // 创建新用户
        var insertCommand = new SqliteCommand(@"
            INSERT INTO Users (Id, Username, Email, Password, FullName, Role) 
            VALUES (@Id, @Username, @Email, @Password, @FullName, @Role)", connection);
        
        insertCommand.Parameters.AddWithValue("@Id", Guid.NewGuid().ToString());
        insertCommand.Parameters.AddWithValue("@Username", username);
        insertCommand.Parameters.AddWithValue("@Email", email);
        insertCommand.Parameters.AddWithValue("@Password", password);
        insertCommand.Parameters.AddWithValue("@FullName", fullName);
        insertCommand.Parameters.AddWithValue("@Role", "user");
        
        await insertCommand.ExecuteNonQueryAsync();

        return Results.Ok(new { message = "用户注册成功" });
    }
    catch (Exception ex)
    {
        return Results.Json(new { message = $"注册失败: {ex.Message}" }, statusCode: 500);
    }
});

Console.WriteLine("🚀 SQLite后端服务器启动成功!");
Console.WriteLine("📍 监听地址: http://localhost:5000");
Console.WriteLine("🔐 默认登录: admin / admin123");
Console.WriteLine("📊 数据库: SQLite (ProductionDataVisualization.db)");

app.Run("http://localhost:5000");
