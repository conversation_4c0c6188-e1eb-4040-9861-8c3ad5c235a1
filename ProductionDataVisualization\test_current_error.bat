@echo off
title 测试当前错误和自动恢复

echo ========================================
echo   测试当前错误和自动恢复
echo ========================================
echo.

echo [INFO] 当前状态:
echo - 后端服务: 运行中 (http://localhost:5000)
echo - 前端报错: "服务器内部错误"
echo - 自动恢复功能: 已部署
echo.

echo [INFO] 诊断步骤:
echo.

echo 1. 测试数据库健康检查:
curl -s http://localhost:5000/api/database/health
echo.
echo.

echo 2. 测试基本API连接:
curl -s http://localhost:5000/api/health
echo.
echo.

echo 3. 如果数据库有问题，尝试手动恢复:
echo curl -X POST http://localhost:5000/api/database/recover
echo.

echo 4. 检查导入任务API:
curl -s http://localhost:5000/api/data-import/tasks
echo.
echo.

echo ========================================
echo   手动测试步骤
echo ========================================
echo.
echo 1. 打开浏览器: http://localhost:3000/data-import
echo 2. 尝试上传一个小的测试文件
echo 3. 观察后端控制台的详细错误日志
echo 4. 如果看到自动恢复消息，等待恢复完成
echo 5. 重新尝试导入
echo.

echo [INFO] 预期的自动恢复日志:
echo "⚠️ 检测到关键表缺失，尝试自动恢复..."
echo "表存在性检查: ImportTasks=false, FileTableMappings=false, Users=true"
echo "✅ 数据库表结构自动恢复成功！"
echo.

pause
