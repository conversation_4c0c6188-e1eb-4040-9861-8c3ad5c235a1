import React, { useEffect, useRef } from 'react';
import { Typography } from 'antd';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import * as THREE from 'three';
import RegisterForm from '../components/auth/RegisterForm';
import authService from '../services/authService';

const { Text } = Typography;

const Register = () => {
  const navigate = useNavigate();
  const threeContainerRef = useRef(null);
  
  useEffect(() => {
    // 如果用户已登录，重定向到首页
    if (authService.isAuthenticated()) {
      navigate('/');
    }
  }, [navigate]);

  // Three.js 背景动画
  useEffect(() => {
    if (!threeContainerRef.current) return;

    // 创建场景、相机和渲染器
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    threeContainerRef.current.appendChild(renderer.domElement);
    
    // 创建粒子系统
    const particlesGeometry = new THREE.BufferGeometry();
    const particlesCount = 2000;
    const posArray = new Float32Array(particlesCount * 3);
    
    for (let i = 0; i < particlesCount * 3; i++) {
      posArray[i] = (Math.random() - 0.5) * 15;
    }
    
    particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
    
    const particlesMaterial = new THREE.PointsMaterial({
      size: 0.01,
      color: 0x666666,
      transparent: true,
      opacity: 0.5,
      sizeAttenuation: true
    });
    
    const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
    scene.add(particlesMesh);
    
    // 设置相机位置
    camera.position.z = 3;
    
    // 创建连接线
    const lineMaterial = new THREE.LineBasicMaterial({ 
      color: 0x999999,
      opacity: 0.3,
      transparent: true
    });
    
    const lines = [];
    const lineCount = 30;
    
    for (let i = 0; i < lineCount; i++) {
      const lineGeometry = new THREE.BufferGeometry();
      const points = [];
      
      // 创建随机线条
      const startPoint = new THREE.Vector3(
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10
      );
      
      const endPoint = new THREE.Vector3(
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10
      );
      
      points.push(startPoint);
      points.push(endPoint);
      
      lineGeometry.setFromPoints(points);
      const line = new THREE.Line(lineGeometry, lineMaterial);
      scene.add(line);
      lines.push({
        line,
        startPoint,
        endPoint,
        speed: Math.random() * 0.01 + 0.002
      });
    }
    
    // 动画循环
    const clock = new THREE.Clock();
    
    const animate = () => {
      const elapsedTime = clock.getElapsedTime();
      
      // 旋转粒子系统
      particlesMesh.rotation.x = elapsedTime * 0.03;
      particlesMesh.rotation.y = elapsedTime * 0.02;
      
      // 更新线条
      lines.forEach(lineObj => {
        lineObj.line.rotation.x = elapsedTime * lineObj.speed;
        lineObj.line.rotation.y = elapsedTime * lineObj.speed * 0.8;
      });
      
      // 渲染场景
      renderer.render(scene, camera);
      
      // 请求下一帧
      requestAnimationFrame(animate);
    };
    
    animate();
    
    // 处理窗口大小变化
    const handleResize = () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    };
    
    window.addEventListener('resize', handleResize);
    
    // 鼠标移动效果
    const handleMouseMove = (event) => {
      // 将鼠标位置归一化为 -1 到 1 之间
      const mouseX = (event.clientX / window.innerWidth) * 2 - 1;
      const mouseY = -(event.clientY / window.innerHeight) * 2 + 1;
      
      // 缓慢移动相机
      camera.position.x = mouseX * 0.2;
      camera.position.y = mouseY * 0.2;
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    
    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('mousemove', handleMouseMove);
      
      // 清理Three.js资源
      scene.traverse((object) => {
        if (object.geometry) object.geometry.dispose();
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose());
          } else {
            object.material.dispose();
          }
        }
      });
      
      renderer.dispose();
      
      if (threeContainerRef.current) {
        threeContainerRef.current.innerHTML = '';
      }
    };
  }, []);

  // 页面容器样式
  const containerStyle = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    width: '100%',
    position: 'relative',
    overflow: 'hidden',
    background: 'linear-gradient(135deg, #f5f5f5 0%, #ffffff 100%)',
    fontFamily: 'Inter, sans-serif',
  };
  
  // Three.js容器样式
  const threeContainerStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    zIndex: 0
  };
  
  // 内容容器样式
  const contentStyle = {
    position: 'relative',
    zIndex: 1,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    maxWidth: '1200px',
    padding: '0 20px'
  };
  
  // 左侧内容样式
  const leftContentStyle = {
    flex: '1',
    maxWidth: '600px',
    color: '#333',
    padding: '40px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center'
  };
  
  // 右侧表单容器样式
  const rightContentStyle = {
    flex: '1',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '40px'
  };

  // 文本内容动画变体
  const textVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i) => ({ 
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.2 + i * 0.1,
        duration: 0.8,
        ease: "easeOut"
      }
    })
  };

  return (
    <div style={containerStyle} className="register-page">
      {/* Three.js背景 */}
      <div ref={threeContainerRef} style={threeContainerStyle}></div>
      
      {/* 主要内容 */}
      <div style={contentStyle} className="register-content">
        <div className="register-container" style={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          flexWrap: 'wrap'
        }}>
          {/* 左侧内容 */}
          <div style={leftContentStyle} className="register-left-content">
            <motion.div
              custom={0}
              variants={textVariants}
              initial="hidden"
              animate="visible"
            >
              <h1 style={{
                fontSize: '42px',
                fontWeight: '700',
                marginBottom: '16px',
                color: '#333',
              }}>Create Your</h1>
            </motion.div>
            
            <motion.div
              custom={1}
              variants={textVariants}
              initial="hidden"
              animate="visible"
            >
              <h1 style={{
                fontSize: '42px',
                fontWeight: '700',
                marginBottom: '24px',
                color: '#333',
              }}>Professional Profile</h1>
            </motion.div>
            
            <motion.div
              custom={2}
              variants={textVariants}
              initial="hidden"
              animate="visible"
            >
              <p style={{
                fontSize: '18px',
                lineHeight: '1.6',
                color: '#666',
                marginBottom: '32px',
                maxWidth: '480px'
              }}>
                Join thousands of job seekers and employers. Register now to access all features and find your perfect career match.
              </p>
            </motion.div>
            
            <motion.div
              custom={3}
              variants={textVariants}
              initial="hidden"
              animate="visible"
            >
              <div style={{
                display: 'flex',
                gap: '24px',
                marginTop: '16px'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <div style={{
                    width: '40px',
                    height: '40px',
                    borderRadius: '50%',
                    background: 'rgba(51, 51, 51, 0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '12px'
                  }}>
                    <span style={{ fontSize: '20px' }}>🔒</span>
                  </div>
                  <div>
                    <h3 style={{
                      fontSize: '16px',
                      fontWeight: '600',
                      color: '#333',
                      margin: '0'
                    }}>Secure Account</h3>
                    <p style={{
                      fontSize: '14px',
                      color: '#666',
                      margin: '4px 0 0 0'
                    }}>Your data is always protected</p>
                  </div>
                </div>
                
                <div style={{
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <div style={{
                    width: '40px',
                    height: '40px',
                    borderRadius: '50%',
                    background: 'rgba(51, 51, 51, 0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '12px'
                  }}>
                    <span style={{ fontSize: '20px' }}>✨</span>
                  </div>
                  <div>
                    <h3 style={{
                      fontSize: '16px',
                      fontWeight: '600',
                      color: '#333',
                      margin: '0'
                    }}>Premium Features</h3>
                    <p style={{
                      fontSize: '14px',
                      color: '#666',
                      margin: '4px 0 0 0'
                    }}>Access all job search tools</p>
                  </div>
                </div>
              </div>
            </motion.div>
            
            <motion.div
              custom={4}
              variants={textVariants}
              initial="hidden"
              animate="visible"
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                marginTop: '40px'
              }}>
                <p style={{
                  fontSize: '14px',
                  color: '#666',
                  margin: '0',
                  marginRight: '20px'
                }}>
                  Trusted by companies like
                </p>
                <div style={{
                  display: 'flex',
                  gap: '20px',
                  alignItems: 'center'
                }}>
                  <img src="https://via.placeholder.com/80x30" alt="Company 1" style={{ opacity: 0.7 }} />
                  <img src="https://via.placeholder.com/80x30" alt="Company 2" style={{ opacity: 0.7 }} />
                  <img src="https://via.placeholder.com/80x30" alt="Company 3" style={{ opacity: 0.7 }} />
                </div>
              </div>
            </motion.div>
          </div>
          
          {/* 右侧注册表单 */}
          <div style={rightContentStyle} className="register-right-content">
            <RegisterForm />
          </div>
        </div>
      </div>
      
      {/* 版权信息 */}
      <div style={{
        position: 'absolute',
        bottom: '20px',
        left: '0',
        width: '100%',
        textAlign: 'center',
        color: '#999',
        fontSize: '14px',
        zIndex: 1
      }}>
        © {new Date().getFullYear()} Assan Jobs. All rights reserved.
      </div>
      
      {/* 响应式样式 */}
      <style jsx="true">{`
        @media (max-width: 1024px) {
          .register-container {
            flex-direction: column !important;
          }
          
          .register-left-content {
            padding: 20px !important;
            text-align: center;
            align-items: center;
          }
          
          .register-left-content h1 {
            font-size: 32px !important;
          }
          
          .register-right-content {
            padding: 20px !important;
          }
        }
      `}</style>
    </div>
  );
};

export default Register; 