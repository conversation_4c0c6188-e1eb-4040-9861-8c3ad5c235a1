// 权限常量
export const Permissions = {
  // 用户管理权限
  VIEW_USERS: 'ViewUsers',
  CREATE_USER: 'CreateUser',
  EDIT_USER: 'EditUser',
  DELETE_USER: 'DeleteUser',
  
  // 角色管理权限
  VIEW_ROLES: 'ViewRoles',
  CREATE_ROLE: 'CreateRole',
  EDIT_ROLE: 'EditRole',
  DELETE_ROLE: 'DeleteRole',
  
  // 权限管理权限
  VIEW_PERMISSIONS: 'ViewPermissions',
  ASSIGN_PERMISSIONS: 'AssignPermissions',
  
  // 数据管理权限
  VIEW_DATA: 'ViewData',
  IMPORT_DATA: 'ImportData',
  EXPORT_DATA: 'ExportData',
  DELETE_DATA: 'DeleteData',
  
  // 可视化管理权限
  VIEW_CHARTS: 'ViewCharts',
  CREATE_CHART: 'CreateChart',
  EDIT_CHART: 'EditChart',
  DELETE_CHART: 'DeleteChart',
  
  // 仪表板管理权限
  VIEW_DASHBOARDS: 'ViewDashboards',
  CREATE_DASHBOARD: 'CreateDashboard',
  EDIT_DASHBOARD: 'EditDashboard',
  DELETE_DASHBOARD: 'DeleteDashboard',
  
  // 系统管理权限
  VIEW_SYSTEM_SETTINGS: 'ViewSystemSettings',
  EDIT_SYSTEM_SETTINGS: 'EditSystemSettings'
};

// 角色常量
export const Roles = {
  ADMIN: 'Admin',
  MANAGER: 'Manager',
  USER: 'User'
};

// 角色权限映射
export const RolePermissions = {
  [Roles.ADMIN]: Object.values(Permissions),
  [Roles.MANAGER]: [
    Permissions.VIEW_USERS,
    Permissions.VIEW_ROLES,
    Permissions.VIEW_PERMISSIONS,
    Permissions.VIEW_DATA,
    Permissions.IMPORT_DATA,
    Permissions.EXPORT_DATA,
    Permissions.VIEW_CHARTS,
    Permissions.CREATE_CHART,
    Permissions.EDIT_CHART,
    Permissions.VIEW_DASHBOARDS,
    Permissions.CREATE_DASHBOARD,
    Permissions.EDIT_DASHBOARD,
  ],
  [Roles.USER]: [
    Permissions.VIEW_DATA,
    Permissions.VIEW_CHARTS,
    Permissions.VIEW_DASHBOARDS,
  ]
};

// 检查用户是否有权限访问某个功能
export const checkPermission = (userPermissions, requiredPermission) => {
  try {
    if (!userPermissions || !requiredPermission) return false;
    return Boolean(userPermissions.includes(requiredPermission));
  } catch (error) {
    console.error('权限检查失败:', error);
    return false;
  }
};

// 检查用户是否有角色
export const checkRole = (userRoles, requiredRole) => {
  try {
    if (!userRoles || !requiredRole) return false;
    return Boolean(userRoles.includes(requiredRole));
  } catch (error) {
    console.error('角色检查失败:', error);
    return false;
  }
};

// 根据角色获取权限
export const getPermissionsByRole = (role) => {
  return RolePermissions[role] || [];
}; 