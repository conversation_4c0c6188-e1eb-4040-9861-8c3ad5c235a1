-- 清理无效的表映射和表
USE ProductionDataVisualizationDb;

-- 1. 查看当前的文件表映射
SELECT Id, FileName, TableName, CreatedAt 
FROM FileTableMappings 
ORDER BY CreatedAt DESC;

-- 2. 删除包含中文字符的无效表映射
DELETE FROM FileTableMappings 
WHERE TableName LIKE '%[一-龯]%' 
   OR TableName LIKE '%注射水%'
   OR TableName LIKE '%电导率%'
   OR TableName LIKE '%分配%';

-- 3. 查看系统中以Data_开头的表
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE' 
  AND TABLE_NAME LIKE 'Data_%'
ORDER BY TABLE_NAME;

-- 4. 删除包含中文字符的无效表（如果存在）
-- 注意：这些DROP语句可能会失败，因为表名无效，这是正常的
BEGIN TRY
    DROP TABLE [Data_注射水分配1电导率_20250721_095032];
END TRY
BEGIN CATCH
    PRINT '删除表失败（可能表名无效）: ' + ERROR_MESSAGE();
END CATCH;

-- 5. 清理ImportTasks表中的相关记录
DELETE FROM ImportTasks 
WHERE FileName LIKE '%注射水%' 
   OR FileName LIKE '%电导率%';

-- 6. 显示清理后的状态
SELECT 'FileTableMappings清理后' AS TableType, COUNT(*) AS RecordCount FROM FileTableMappings
UNION ALL
SELECT 'ImportTasks清理后' AS TableType, COUNT(*) AS RecordCount FROM ImportTasks;

PRINT '数据库清理完成！';
