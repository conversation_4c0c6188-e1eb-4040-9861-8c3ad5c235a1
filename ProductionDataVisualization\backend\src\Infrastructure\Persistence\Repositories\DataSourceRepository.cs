using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Domain.Common;
using Domain.DataAggregate;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// 数据源仓储实现
    /// </summary>
    public class DataSourceRepository : BaseRepository<DataSource>, IDataSourceRepository
    {
        public DataSourceRepository(ApplicationDbContext dbContext) : base(dbContext)
        {
        }

        public async Task<IEnumerable<DataSource>> GetByImportedByAsync(Guid userId)
        {
            return await DbContext.DataSources
                .Where(ds => ds.ImportedBy == userId)
                .OrderByDescending(ds => ds.ImportedAt)
                .ToListAsync();
        }

        public async Task<DataSource?> GetByNameAsync(string name)
        {
            return await DbContext.DataSources
                .FirstOrDefaultAsync(ds => ds.Name == name);
        }

        public async Task<IEnumerable<DataSource>> GetBySourceTypeAsync(string sourceType)
        {
            return await DbContext.DataSources
                .Where(ds => ds.SourceType == sourceType)
                .OrderByDescending(ds => ds.ImportedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<DataSource>> GetRecentAsync(int count)
        {
            return await DbContext.DataSources
                .OrderByDescending(ds => ds.ImportedAt)
                .Take(count)
                .ToListAsync();
        }
    }
} 