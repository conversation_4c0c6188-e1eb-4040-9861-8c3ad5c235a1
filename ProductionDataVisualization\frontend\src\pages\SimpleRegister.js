import React, { useState } from 'react';
import { Form, Input, Button, message } from 'antd';
import { useNavigate, Link } from 'react-router-dom';
import authService from '../services/authService';

// 添加CSS动画
const styles = `
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 0.3;
      transform: scale(1);
    }
    50% {
      opacity: 0.6;
      transform: scale(1.1);
    }
  }

  @keyframes drift {
    0%, 100% {
      transform: translateX(0px) translateY(0px);
    }
    25% {
      transform: translateX(10px) translateY(-5px);
    }
    50% {
      transform: translateX(-5px) translateY(-10px);
    }
    75% {
      transform: translateX(-10px) translateY(5px);
    }
  }

  @keyframes blink {
    0%, 90%, 100% {
      transform: scaleY(1);
    }
    95% {
      transform: scaleY(0.1);
    }
  }

  @keyframes drop {
    0% {
      transform: translateY(0px) scale(1);
      opacity: 0.8;
    }
    100% {
      transform: translateY(30px) scale(0.8);
      opacity: 0.2;
    }
  }

  @keyframes chartGrow {
    0%, 100% {
      height: 20px;
    }
    50% {
      height: 35px;
    }
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }
`;

// 注入样式到页面
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.type = "text/css";
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}

const SimpleRegister = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [registerSuccess, setRegisterSuccess] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 50, y: 50 });
  const navigate = useNavigate();

  // 鼠标移动事件处理
  const handleMouseMove = (e) => {
    const { clientX, clientY } = e;
    const { innerWidth, innerHeight } = window;
    const x = (clientX / innerWidth) * 100;
    const y = (clientY / innerHeight) * 100;
    setMousePosition({ x, y });
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      // 调用真实的注册API
      const result = await authService.register(
        values.username,
        values.email,
        values.password,
        values.realName
      );

      console.log('注册结果:', result);
      setRegisterSuccess(true);
      message.success('注册成功！');

      // 添加延迟以显示成功动画
      setTimeout(() => {
        navigate('/login');
      }, 1500);
    } catch (error) {
      console.error('注册失败:', error);
      message.error(error.message || '注册失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f5f5f5',
        position: 'relative',
        overflow: 'hidden'
      }}
      onMouseMove={handleMouseMove}
    >
      {/* 背景装饰元素 */}
      <div style={{
        position: 'absolute',
        top: '8%',
        left: '6%',
        width: '120px',
        height: '120px',
        borderRadius: '50%',
        background: 'linear-gradient(135deg, rgba(74, 144, 226, 0.08), rgba(80, 200, 120, 0.08))',
        animation: 'float 6s ease-in-out infinite',
        transform: 'translate(' + ((mousePosition.x - 50) * 0.3) + 'px, ' + ((mousePosition.y - 50) * 0.3) + 'px)',
        transition: 'transform 0.3s ease-out'
      }}></div>

      <div style={{
        position: 'absolute',
        top: '12%',
        right: '10%',
        width: '80px',
        height: '80px',
        background: 'rgba(74, 144, 226, 0.06)',
        transform: 'rotate(45deg)',
        animation: 'float 4s ease-in-out infinite reverse'
      }}></div>

      <div style={{
        position: 'absolute',
        bottom: '18%',
        left: '12%',
        width: '100px',
        height: '100px',
        borderRadius: '50%',
        background: 'rgba(80, 200, 120, 0.05)',
        animation: 'float 5s ease-in-out infinite'
      }}></div>

      <div style={{
        position: 'absolute',
        bottom: '28%',
        right: '6%',
        width: '60px',
        height: '60px',
        background: 'linear-gradient(45deg, rgba(74, 144, 226, 0.08), transparent)',
        borderRadius: '50%',
        animation: 'float 3s ease-in-out infinite reverse'
      }}></div>

      {/* 装饰线条 */}
      <div style={{
        position: 'absolute',
        top: '22%',
        left: '3%',
        width: '180px',
        height: '1px',
        background: 'linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.3), transparent)',
        transform: 'rotate(12deg)'
      }}></div>

      <div style={{
        position: 'absolute',
        bottom: '32%',
        right: '4%',
        width: '140px',
        height: '1px',
        background: 'linear-gradient(90deg, transparent, rgba(80, 200, 120, 0.3), transparent)',
        transform: 'rotate(-18deg)'
      }}></div>

      <div style={{
        position: 'absolute',
        top: '65%',
        left: '2%',
        width: '90px',
        height: '1px',
        background: 'linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.2), transparent)',
        transform: 'rotate(40deg)'
      }}></div>

      {/* 小圆点装饰 */}
      <div style={{
        position: 'absolute',
        top: '35%',
        left: '18%',
        width: '4px',
        height: '4px',
        borderRadius: '50%',
        background: 'rgba(74, 144, 226, 0.4)'
      }}></div>

      <div style={{
        position: 'absolute',
        top: '75%',
        right: '22%',
        width: '6px',
        height: '6px',
        borderRadius: '50%',
        background: 'rgba(80, 200, 120, 0.4)'
      }}></div>

      <div style={{
        position: 'absolute',
        top: '45%',
        right: '3%',
        width: '3px',
        height: '3px',
        borderRadius: '50%',
        background: 'rgba(74, 144, 226, 0.5)'
      }}></div>

      {/* 六边形装饰 */}
      <div style={{
        position: 'absolute',
        top: '30%',
        right: '18%',
        width: '35px',
        height: '35px',
        background: 'rgba(80, 200, 120, 0.12)',
        clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
        animation: 'rotate 18s linear infinite'
      }}></div>

      {/* 三角形装饰 */}
      <div style={{
        position: 'absolute',
        bottom: '40%',
        left: '20%',
        width: '0',
        height: '0',
        borderLeft: '12px solid transparent',
        borderRight: '12px solid transparent',
        borderBottom: '20px solid rgba(74, 144, 226, 0.1)',
        animation: 'pulse 5s ease-in-out infinite'
      }}></div>

      {/* 波浪线装饰 */}
      <div style={{
        position: 'absolute',
        top: '85%',
        left: '8%',
        width: '100px',
        height: '2px',
        background: 'linear-gradient(90deg, transparent, rgba(80, 200, 120, 0.4), transparent)',
        borderRadius: '2px',
        transform: 'rotate(8deg)',
        animation: 'drift 9s ease-in-out infinite'
      }}></div>

      {/* 环形装饰 */}
      <div style={{
        position: 'absolute',
        top: '12%',
        left: '25%',
        width: '25px',
        height: '25px',
        border: '2px solid rgba(74, 144, 226, 0.25)',
        borderRadius: '50%',
        animation: 'pulse 7s ease-in-out infinite'
      }}></div>

      {/* 星形装饰点 */}
      <div style={{
        position: 'absolute',
        bottom: '55%',
        right: '25%',
        width: '10px',
        height: '10px',
        background: 'rgba(80, 200, 120, 0.5)',
        clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)',
        animation: 'rotate 12s linear infinite'
      }}></div>

      {/* 虚线圆弧 */}
      <div style={{
        position: 'absolute',
        top: '50%',
        left: '5%',
        width: '45px',
        height: '45px',
        border: '1px dashed rgba(74, 144, 226, 0.35)',
        borderRadius: '50%',
        borderBottom: 'none',
        borderLeft: 'none',
        animation: 'rotate 22s linear infinite reverse'
      }}></div>

      {/* 渐变条纹 */}
      <div style={{
        position: 'absolute',
        bottom: '15%',
        right: '12%',
        width: '70px',
        height: '3px',
        background: 'repeating-linear-gradient(90deg, rgba(80, 200, 120, 0.15) 0px, rgba(80, 200, 120, 0.15) 6px, transparent 6px, transparent 12px)',
        transform: 'rotate(-15deg)',
        animation: 'drift 11s ease-in-out infinite'
      }}></div>

      {/* 菱形装饰 */}
      <div style={{
        position: 'absolute',
        top: '70%',
        right: '6%',
        width: '18px',
        height: '18px',
        background: 'rgba(74, 144, 226, 0.18)',
        transform: 'rotate(45deg)',
        animation: 'float 8s ease-in-out infinite'
      }}></div>

      {/* 椭圆装饰 */}
      <div style={{
        position: 'absolute',
        top: '25%',
        left: '35%',
        width: '60px',
        height: '30px',
        background: 'rgba(80, 200, 120, 0.06)',
        borderRadius: '50%',
        transform: 'rotate(25deg)',
        animation: 'drift 13s ease-in-out infinite'
      }}></div>

      {/* 眼睛装饰 */}
      <div style={{
        position: 'absolute',
        top: '18%',
        left: '12%',
        width: '35px',
        height: '22px',
        background: 'rgba(74, 144, 226, 0.12)',
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        animation: 'blink 5s ease-in-out infinite',
        transform: 'translate(' + ((mousePosition.x - 50) * 0.4) + 'px, ' + ((mousePosition.y - 50) * 0.4) + 'px)',
        transition: 'transform 0.25s ease-out'
      }}>
        <div style={{
          width: '10px',
          height: '10px',
          background: 'rgba(74, 144, 226, 0.35)',
          borderRadius: '50%',
          transform: 'translate(' + ((mousePosition.x - 50) * 0.2) + 'px, ' + ((mousePosition.y - 50) * 0.2) + 'px)',
          transition: 'transform 0.15s ease-out'
        }}></div>
      </div>

      {/* 眼药水滴装饰 */}
      <div style={{
        position: 'absolute',
        top: '28%',
        right: '20%',
        width: '7px',
        height: '11px',
        background: 'rgba(80, 200, 120, 0.45)',
        borderRadius: '50% 50% 50% 0',
        transform: 'rotate(-45deg)',
        animation: 'drop 3.5s ease-in-out infinite'
      }}></div>

      <div style={{
        position: 'absolute',
        top: '80%',
        left: '18%',
        width: '5px',
        height: '9px',
        background: 'rgba(80, 200, 120, 0.35)',
        borderRadius: '50% 50% 50% 0',
        transform: 'rotate(-45deg)',
        animation: 'drop 4.5s ease-in-out infinite 1.5s'
      }}></div>

      {/* 图表柱状图装饰 */}
      <div style={{
        position: 'absolute',
        bottom: '25%',
        left: '3%',
        display: 'flex',
        alignItems: 'flex-end',
        gap: '2px'
      }}>
        <div style={{
          width: '3px',
          height: '18px',
          background: 'rgba(74, 144, 226, 0.25)',
          animation: 'chartGrow 2.5s ease-in-out infinite'
        }}></div>
        <div style={{
          width: '3px',
          height: '28px',
          background: 'rgba(74, 144, 226, 0.3)',
          animation: 'chartGrow 2.5s ease-in-out infinite 0.3s'
        }}></div>
        <div style={{
          width: '3px',
          height: '12px',
          background: 'rgba(74, 144, 226, 0.25)',
          animation: 'chartGrow 2.5s ease-in-out infinite 0.6s'
        }}></div>
        <div style={{
          width: '3px',
          height: '22px',
          background: 'rgba(74, 144, 226, 0.3)',
          animation: 'chartGrow 2.5s ease-in-out infinite 0.9s'
        }}></div>
        <div style={{
          width: '3px',
          height: '16px',
          background: 'rgba(74, 144, 226, 0.25)',
          animation: 'chartGrow 2.5s ease-in-out infinite 1.2s'
        }}></div>
      </div>

      {/* 饼图装饰 */}
      <div style={{
        position: 'absolute',
        top: '60%',
        right: '12%',
        width: '25px',
        height: '25px',
        borderRadius: '50%',
        background: `conic-gradient(
          rgba(74, 144, 226, 0.35) 0deg 100deg,
          rgba(80, 200, 120, 0.35) 100deg 220deg,
          rgba(74, 144, 226, 0.15) 220deg 360deg
        )`,
        animation: 'rotate 18s linear infinite',
        transform: `translate(${(mousePosition.x - 50) * -0.1}px, ${(mousePosition.y - 50) * -0.1}px)`,
        transition: 'transform 0.5s ease-out'
      }}></div>

      {/* 医药瓶装饰 */}
      <div style={{
        position: 'absolute',
        top: '35%',
        left: '30%',
        width: '10px',
        height: '18px',
        background: 'rgba(80, 200, 120, 0.25)',
        borderRadius: '2px 2px 5px 5px'
      }}>
        <div style={{
          position: 'absolute',
          top: '-2px',
          left: '2px',
          width: '6px',
          height: '3px',
          background: 'rgba(80, 200, 120, 0.35)',
          borderRadius: '2px 2px 0 0'
        }}></div>
      </div>

      {/* 分子结构装饰 */}
      <div style={{
        position: 'absolute',
        bottom: '20%',
        left: '25%',
        width: '35px',
        height: '25px'
      }}>
        <div style={{
          position: 'absolute',
          top: '0',
          left: '0',
          width: '5px',
          height: '5px',
          background: 'rgba(74, 144, 226, 0.45)',
          borderRadius: '50%'
        }}></div>
        <div style={{
          position: 'absolute',
          top: '12px',
          left: '18px',
          width: '4px',
          height: '4px',
          background: 'rgba(80, 200, 120, 0.45)',
          borderRadius: '50%'
        }}></div>
        <div style={{
          position: 'absolute',
          top: '3px',
          left: '25px',
          width: '3px',
          height: '3px',
          background: 'rgba(74, 144, 226, 0.35)',
          borderRadius: '50%'
        }}></div>
        <div style={{
          position: 'absolute',
          top: '1px',
          left: '6px',
          width: '12px',
          height: '1px',
          background: 'rgba(74, 144, 226, 0.25)',
          transform: 'rotate(20deg)'
        }}></div>
        <div style={{
          position: 'absolute',
          top: '10px',
          left: '12px',
          width: '10px',
          height: '1px',
          background: 'rgba(80, 200, 120, 0.25)',
          transform: 'rotate(-25deg)'
        }}></div>
      </div>

      {/* 心电图装饰 */}
      <div style={{
        position: 'absolute',
        top: '75%',
        right: '25%',
        width: '40px',
        height: '20px',
        background: `linear-gradient(
          to right,
          transparent 0%,
          transparent 10%,
          rgba(80, 200, 120, 0.3) 10%,
          rgba(80, 200, 120, 0.3) 12%,
          transparent 12%,
          transparent 25%,
          rgba(80, 200, 120, 0.4) 25%,
          rgba(80, 200, 120, 0.4) 27%,
          transparent 27%,
          transparent 50%,
          rgba(80, 200, 120, 0.5) 50%,
          rgba(80, 200, 120, 0.5) 52%,
          transparent 52%,
          transparent 75%,
          rgba(80, 200, 120, 0.3) 75%,
          rgba(80, 200, 120, 0.3) 77%,
          transparent 77%
        )`,
        clipPath: 'polygon(0% 50%, 15% 50%, 20% 20%, 25% 80%, 30% 30%, 35% 50%, 50% 50%, 55% 10%, 60% 90%, 65% 40%, 70% 50%, 100% 50%, 100% 60%, 0% 60%)',
        animation: 'pulse 2.5s ease-in-out infinite'
      }}></div>

      <div style={{
        width: '100%',
        maxWidth: '420px',
        padding: '48px',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderRadius: '20px',
        boxShadow: '0 20px 60px rgba(0,0,0,0.15), 0 8px 32px rgba(0,0,0,0.1)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        position: 'relative',
        zIndex: 10,
        backdropFilter: 'blur(15px)',
        animation: 'fadeIn 0.8s ease-out',
        overflow: 'hidden'
      }}>
        {/* 卡片内部装饰渐变 */}
        <div style={{
          position: 'absolute',
          top: '-50%',
          right: '-50%',
          width: '200%',
          height: '200%',
          background: 'linear-gradient(45deg, rgba(74, 144, 226, 0.03), rgba(80, 200, 120, 0.03))',
          transform: 'rotate(15deg)',
          pointerEvents: 'none',
          zIndex: -1
        }}></div>

        {/* 内容容器 */}
        <div style={{
          position: 'relative',
          zIndex: 1
        }}>
        {/* 标题 */}
        <div style={{ textAlign: 'center', marginBottom: '40px' }}>
          <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            marginBottom: '12px'
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '12px',
              background: 'linear-gradient(135deg, #4A90E2, #50C878)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '12px',
              boxShadow: '0 4px 12px rgba(74, 144, 226, 0.3)'
            }}>
              <span style={{ color: 'white', fontSize: '18px', fontWeight: 'bold' }}>远</span>
            </div>
            <h1 style={{
              fontSize: '28px',
              margin: 0,
              background: 'linear-gradient(135deg, #4A90E2, #50C878)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: '600'
            }}>注册账户</h1>
          </div>
          <p style={{
            fontSize: '16px',
            color: '#666',
            margin: 0,
            fontWeight: '400',
            letterSpacing: '0.5px'
          }}>远大医药偏差报告系统</p>
          <div style={{
            width: '60px',
            height: '3px',
            background: 'linear-gradient(135deg, #4A90E2, #50C878)',
            margin: '16px auto 0',
            borderRadius: '2px'
          }}></div>
        </div>

        {/* 注册表单 */}
        <Form
          form={form}
          name="register"
          onFinish={onFinish}
          layout="vertical"
        >
          <Form.Item
            label={<span style={{ color: '#333', fontWeight: '500' }}>用户名</span>}
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' }
            ]}
          >
            <Input
              placeholder="请输入用户名"
              style={{
                height: '48px',
                borderRadius: '12px',
                border: '2px solid #f0f0f0',
                fontSize: '16px',
                transition: 'all 0.3s ease'
              }}
            />
          </Form.Item>

          <Form.Item
            label={<span style={{ color: '#333', fontWeight: '500' }}>邮箱</span>}
            name="email"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input
              placeholder="请输入邮箱"
              style={{
                height: '48px',
                borderRadius: '12px',
                border: '2px solid #f0f0f0',
                fontSize: '16px',
                transition: 'all 0.3s ease'
              }}
            />
          </Form.Item>

          <Form.Item
            label={<span style={{ color: '#333', fontWeight: '500' }}>密码</span>}
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input
              type="password"
              placeholder="请输入密码"
              autoComplete="new-password"
              style={{
                height: '48px',
                borderRadius: '12px',
                border: '2px solid #f0f0f0',
                fontSize: '16px',
                transition: 'all 0.3s ease'
              }}
            />
          </Form.Item>

          <Form.Item
            label={<span style={{ color: '#333', fontWeight: '500' }}>确认密码</span>}
            name="confirmPassword"
            dependencies={['password']}
            rules={[
              { required: true, message: '请确认密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input
              type="password"
              placeholder="请再次输入密码"
              autoComplete="new-password"
              style={{
                height: '48px',
                borderRadius: '12px',
                border: '2px solid #f0f0f0',
                fontSize: '16px',
                transition: 'all 0.3s ease'
              }}
            />
          </Form.Item>

          <Form.Item
            label={<span style={{ color: '#333', fontWeight: '500' }}>真实姓名</span>}
            name="realName"
            rules={[{ required: true, message: '请输入真实姓名' }]}
          >
            <Input
              placeholder="请输入真实姓名"
              style={{
                height: '48px',
                borderRadius: '12px',
                border: '2px solid #f0f0f0',
                fontSize: '16px',
                transition: 'all 0.3s ease'
              }}
            />
          </Form.Item>

          <Form.Item
            label={<span style={{ color: '#333', fontWeight: '500' }}>部门</span>}
            name="department"
            rules={[{ required: true, message: '请输入部门' }]}
          >
            <Input
              placeholder="请输入部门"
              style={{
                height: '48px',
                borderRadius: '12px',
                border: '2px solid #f0f0f0',
                fontSize: '16px',
                transition: 'all 0.3s ease'
              }}
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              size="large"
              style={{
                height: '52px',
                borderRadius: '12px',
                fontSize: '16px',
                fontWeight: '500',
                background: 'linear-gradient(135deg, #4A90E2, #50C878)',
                border: 'none',
                boxShadow: '0 4px 16px rgba(74, 144, 226, 0.3)',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 6px 20px rgba(74, 144, 226, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 4px 16px rgba(74, 144, 226, 0.3)';
              }}
            >
              注册
            </Button>
          </Form.Item>
        </Form>

        {/* 登录链接 */}
        <div style={{ textAlign: 'center', marginTop: '32px' }}>
          <span style={{ color: '#666', fontSize: '14px' }}>已有账户？</span>
          <Link
            to="/login"
            style={{
              color: '#4A90E2',
              textDecoration: 'none',
              fontWeight: '500',
              marginLeft: '8px',
              transition: 'color 0.3s ease'
            }}
            onMouseEnter={(e) => e.target.style.color = '#50C878'}
            onMouseLeave={(e) => e.target.style.color = '#4A90E2'}
          >
            立即登录
          </Link>
        </div>
        </div>

        {/* 浮动帮助按钮 */}
        <div style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          width: '56px',
          height: '56px',
          borderRadius: '50%',
          background: 'linear-gradient(135deg, #4A90E2, #50C878)',
          boxShadow: '0 4px 20px rgba(74, 144, 226, 0.3)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          zIndex: 1000,
          transition: 'all 0.3s ease',
          animation: 'float 3s ease-in-out infinite'
        }}
        onMouseEnter={(e) => {
          e.target.style.transform = 'scale(1.1)';
          e.target.style.boxShadow = '0 6px 25px rgba(74, 144, 226, 0.4)';
        }}
        onMouseLeave={(e) => {
          e.target.style.transform = 'scale(1)';
          e.target.style.boxShadow = '0 4px 20px rgba(74, 144, 226, 0.3)';
        }}
        onClick={() => message.info('如需帮助，请联系系统管理员')}
        >
          <span style={{ color: 'white', fontSize: '24px' }}>?</span>
        </div>

        {/* 注册成功状态覆盖层 */}
        {registerSuccess && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'rgba(74, 144, 226, 0.1)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9999,
            animation: 'fadeIn 0.5s ease-out'
          }}>
            <div style={{
              background: 'white',
              padding: '40px',
              borderRadius: '20px',
              boxShadow: '0 20px 60px rgba(0,0,0,0.2)',
              textAlign: 'center',
              animation: 'scaleIn 0.5s ease-out'
            }}>
              <div style={{
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #4A90E2, #50C878)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 20px',
                animation: 'scaleIn 0.8s ease-out'
              }}>
                <span style={{ color: 'white', fontSize: '30px' }}>✓</span>
              </div>
              <h3 style={{ margin: '0 0 10px 0', color: '#333' }}>注册成功！</h3>
              <p style={{ margin: 0, color: '#666' }}>正在跳转到登录页面...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleRegister;
