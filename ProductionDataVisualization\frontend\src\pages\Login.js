import React, { useState, useEffect } from 'react';
import { Typography, Button, Space, Row, Col } from 'antd';
import { useNavigate, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import authService from '../services/authService';
import MinimalLoginForm from '../components/auth/MinimalLoginForm';
import ElegantTechBackground from '../components/common/ElegantTechBackground';

const { Title, Text, Paragraph } = Typography;

const Login = () => {
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // 检查是否已登录
  useEffect(() => {
    if (authService.isAuthenticated()) {
      navigate('/');
    }
  }, [navigate]);

  // 动画变体
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: "beforeChildren",
        staggerChildren: 0.1,
        duration: 0.6
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <div className="sovaso-login-container">
      {/* 添加背景组件 */}
      <ElegantTechBackground 
        particleCount={40} 
        showGrid={true}
        showFloatingElements={false}
        particleSpeed={0.2}
      />
      
      {/* 导航栏 */}
      <header className="sovaso-header">
        <div className="logo">
          <div className="logo-icon"></div>
          <span>SOVASO</span>
        </div>
        <nav className="main-nav">
          <a href="#" className="nav-item">HOME</a>
          <a href="#" className="nav-item">ABOUT US</a>
          <a href="#" className="nav-item">TEAM</a>
          <a href="#" className="nav-item">PRICING</a>
          <a href="#" className="nav-item">BLOGS</a>
          <a href="#" className="nav-item">CONTACT US</a>
        </nav>
      </header>

      {/* 主内容 */}
      <main className="sovaso-main">
        <Row className="sovaso-content">
          <Col xs={24} md={12} className="sovaso-info">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="consulting-label">- Consulting Agency</div>
              <Title className="main-title">Business & Technology Consulting</Title>
              <Paragraph className="subtitle">
                Digital, business & technology consulting for business resilience.
              </Paragraph>
              
              <div className="action-buttons">
                <Button type="primary" className="primary-button">Get registered</Button>
                <Button className="secondary-button">Learn more</Button>
              </div>
              
              <div className="stats-container">
                <div className="stat-item">
                  <div className="stat-number">720+</div>
                  <div className="stat-label">Happy clients<br />this year</div>
                </div>
                
                <div className="stat-item">
                  <div className="stat-number">15+</div>
                  <div className="stat-label">Years of<br />experience</div>
                </div>
                
                <div className="stat-item">
                  <div className="stat-number">32+</div>
                  <div className="stat-label">Professional<br />expert</div>
                </div>
              </div>
            </motion.div>
          </Col>
          
          <Col xs={24} md={12} className="sovaso-form">
            <motion.div 
              className="login-card"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <MinimalLoginForm />
            </motion.div>
          </Col>
        </Row>
      </main>

      <style jsx="true">{`
        .sovaso-login-container {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          background-color: transparent; /* 改为透明，让背景组件显示 */
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .sovaso-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px 40px;
          border-bottom: 1px solid rgba(0, 0, 0, 0.05);
          background-color: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(10px);
          z-index: 10;
        }
        
        .logo {
          display: flex;
          align-items: center;
          font-weight: 700;
          font-size: 18px;
          color: #333;
        }
        
        .logo-icon {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background-color: #35A87C;
          margin-right: 8px;
          position: relative;
        }
        
        .logo-icon::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: 2px solid white;
        }
        
        .main-nav {
          display: flex;
          gap: 24px;
        }
        
        .nav-item {
          color: #666;
          font-size: 14px;
          font-weight: 500;
          text-decoration: none;
          transition: color 0.3s;
        }
        
        .nav-item:hover {
          color: #35A87C;
        }
        
        .sovaso-main {
          flex: 1;
          padding: 40px;
          max-width: 1200px;
          margin: 0 auto;
          width: 100%;
        }
        
        .sovaso-content {
          display: flex;
          min-height: 500px;
          align-items: center;
        }
        
        .sovaso-info {
          padding: 40px 20px 40px 0;
        }
        
        .consulting-label {
          color: #35A87C;
          font-size: 14px;
          margin-bottom: 16px;
          font-weight: 500;
        }
        
        .main-title {
          font-size: 36px !important;
          font-weight: 700 !important;
          line-height: 1.2 !important;
          color: #333 !important;
          margin-bottom: 16px !important;
        }
        
        .subtitle {
          font-size: 16px;
          color: #666;
          margin-bottom: 32px;
        }
        
        .action-buttons {
          display: flex;
          gap: 16px;
          margin-bottom: 40px;
        }
        
        .primary-button {
          background-color: #35A87C !important;
          border-color: #35A87C !important;
          height: 44px;
          padding: 0 24px;
          font-weight: 500;
          border-radius: 4px;
        }
        
        .secondary-button {
          border: 1px solid #ddd !important;
          color: #666 !important;
          background: transparent !important;
          height: 44px;
          padding: 0 24px;
          font-weight: 500;
          border-radius: 4px;
        }
        
        .stats-container {
          display: flex;
          gap: 40px;
        }
        
        .stat-item {
          margin-top: 20px;
        }
        
        .stat-number {
          font-size: 28px;
          font-weight: 700;
          color: #35A87C;
          margin-bottom: 8px;
        }
        
        .stat-label {
          font-size: 14px;
          color: #666;
          line-height: 1.4;
        }
        
        .sovaso-form {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 20px;
        }
        
        .login-card {
          width: 100%;
          max-width: 450px;
          background: rgba(255, 255, 255, 0.95);
          border-radius: 16px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
          overflow: hidden;
          padding: 40px;
          position: relative;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        @media (max-width: 768px) {
          .sovaso-header {
            flex-direction: column;
            padding: 20px;
          }
          
          .main-nav {
            margin-top: 20px;
            flex-wrap: wrap;
            justify-content: center;
            gap: 16px;
          }
          
          .sovaso-main {
            padding: 20px;
          }
          
          .sovaso-content {
            flex-direction: column;
          }
          
          .sovaso-info {
            padding: 20px 0;
            text-align: center;
          }
          
          .action-buttons {
            justify-content: center;
          }
          
          .stats-container {
            justify-content: center;
          }
          
          .login-card {
            padding: 30px 20px;
          }
        }
      `}</style>
    </div>
  );
};

export default Login;
