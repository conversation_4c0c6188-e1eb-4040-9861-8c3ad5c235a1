import React, { useState, useEffect, useRef } from 'react';
import { 
  Card, 
  Progress, 
  Timeline, 
  Alert, 
  Button, 
  Space, 
  Typography, 
  Statistic, 
  Row, 
  Col,
  Tag,
  Table,
  Modal,
  notification
} from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  FileTextOutlined,
  DatabaseOutlined,
  BarChartOutlined,
  DownloadOutlined,
  EyeOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';

const { Title, Text } = Typography;

const ImportMonitor = ({
  importTasks = [],
  onTaskCancel,
  onTaskRetry,
  onTaskDetails,
  onTaskDelete,
  onRefresh,
  realTimeUpdates = true
}) => {
  const [tasks, setTasks] = useState(importTasks);
  const [selectedTask, setSelectedTask] = useState(null);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [systemStats, setSystemStats] = useState({
    totalTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    activeTasks: 0,
    totalRows: 0,
    processedRows: 0
  });

  const intervalRef = useRef(null);

  // 任务状态映射
  const statusConfig = {
    pending: { color: 'default', icon: <ClockCircleOutlined />, text: '等待中' },
    processing: { color: 'processing', icon: <LoadingOutlined />, text: '处理中' },
    completed: { color: 'success', icon: <CheckCircleOutlined />, text: '已完成' },
    failed: { color: 'error', icon: <ExclamationCircleOutlined />, text: '失败' },
    cancelled: { color: 'default', icon: <ExclamationCircleOutlined />, text: '已取消' }
  };

  // 更新系统统计
  const updateSystemStats = (taskList) => {
    const stats = {
      totalTasks: taskList.length,
      completedTasks: taskList.filter(t => t.status === 'completed').length,
      failedTasks: taskList.filter(t => t.status === 'failed').length,
      activeTasks: taskList.filter(t => t.status === 'processing').length,
      totalRows: taskList.reduce((sum, t) => sum + (t.totalRows || 0), 0),
      processedRows: taskList.reduce((sum, t) => sum + (t.processedRows || 0), 0)
    };
    setSystemStats(stats);
  };

  // 模拟实时更新
  useEffect(() => {
    if (realTimeUpdates) {
      intervalRef.current = setInterval(() => {
        setTasks(prevTasks => {
          const updatedTasks = prevTasks.map(task => {
            if (task.status === 'processing') {
              const newProgress = Math.min(task.progress + Math.random() * 5, 100);
              const newProcessedRows = Math.floor((newProgress / 100) * task.totalRows);
              
              return {
                ...task,
                progress: newProgress,
                processedRows: newProcessedRows,
                status: newProgress >= 100 ? 'completed' : 'processing',
                endTime: newProgress >= 100 ? new Date() : null
              };
            }
            return task;
          });
          
          updateSystemStats(updatedTasks);
          return updatedTasks;
        });
      }, 1000);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [realTimeUpdates]);

  // 初始化任务数据
  useEffect(() => {
    setTasks(importTasks);
    updateSystemStats(importTasks);
  }, [importTasks]);

  // 自动刷新任务列表
  useEffect(() => {
    // 立即刷新一次
    if (onRefresh) {
      console.log('ImportMonitor: 组件挂载，立即刷新任务列表');
      onRefresh();
    }

    // 设置定时刷新
    const refreshInterval = setInterval(() => {
      if (onRefresh) {
        console.log('ImportMonitor: 定时刷新任务列表');
        onRefresh();
      }
    }, 5000); // 每5秒刷新一次

    return () => {
      clearInterval(refreshInterval);
    };
  }, [onRefresh]);

  // 格式化时间
  const formatDuration = (startTime, endTime) => {
    if (!startTime) return '--';
    const end = endTime || new Date();
    const duration = Math.floor((end - startTime) / 1000);
    
    if (duration < 60) return `${duration}秒`;
    if (duration < 3600) return `${Math.floor(duration / 60)}分${duration % 60}秒`;
    return `${Math.floor(duration / 3600)}时${Math.floor((duration % 3600) / 60)}分`;
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (!bytes) return '--';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  // 任务操作
  const handleTaskAction = (taskId, action) => {
    switch (action) {
      case 'cancel':
        if (onTaskCancel) onTaskCancel(taskId);
        setTasks(prev => prev.map(t => 
          t.id === taskId ? { ...t, status: 'cancelled' } : t
        ));
        notification.warning({
          message: '任务已取消',
          description: '导入任务已被用户取消'
        });
        break;
      case 'retry':
        if (onTaskRetry) onTaskRetry(taskId);
        setTasks(prev => prev.map(t => 
          t.id === taskId ? { ...t, status: 'pending', progress: 0, processedRows: 0 } : t
        ));
        notification.info({
          message: '任务重试',
          description: '正在重新开始导入任务'
        });
        break;
      case 'details':
        const task = tasks.find(t => t.id === taskId);
        setSelectedTask(task);
        setDetailsVisible(true);
        if (onTaskDetails) onTaskDetails(taskId);
        break;
      case 'delete':
        const taskToDelete = tasks.find(t => t.id === taskId);
        const isProcessing = taskToDelete?.status === 'processing' || taskToDelete?.status === 'pending';

        Modal.confirm({
          title: '确认删除',
          content: isProcessing
            ? '该任务正在处理中或等待处理，删除后将中断导入过程。确定要删除吗？此操作不可恢复。'
            : '确定要删除这个导入任务吗？此操作不可恢复。',
          okText: '确认删除',
          okType: 'danger',
          cancelText: '取消',
          onOk: () => {
            if (onTaskDelete) onTaskDelete(taskId);
            setTasks(prev => prev.filter(t => t.id !== taskId));
            notification.success({
              message: '任务已删除',
              description: isProcessing
                ? '正在处理的任务已被删除并中断'
                : '导入任务已成功删除'
            });
          }
        });
        break;
    }
  };

  // 任务表格列配置
  const taskColumns = [
    {
      title: '文件名',
      dataIndex: 'fileName',
      key: 'fileName',
      width: 200,
      ellipsis: true,
      render: (text, record) => (
        <Space>
          <FileTextOutlined />
          <Text strong>{text}</Text>
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const config = statusConfig[status];
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      }
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 150,
      render: (progress, record) => (
        <Progress
          percent={progress}
          size="small"
          status={record.status === 'failed' ? 'exception' : 
                 record.status === 'completed' ? 'success' : 'active'}
        />
      )
    },
    {
      title: '数据量',
      key: 'dataInfo',
      width: 120,
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Text type="secondary">{record.processedRows || 0} / {record.totalRows || 0}</Text>
          <Text type="secondary">{formatFileSize(record.fileSize)}</Text>
        </Space>
      )
    },
    {
      title: '耗时',
      key: 'duration',
      width: 100,
      render: (_, record) => (
        <Text type="secondary">
          {formatDuration(record.startTime, record.endTime)}
        </Text>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleTaskAction(record.id, 'details')}
          >
            详情
          </Button>
          {record.status === 'processing' && (
            <Button
              type="link"
              size="small"
              danger
              onClick={() => handleTaskAction(record.id, 'cancel')}
            >
              取消
            </Button>
          )}
          {record.status === 'failed' && (
            <Button
              type="link"
              size="small"
              onClick={() => handleTaskAction(record.id, 'retry')}
            >
              重试
            </Button>
          )}
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleTaskAction(record.id, 'delete')}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div className="import-monitor">
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 系统统计 */}
        <Card>
          <Title level={4}>
            <BarChartOutlined /> 导入监控面板
          </Title>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="总任务数"
                value={systemStats.totalTasks}
                prefix={<FileTextOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="已完成"
                value={systemStats.completedTasks}
                valueStyle={{ color: '#52c41a' }}
                prefix={<CheckCircleOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="处理中"
                value={systemStats.activeTasks}
                valueStyle={{ color: '#1890ff' }}
                prefix={<LoadingOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="失败"
                value={systemStats.failedTasks}
                valueStyle={{ color: '#ff4d4f' }}
                prefix={<ExclamationCircleOutlined />}
              />
            </Col>
          </Row>
          
          {systemStats.totalRows > 0 && (
            <div style={{ marginTop: 16 }}>
              <Text strong>总体进度: </Text>
              <Progress
                percent={Math.round((systemStats.processedRows / systemStats.totalRows) * 100)}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
              <Text type="secondary">
                {systemStats.processedRows.toLocaleString()} / {systemStats.totalRows.toLocaleString()} 行
              </Text>
            </div>
          )}
        </Card>

        {/* 任务列表 */}
        <Card title="导入任务列表">
          <Table
            columns={taskColumns}
            dataSource={tasks}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 个任务`
            }}
            size="small"
          />
        </Card>

        {/* 实时活动时间线 */}
        {tasks.filter(t => t.status === 'processing').length > 0 && (
          <Card title="实时活动">
            <Timeline>
              <AnimatePresence>
                {tasks
                  .filter(t => t.status === 'processing')
                  .map(task => (
                    <motion.div
                      key={task.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                    >
                      <Timeline.Item
                        dot={<LoadingOutlined style={{ fontSize: '16px' }} />}
                        color="blue"
                      >
                        <Space direction="vertical" size="small">
                          <Text strong>{task.fileName}</Text>
                          <Progress percent={task.progress} size="small" />
                          <Text type="secondary">
                            已处理 {task.processedRows || 0} / {task.totalRows || 0} 行
                          </Text>
                        </Space>
                      </Timeline.Item>
                    </motion.div>
                  ))}
              </AnimatePresence>
            </Timeline>
          </Card>
        )}
      </Space>

      {/* 任务详情模态框 */}
      <Modal
        title="任务详情"
        open={detailsVisible}
        onCancel={() => setDetailsVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailsVisible(false)}>
            关闭
          </Button>,
          selectedTask?.status === 'completed' && (
            <Button key="download" type="primary" icon={<DownloadOutlined />}>
              下载报告
            </Button>
          )
        ].filter(Boolean)}
        width={800}
      >
        {selectedTask && (
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            <Card size="small">
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic title="文件名" value={selectedTask.fileName} />
                </Col>
                <Col span={12}>
                  <Statistic title="文件大小" value={formatFileSize(selectedTask.fileSize)} />
                </Col>
              </Row>
            </Card>
            
            <Card size="small">
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic title="总行数" value={selectedTask.totalRows || 0} />
                </Col>
                <Col span={8}>
                  <Statistic title="已处理" value={selectedTask.processedRows || 0} />
                </Col>
                <Col span={8}>
                  <Statistic title="错误行数" value={selectedTask.errorRows || 0} />
                </Col>
              </Row>
            </Card>

            {selectedTask.errors && selectedTask.errors.length > 0 && (
              <Alert
                message="处理错误"
                type="error"
                description={
                  <div style={{ maxHeight: '200px', overflow: 'auto' }}>
                    {selectedTask.errors.map((error, index) => (
                      <div key={index}>{error}</div>
                    ))}
                  </div>
                }
                showIcon
              />
            )}

            <Timeline>
              <Timeline.Item color="green">
                <Text>任务创建: {selectedTask.createTime?.toLocaleString()}</Text>
              </Timeline.Item>
              {selectedTask.startTime && (
                <Timeline.Item color="blue">
                  <Text>开始处理: {selectedTask.startTime.toLocaleString()}</Text>
                </Timeline.Item>
              )}
              {selectedTask.endTime && (
                <Timeline.Item color={selectedTask.status === 'completed' ? 'green' : 'red'}>
                  <Text>
                    {selectedTask.status === 'completed' ? '处理完成' : '处理失败'}: 
                    {selectedTask.endTime.toLocaleString()}
                  </Text>
                </Timeline.Item>
              )}
            </Timeline>
          </Space>
        )}
      </Modal>
    </div>
  );
};

export default ImportMonitor;
