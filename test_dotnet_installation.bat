@echo off
title .NET SDK Installation Test

echo ==========================================
echo   .NET SDK Installation Test
echo ==========================================
echo.

echo [1] Checking .NET installation locations...
echo.

REM Check Program Files
if exist "C:\Program Files\dotnet\dotnet.exe" (
    echo [FOUND] .NET SDK in: C:\Program Files\dotnet\
    set DOTNET_PATH=C:\Program Files\dotnet\dotnet.exe
    goto :test_version
) else (
    echo [INFO] .NET not found in C:\Program Files\dotnet\
)

REM Check Program Files (x86)
if exist "C:\Program Files (x86)\dotnet\dotnet.exe" (
    echo [FOUND] .NET SDK in: C:\Program Files (x86)\dotnet\
    set DOTNET_PATH=C:\Program Files (x86)\dotnet\dotnet.exe
    goto :test_version
) else (
    echo [INFO] .NET not found in C:\Program Files (x86)\dotnet\
)

REM Check user profile
if exist "%USERPROFILE%\.dotnet\dotnet.exe" (
    echo [FOUND] .NET SDK in: %USERPROFILE%\.dotnet\
    set DOTNET_PATH=%USERPROFILE%\.dotnet\dotnet.exe
    goto :test_version
) else (
    echo [INFO] .NET not found in %USERPROFILE%\.dotnet\
)

echo [ERROR] .NET SDK not found in common locations
goto :not_found

:test_version
echo.
echo [2] Testing .NET SDK execution...
echo Using path: %DOTNET_PATH%
echo.

echo .NET SDK Version:
"%DOTNET_PATH%" --version
if %errorlevel% equ 0 (
    echo [SUCCESS] .NET SDK is working!
) else (
    echo [ERROR] .NET SDK execution failed
    goto :error
)

echo.
echo [3] Testing .NET SDK info...
"%DOTNET_PATH%" --info
if %errorlevel% equ 0 (
    echo [SUCCESS] .NET SDK info command works
) else (
    echo [WARNING] .NET SDK info command failed
)

echo.
echo [4] Checking PATH configuration...
echo Current PATH contains:
echo %PATH% | findstr /i "dotnet"
if %errorlevel% equ 0 (
    echo [SUCCESS] .NET path found in PATH
) else (
    echo [WARNING] .NET path not found in PATH
    echo [INFO] You may need to add C:\Program Files\dotnet to PATH
)

echo.
echo [5] Testing project compatibility...
if exist "ProductionDataVisualization\backend\SqlServerAPI\SqlServerAPI.csproj" (
    echo [INFO] Found C# project file
    cd ProductionDataVisualization\backend\SqlServerAPI
    echo Testing dotnet restore...
    "%DOTNET_PATH%" restore
    if %errorlevel% equ 0 (
        echo [SUCCESS] Project restore successful
    ) else (
        echo [WARNING] Project restore failed
    )
    cd ..\..\..
) else (
    echo [INFO] C# project file not found
)

goto :success

:not_found
echo.
echo ==========================================
echo   .NET SDK Not Found
echo ==========================================
echo.
echo .NET SDK was not found in common installation locations.
echo.
echo Please download and install .NET SDK from:
echo https://dotnet.microsoft.com/download
echo.
echo Make sure to download the SDK (not just Runtime)
echo Recommended version: .NET 8.0 SDK
echo.
goto :end

:error
echo.
echo ==========================================
echo   .NET SDK Installation Issue
echo ==========================================
echo.
echo .NET SDK was found but cannot execute properly.
echo This might be due to:
echo 1. Corrupted installation
echo 2. Missing dependencies
echo 3. Permission issues
echo.
echo Please try reinstalling .NET SDK
echo.
goto :end

:success
echo.
echo ==========================================
echo   .NET SDK Status Summary
echo ==========================================
echo.
echo [SUCCESS] .NET SDK is installed and working!
echo Location: %DOTNET_PATH%
echo.
echo Next steps:
echo 1. Add C:\Program Files\dotnet to system PATH (if not already)
echo 2. Restart command prompt to use 'dotnet' command directly
echo 3. Test with: dotnet --version
echo.

:end
echo.
echo Press any key to exit...
pause > nul
