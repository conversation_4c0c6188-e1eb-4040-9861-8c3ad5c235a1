import React from 'react';
import { Button } from 'antd';
import { motion } from 'framer-motion';

const CleanButton = ({ 
  children,
  type = 'default',
  size = 'middle',
  variant = 'default', // default, primary, outline, ghost
  icon,
  loading = false,
  disabled = false,
  block = false,
  className = '',
  style = {},
  onClick,
  ...props 
}) => {
  const getButtonStyle = () => {
    const baseStyle = {
      borderRadius: '8px',
      fontWeight: '600',
      transition: 'all 0.2s ease',
      border: 'none',
      height: size === 'large' ? '44px' : size === 'small' ? '32px' : '40px',
      padding: size === 'large' ? '0 24px' : size === 'small' ? '0 12px' : '0 16px',
      fontSize: size === 'large' ? '16px' : '14px',
      ...style
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          background: '#6366f1',
          color: 'white',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
        };
      case 'outline':
        return {
          ...baseStyle,
          background: 'transparent',
          border: '1px solid #d1d5db',
          color: '#374151',
        };
      case 'ghost':
        return {
          ...baseStyle,
          background: 'transparent',
          border: 'none',
          color: '#6366f1',
        };
      default:
        return {
          ...baseStyle,
          background: '#ffffff',
          border: '1px solid #d1d5db',
          color: '#374151',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
        };
    }
  };

  const getHoverStyle = () => {
    switch (variant) {
      case 'primary':
        return {
          background: '#5856eb',
          transform: 'translateY(-1px)',
          boxShadow: '0 4px 12px rgba(99, 102, 241, 0.3)',
        };
      case 'outline':
        return {
          borderColor: '#6366f1',
          color: '#6366f1',
          background: '#f8fafc',
        };
      case 'ghost':
        return {
          background: '#f8fafc',
          color: '#5856eb',
        };
      default:
        return {
          borderColor: '#9ca3af',
          background: '#f9fafb',
          transform: 'translateY(-1px)',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        };
    }
  };

  return (
    <motion.div
      whileHover={!disabled ? getHoverStyle() : {}}
      whileTap={!disabled ? { scale: 0.98 } : {}}
      style={{ display: block ? 'block' : 'inline-block', width: block ? '100%' : 'auto' }}
    >
      <Button
        type={type}
        size={size}
        icon={icon}
        loading={loading}
        disabled={disabled}
        block={block}
        onClick={onClick}
        style={getButtonStyle()}
        className={`clean-button ${className}`}
        {...props}
      >
        {children}
      </Button>

      <style jsx="true">{`
        .clean-button:hover {
          border-color: ${variant === 'primary' ? '#5856eb' : '#6366f1'} !important;
        }

        .clean-button:focus {
          outline: none;
          box-shadow: 0 0 0 3px ${variant === 'primary' ? 'rgba(99, 102, 241, 0.2)' : 'rgba(99, 102, 241, 0.1)'} !important;
        }

        .clean-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none !important;
        }
      `}</style>
    </motion.div>
  );
};

export default CleanButton;
