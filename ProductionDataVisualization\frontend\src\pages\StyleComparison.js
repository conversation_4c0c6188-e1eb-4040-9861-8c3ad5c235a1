import React, { useState } from 'react';
import { Card, Button, Typography, <PERSON>, Row, Col, Switch } from 'antd';
import { motion } from 'framer-motion';

const { Title, Text, Paragraph } = Typography;

const StyleComparison = () => {
  const [showEnhanced, setShowEnhanced] = useState(true);

  const simpleCardStyle = {
    background: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(20px)',
    borderRadius: '16px',
    border: '1px solid rgba(255, 255, 255, 0.8)',
    boxShadow: '0 32px 64px rgba(0, 0, 0, 0.08), 0 16px 32px rgba(0, 0, 0, 0.04), 0 8px 16px rgba(0, 0, 0, 0.02)'
  };

  const enhancedCardStyle = {
    background: `
      linear-gradient(145deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(248, 250, 252, 0.9) 50%,
        rgba(255, 255, 255, 0.95) 100%
      )
    `,
    backdropFilter: 'blur(25px)',
    borderRadius: '24px',
    border: '1px solid rgba(255, 255, 255, 0.8)',
    boxShadow: `
      0 32px 64px rgba(102, 126, 234, 0.15),
      0 16px 32px rgba(118, 75, 162, 0.1),
      0 8px 16px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(255, 255, 255, 0.5)
    `,
    position: 'relative',
    overflow: 'hidden'
  };

  const simpleBackground = {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  };

  const enhancedBackground = {
    background: `
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
      linear-gradient(135deg, #667eea 0%, #764ba2 100%)
    `
  };

  return (
    <div style={{
      minHeight: '100vh',
      padding: '40px 20px',
      ...(showEnhanced ? enhancedBackground : simpleBackground),
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* 背景装饰效果 */}
      {showEnhanced && (
        <>
          <div style={{
            position: 'absolute',
            top: '10%',
            left: '10%',
            width: '200px',
            height: '200px',
            borderRadius: '50%',
            background: 'linear-gradient(45deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05))',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            animation: 'float 8s ease-in-out infinite'
          }} />
          <div style={{
            position: 'absolute',
            top: '60%',
            right: '15%',
            width: '150px',
            height: '150px',
            borderRadius: '50%',
            background: 'linear-gradient(45deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05))',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            animation: 'float 8s ease-in-out infinite 2s'
          }} />
        </>
      )}

      <div style={{ maxWidth: '1200px', margin: '0 auto', position: 'relative', zIndex: 1 }}>
        {/* 控制面板 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          style={{ textAlign: 'center', marginBottom: '48px' }}
        >
          <Card
            style={{
              ...(showEnhanced ? enhancedCardStyle : simpleCardStyle),
              maxWidth: '600px',
              margin: '0 auto'
            }}
            bodyStyle={{ padding: '32px' }}
          >
            <Title level={2} style={{ 
              color: '#1a202c', 
              marginBottom: '24px',
              background: showEnhanced ? 'linear-gradient(135deg, #667eea, #764ba2)' : '#1a202c',
              WebkitBackgroundClip: showEnhanced ? 'text' : 'unset',
              WebkitTextFillColor: showEnhanced ? 'transparent' : '#1a202c'
            }}>
              Assan 风格对比展示
            </Title>
            
            <Space size="large" style={{ marginBottom: '24px' }}>
              <Text style={{ fontSize: '16px', color: '#64748b' }}>简单版本</Text>
              <Switch 
                checked={showEnhanced} 
                onChange={setShowEnhanced}
                style={{
                  background: showEnhanced ? '#667eea' : '#ccc'
                }}
              />
              <Text style={{ fontSize: '16px', color: '#64748b' }}>增强版本</Text>
            </Space>

            <Paragraph style={{ color: '#64748b', fontSize: '16px', margin: 0 }}>
              {showEnhanced 
                ? '当前显示：增强版本 - 包含多层渐变背景、粒子效果、光线动画、复杂阴影和交互动画'
                : '当前显示：简单版本 - 基础的渐变背景和简单的玻璃态效果'
              }
            </Paragraph>
          </Card>
        </motion.div>

        {/* 功能展示区域 */}
        <Row gutter={[32, 32]}>
          <Col xs={24} lg={8}>
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              <Card
                title="背景效果"
                style={showEnhanced ? enhancedCardStyle : simpleCardStyle}
                bodyStyle={{ padding: '24px' }}
              >
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <div>
                    <Text strong>简单版本:</Text>
                    <br />
                    <Text type="secondary">单一线性渐变背景</Text>
                  </div>
                  <div>
                    <Text strong>增强版本:</Text>
                    <br />
                    <Text type="secondary">多层径向渐变 + 浮动装饰元素 + 粒子效果</Text>
                  </div>
                </Space>
              </Card>
            </motion.div>
          </Col>

          <Col xs={24} lg={8}>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
            >
              <Card
                title="卡片样式"
                style={showEnhanced ? enhancedCardStyle : simpleCardStyle}
                bodyStyle={{ padding: '24px' }}
              >
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <div>
                    <Text strong>简单版本:</Text>
                    <br />
                    <Text type="secondary">基础玻璃态 + 简单阴影</Text>
                  </div>
                  <div>
                    <Text strong>增强版本:</Text>
                    <br />
                    <Text type="secondary">多层渐变 + 复杂阴影 + 内发光 + 闪光动画</Text>
                  </div>
                </Space>
              </Card>
            </motion.div>
          </Col>

          <Col xs={24} lg={8}>
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
            >
              <Card
                title="交互效果"
                style={showEnhanced ? enhancedCardStyle : simpleCardStyle}
                bodyStyle={{ padding: '24px' }}
              >
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <div>
                    <Text strong>简单版本:</Text>
                    <br />
                    <Text type="secondary">基础悬停效果</Text>
                  </div>
                  <div>
                    <Text strong>增强版本:</Text>
                    <br />
                    <Text type="secondary">3D变换 + 缩放 + 增强阴影 + 颜色过渡</Text>
                  </div>
                </Space>
              </Card>
            </motion.div>
          </Col>
        </Row>

        {/* 按钮演示 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.6 }}
          style={{ textAlign: 'center', marginTop: '48px' }}
        >
          <Card
            style={{
              ...(showEnhanced ? enhancedCardStyle : simpleCardStyle),
              maxWidth: '500px',
              margin: '0 auto'
            }}
            bodyStyle={{ padding: '32px' }}
          >
            <Title level={3} style={{ marginBottom: '24px', color: '#1a202c' }}>
              按钮效果对比
            </Title>
            
            <Space size="large">
              <Button 
                type="primary" 
                size="large"
                style={{
                  height: '56px',
                  padding: '0 32px',
                  borderRadius: '12px',
                  background: showEnhanced 
                    ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' 
                    : '#667eea',
                  border: 'none',
                  fontWeight: '600',
                  boxShadow: showEnhanced 
                    ? '0 4px 20px rgba(102, 126, 234, 0.3)' 
                    : '0 2px 8px rgba(102, 126, 234, 0.2)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  if (showEnhanced) {
                    e.target.style.transform = 'translateY(-3px) scale(1.02)';
                    e.target.style.boxShadow = '0 12px 30px rgba(102, 126, 234, 0.5)';
                  }
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'translateY(0) scale(1)';
                  e.target.style.boxShadow = showEnhanced 
                    ? '0 4px 20px rgba(102, 126, 234, 0.3)' 
                    : '0 2px 8px rgba(102, 126, 234, 0.2)';
                }}
              >
                体验效果
              </Button>
              
              <Button 
                size="large"
                style={{
                  height: '56px',
                  padding: '0 32px',
                  borderRadius: '12px',
                  background: 'transparent',
                  border: showEnhanced 
                    ? '2px solid rgba(255, 255, 255, 0.5)' 
                    : '2px solid rgba(255, 255, 255, 0.3)',
                  color: 'white',
                  fontWeight: '600'
                }}
              >
                了解更多
              </Button>
            </Space>
          </Card>
        </motion.div>
      </div>

      <style jsx="true">{`
        @keyframes float {
          0%, 100% { 
            transform: translateY(0px) rotate(0deg) scale(1); 
            opacity: 0.7;
          }
          33% { 
            transform: translateY(-30px) rotate(120deg) scale(1.1); 
            opacity: 0.9;
          }
          66% { 
            transform: translateY(-15px) rotate(240deg) scale(0.9); 
            opacity: 0.8;
          }
        }
      `}</style>
    </div>
  );
};

export default StyleComparison;
