@echo off
title 生产数据可视化系统 - 正确启动

echo ==========================================
echo   生产数据可视化系统 - 正确启动
echo ==========================================
echo.

echo [1] 清理旧的后端进程...
echo.

REM 结束所有可能的后端进程
taskkill /F /IM SqlServerAPI.exe >nul 2>&1
taskkill /F /IM SimpleBackend.exe >nul 2>&1
taskkill /F /IM dotnet.exe >nul 2>&1

echo    已清理旧进程

REM 等待端口释放
timeout /t 3 /nobreak >nul

echo.
echo [2] 启动正确的SQLite后端...
echo.

cd /d "SimpleBackend\bin\Release\net8.0\win-x64\publish"

if not exist "SimpleBackend.exe" (
    echo    错误: 找不到 SimpleBackend.exe
    echo    请确保已正确构建项目
    pause
    exit /b 1
)

echo    启动 SimpleBackend.exe...
start /B SimpleBackend.exe

cd /d "%~dp0"

echo    等待后端启动...
timeout /t 10 /nobreak >nul

REM 验证后端启动
curl -s http://localhost:5000/api/health >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✓ SQLite后端启动成功
) else (
    echo    ✗ SQLite后端启动失败
    echo    请检查错误信息
    pause
    exit /b 1
)

echo.
echo [3] 检查前端状态...
echo.

curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✓ 前端应用正在运行
) else (
    echo    启动React前端...
    cd /d "ProductionDataVisualization\frontend"
    start /B npm start
    cd /d "%~dp0"
    
    echo    等待前端启动...
    timeout /t 20 /nobreak >nul
    
    curl -s http://localhost:3000 >nul 2>&1
    if %errorlevel% equ 0 (
        echo    ✓ 前端应用启动成功
    ) else (
        echo    ⚠ 前端应用启动可能需要更多时间
    )
)

echo.
echo [4] 系统功能验证...
echo.

echo 测试健康检查API...
curl -s http://localhost:5000/api/health
echo.
echo.

echo 测试数据库连接...
curl -s http://localhost:5000/api/health/database
echo.
echo.

echo 测试用户API...
curl -s http://localhost:5000/api/simple-auth/users
echo.
echo.

echo 测试兼容API...
curl -s http://localhost:5000/api/auth/login -X POST -H "Content-Type: application/json" -d "{\"usernameOrEmail\":\"admin\",\"password\":\"admin123\"}"
echo.
echo.

echo ==========================================
echo   系统启动完成并验证成功!
echo ==========================================
echo.
echo 🎉 所有服务正常运行:
echo   ✓ SQLite后端: http://localhost:5000
echo   ✓ React前端: http://localhost:3000
echo   ✓ 数据库连接: 正常
echo   ✓ 用户管理: 可用
echo.
echo 🔐 默认登录信息:
echo   用户名: admin
echo   密码: admin123
echo.
echo 📋 支持的API:
echo   /api/health - 健康检查
echo   /api/auth/login - 用户登录
echo   /api/auth/register - 用户注册
echo   /api/simple-auth/* - 简化API
echo.
echo 正在打开浏览器...
start http://localhost:3000
echo.
echo 现在可以正常使用用户注册功能了!
echo.

pause
