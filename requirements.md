# 生产数据可视化系统需求文档

## 1. 项目概述

### 1.1 背景
本项目旨在开发一套生产数据可视化系统，主要用于领导查看生产过程中的数据。系统将显示生产过程中数据是否超过设定限制，并以直观的方式展示这些数据。

### 1.2 目标
开发一个易于部署、使用简单的数据可视化系统，能够解析不同格式的数据源文件，并以图表形式展示数据，特别是突出显示超出限制的数据点。

### 1.3 范围
系统将部署在局域网环境中，用户通过浏览器访问。系统将支持导入TXT、XLSX、CSV等格式的数据文件，解析后存储到SQL Server数据库，并通过各种图表形式展示数据。

### 1.4 系统本质
这是一个面向企业内部的生产数据可视化系统，主要服务于领导层对生产过程数据的监控需求，特别关注异常数据的突出显示。系统采用"数据文件导入→SQL Server存储→可视化展示"的数据流向，为用户提供清晰直观的数据展示服务。

## 2. 用户需求

### 2.1 用户角色
系统涉及三类用户：
- **维护者**：负责系统维护和管理
- **导数据者**：负责导入数据源文件
- **观看者**：查看数据展示（主要是领导）

### 2.2 用户场景
1. 领导需要通过局域网登录系统查看生产数据
2. 工作人员需要将生产数据文件导入系统
3. 系统需要突出显示超出限制的数据
4. 维护人员需要管理系统和用户

## 3. 功能需求

### 3.1 核心功能
1. **用户管理**
   - 用户注册与登录
   - 角色权限控制
   - 用户信息管理

2. **数据导入**
   - 支持TXT、XLSX、CSV等格式文件导入
   - 单个文件导入功能
   - 支持大型文件(100万行以上)的处理
   - 数据导入过程监控和错误处理

3. **数据存储**
   - 解析后的数据存储到SQL Server数据库
   - 支持大量数据的高效存储和查询

4. **数据可视化**
   - 基本图表展示：表格、折线图、饼状图、柱状图、散点图
   - 数据异常高亮显示
   - 图表交互功能

### 3.2 扩展功能（未来计划）
1. 数据导出和报表生成
2. 更多图表类型支持
3. 数据分析功能
4. 告警通知功能

## 4. 技术需求

### 4.1 系统架构要求
- 采用面向对象设计方法
- 系统应高度模块化
- 各模块之间低耦合
- 易于扩展和维护

### 4.2 性能要求
- 支持处理大量数据(100万行以上)
- 页面加载和图表渲染速度快
- 数据导入处理高效

### 4.3 安全要求
- 基本用户认证和授权
- 角色权限管理

### 4.4 兼容性要求
- 支持主流浏览器
- 支持局域网环境部署

## 5. 约束条件

### 5.1 技术约束
- 使用SQL Server作为数据库
- 系统部署在笔记本电脑上
- 在局域网环境下运行

### 5.2 非功能约束
- 部署流程应尽量简单
- 系统操作应直观易用
- 系统应易于非技术人员理解和使用

### 5.3 开发约束
- 开发过程中边开发边运行完整系统，采用增量式开发方法
- 开发过程中直接按完整系统开发和运行，不添加简化系统
- 避免创建多余文件，保持系统文件结构清晰

### 5.4 部署约束
- 完整系统部署运行启动方式唯一，停止系统方式唯一
- 启动和停止分别由两个独立文件控制
- 所有命令窗口中文不能出现乱码，确保正确的字符编码支持