/* Typography.css - 现代简约黑白灰主题配色系统 */

/* 字体导入 - 增加更多字重选项和次要字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Manrope:wght@400;500;600;700&display=swap');

/* 全局字体变量 */
:root {
  /* 字体颜色 - 更精致的层次 */
  --text-primary: #333333;    /* 主要文本 - 深灰色 */
  --text-secondary: #666666;  /* 次要文本 - 中灰色 */
  --text-tertiary: #999999;   /* 第三级文本 - 浅灰色 */
  --text-disabled: #CCCCCC;   /* 禁用文本 - 极浅灰色 */
  --text-inverse: #FFFFFF;    /* 反色文本 - 白色，用于深色背景 */
  --text-highlight: #555555;  /* 高亮文本 - 中灰色 */
  
  /* 主题色 - 黑白灰色彩系统 */
  --primary-color: #333333;   /* 主色 - 深灰色 */
  --primary-hover: #555555;   /* 主色悬停 - 中灰色 */
  --primary-active: #111111;  /* 主色激活 - 近黑色 */
  --primary-light: rgba(51, 51, 51, 0.1);   /* 主色浅色 - 半透明灰色 */
  --primary-lighter: rgba(51, 51, 51, 0.05); /* 主色更浅色 - 微透明灰色 */
  --primary-bg: #F5F5F5;      /* 主色背景 - 浅灰白色 */
  
  /* 辅助色 - 黑白灰色彩系统 */
  --secondary-color: #666666; /* 辅助色 - 中灰色 */
  --secondary-hover: #888888; /* 辅助色悬停 - 浅灰色 */
  --secondary-active: #444444; /* 辅助色激活 - 深灰色 */
  --accent-color: #999999;    /* 强调色 - 浅灰色 */
  --accent-hover: #AAAAAA;    /* 强调色悬停 - 更浅灰色 */
  --accent-active: #777777;   /* 强调色激活 - 中灰色 */
  --border-color: #E0E0E0;    /* 边框色 - 浅灰色 */
  --border-hover: #CCCCCC;    /* 边框悬停色 - 中灰色 */
  --divider-color: rgba(204, 204, 204, 0.6); /* 分隔线 - 半透明极浅灰色 */
  
  /* 背景色 - 黑白灰层次 */
  --bg-base: #F5F5F5;         /* 基础背景 - 浅灰白色 */
  --bg-light: #FFFFFF;        /* 浅色背景 - 纯白色 */
  --bg-dark: #E0E0E0;         /* 深色背景 - 浅灰色 */
  --bg-darker: #CCCCCC;       /* 更深背景 - 中灰色 */
  --bg-card: #FFFFFF;         /* 卡片背景 - 纯白色 */
  --bg-hover: #F5F5F5;        /* 悬停背景 - 浅灰白色 */
  --bg-active: #E0E0E0;       /* 激活背景 - 浅灰色 */
  
  /* 状态色 - 保留有色彩的状态指示 */
  --success: #2E7D32;         /* 成功 - 深绿色 */
  --success-light: rgba(46, 125, 50, 0.1); /* 成功浅色 */
  --success-hover: #388E3C;   /* 成功悬停 - 亮绿色 */
  --warning: #ED6C02;         /* 警告 - 橙色 */
  --warning-light: rgba(237, 108, 2, 0.1); /* 警告浅色 */
  --warning-hover: #F57C00;   /* 警告悬停 - 亮橙色 */
  --error: #D32F2F;           /* 错误 - 红色 */
  --error-light: rgba(211, 47, 47, 0.1);   /* 错误浅色 */
  --error-hover: #E53935;     /* 错误悬停 - 亮红色 */
  --info: #0288D1;            /* 信息 - 蓝色 */
  --info-light: rgba(2, 136, 209, 0.1);   /* 信息浅色 */
  --info-hover: #0299E3;      /* 信息悬停 - 亮蓝色 */
  
  /* 微妙色彩点缀 - 低饱和度灰色 */
  --accent-blue: rgba(2, 136, 209, 0.1);    /* 蓝色点缀 */
  --accent-green: rgba(46, 125, 50, 0.1);    /* 绿色点缀 */
  --accent-gold: rgba(237, 108, 2, 0.1);    /* 金色点缀 */
  --accent-red: rgba(211, 47, 47, 0.1);      /* 红色点缀 */
  --accent-purple: rgba(123, 31, 162, 0.1);  /* 紫色点缀 */
  --accent-pink: rgba(194, 24, 91, 0.1);    /* 粉色点缀 */
  
  /* 渐变色 - 黑白灰渐变 */
  --gradient-main: linear-gradient(135deg, #333333, #555555);
  --gradient-main-soft: linear-gradient(135deg, rgba(51, 51, 51, 0.8), rgba(85, 85, 85, 0.8));
  --gradient-secondary: linear-gradient(135deg, #666666, #888888);
  --gradient-secondary-soft: linear-gradient(135deg, rgba(102, 102, 102, 0.8), rgba(136, 136, 136, 0.8));
  --gradient-light: linear-gradient(135deg, #FFFFFF, #F5F5F5);
  --gradient-sidebar: linear-gradient(180deg, #FFFFFF, rgba(224, 224, 224, 0.5));
  --gradient-header: linear-gradient(90deg, rgba(51, 51, 51, 0.05), rgba(245, 245, 245, 0));
  --gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(224, 224, 224, 0.9));
  --gradient-success: linear-gradient(135deg, #2E7D32, #388E3C);
  --gradient-info: linear-gradient(135deg, #0288D1, #0299E3);
  --gradient-warning: linear-gradient(135deg, #ED6C02, #F57C00);
  --gradient-error: linear-gradient(135deg, #D32F2F, #E53935);
  
  /* 登录页面蓝色主题 */
  --login-bg-dark: #0A1929;    /* 深蓝色背景 */
  --login-bg-medium: #0F2942;  /* 中蓝色背景 */
  --login-bg-light: #1E3A5F;   /* 浅蓝色背景 */
  --login-accent: #38BDF8;     /* 亮蓝色强调 */
  --login-accent-glow: rgba(56, 189, 248, 0.4); /* 亮蓝色光晕 */
  --gradient-blue: linear-gradient(135deg, #0284C7, #38BDF8); /* 蓝色渐变 */
  --gradient-blue-dark: linear-gradient(135deg, #0C4A6E, #0284C7); /* 深蓝色渐变 */
  --shadow-blue-glow: 0 0 15px rgba(56, 189, 248, 0.5); /* 蓝色发光阴影 */
  --shadow-blue-sm: 0 2px 5px rgba(14, 165, 233, 0.3); /* 小型蓝色阴影 */
  --shadow-blue-md: 0 4px 10px rgba(14, 165, 233, 0.4); /* 中型蓝色阴影 */
  --shadow-blue-lg: 0 8px 20px rgba(14, 165, 233, 0.5); /* 大型蓝色阴影 */
  
  /* 新增 - 微妙纹理 */
  --texture-noise: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.05'/%3E%3C/svg%3E");
  --texture-grid: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.03'%3E%3Cpath d='M0 0h20v1H0zM0 0v20h1V0z'/%3E%3C/g%3E%3C/svg%3E");
  --texture-dots: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.03'%3E%3Ccircle cx='10' cy='10' r='1'/%3E%3C/g%3E%3C/svg%3E");
  --texture-blue-noise: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.05' fill='%230EA5E9'/%3E%3C/svg%3E");
  
  /* 字体大小 - 更精确的比例 */
  --font-size-xs: 12px;       /* 超小字体 */
  --font-size-sm: 14px;       /* 小字体 */
  --font-size-md: 16px;       /* 中等字体 */
  --font-size-lg: 18px;       /* 大字体 */
  --font-size-xl: 20px;       /* 超大字体 */
  --font-size-xxl: 24px;      /* 特大字体 */
  --font-size-title: 28px;    /* 标题字体 */
  --font-size-header: 32px;   /* 页头字体 */
  --font-size-display: 40px;  /* 展示字体 */
  --font-size-hero: 48px;     /* 英雄字体 */
  
  /* 字体粗细 */
  --font-weight-light: 300;   /* 轻量 */
  --font-weight-regular: 400; /* 常规 */
  --font-weight-medium: 500;  /* 中等 */
  --font-weight-semibold: 600;/* 半粗 */
  --font-weight-bold: 700;    /* 粗体 */
  
  /* 字体家族 */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  --font-family-secondary: 'Manrope', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  
  /* 行高 */
  --line-height-tight: 1.2;   /* 紧凑行高 */
  --line-height-normal: 1.5;  /* 标准行高 */
  --line-height-relaxed: 1.8; /* 宽松行高 */
  
  /* 字间距 */
  --letter-spacing-tight: -0.025em; /* 紧凑字间距 */
  --letter-spacing-normal: 0;      /* 标准字间距 */
  --letter-spacing-wide: 0.025em;  /* 宽松字间距 */
  --letter-spacing-wider: 0.05em;  /* 更宽字间距 */
  
  /* 阴影系统 - 更细致的层次和质感 */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);                /* 极小阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);               /* 小阴影 */
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);                /* 中阴影 */
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.05);              /* 大阴影 */
  --shadow-xl: 0 16px 24px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(0, 0, 0, 0.05);              /* 特大阴影 */
  --shadow-2xl: 0 24px 32px rgba(0, 0, 0, 0.12), 0 8px 16px rgba(0, 0, 0, 0.08);            /* 超大阴影 */
  --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.05);       /* 内阴影 */
  --shadow-focus: 0 0 0 3px rgba(51, 51, 51, 0.2);         /* 焦点阴影 */
  --shadow-active: 0 0 0 3px rgba(51, 51, 51, 0.3);        /* 激活阴影 */
  --shadow-success: 0 0 0 3px rgba(46, 125, 50, 0.2);       /* 成功阴影 */
  --shadow-error: 0 0 0 3px rgba(211, 47, 47, 0.2);         /* 错误阴影 */
  --shadow-card-hover: 0 12px 20px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(0, 0, 0, 0.05);      /* 卡片悬停阴影 */
  --shadow-dropdown: 0 6px 16px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.05);         /* 下拉菜单阴影 */
  --shadow-popup: 0 12px 24px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(0, 0, 0, 0.05);           /* 弹出层阴影 */
  --shadow-modal: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.05);      /* 模态框阴影 */
  --shadow-glow: 0 0 15px rgba(51, 51, 51, 0.3);           /* 发光阴影 */
  --shadow-glow-hover: 0 0 20px rgba(51, 51, 51, 0.4);     /* 发光悬停阴影 */
  --shadow-glow-success: 0 0 15px rgba(46, 125, 50, 0.3);   /* 成功发光阴影 */
  --shadow-glow-error: 0 0 15px rgba(211, 47, 47, 0.3);      /* 错误发光阴影 */
  
  /* 圆角 */
  --radius-xs: 2px;           /* 极小圆角 */
  --radius-sm: 4px;           /* 小圆角 */
  --radius-md: 8px;           /* 中圆角 */
  --radius-lg: 12px;          /* 大圆角 */
  --radius-xl: 16px;          /* 特大圆角 */
  --radius-2xl: 24px;         /* 超大圆角 */
  --radius-full: 9999px;      /* 全圆角 */
  
  /* 间距系统 */
  --space-xs: 4px;            /* 极小间距 */
  --space-sm: 8px;            /* 小间距 */
  --space-md: 16px;           /* 中间距 */
  --space-lg: 24px;           /* 大间距 */
  --space-xl: 32px;           /* 特大间距 */
  --space-2xl: 48px;          /* 超大间距 */
  --space-3xl: 64px;          /* 巨大间距 */
  
  /* 边框 */
  --border-width-thin: 1px;   /* 细边框 */
  --border-width-medium: 2px; /* 中等边框 */
  --border-width-thick: 3px;  /* 粗边框 */
  
  /* 动效变量 */
  --transition-fast: 0.15s;   /* 快速过渡 */
  --transition-normal: 0.25s; /* 标准过渡 */
  --transition-slow: 0.4s;    /* 慢速过渡 */
  --transition-very-slow: 0.6s; /* 非常慢的过渡 */
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);       /* 标准缓动 */
  --ease-out: cubic-bezier(0, 0, 0.2, 1);            /* 缓出 */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);             /* 缓入 */
  --ease-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);  /* 弹性缓动 */
  --ease-elastic: cubic-bezier(0.68, -0.55, 0.265, 1.55); /* 弹性缓动 */
  --animation-spin: spin 1s linear infinite;          /* 旋转动画 */
  --animation-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;  /* 闪烁动画 */
  --animation-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; /* 脉冲动画 */
  --animation-bounce: bounce 1s infinite;             /* 弹跳动画 */
  --animation-glow: glow 2s ease-in-out infinite;     /* 发光动画 */
  --animation-float: float 3s ease-in-out infinite;   /* 浮动动画 */
  --animation-shimmer: shimmer 2s linear infinite;    /* 微光动画 */
  
  /* Z-index层级系统 */
  --z-negative: -1;           /* 负层级 */
  --z-base: 0;                /* 基础层级 */
  --z-raised: 1;              /* 提升层级 */
  --z-dropdown: 1000;         /* 下拉层级 */
  --z-sticky: 1100;           /* 粘性层级 */
  --z-fixed: 1200;            /* 固定层级 */
  --z-modal: 1300;            /* 模态层级 */
  --z-popover: 1400;          /* 弹出层级 */
  --z-tooltip: 1500;          /* 提示层级 */
  --z-toast: 1600;            /* 通知层级 */
  --z-max: 9999;              /* 最高层级 */
  
  /* 透明度 */
  --opacity-0: 0;             /* 完全透明 */
  --opacity-25: 0.25;         /* 25%不透明 */
  --opacity-50: 0.5;          /* 50%不透明 */
  --opacity-75: 0.75;         /* 75%不透明 */
  --opacity-100: 1;           /* 完全不透明 */
  
  /* 滤镜 */
  --blur-sm: blur(4px);       /* 小模糊 */
  --blur-md: blur(8px);       /* 中模糊 */
  --blur-lg: blur(16px);      /* 大模糊 */
  --backdrop-blur-sm: blur(4px);  /* 小背景模糊 */
  --backdrop-blur-md: blur(8px);  /* 中背景模糊 */
  --backdrop-blur-lg: blur(16px); /* 大背景模糊 */
}

/* 动画关键帧 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideLeft {
  from { transform: translateX(10px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideRight {
  from { transform: translateX(-10px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes zoomIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes glow {
  0%, 100% { 
    box-shadow: 0 0 5px rgba(51, 51, 51, 0.3);
    border-color: rgba(51, 51, 51, 0.3);
  }
  50% { 
    box-shadow: 0 0 20px rgba(51, 51, 51, 0.5);
    border-color: rgba(51, 51, 51, 0.5);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 基础字体样式 */
body {
  font-family: var(--font-family-primary);
  color: var(--text-primary);
  line-height: var(--line-height-normal);
  background-color: var(--bg-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 标题样式 */
.heading-1 {
  font-size: var(--font-size-header);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
  margin-bottom: var(--space-xl);
  letter-spacing: -0.5px;
}

.heading-2 {
  font-size: var(--font-size-title);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  letter-spacing: -0.3px;
}

.heading-3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

.heading-4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

/* 正文样式 */
.text-body {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
  color: var(--text-secondary);
  margin-bottom: var(--space-md);
}

.text-body-small {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
}

/* 标签和说明文字 */
.text-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--space-xs);
}

.text-caption {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
  color: var(--text-tertiary);
  margin-bottom: var(--space-xs);
}

/* 强调文本 */
.text-emphasis {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

/* 链接文本 */
.text-link {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast) var(--ease-in-out);
}

.text-link:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* 数据值文本 */
.text-data {
  font-family: 'Inter', monospace;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  letter-spacing: 0.2px;
}

/* 表格标题 */
.table-header {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  text-transform: none;
  letter-spacing: 0.3px;
}

/* 表格内容 */
.table-cell {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-regular);
  color: var(--text-secondary);
}

/* 表单标签 */
.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  margin-bottom: var(--space-xs);
}

/* 按钮文本 */
.button-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.3px;
}

/* 导航菜单 */
.menu-item {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.menu-item-active {
  font-weight: var(--font-weight-semibold);
}

/* 状态文本 */
.status-text {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 应用到Ant Design组件 */
.ant-typography {
  color: var(--text-primary);
}

.ant-typography.ant-typography-secondary {
  color: var(--text-secondary);
}

.ant-table-thead > tr > th {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  background-color: var(--bg-dark);
}

.ant-table-tbody > tr > td {
  font-weight: var(--font-weight-regular);
  color: var(--text-secondary);
}

.ant-btn {
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal) var(--ease-out);
}

.ant-form-item-label > label {
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.ant-menu-item {
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal) var(--ease-out);
}

.ant-menu-item-selected {
  font-weight: var(--font-weight-semibold);
}

/* 卡片样式 */
.card-hover {
  transition: all var(--transition-normal) var(--ease-out);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-card-hover);
}

/* 动画类 */
.fade-in {
  animation: fadeIn var(--transition-normal) var(--ease-out);
}

.slide-up {
  animation: slideUp var(--transition-normal) var(--ease-out);
}

.slide-down {
  animation: slideDown var(--transition-normal) var(--ease-out);
}

.slide-left {
  animation: slideLeft var(--transition-normal) var(--ease-out);
}

.slide-right {
  animation: slideRight var(--transition-normal) var(--ease-out);
}

.zoom-in {
  animation: zoomIn var(--transition-normal) var(--ease-out);
}

/* 响应式调整 */
@media (max-width: 768px) {
  :root {
    --font-size-header: 28px;
    --font-size-title: 24px;
    --font-size-xl: 18px;
    --font-size-lg: 16px;
    --font-size-md: 14px;
    --font-size-sm: 12px;
    --font-size-xs: 10px;
    
    --space-xl: 24px;
    --space-lg: 16px;
    --space-md: 12px;
    --space-sm: 8px;
    --space-xs: 4px;
  }
} 