using Domain.VisualizationAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 仪表盘-图表关联实体配置
    /// </summary>
    public class DashboardChartConfiguration : IEntityTypeConfiguration<DashboardChart>
    {
        public void Configure(EntityTypeBuilder<DashboardChart> builder)
        {
            builder.ToTable("DashboardCharts");

            builder.HasKey(dc => dc.Id);

            builder.Property(dc => dc.DashboardId)
                .IsRequired();

            builder.Property(dc => dc.ChartId)
                .IsRequired();

            builder.Property(dc => dc.PositionX)
                .IsRequired();

            builder.Property(dc => dc.PositionY)
                .IsRequired();

            builder.Property(dc => dc.Width)
                .IsRequired();

            builder.Property(dc => dc.Height)
                .IsRequired();

            builder.Property(dc => dc.CreatedAt)
                .IsRequired();

            builder.Property(dc => dc.ModifiedAt);

            builder.Property(dc => dc.CreatedBy)
                .HasMaxLength(50);

            builder.Property(dc => dc.ModifiedBy)
                .HasMaxLength(50);

            // 关系
            builder.HasOne(dc => dc.Dashboard)
                .WithMany(d => d.DashboardCharts)
                .HasForeignKey(dc => dc.DashboardId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(dc => dc.Chart)
                .WithMany(c => c.DashboardCharts)
                .HasForeignKey(dc => dc.ChartId)
                .OnDelete(DeleteBehavior.Restrict);

            // 索引
            builder.HasIndex(dc => new { dc.DashboardId, dc.ChartId })
                .IsUnique();
        }
    }
} 