/* Assan风格全局样式 */

/* CSS变量定义 */
:root {
  /* 主色调 */
  --color-primary-50: #f0f4ff;
  --color-primary-100: #e0e7ff;
  --color-primary-200: #c7d2fe;
  --color-primary-300: #a5b4fc;
  --color-primary-400: #818cf8;
  --color-primary-500: #667eea;
  --color-primary-600: #5a67d8;
  --color-primary-700: #4c51bf;
  --color-primary-800: #434190;
  --color-primary-900: #3c366b;

  /* 次要色调 */
  --color-secondary-500: #764ba2;
  --color-secondary-600: #6b46c1;

  /* 灰色调 */
  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-200: #e2e8f0;
  --color-gray-300: #cbd5e1;
  --color-gray-400: #94a3b8;
  --color-gray-500: #64748b;
  --color-gray-600: #475569;
  --color-gray-700: #334155;
  --color-gray-800: #1e293b;
  --color-gray-900: #0f172a;

  /* 渐变 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-primary-hover: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  --gradient-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-glass: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);

  /* 阴影 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  --shadow-card: 0 32px 64px rgba(0, 0, 0, 0.08), 0 16px 32px rgba(0, 0, 0, 0.04), 0 8px 16px rgba(0, 0, 0, 0.02);
  --shadow-button: 0 4px 20px rgba(102, 126, 234, 0.3);
  --shadow-button-hover: 0 8px 25px rgba(102, 126, 234, 0.4);

  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-3xl: 24px;

  /* 间距 */
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-8: 32px;
  --spacing-10: 40px;
  --spacing-12: 48px;
  --spacing-16: 64px;

  /* 字体 */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-size-4xl: 36px;

  /* 动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.6s ease;
}

/* 全局重置和基础样式 */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--color-gray-800);
  background: var(--color-gray-50);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Assan风格工具类 */
.assan-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
}

.assan-card {
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow: var(--shadow-card);
  padding: var(--spacing-12);
  position: relative;
  overflow: hidden;
}

.assan-button {
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  font-weight: 600;
  padding: var(--spacing-4) var(--spacing-8);
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-button);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  text-decoration: none;
  font-size: var(--font-size-base);
}

.assan-button:hover {
  background: var(--gradient-primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-button-hover);
}

.assan-button:active {
  transform: translateY(0);
}

.assan-button.large {
  height: 56px;
  padding: 0 var(--spacing-8);
  font-size: var(--font-size-lg);
}

.assan-input {
  width: 100%;
  height: 56px;
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  padding: 0 var(--spacing-4);
  font-size: var(--font-size-base);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.assan-input:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.assan-input:hover {
  border-color: var(--color-primary-400);
  background: rgba(255, 255, 255, 0.9);
}

/* 背景装饰 */
.assan-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-background);
  z-index: -1;
}

.assan-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.assan-float {
  animation: float 6s ease-in-out infinite;
}

/* 渐入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.assan-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* 缩放动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.assan-scale-in {
  animation: scaleIn 0.5s ease-out;
}

/* 玻璃态效果 */
.assan-glass {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

/* 文本样式 */
.assan-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--color-gray-900);
  line-height: 1.2;
  margin: 0 0 var(--spacing-4) 0;
}

.assan-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  line-height: 1.5;
  margin: 0 0 var(--spacing-6) 0;
}

.assan-text {
  font-size: var(--font-size-base);
  color: var(--color-gray-700);
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .assan-container {
    padding: 0 var(--spacing-4);
  }
  
  .assan-card {
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
  }
  
  .assan-title {
    font-size: var(--font-size-3xl);
  }
  
  .assan-subtitle {
    font-size: var(--font-size-base);
  }
}

@media (max-width: 480px) {
  .assan-container {
    padding: 0 var(--spacing-3);
  }
  
  .assan-card {
    padding: var(--spacing-6);
    border-radius: var(--radius-lg);
  }
  
  .assan-title {
    font-size: var(--font-size-2xl);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--radius-md);
  transition: background var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* 选择文本样式 */
::selection {
  background: rgba(102, 126, 234, 0.2);
  color: var(--color-gray-900);
}

::-moz-selection {
  background: rgba(102, 126, 234, 0.2);
  color: var(--color-gray-900);
}
