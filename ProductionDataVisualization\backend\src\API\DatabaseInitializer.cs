using Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Domain.UserAggregate;

namespace API
{
    public static class DatabaseInitializer
    {
        public static async Task InitializeAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            
            try
            {
                // 确保数据库存在
                await context.Database.EnsureCreatedAsync();
                
                Console.WriteLine("数据库连接成功");
                Console.WriteLine($"数据库连接字符串: {context.Database.GetConnectionString()}");
                
                // 检查是否需要初始化数据
                if (!await context.Users.AnyAsync())
                {
                    Console.WriteLine("正在初始化默认用户数据...");
                    await SeedDefaultUsersAsync(context);
                }
                else
                {
                    var userCount = await context.Users.CountAsync();
                    Console.WriteLine($"数据库中已有 {userCount} 个用户");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"数据库初始化失败: {ex.Message}");
                throw;
            }
        }
        
        private static async Task SeedDefaultUsersAsync(ApplicationDbContext context)
        {
            var adminUser = new User(
                "admin",
                "<EMAIL>",
                "admin123", // 实际应用中应该加密
                "系统管理员"
            );

            var testUser = new User(
                "testuser",
                "<EMAIL>",
                "password123", // 实际应用中应该加密
                "测试用户"
            );

            context.Users.AddRange(adminUser, testUser);
            await context.SaveChangesAsync();

            Console.WriteLine("默认用户创建完成");
        }
    }
}
