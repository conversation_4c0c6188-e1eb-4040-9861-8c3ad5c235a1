import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Select, 
  Input, 
  InputNumber,
  Switch,
  Button,
  Space,
  Divider,
  Slider,
  Row,
  Col,
  Tabs,
  Tooltip
} from 'antd';
import { 
  SettingOutlined, 
  PictureOutlined, 
  LineChartOutlined,
  Bar<PERSON><PERSON>Outlined,
  Pie<PERSON>hartOutlined,
  TableOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { DEFAULT_COLORS, PHARMA_COLORS } from '../../utils/chartUtils';

const { Option } = Select;
const { TabPane } = Tabs;

const ChartConfigPanel = ({
  chartType = 'line',
  config = {},
  onConfigChange,
  onSave,
  onReset,
  columns = [],
  ...props
}) => {
  const [form] = Form.useForm();
  const [currentConfig, setCurrentConfig] = useState({
    // 基础配置
    title: '',
    subtitle: '',
    width: '100%',
    height: 400,
    
    // 数据配置
    xAxis: '',
    yAxis: [],
    series: [],
    
    // 样式配置
    theme: 'default',
    colors: DEFAULT_COLORS,
    backgroundColor: 'transparent',
    
    // 图表特定配置
    smooth: false,
    showArea: false,
    showSymbol: true,
    showGrid: true,
    showLegend: true,
    showTooltip: true,
    
    // 动画配置
    animation: true,
    animationDuration: 1000,
    
    ...config
  });

  // 图表类型配置
  const chartTypeOptions = [
    { value: 'line', label: '折线图', icon: <LineChartOutlined /> },
    { value: 'bar', label: '柱状图', icon: <BarChartOutlined /> },
    { value: 'pie', label: '饼图', icon: <PieChartOutlined /> },
    { value: 'table', label: '数据表', icon: <TableOutlined /> }
  ];

  // 主题选项
  const themeOptions = [
    { value: 'default', label: '默认主题' },
    { value: 'dark', label: '深色主题' },
    { value: 'pharma', label: '医药主题' },
    { value: 'minimal', label: '简约主题' }
  ];

  // 颜色预设
  const colorPresets = [
    { name: '默认', colors: DEFAULT_COLORS },
    { name: '医药', colors: Object.values(PHARMA_COLORS) },
    { name: '蓝色系', colors: ['#1890ff', '#40a9ff', '#69c0ff', '#91d5ff', '#bae7ff'] },
    { name: '绿色系', colors: ['#52c41a', '#73d13d', '#95de64', '#b7eb8f', '#d9f7be'] },
    { name: '红色系', colors: ['#f5222d', '#ff4d4f', '#ff7875', '#ffa39e', '#ffccc7'] }
  ];

  // 更新配置
  const updateConfig = (newConfig) => {
    const updatedConfig = { ...currentConfig, ...newConfig };
    setCurrentConfig(updatedConfig);
    
    if (onConfigChange) {
      onConfigChange(updatedConfig);
    }
  };

  // 表单值变化处理
  const handleFormChange = (changedValues, allValues) => {
    updateConfig(allValues);
  };

  // 保存配置
  const saveConfig = () => {
    form.validateFields().then(values => {
      updateConfig(values);
      if (onSave) {
        onSave(values);
      }
    });
  };

  // 重置配置
  const resetConfig = () => {
    const defaultConfig = {
      title: '',
      subtitle: '',
      width: '100%',
      height: 400,
      theme: 'default',
      colors: DEFAULT_COLORS,
      backgroundColor: 'transparent',
      smooth: false,
      showArea: false,
      showSymbol: true,
      showGrid: true,
      showLegend: true,
      showTooltip: true,
      animation: true,
      animationDuration: 1000
    };
    
    form.setFieldsValue(defaultConfig);
    updateConfig(defaultConfig);
    
    if (onReset) {
      onReset();
    }
  };

  // 渲染基础配置
  const renderBasicConfig = () => (
    <div>
      <Form.Item name="title" label="图表标题">
        <Input placeholder="输入图表标题" />
      </Form.Item>
      
      <Form.Item name="subtitle" label="副标题">
        <Input placeholder="输入副标题" />
      </Form.Item>
      
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item name="width" label="宽度">
            <Input placeholder="100%" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name="height" label="高度">
            <InputNumber
              min={200}
              max={1000}
              step={50}
              style={{ width: '100%' }}
              addonAfter="px"
            />
          </Form.Item>
        </Col>
      </Row>
      
      <Form.Item name="theme" label="主题">
        <Select>
          {themeOptions.map(option => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Select>
      </Form.Item>
    </div>
  );

  // 渲染数据配置
  const renderDataConfig = () => (
    <div>
      <Form.Item name="xAxis" label="X轴字段">
        <Select placeholder="选择X轴数据字段">
          {columns.map(column => (
            <Option key={column.dataIndex} value={column.dataIndex}>
              {column.title}
            </Option>
          ))}
        </Select>
      </Form.Item>
      
      <Form.Item name="yAxis" label="Y轴字段">
        <Select
          mode="multiple"
          placeholder="选择Y轴数据字段"
          maxTagCount={3}
        >
          {columns.filter(col => col.type === 'number').map(column => (
            <Option key={column.dataIndex} value={column.dataIndex}>
              {column.title}
            </Option>
          ))}
        </Select>
      </Form.Item>
      
      {chartType === 'pie' && (
        <>
          <Form.Item name="nameField" label="名称字段">
            <Select placeholder="选择名称字段">
              {columns.map(column => (
                <Option key={column.dataIndex} value={column.dataIndex}>
                  {column.title}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item name="valueField" label="数值字段">
            <Select placeholder="选择数值字段">
              {columns.filter(col => col.type === 'number').map(column => (
                <Option key={column.dataIndex} value={column.dataIndex}>
                  {column.title}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </>
      )}
    </div>
  );

  // 渲染样式配置
  const renderStyleConfig = () => (
    <div>
      <Form.Item name="backgroundColor" label="背景颜色">
        <Select placeholder="选择背景颜色">
          <Option value="transparent">透明</Option>
          <Option value="#ffffff">白色</Option>
          <Option value="#f5f5f5">浅灰</Option>
          <Option value="#001529">深色</Option>
        </Select>
      </Form.Item>
      
      <Form.Item label="颜色方案">
        <div>
          {colorPresets.map((preset, index) => (
            <div key={index} style={{ marginBottom: 8 }}>
              <div style={{ fontSize: '12px', marginBottom: 4 }}>{preset.name}</div>
              <Space>
                {preset.colors.map((color, colorIndex) => (
                  <div
                    key={colorIndex}
                    style={{
                      width: 20,
                      height: 20,
                      backgroundColor: color,
                      border: '1px solid #d9d9d9',
                      borderRadius: 2,
                      cursor: 'pointer'
                    }}
                    onClick={() => updateConfig({ colors: preset.colors })}
                  />
                ))}
              </Space>
            </div>
          ))}
        </div>
      </Form.Item>
      
      <Divider />
      
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item name="showGrid" valuePropName="checked">
            <Switch checkedChildren="显示网格" unCheckedChildren="隐藏网格" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name="showLegend" valuePropName="checked">
            <Switch checkedChildren="显示图例" unCheckedChildren="隐藏图例" />
          </Form.Item>
        </Col>
      </Row>
      
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item name="showTooltip" valuePropName="checked">
            <Switch checkedChildren="显示提示" unCheckedChildren="隐藏提示" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name="animation" valuePropName="checked">
            <Switch checkedChildren="启用动画" unCheckedChildren="禁用动画" />
          </Form.Item>
        </Col>
      </Row>
      
      {currentConfig.animation && (
        <Form.Item name="animationDuration" label="动画时长">
          <Slider
            min={500}
            max={3000}
            step={100}
            marks={{
              500: '0.5s',
              1000: '1s',
              2000: '2s',
              3000: '3s'
            }}
          />
        </Form.Item>
      )}
    </div>
  );

  // 渲染图表特定配置
  const renderChartSpecificConfig = () => {
    switch (chartType) {
      case 'line':
        return (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="smooth" valuePropName="checked">
                  <Switch checkedChildren="平滑曲线" unCheckedChildren="直线连接" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="showArea" valuePropName="checked">
                  <Switch checkedChildren="显示面积" unCheckedChildren="仅显示线条" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item name="showSymbol" valuePropName="checked">
              <Switch checkedChildren="显示数据点" unCheckedChildren="隐藏数据点" />
            </Form.Item>
          </div>
        );
      
      case 'bar':
        return (
          <div>
            <Form.Item name="barWidth" label="柱子宽度">
              <Slider
                min={20}
                max={100}
                marks={{
                  20: '20%',
                  40: '40%',
                  60: '60%',
                  80: '80%',
                  100: '100%'
                }}
              />
            </Form.Item>
            <Form.Item name="borderRadius" label="圆角大小">
              <InputNumber min={0} max={20} addonAfter="px" />
            </Form.Item>
          </div>
        );
      
      case 'pie':
        return (
          <div>
            <Form.Item name="radius" label="饼图半径">
              <Row gutter={8}>
                <Col span={12}>
                  <InputNumber
                    min={0}
                    max={100}
                    placeholder="内半径"
                    addonAfter="%"
                  />
                </Col>
                <Col span={12}>
                  <InputNumber
                    min={0}
                    max={100}
                    placeholder="外半径"
                    addonAfter="%"
                  />
                </Col>
              </Row>
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="showLabel" valuePropName="checked">
                  <Switch checkedChildren="显示标签" unCheckedChildren="隐藏标签" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="showLabelLine" valuePropName="checked">
                  <Switch checkedChildren="显示引导线" unCheckedChildren="隐藏引导线" />
                </Form.Item>
              </Col>
            </Row>
          </div>
        );
      
      default:
        return null;
    }
  };

  useEffect(() => {
    form.setFieldsValue(currentConfig);
  }, [currentConfig, form]);

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        title={
          <Space>
            <SettingOutlined />
            图表配置
          </Space>
        }
        size="small"
        extra={
          <Space>
            <Tooltip title="重置配置">
              <Button size="small" icon={<ReloadOutlined />} onClick={resetConfig} />
            </Tooltip>
            <Tooltip title="保存配置">
              <Button type="primary" size="small" icon={<SaveOutlined />} onClick={saveConfig} />
            </Tooltip>
          </Space>
        }
        {...props}
      >
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleFormChange}
          size="small"
        >
          <Tabs size="small" type="card">
            <TabPane tab="基础" key="basic">
              {renderBasicConfig()}
            </TabPane>
            
            <TabPane tab="数据" key="data">
              {renderDataConfig()}
            </TabPane>
            
            <TabPane tab="样式" key="style">
              {renderStyleConfig()}
            </TabPane>
            
            <TabPane tab="图表" key="chart">
              {renderChartSpecificConfig()}
            </TabPane>
          </Tabs>
        </Form>
      </Card>
    </motion.div>
  );
};

export default ChartConfigPanel;
