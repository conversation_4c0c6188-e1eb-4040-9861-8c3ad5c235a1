@echo off
title 生产数据可视化系统 - SQL Server版本 (正确启动)

echo ==========================================
echo   生产数据可视化系统 - SQL Server版本
echo   正确启动脚本
echo ==========================================
echo.

echo 🔧 重要提示: 
echo   - 此脚本启动SQL Server版本的系统
echo   - 所有数据保存在SQL Server数据库中
echo   - 适合长期存储和企业级使用
echo.

echo [1] 检查SQL Server连接...
echo.

REM 使用简单的方式检查SQL Server
sqlcmd -S "localhost\SQLEXPRESS" -E -Q "SELECT 1" -W >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✓ SQL Server连接正常
) else (
    echo    ✗ SQL Server连接失败
    echo    请确保SQL Server Express正在运行
    echo    可以尝试启动SQL Server服务：
    echo    net start "SQL Server (SQLEXPRESS)"
    pause
    exit /b 1
)

echo.
echo [2] 停止旧的后端进程...
echo.

REM 停止可能运行的旧版本后端
taskkill /F /IM SimpleBackend.exe >nul 2>&1
taskkill /F /IM SqlServerAPI.exe >nul 2>&1
echo    已清理旧的后端进程

echo.
echo [3] 启动SQL Server后端...
echo.

cd /d "ProductionDataVisualization\backend\SqlServerAPI\bin\Release\net8.0\win-x64\publish"

if not exist "SqlServerAPI.exe" (
    echo    ❌ 错误: 找不到 SqlServerAPI.exe
    echo    请先构建SQL Server后端:
    echo    cd ProductionDataVisualization\backend\SqlServerAPI
    echo    dotnet publish -c Release
    pause
    exit /b 1
)

echo    启动SQL Server后端...
start /B SqlServerAPI.exe
cd /d "%~dp0"

echo    等待后端启动...
timeout /t 15 /nobreak >nul

REM 检查后端是否启动成功
curl -s http://localhost:5000/api/health >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✓ SQL Server后端启动成功
) else (
    echo    ❌ SQL Server后端启动失败
    echo    请检查错误信息并重试
    pause
    exit /b 1
)

echo.
echo [4] 启动前端应用...
echo.

REM 检查前端是否已经运行
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✓ 前端应用已在运行
) else (
    echo    启动React前端...
    cd /d "ProductionDataVisualization\frontend"
    
    if not exist "package.json" (
        echo    ❌ 错误: 找不到前端项目
        echo    请确保在正确的目录中运行此脚本
        pause
        exit /b 1
    )
    
    start /B npm start
    cd /d "%~dp0"
    
    echo    等待前端启动...
    timeout /t 25 /nobreak >nul
    
    REM 再次检查前端
    curl -s http://localhost:3000 >nul 2>&1
    if %errorlevel% equ 0 (
        echo    ✓ 前端应用启动成功
    ) else (
        echo    ⚠ 前端可能需要更多时间启动
        echo    请稍后手动访问 http://localhost:3000
    )
)

echo.
echo [5] 系统验证...
echo.

echo 测试后端API健康检查...
curl -s http://localhost:5000/api/health
echo.
echo.

echo 测试用户登录功能...
curl -s -X POST -H "Content-Type: application/json" -d "{\"usernameOrEmail\":\"admin\",\"password\":\"admin123\"}" http://localhost:5000/api/auth/login
echo.
echo.

echo ==========================================
echo   🎉 SQL Server系统启动完成!
echo ==========================================
echo.
echo 📊 系统信息:
echo   ✓ 数据库: SQL Server (ProductionDataVisualizationDb)
echo   ✓ 后端API: http://localhost:5000
echo   ✓ 前端应用: http://localhost:3000
echo   ✓ 数据存储: 长期存储在SQL Server中
echo.
echo 🔐 默认登录信息:
echo   用户名: admin
echo   密码: admin123
echo.
echo 📋 功能特点:
echo   ✓ 用户信息: 存储在SQL Server Users表
echo   ✓ 导入数据: 存储在SQL Server动态表
echo   ✓ 系统配置: 存储在SQL Server SystemSettings表
echo   ✓ 长期保存: 所有数据持久化存储
echo.
echo 🌐 访问地址:
echo   主界面: http://localhost:3000
echo   API文档: http://localhost:5000/api/health
echo.

echo 正在打开浏览器...
timeout /t 3 /nobreak >nul
start http://localhost:3000

echo.
echo ✅ 系统已启动完成!
echo    现在所有数据都保存在SQL Server中，适合长期存储!
echo.
echo 💡 提示:
echo    - 如需停止系统，请关闭相关进程
echo    - 数据库连接: localhost\SQLEXPRESS
echo    - 所有用户数据已迁移到SQL Server
echo.

pause
