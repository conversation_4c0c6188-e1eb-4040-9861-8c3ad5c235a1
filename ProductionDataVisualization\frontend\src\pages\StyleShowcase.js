import React, { useState } from 'react';
import { Card, Button, Typography, Space, Row, Col, Switch, Tag } from 'antd';
import { motion } from 'framer-motion';
import { 
  DashboardOutlined, 
  UserOutlined, 
  SettingOutlined,
  CheckOutlined,
  StarOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

const StyleShowcase = () => {
  const [currentStyle, setCurrentStyle] = useState('clean');

  const styles = {
    clean: {
      name: 'Clean Assan（推荐）',
      description: '基于您提供的 Assan 模板，简洁现代的设计风格',
      background: '#fafbfc',
      cardBg: '#ffffff',
      primary: '#6366f1',
      features: [
        '简洁的白色背景',
        '现代化字体排版',
        '清晰的视觉层次',
        '优雅的卡片设计',
        '专业的配色方案',
        '响应式布局'
      ]
    },
    enhanced: {
      name: 'Enhanced <PERSON><PERSON>',
      description: '增强版本，包含丰富的视觉效果和动画',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      cardBg: 'rgba(255, 255, 255, 0.95)',
      primary: '#667eea',
      features: [
        '多层渐变背景',
        '动态粒子效果',
        '光线扫描动画',
        '玻璃态卡片',
        '3D 交互效果',
        '复杂视觉层次'
      ]
    },
    original: {
      name: 'Original',
      description: '原始系统样式，基础功能导向',
      background: '#f5f5f5',
      cardBg: '#ffffff',
      primary: '#1890ff',
      features: [
        '标准 Ant Design',
        '基础配色方案',
        '简单布局',
        '功能优先',
        '轻量级样式',
        '快速加载'
      ]
    }
  };

  const currentStyleData = styles[currentStyle];

  return (
    <div style={{
      minHeight: '100vh',
      background: currentStyleData.background,
      padding: '40px 20px',
      transition: 'all 0.5s ease'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* 标题区域 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          style={{ textAlign: 'center', marginBottom: '48px' }}
        >
          <Card
            style={{
              background: currentStyleData.cardBg,
              borderRadius: currentStyle === 'enhanced' ? '24px' : '12px',
              border: currentStyle === 'enhanced' ? '1px solid rgba(255, 255, 255, 0.8)' : '1px solid #e5e7eb',
              boxShadow: currentStyle === 'enhanced' 
                ? '0 32px 64px rgba(102, 126, 234, 0.15)' 
                : '0 4px 6px rgba(0, 0, 0, 0.05)',
              backdropFilter: currentStyle === 'enhanced' ? 'blur(25px)' : 'none',
              maxWidth: '800px',
              margin: '0 auto'
            }}
            bodyStyle={{ padding: '40px' }}
          >
            <Title level={1} style={{ 
              color: currentStyle === 'clean' ? '#111827' : '#1a202c',
              marginBottom: '16px',
              fontSize: '36px',
              fontWeight: '700'
            }}>
              Assan 设计系统展示
            </Title>
            
            <Paragraph style={{ 
              fontSize: '18px', 
              color: currentStyle === 'clean' ? '#6b7280' : '#64748b',
              marginBottom: '32px'
            }}>
              探索不同的设计风格，选择最适合您需求的界面样式
            </Paragraph>

            {/* 风格切换器 */}
            <Space size="large" wrap style={{ justifyContent: 'center' }}>
              {Object.entries(styles).map(([key, style]) => (
                <Button
                  key={key}
                  type={currentStyle === key ? 'primary' : 'default'}
                  size="large"
                  onClick={() => setCurrentStyle(key)}
                  style={{
                    borderRadius: '8px',
                    height: '48px',
                    padding: '0 24px',
                    fontWeight: '600',
                    background: currentStyle === key ? currentStyleData.primary : 'transparent',
                    borderColor: currentStyle === key ? currentStyleData.primary : '#d1d5db'
                  }}
                >
                  {style.name}
                  {key === 'clean' && <StarOutlined style={{ marginLeft: '8px', color: '#f59e0b' }} />}
                </Button>
              ))}
            </Space>
          </Card>
        </motion.div>

        {/* 当前风格介绍 */}
        <motion.div
          key={currentStyle}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          style={{ marginBottom: '32px' }}
        >
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <span>{currentStyleData.name}</span>
                {currentStyle === 'clean' && (
                  <Tag color="gold" icon={<StarOutlined />}>推荐</Tag>
                )}
              </div>
            }
            style={{
              background: currentStyleData.cardBg,
              borderRadius: currentStyle === 'enhanced' ? '20px' : '12px',
              border: currentStyle === 'enhanced' ? '1px solid rgba(255, 255, 255, 0.8)' : '1px solid #e5e7eb',
              boxShadow: currentStyle === 'enhanced' 
                ? '0 20px 40px rgba(102, 126, 234, 0.12)' 
                : '0 4px 6px rgba(0, 0, 0, 0.05)',
              backdropFilter: currentStyle === 'enhanced' ? 'blur(25px)' : 'none'
            }}
            bodyStyle={{ padding: '32px' }}
          >
            <Row gutter={[32, 32]}>
              <Col xs={24} md={12}>
                <Title level={3} style={{ 
                  color: currentStyle === 'clean' ? '#111827' : '#1a202c',
                  marginBottom: '16px'
                }}>
                  设计特色
                </Title>
                <Paragraph style={{ 
                  fontSize: '16px',
                  color: currentStyle === 'clean' ? '#6b7280' : '#64748b',
                  marginBottom: '24px'
                }}>
                  {currentStyleData.description}
                </Paragraph>
                
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  {currentStyleData.features.map((feature, index) => (
                    <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      <CheckOutlined style={{ 
                        color: currentStyleData.primary,
                        fontSize: '16px'
                      }} />
                      <Text style={{ 
                        fontSize: '15px',
                        color: currentStyle === 'clean' ? '#374151' : '#4a5568'
                      }}>
                        {feature}
                      </Text>
                    </div>
                  ))}
                </Space>
              </Col>
              
              <Col xs={24} md={12}>
                <Title level={3} style={{ 
                  color: currentStyle === 'clean' ? '#111827' : '#1a202c',
                  marginBottom: '16px'
                }}>
                  快速访问
                </Title>
                
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  {currentStyle === 'clean' && (
                    <>
                      <Button 
                        type="primary" 
                        icon={<DashboardOutlined />}
                        size="large"
                        block
                        href="/dashboard"
                        style={{
                          background: currentStyleData.primary,
                          borderColor: currentStyleData.primary,
                          borderRadius: '8px',
                          height: '48px',
                          fontWeight: '600'
                        }}
                      >
                        Clean 仪表板
                      </Button>
                      <Button 
                        icon={<UserOutlined />}
                        size="large"
                        block
                        href="/login"
                        style={{
                          borderRadius: '8px',
                          height: '48px',
                          fontWeight: '500'
                        }}
                      >
                        Clean 登录页面
                      </Button>
                    </>
                  )}
                  
                  {currentStyle === 'enhanced' && (
                    <>
                      <Button 
                        type="primary" 
                        icon={<DashboardOutlined />}
                        size="large"
                        block
                        href="/dashboard-enhanced"
                        style={{
                          background: currentStyleData.primary,
                          borderColor: currentStyleData.primary,
                          borderRadius: '12px',
                          height: '48px',
                          fontWeight: '600'
                        }}
                      >
                        Enhanced 仪表板
                      </Button>
                      <Button 
                        icon={<UserOutlined />}
                        size="large"
                        block
                        href="/login-enhanced"
                        style={{
                          borderRadius: '12px',
                          height: '48px',
                          fontWeight: '500'
                        }}
                      >
                        Enhanced 登录页面
                      </Button>
                    </>
                  )}
                  
                  {currentStyle === 'original' && (
                    <>
                      <Button 
                        type="primary" 
                        icon={<DashboardOutlined />}
                        size="large"
                        block
                        href="/dashboard-original"
                        style={{
                          borderRadius: '6px',
                          height: '40px'
                        }}
                      >
                        原始仪表板
                      </Button>
                      <Button 
                        icon={<UserOutlined />}
                        size="large"
                        block
                        href="/login-original"
                        style={{
                          borderRadius: '6px',
                          height: '40px'
                        }}
                      >
                        原始登录页面
                      </Button>
                    </>
                  )}
                  
                  <Button 
                    icon={<SettingOutlined />}
                    size="large"
                    block
                    href="/demo"
                    style={{
                      borderRadius: currentStyle === 'enhanced' ? '12px' : '8px',
                      height: '48px',
                      fontWeight: '500'
                    }}
                  >
                    查看完整演示
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>
        </motion.div>

        {/* 推荐说明 */}
        {currentStyle === 'clean' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card
              style={{
                background: '#f0f9ff',
                border: '1px solid #0ea5e9',
                borderRadius: '12px',
                textAlign: 'center'
              }}
              bodyStyle={{ padding: '24px' }}
            >
              <StarOutlined style={{ fontSize: '24px', color: '#0ea5e9', marginBottom: '12px' }} />
              <Title level={4} style={{ color: '#0c4a6e', marginBottom: '8px' }}>
                推荐使用 Clean Assan 风格
              </Title>
              <Text style={{ color: '#075985', fontSize: '16px' }}>
                基于您提供的 Assan 模板设计，完美平衡了美观性和实用性，
                适合专业的生产数据可视化系统。
              </Text>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default StyleShowcase;
