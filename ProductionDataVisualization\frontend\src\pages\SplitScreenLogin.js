import React, { useState } from 'react';
import { Form, Input, Button, message } from 'antd';
import { motion } from 'framer-motion';
import { useNavigate, Link } from 'react-router-dom';
import authService from '../services/authService';
import './SplitScreenLogin.css';

const SplitScreenLogin = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const onFinish = async (values) => {
    setLoading(true);
    try {
      const success = await authService.login(values.username, values.password);
      if (success) {
        message.success('登录成功！');
        navigate('/dashboard');
      } else {
        message.error('用户名或密码错误');
      }
    } catch (error) {
      message.error('登录失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = () => {
    message.info('请联系系统管理员重置密码');
  };

  return (
    <div className="split-login-container">
      {/* 动态不规整灰色线条 */}
      <div className="tech-lines">
        <svg className="irregular-line irregular-line-1" viewBox="0 0 800 200">
          <path d="M-50,100 Q150,50 300,120 T600,80 Q750,100 850,90" />
          <path d="M-30,120 Q200,80 350,140 T650,100 Q780,90 870,110" />
        </svg>
        <svg className="irregular-line irregular-line-2" viewBox="0 0 800 150">
          <path d="M850,75 Q650,40 500,90 T200,60 Q50,80 -50,70" />
          <path d="M870,95 Q680,60 520,110 T180,80 Q30,100 -70,90" />
        </svg>
        <svg className="irregular-line irregular-line-3" viewBox="0 0 800 180">
          <path d="M-50,90 Q100,130 250,70 T500,110 Q650,60 850,100" />
          <path d="M-70,110 Q120,150 280,90 T520,130 Q670,80 870,120" />
        </svg>
        {/* 额外的随机线条 */}
        <svg className="irregular-line" style={{top: '30%', left: '0', width: '100%', height: '100px'}}>
          <path d="M-20,50 Q180,20 320,70 Q480,30 620,60 Q750,80 850,45"
                style={{stroke: 'rgba(156, 163, 175, 0.15)', strokeWidth: '0.8', animation: 'drawLine1 22s ease-in-out infinite'}} />
        </svg>
        <svg className="irregular-line" style={{bottom: '35%', right: '0', width: '100%', height: '120px'}}>
          <path d="M850,60 Q700,90 550,40 Q400,80 250,50 Q100,30 -50,70"
                style={{stroke: 'rgba(156, 163, 175, 0.2)', strokeWidth: '1', animation: 'drawLine2 25s ease-in-out infinite reverse'}} />
        </svg>
      </div>



      {/* 简化的顶部Logo */}
      <div className="top-logo">
        <div className="logo">
          <div className="logo-square">
            <span style={{ color: 'white', fontSize: '14px', fontWeight: 'bold' }}>远</span>
          </div>
          <span className="logo-text">远大医药</span>
        </div>
      </div>

      {/* 主登录区域 */}
      <motion.div
        className="login-container"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="login-card">
          <h1 className="login-title">远大医药</h1>
          <p className="login-subtitle">偏差报表管理系统 - 专业 · 安全 · 高效</p>

        {/* 登录表单 */}
        <Form
          form={form}
          name="login"
          onFinish={onFinish}
          className="login-form"
          layout="vertical"
        >
          <div className="form-group">
            <label className="form-label">用户名</label>
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入用户名' }]}
              style={{ margin: 0 }}
            >
              <Input
                placeholder="请输入用户名"
                className="form-input"
              />
            </Form.Item>
          </div>

          <div className="form-group">
            <label className="form-label">密码</label>
            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码' }]}
              style={{ margin: 0 }}
            >
              <Input.Password
                placeholder="请输入密码"
                className="form-input"
              />
            </Form.Item>
          </div>

          <div className="form-options">
            <label className="remember-label">
              <input type="checkbox" className="remember-checkbox" />
              <span className="remember-text">记住我</span>
            </label>
            <button
              type="button"
              className="forgot-link"
              onClick={handleForgotPassword}
            >
              忘记密码？
            </button>
          </div>

          <Form.Item style={{ margin: 0 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              className="gradient-button"
              block
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        {/* 底部链接 */}
        <div className="bottom-links">
          <div className="social-login">
            <div className="social-text">其他登录方式</div>
            <div className="social-buttons">
              <button className="social-btn wechat">微信登录</button>
              <button className="social-btn enterprise">企业微信</button>
            </div>
          </div>

          <div className="help-links">
            <Link to="/help" className="help-link">帮助中心</Link>
            <span className="separator">·</span>
            <Link to="/contact" className="help-link">技术支持</Link>
            <span className="separator">·</span>
            <Link to="/register" className="help-link">用户注册</Link>
          </div>
        </div>
        </div>
      </motion.div>
    </div>
  );
};

export default SplitScreenLogin;
