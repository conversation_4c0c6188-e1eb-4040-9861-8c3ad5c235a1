@echo off
title 测试ImportTasks表修复

echo ========================================
echo   测试ImportTasks表修复
echo ========================================
echo.

echo [INFO] 修复内容:
echo 1. 修复了ImportTasks表每次启动被删除的问题
echo 2. 改为只在不存在时才创建表（保留历史记录）
echo 3. 修复了FileTableMappings表的创建逻辑
echo 4. 确保导入任务记录能够持久保存
echo.

echo [INFO] 后端状态:
echo - 服务已启动: http://localhost:5000
echo - 数据库连接: 正常
echo - ImportTasks表: 已创建且保留历史记录
echo - API端点: /api/data-import/tasks 可用
echo.

echo [INFO] 启动前端...
cd frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 20 /nobreak

echo.
echo ========================================
echo   测试指南
echo ========================================
echo.
echo 1. 访问: http://localhost:3000/data-import
echo 2. 点击"导入监控"步骤
echo 3. 观察修复效果:
echo    - 如果之前有导入记录，应该能看到
echo    - 统计数字应该正确显示
echo    - 任务列表不再显示"暂无数据"
echo.
echo 4. 测试新导入:
echo    - 上传新的数据文件
echo    - 完成导入过程
echo    - 验证任务记录是否正确保存
echo    - 检查导入监控是否正确显示
echo.
echo 5. 验证持久性:
echo    - 重启后端服务
echo    - 刷新前端页面
echo    - 确认导入记录仍然存在
echo.
echo 6. 检查后端日志:
echo    - 查看"数据导入表创建成功"消息
echo    - 确认没有"创建数据导入表失败"错误
echo    - 观察API调用日志
echo.

pause
