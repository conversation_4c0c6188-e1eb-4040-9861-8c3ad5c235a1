@echo off
title 测试数据库自动恢复功能

echo ========================================
echo   测试数据库自动恢复功能
echo ========================================
echo.

echo [INFO] 新增功能:
echo 1. 自动检查关键表是否存在
echo 2. 导入前自动恢复缺失的表结构
echo 3. 数据库健康检查API
echo 4. 手动恢复数据库表结构API
echo 5. 增强的错误处理和用户提示
echo.

echo [INFO] 测试场景:
echo - 删除数据库表结构后的自动恢复
echo - 导入数据时的表结构检查
echo - 友好的错误提示和恢复建议
echo.

echo [INFO] 后端状态:
echo - 服务已启动: http://localhost:5000
echo - 数据库连接: 正常
echo - 自动恢复功能: 已启用
echo.

echo [INFO] 启动前端...
cd frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 20 /nobreak

echo.
echo ========================================
echo   测试步骤
echo ========================================
echo.
echo 1. 正常导入测试:
echo    - 访问: http://localhost:3000/data-import
echo    - 上传数据文件并完成导入
echo    - 验证导入功能正常工作
echo.
echo 2. 删除表结构测试:
echo    - 使用SQL Server Management Studio
echo    - 删除ImportTasks和FileTableMappings表
echo    - 或者删除所有Data_开头的数据表
echo.
echo 3. 自动恢复测试:
echo    - 重新尝试导入数据
echo    - 观察后端控制台日志:
echo      * "⚠️ 检测到关键表缺失，尝试自动恢复..."
echo      * "✅ 数据库表结构自动恢复成功！"
echo    - 验证导入是否成功完成
echo.
echo 4. 健康检查测试:
echo    - 访问: http://localhost:5000/api/database/health
echo    - 查看数据库健康状态
echo    - 测试手动恢复: POST http://localhost:5000/api/database/recover
echo.
echo 5. 错误提示测试:
echo    - 如果自动恢复失败
echo    - 观察前端是否显示友好的错误提示
echo    - 检查是否提供恢复建议
echo.

echo ========================================
echo   预期结果
echo ========================================
echo.
echo ✅ 删除表结构后导入不再报500错误
echo ✅ 系统自动检测并恢复缺失的表
echo ✅ 用户看到清晰的错误提示和恢复建议
echo ✅ 数据库健康检查API正常工作
echo ✅ 手动恢复功能可用
echo.

pause
