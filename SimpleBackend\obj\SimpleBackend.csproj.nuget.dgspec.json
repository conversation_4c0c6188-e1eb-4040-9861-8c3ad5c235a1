{"format": 1, "restore": {"D:\\Source\\Report\\SimpleBackend\\SimpleBackend.csproj": {}}, "projects": {"D:\\Source\\Report\\SimpleBackend\\SimpleBackend.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Source\\Report\\SimpleBackend\\SimpleBackend.csproj", "projectName": "SimpleBackend", "projectPath": "D:\\Source\\Report\\SimpleBackend\\SimpleBackend.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Source\\Report\\SimpleBackend\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Data.Sqlite": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.6.25358.103/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.AspNetCore": "(,8.0.32767]", "Microsoft.AspNetCore.Antiforgery": "(,8.0.32767]", "Microsoft.AspNetCore.Authentication": "(,8.0.32767]", "Microsoft.AspNetCore.Authentication.Abstractions": "(,8.0.32767]", "Microsoft.AspNetCore.Authentication.BearerToken": "(,8.0.32767]", "Microsoft.AspNetCore.Authentication.Cookies": "(,8.0.32767]", "Microsoft.AspNetCore.Authentication.Core": "(,8.0.32767]", "Microsoft.AspNetCore.Authentication.OAuth": "(,8.0.32767]", "Microsoft.AspNetCore.Authorization": "(,8.0.32767]", "Microsoft.AspNetCore.Authorization.Policy": "(,8.0.32767]", "Microsoft.AspNetCore.Components": "(,8.0.32767]", "Microsoft.AspNetCore.Components.Authorization": "(,8.0.32767]", "Microsoft.AspNetCore.Components.Endpoints": "(,8.0.32767]", "Microsoft.AspNetCore.Components.Forms": "(,8.0.32767]", "Microsoft.AspNetCore.Components.Server": "(,8.0.32767]", "Microsoft.AspNetCore.Components.Web": "(,8.0.32767]", "Microsoft.AspNetCore.Connections.Abstractions": "(,8.0.32767]", "Microsoft.AspNetCore.CookiePolicy": "(,8.0.32767]", "Microsoft.AspNetCore.Cors": "(,8.0.32767]", "Microsoft.AspNetCore.Cryptography.Internal": "(,8.0.32767]", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "(,8.0.32767]", "Microsoft.AspNetCore.DataProtection": "(,8.0.32767]", "Microsoft.AspNetCore.DataProtection.Abstractions": "(,8.0.32767]", "Microsoft.AspNetCore.DataProtection.Extensions": "(,8.0.32767]", "Microsoft.AspNetCore.Diagnostics": "(,8.0.32767]", "Microsoft.AspNetCore.Diagnostics.Abstractions": "(,8.0.32767]", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "(,8.0.32767]", "Microsoft.AspNetCore.HostFiltering": "(,8.0.32767]", "Microsoft.AspNetCore.Hosting": "(,8.0.32767]", "Microsoft.AspNetCore.Hosting.Abstractions": "(,8.0.32767]", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "(,8.0.32767]", "Microsoft.AspNetCore.Html.Abstractions": "(,8.0.32767]", "Microsoft.AspNetCore.Http": "(,8.0.32767]", "Microsoft.AspNetCore.Http.Abstractions": "(,8.0.32767]", "Microsoft.AspNetCore.Http.Connections": "(,8.0.32767]", "Microsoft.AspNetCore.Http.Connections.Common": "(,8.0.32767]", "Microsoft.AspNetCore.Http.Extensions": "(,8.0.32767]", "Microsoft.AspNetCore.Http.Features": "(,8.0.32767]", "Microsoft.AspNetCore.Http.Results": "(,8.0.32767]", "Microsoft.AspNetCore.HttpLogging": "(,8.0.32767]", "Microsoft.AspNetCore.HttpOverrides": "(,8.0.32767]", "Microsoft.AspNetCore.HttpsPolicy": "(,8.0.32767]", "Microsoft.AspNetCore.Identity": "(,8.0.32767]", "Microsoft.AspNetCore.Localization": "(,8.0.32767]", "Microsoft.AspNetCore.Localization.Routing": "(,8.0.32767]", "Microsoft.AspNetCore.Metadata": "(,8.0.32767]", "Microsoft.AspNetCore.Mvc": "(,8.0.32767]", "Microsoft.AspNetCore.Mvc.Abstractions": "(,8.0.32767]", "Microsoft.AspNetCore.Mvc.ApiExplorer": "(,8.0.32767]", "Microsoft.AspNetCore.Mvc.Core": "(,8.0.32767]", "Microsoft.AspNetCore.Mvc.Cors": "(,8.0.32767]", "Microsoft.AspNetCore.Mvc.DataAnnotations": "(,8.0.32767]", "Microsoft.AspNetCore.Mvc.Formatters.Json": "(,8.0.32767]", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "(,8.0.32767]", "Microsoft.AspNetCore.Mvc.Localization": "(,8.0.32767]", "Microsoft.AspNetCore.Mvc.Razor": "(,8.0.32767]", "Microsoft.AspNetCore.Mvc.RazorPages": "(,8.0.32767]", "Microsoft.AspNetCore.Mvc.TagHelpers": "(,8.0.32767]", "Microsoft.AspNetCore.Mvc.ViewFeatures": "(,8.0.32767]", "Microsoft.AspNetCore.OutputCaching": "(,8.0.32767]", "Microsoft.AspNetCore.RateLimiting": "(,8.0.32767]", "Microsoft.AspNetCore.Razor": "(,8.0.32767]", "Microsoft.AspNetCore.Razor.Runtime": "(,8.0.32767]", "Microsoft.AspNetCore.RequestDecompression": "(,8.0.32767]", "Microsoft.AspNetCore.ResponseCaching": "(,8.0.32767]", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "(,8.0.32767]", "Microsoft.AspNetCore.ResponseCompression": "(,8.0.32767]", "Microsoft.AspNetCore.Rewrite": "(,8.0.32767]", "Microsoft.AspNetCore.Routing": "(,8.0.32767]", "Microsoft.AspNetCore.Routing.Abstractions": "(,8.0.32767]", "Microsoft.AspNetCore.Server.HttpSys": "(,8.0.32767]", "Microsoft.AspNetCore.Server.IIS": "(,8.0.32767]", "Microsoft.AspNetCore.Server.IISIntegration": "(,8.0.32767]", "Microsoft.AspNetCore.Server.Kestrel": "(,8.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Core": "(,8.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "(,8.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "(,8.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "(,8.0.32767]", "Microsoft.AspNetCore.Session": "(,8.0.32767]", "Microsoft.AspNetCore.SignalR": "(,8.0.32767]", "Microsoft.AspNetCore.SignalR.Common": "(,8.0.32767]", "Microsoft.AspNetCore.SignalR.Core": "(,8.0.32767]", "Microsoft.AspNetCore.SignalR.Protocols.Json": "(,8.0.32767]", "Microsoft.AspNetCore.StaticFiles": "(,8.0.32767]", "Microsoft.AspNetCore.WebSockets": "(,8.0.32767]", "Microsoft.AspNetCore.WebUtilities": "(,8.0.32767]", "Microsoft.CSharp": "(,4.7.32767]", "Microsoft.Extensions.Caching.Abstractions": "(,8.0.32767]", "Microsoft.Extensions.Caching.Memory": "(,8.0.32767]", "Microsoft.Extensions.Configuration": "(,8.0.32767]", "Microsoft.Extensions.Configuration.Abstractions": "(,8.0.32767]", "Microsoft.Extensions.Configuration.Binder": "(,8.0.32767]", "Microsoft.Extensions.Configuration.CommandLine": "(,8.0.32767]", "Microsoft.Extensions.Configuration.EnvironmentVariables": "(,8.0.32767]", "Microsoft.Extensions.Configuration.FileExtensions": "(,8.0.32767]", "Microsoft.Extensions.Configuration.Ini": "(,8.0.32767]", "Microsoft.Extensions.Configuration.Json": "(,8.0.32767]", "Microsoft.Extensions.Configuration.KeyPerFile": "(,8.0.32767]", "Microsoft.Extensions.Configuration.UserSecrets": "(,8.0.32767]", "Microsoft.Extensions.Configuration.Xml": "(,8.0.32767]", "Microsoft.Extensions.DependencyInjection": "(,8.0.32767]", "Microsoft.Extensions.DependencyInjection.Abstractions": "(,8.0.32767]", "Microsoft.Extensions.Diagnostics": "(,8.0.32767]", "Microsoft.Extensions.Diagnostics.Abstractions": "(,8.0.32767]", "Microsoft.Extensions.Diagnostics.HealthChecks": "(,8.0.32767]", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "(,8.0.32767]", "Microsoft.Extensions.Features": "(,8.0.32767]", "Microsoft.Extensions.FileProviders.Abstractions": "(,8.0.32767]", "Microsoft.Extensions.FileProviders.Composite": "(,8.0.32767]", "Microsoft.Extensions.FileProviders.Embedded": "(,8.0.32767]", "Microsoft.Extensions.FileProviders.Physical": "(,8.0.32767]", "Microsoft.Extensions.FileSystemGlobbing": "(,8.0.32767]", "Microsoft.Extensions.Hosting": "(,8.0.32767]", "Microsoft.Extensions.Hosting.Abstractions": "(,8.0.32767]", "Microsoft.Extensions.Http": "(,8.0.32767]", "Microsoft.Extensions.Identity.Core": "(,8.0.32767]", "Microsoft.Extensions.Identity.Stores": "(,8.0.32767]", "Microsoft.Extensions.Localization": "(,8.0.32767]", "Microsoft.Extensions.Localization.Abstractions": "(,8.0.32767]", "Microsoft.Extensions.Logging": "(,8.0.32767]", "Microsoft.Extensions.Logging.Abstractions": "(,8.0.32767]", "Microsoft.Extensions.Logging.Configuration": "(,8.0.32767]", "Microsoft.Extensions.Logging.Console": "(,8.0.32767]", "Microsoft.Extensions.Logging.Debug": "(,8.0.32767]", "Microsoft.Extensions.Logging.EventLog": "(,8.0.32767]", "Microsoft.Extensions.Logging.EventSource": "(,8.0.32767]", "Microsoft.Extensions.Logging.TraceSource": "(,8.0.32767]", "Microsoft.Extensions.ObjectPool": "(,8.0.32767]", "Microsoft.Extensions.Options": "(,8.0.32767]", "Microsoft.Extensions.Options.ConfigurationExtensions": "(,8.0.32767]", "Microsoft.Extensions.Options.DataAnnotations": "(,8.0.32767]", "Microsoft.Extensions.Primitives": "(,8.0.32767]", "Microsoft.Extensions.WebEncoders": "(,8.0.32767]", "Microsoft.JSInterop": "(,8.0.32767]", "Microsoft.Net.Http.Headers": "(,8.0.32767]", "Microsoft.NETCore.App": "(,2.1.32767]", "Microsoft.VisualBasic": "(,10.3.32767]", "Microsoft.Win32.Primitives": "(,4.3.32767]", "Microsoft.Win32.Registry": "(,5.0.32767]", "Microsoft.Win32.SystemEvents": "(,5.0.32767]", "runtime.any.System.Collections": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.any.System.Globalization": "(,4.3.32767]", "runtime.any.System.Globalization.Calendars": "(,4.3.32767]", "runtime.any.System.IO": "(,4.3.32767]", "runtime.any.System.Reflection": "(,4.3.32767]", "runtime.any.System.Reflection.Extensions": "(,4.3.32767]", "runtime.any.System.Reflection.Primitives": "(,4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.any.System.Runtime": "(,4.3.32767]", "runtime.any.System.Runtime.Handles": "(,4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.any.System.Text.Encoding": "(,4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.any.System.Threading.Tasks": "(,4.3.32767]", "runtime.any.System.Threading.Timer": "(,4.3.32767]", "runtime.aot.System.Collections": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.aot.System.Globalization": "(,4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(,4.3.32767]", "runtime.aot.System.IO": "(,4.3.32767]", "runtime.aot.System.Reflection": "(,4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(,4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(,4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.aot.System.Runtime": "(,4.3.32767]", "runtime.aot.System.Runtime.Handles": "(,4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.aot.System.Text.Encoding": "(,4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.aot.System.Threading.Tasks": "(,4.3.32767]", "runtime.aot.System.Threading.Timer": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.unix.System.Console": "(,4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.unix.System.IO.FileSystem": "(,4.3.32767]", "runtime.unix.System.Net.Primitives": "(,4.3.32767]", "runtime.unix.System.Net.Sockets": "(,4.3.32767]", "runtime.unix.System.Private.Uri": "(,4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.win.System.Console": "(,4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.win.System.IO.FileSystem": "(,4.3.32767]", "runtime.win.System.Net.Primitives": "(,4.3.32767]", "runtime.win.System.Net.Sockets": "(,4.3.32767]", "runtime.win.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7.System.Private.Uri": "(,4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(,4.3.32767]", "System.AppContext": "(,4.3.32767]", "System.Buffers": "(,4.5.32767]", "System.Collections": "(,4.3.32767]", "System.Collections.Concurrent": "(,4.3.32767]", "System.Collections.Immutable": "(,8.0.32767]", "System.Collections.NonGeneric": "(,4.3.32767]", "System.Collections.Specialized": "(,4.3.32767]", "System.ComponentModel": "(,4.3.32767]", "System.ComponentModel.Annotations": "(,5.0.32767]", "System.ComponentModel.EventBasedAsync": "(,4.3.32767]", "System.ComponentModel.Primitives": "(,4.3.32767]", "System.ComponentModel.TypeConverter": "(,4.3.32767]", "System.Console": "(,4.3.32767]", "System.Data.Common": "(,4.3.32767]", "System.Data.DataSetExtensions": "(,4.5.32767]", "System.Diagnostics.Contracts": "(,4.3.32767]", "System.Diagnostics.Debug": "(,4.3.32767]", "System.Diagnostics.DiagnosticSource": "(,8.0.32767]", "System.Diagnostics.EventLog": "(,8.0.32767]", "System.Diagnostics.FileVersionInfo": "(,4.3.32767]", "System.Diagnostics.Process": "(,4.3.32767]", "System.Diagnostics.StackTrace": "(,4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(,4.3.32767]", "System.Diagnostics.Tools": "(,4.3.32767]", "System.Diagnostics.TraceSource": "(,4.3.32767]", "System.Diagnostics.Tracing": "(,4.3.32767]", "System.Drawing.Common": "(,5.0.32767]", "System.Drawing.Primitives": "(,4.3.32767]", "System.Dynamic.Runtime": "(,4.3.32767]", "System.Formats.Asn1": "(,8.0.32767]", "System.Globalization": "(,4.3.32767]", "System.Globalization.Calendars": "(,4.3.32767]", "System.Globalization.Extensions": "(,4.3.32767]", "System.IO": "(,4.3.32767]", "System.IO.Compression": "(,4.3.32767]", "System.IO.Compression.ZipFile": "(,4.3.32767]", "System.IO.FileSystem": "(,4.3.32767]", "System.IO.FileSystem.AccessControl": "(,5.0.32767]", "System.IO.FileSystem.DriveInfo": "(,4.3.32767]", "System.IO.FileSystem.Primitives": "(,4.3.32767]", "System.IO.FileSystem.Watcher": "(,4.3.32767]", "System.IO.IsolatedStorage": "(,4.3.32767]", "System.IO.MemoryMappedFiles": "(,4.3.32767]", "System.IO.Pipelines": "(,8.0.32767]", "System.IO.Pipes": "(,4.3.32767]", "System.IO.Pipes.AccessControl": "(,4.6.32767]", "System.IO.UnmanagedMemoryStream": "(,4.3.32767]", "System.Linq": "(,4.3.32767]", "System.Linq.Expressions": "(,4.3.32767]", "System.Linq.Parallel": "(,4.3.32767]", "System.Linq.Queryable": "(,4.3.32767]", "System.Memory": "(,4.5.32767]", "System.Net.Http": "(,4.3.32767]", "System.Net.Http.Json": "(,8.0.32767]", "System.Net.NameResolution": "(,4.3.32767]", "System.Net.NetworkInformation": "(,4.3.32767]", "System.Net.Ping": "(,4.3.32767]", "System.Net.Primitives": "(,4.3.32767]", "System.Net.Requests": "(,4.3.32767]", "System.Net.Security": "(,4.3.32767]", "System.Net.Sockets": "(,4.3.32767]", "System.Net.WebHeaderCollection": "(,4.3.32767]", "System.Net.WebSockets": "(,4.3.32767]", "System.Net.WebSockets.Client": "(,4.3.32767]", "System.Numerics.Vectors": "(,4.5.32767]", "System.ObjectModel": "(,4.3.32767]", "System.Private.DataContractSerialization": "(,4.3.32767]", "System.Private.Uri": "(,4.3.32767]", "System.Reflection": "(,4.3.32767]", "System.Reflection.DispatchProxy": "(,4.7.32767]", "System.Reflection.Emit": "(,4.7.32767]", "System.Reflection.Emit.ILGeneration": "(,4.7.32767]", "System.Reflection.Emit.Lightweight": "(,4.7.32767]", "System.Reflection.Extensions": "(,4.3.32767]", "System.Reflection.Metadata": "(,8.0.32767]", "System.Reflection.Primitives": "(,4.3.32767]", "System.Reflection.TypeExtensions": "(,4.7.32767]", "System.Resources.Reader": "(,4.3.32767]", "System.Resources.ResourceManager": "(,4.3.32767]", "System.Resources.Writer": "(,4.3.32767]", "System.Runtime": "(,4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(,6.0.32767]", "System.Runtime.CompilerServices.VisualC": "(,4.3.32767]", "System.Runtime.Extensions": "(,4.3.32767]", "System.Runtime.Handles": "(,4.3.32767]", "System.Runtime.InteropServices": "(,4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(,4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(,4.3.32767]", "System.Runtime.Loader": "(,4.3.32767]", "System.Runtime.Numerics": "(,4.3.32767]", "System.Runtime.Serialization.Formatters": "(,4.3.32767]", "System.Runtime.Serialization.Json": "(,4.3.32767]", "System.Runtime.Serialization.Primitives": "(,4.3.32767]", "System.Runtime.Serialization.Xml": "(,4.3.32767]", "System.Runtime.WindowsRuntime": "(,4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(,4.7.32767]", "System.Security.AccessControl": "(,6.0.32767]", "System.Security.Claims": "(,4.3.32767]", "System.Security.Cryptography.Algorithms": "(,4.3.32767]", "System.Security.Cryptography.Cng": "(,5.0.32767]", "System.Security.Cryptography.Csp": "(,4.3.32767]", "System.Security.Cryptography.Encoding": "(,4.3.32767]", "System.Security.Cryptography.OpenSsl": "(,5.0.32767]", "System.Security.Cryptography.Pkcs": "(,8.0.32767]", "System.Security.Cryptography.Primitives": "(,4.3.32767]", "System.Security.Cryptography.X509Certificates": "(,4.3.32767]", "System.Security.Cryptography.Xml": "(,8.0.32767]", "System.Security.Permissions": "(,5.0.32767]", "System.Security.Principal": "(,4.3.32767]", "System.Security.Principal.Windows": "(,5.0.32767]", "System.Security.SecureString": "(,4.3.32767]", "System.Text.Encoding": "(,4.3.32767]", "System.Text.Encoding.CodePages": "(,8.0.32767]", "System.Text.Encoding.Extensions": "(,4.3.32767]", "System.Text.Encodings.Web": "(,8.0.32767]", "System.Text.Json": "(,8.0.32767]", "System.Text.RegularExpressions": "(,4.3.32767]", "System.Threading": "(,4.3.32767]", "System.Threading.Channels": "(,8.0.32767]", "System.Threading.Overlapped": "(,4.3.32767]", "System.Threading.RateLimiting": "(,8.0.32767]", "System.Threading.Tasks": "(,4.3.32767]", "System.Threading.Tasks.Dataflow": "(,8.0.32767]", "System.Threading.Tasks.Extensions": "(,4.5.32767]", "System.Threading.Tasks.Parallel": "(,4.3.32767]", "System.Threading.Thread": "(,4.3.32767]", "System.Threading.ThreadPool": "(,4.3.32767]", "System.Threading.Timer": "(,4.3.32767]", "System.ValueTuple": "(,4.5.32767]", "System.Windows.Extensions": "(,5.0.32767]", "System.Xml.ReaderWriter": "(,4.3.32767]", "System.Xml.XDocument": "(,4.3.32767]", "System.Xml.XmlDocument": "(,4.3.32767]", "System.Xml.XmlSerializer": "(,4.3.32767]", "System.Xml.XPath": "(,4.3.32767]", "System.Xml.XPath.XDocument": "(,4.3.32767]"}}}, "runtimes": {"win-x64": {"#import": []}}}}}