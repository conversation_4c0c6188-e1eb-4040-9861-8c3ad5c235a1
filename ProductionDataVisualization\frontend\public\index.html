<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="眼药制剂生产数据可视化系统"
    />
    <!-- 禁止缓存 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>眼药制剂生产数据可视化系统</title>
    <!-- 强制刷新CSS的脚本 -->
    <script src="%PUBLIC_URL%/refresh.js" defer></script>
    <script>
      // 智能清除认证缓存
      window.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载 - 检查是否需要清除认证缓存');

        // 检查是否在登录保护窗口内
        const loginSuccessTime = sessionStorage.getItem('loginSuccessTime');
        const now = Date.now();
        const isInProtectionWindow = loginSuccessTime && (now - parseInt(loginSuccessTime)) < 5000;

        if (!isInProtectionWindow) {
          console.log('页面加载 - 不在保护窗口内，清除认证缓存');

          // 强制清除所有认证相关的缓存
          try {
            // 清除localStorage中的认证数据
            const authKeys = ['token', 'user', 'authToken', 'currentUser', 'userSession', 'registeredUsers'];
            authKeys.forEach(key => {
              localStorage.removeItem(key);
            });

            // 清除所有以auth、user、token开头的存储项
            for (let i = localStorage.length - 1; i >= 0; i--) {
              const key = localStorage.key(i);
              if (key && (key.startsWith('auth') || key.startsWith('user') || key.startsWith('token'))) {
                localStorage.removeItem(key);
              }
            }

            console.log('认证缓存已清除 - 用户必须重新登录');
          } catch (error) {
            console.error('清除认证缓存时发生错误:', error);
          }
        } else {
          console.log('页面加载 - 在登录保护窗口内，跳过清除');
        }

        // 检查是否需要强制刷新
        const lastRefresh = localStorage.getItem('lastRefresh');
        const now = new Date().getTime();

        // 如果上次刷新时间超过5分钟，则强制刷新
        if (!lastRefresh || (now - lastRefresh) > 300000) {
          localStorage.setItem('lastRefresh', now);
          // 清除缓存并重新加载
          if (window.location.href.indexOf('?refresh=true') === -1) {
            window.location.href = window.location.href + (window.location.href.indexOf('?') === -1 ? '?refresh=true' : '&refresh=true');
          }
        }

        // 移除URL中的refresh参数
        if (window.location.href.indexOf('refresh=true') !== -1) {
          const url = window.location.href.replace(/[?&]refresh=true/g, '');
          window.history.replaceState({}, document.title, url);
        }
      });
    </script>
  </head>
  <body>
    <noscript>您需要启用JavaScript来运行此应用程序。</noscript>
    <div id="root"></div>
  </body>
</html>
