-- 检查数据导入相关表是否存在
USE ProductionDataVisualizationDb;

-- 检查ImportTasks表
IF EXISTS (SELECT * FROM sysobjects WHERE name='ImportTasks' AND xtype='U')
BEGIN
    PRINT '✓ ImportTasks表存在'
    SELECT COUNT(*) as ImportTasks_Count FROM ImportTasks;
    SELECT TOP 5 * FROM ImportTasks ORDER BY CreatedAt DESC;
END
ELSE
BEGIN
    PRINT '✗ ImportTasks表不存在'
END

-- 检查ImportedData表
IF EXISTS (SELECT * FROM sysobjects WHERE name='ImportedData' AND xtype='U')
BEGIN
    PRINT '✓ ImportedData表存在'
    SELECT COUNT(*) as ImportedData_Count FROM ImportedData;
    SELECT TOP 10 * FROM ImportedData ORDER BY CreatedAt DESC;
END
ELSE
BEGIN
    PRINT '✗ ImportedData表不存在'
END

-- 检查所有表
SELECT 
    TABLE_NAME,
    TABLE_TYPE
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;
