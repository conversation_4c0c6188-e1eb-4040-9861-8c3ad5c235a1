@echo off
title 验证dataResult错误修复

echo ========================================
echo   验证dataResult错误修复
echo ========================================
echo.

echo [INFO] 问题诊断结果:
echo ✅ 后端功能正常 - 数据成功导入到数据库
echo ✅ 表名生成正确 - Data_test_data
echo ✅ 自动恢复功能正常 - 表存在性检查通过
echo ❌ 前端JavaScript错误 - dataResult is not defined
echo.

echo [INFO] 修复内容:
echo - 修复了第386行的变量引用错误
echo - 将 dataResult.processedRows 改为 processedRows
echo - 前端代码现在应该正常工作
echo.

echo [INFO] 后端日志确认:
echo "表存在性检查: ImportTasks=True, FileTableMappings=True, Users=True"
echo "生成表名: Data_test_data"
echo "数据导入完成，共导入 5 行到表 [Data_test_data]"
echo "返回状态码: 200"
echo.

echo [INFO] 启动前端...
cd frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 20 /nobreak

echo.
echo ========================================
echo   验证步骤
echo ========================================
echo.
echo 1. 访问: http://localhost:3000/data-import
echo 2. 上传 test_data.csv 文件
echo 3. 观察是否还有 "dataResult is not defined" 错误
echo 4. 验证导入成功后是否正确跳转到监控页面
echo 5. 检查导入任务列表是否显示新的导入记录
echo.

echo [INFO] 预期结果:
echo ✅ 不再出现 "dataResult is not defined" 错误
echo ✅ 导入成功后显示成功消息
echo ✅ 自动跳转到导入监控页面
echo ✅ 任务列表显示新的导入记录
echo ✅ 数据预览功能正常工作
echo.

echo [INFO] 数据库验证:
echo - 表名: Data_test_data
echo - 行数: 5行测试数据
echo - 列: VarName, TimeString, VarValue, Validity, Time_ms
echo.

pause
