import api from './api';
import { jwtDecode } from 'jwt-decode';

const AUTH_ENDPOINT = '/api/auth';
const SIMPLE_AUTH_ENDPOINT = '/api/simple-auth';

// 用户登录
const login = async (usernameOrEmail, password) => {
  try {
    // 尝试调用后端API登录
    const response = await api.post(`${AUTH_ENDPOINT}/login`, {
      usernameOrEmail,
      password
    });
    
    if (response.data.token) {
      // 保存token和用户信息到localStorage
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify({
        id: response.data.userId,
        username: response.data.username,
        email: response.data.email,
        fullName: response.data.fullName,
        roles: response.data.roles,
        permissions: response.data.permissions
      }));

      // 设置登录成功保护窗口
      sessionStorage.setItem('loginSuccessTime', Date.now().toString());
      console.log('登录成功，设置保护窗口');
    }

    return response.data;
  } catch (error) {
    console.error('登录API调用失败，使用临时登录:', error);
    
    // 如果API调用失败，使用临时登录（仅用于开发测试）
    if (usernameOrEmail === 'admin' && password === 'admin123') {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkFkbWluIFVzZXIiLCJpYXQiOjE1MTYyMzkwMjIsImV4cCI6MTkxNjIzOTAyMn0.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
      
      // 确保包含所有必要的权限
      const mockUser = {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        fullName: '管理员',
        roles: ['Admin'],
        permissions: [
          'ViewUsers', 'CreateUser', 'EditUser', 'DeleteUser',
          'ViewRoles', 'CreateRole', 'EditRole', 'DeleteRole',
          'ViewPermissions', 'AssignPermissions',
          'ViewData', 'ImportData', 'ExportData', 'DeleteData',
          'ViewCharts', 'CreateChart', 'EditChart', 'DeleteChart',
          'ViewDashboards', 'CreateDashboard', 'EditDashboard', 'DeleteDashboard',
          'ViewSystemSettings', 'EditSystemSettings'
        ]
      };
      
      console.log("使用模拟登录数据:", mockUser);
      localStorage.setItem('token', mockToken);
      localStorage.setItem('user', JSON.stringify(mockUser));

      // 设置登录成功保护窗口
      sessionStorage.setItem('loginSuccessTime', Date.now().toString());
      console.log('模拟登录成功，设置保护窗口');

      return {
        userId: mockUser.id,
        username: mockUser.username,
        email: mockUser.email,
        fullName: mockUser.fullName,
        token: mockToken,
        roles: mockUser.roles,
        permissions: mockUser.permissions
      };
    } else if (usernameOrEmail === 'user' && password === 'user123') {
      // 添加普通用户的模拟登录
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyMzQ1Njc4OTAxIiwibmFtZSI6IlVzZXIiLCJpYXQiOjE1MTYyMzkwMjIsImV4cCI6MTkxNjIzOTAyMn0.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
      const mockUser = {
        id: '3',
        username: 'user',
        email: '<EMAIL>',
        fullName: '普通用户',
        roles: ['User'],
        permissions: [
          'ViewData', 
          'ViewCharts', 
          'ViewDashboards'
        ]
      };
      
      console.log("使用模拟登录数据:", mockUser);
      localStorage.setItem('token', mockToken);
      localStorage.setItem('user', JSON.stringify(mockUser));
      
      return {
        userId: mockUser.id,
        username: mockUser.username,
        email: mockUser.email,
        fullName: mockUser.fullName,
        token: mockToken,
        roles: mockUser.roles,
        permissions: mockUser.permissions
      };
    }

    // 🚫 移除localStorage回退机制，直接抛出错误
    console.error('🚫 登录失败，不使用本地存储回退');

    throw error.response ? error.response.data : { message: '用户名或密码错误' };
  }
};

// 用户注册
const register = async (username, email, password, fullName) => {
  try {
    // 首先尝试调用简单的后端API
    const response = await api.post(`${SIMPLE_AUTH_ENDPOINT}/register`, {
      username,
      email,
      password,
      fullName
    });

    console.log('用户注册成功（简单后端API）:', response.data);
    return response.data;
  } catch (error) {
    // 详细记录API调用失败的原因
    console.error('❌ 注册API调用失败，详细信息:');
    console.error('- 错误类型:', error.name);
    console.error('- 错误消息:', error.message);
    console.error('- API地址:', api.defaults.baseURL + SIMPLE_AUTH_ENDPOINT + '/register');

    if (error.response) {
      console.error('- 响应状态:', error.response.status);
      console.error('- 响应数据:', error.response.data);
    } else if (error.request) {
      console.error('- 请求发送但无响应:', error.request);
    } else {
      console.error('- 请求配置错误:', error.config);
    }

    // 🚫 移除localStorage回退机制，直接抛出错误
    console.error('🚫 注册失败，不使用本地存储回退');

    // 直接抛出API错误，不回退到localStorage
    throw error.response ? error.response.data : { message: '注册失败，请检查网络连接或联系管理员' };
  }
};

// 获取当前用户信息
const getCurrentUser = async () => {
  try {
    const response = await api.get(`${AUTH_ENDPOINT}/me`);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : { message: '获取用户信息失败' };
  }
};

// 退出登录 - 强制清除所有认证缓存
const logout = () => {
  console.log('执行强制登出，清除所有认证缓存');

  // 清除localStorage中的所有认证相关数据
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  localStorage.removeItem('authToken');
  localStorage.removeItem('currentUser');
  localStorage.removeItem('userSession');
  localStorage.removeItem('registeredUsers');

  // 清除sessionStorage中的所有认证相关数据
  sessionStorage.removeItem('token');
  sessionStorage.removeItem('user');
  sessionStorage.removeItem('authToken');
  sessionStorage.removeItem('currentUser');
  sessionStorage.removeItem('userSession');

  console.log('所有认证缓存已清除');
};

// 强制登出 - 用于系统启动时清除所有缓存
const forceLogout = () => {
  console.log('执行强制登出，系统要求重新登录');
  logout();

  // 额外清除可能的其他缓存
  try {
    // 清除所有以auth、user、token开头的localStorage项
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith('auth') || key.startsWith('user') || key.startsWith('token'))) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key));

    // 清除所有以auth、user、token开头的sessionStorage项
    const sessionKeysToRemove = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && (key.startsWith('auth') || key.startsWith('user') || key.startsWith('token'))) {
        sessionKeysToRemove.push(key);
      }
    }
    sessionKeysToRemove.forEach(key => sessionStorage.removeItem(key));

    console.log('强制登出完成，所有相关缓存已清除');
  } catch (error) {
    console.error('清除缓存时发生错误:', error);
  }
};

// 检查用户是否已登录
const isAuthenticated = () => {
  try {
    const token = localStorage.getItem('token');
    if (!token) return false;

    // 检查是否是本地用户token（以local_user_token_开头）
    if (token.startsWith('local_user_token_')) {
      // 本地用户token，检查用户信息是否存在
      const user = localStorage.getItem('user');
      return !!user;
    }

    try {
      // 检查JWT token是否过期
      const decoded = jwtDecode(token);
      const currentTime = Date.now() / 1000;

      if (decoded.exp < currentTime) {
        // token已过期，清除本地存储
        logout();
        return false;
      }

      return true;
    } catch (error) {
      // token解析失败，可能是模拟token
      console.warn('Token解析失败，检查是否为模拟token:', error);

      // 检查是否有用户信息，如果有则认为是有效的模拟登录
      const user = localStorage.getItem('user');
      if (user) {
        console.log('发现模拟用户登录，认证通过');
        return true;
      }

      logout();
      return false;
    }
  } catch (error) {
    console.error('认证检查失败:', error);
    return false;
  }
};

// 获取当前用户信息（从localStorage）
const getUser = () => {
  const userStr = localStorage.getItem('user');
  if (!userStr) return null;
  
  try {
    return JSON.parse(userStr);
  } catch (error) {
    return null;
  }
};

// 检查用户是否有特定权限
const hasPermission = (permission) => {
  try {
    const user = getUser();
    if (!user) {
      console.log(`hasPermission: 用户信息不存在，权限检查 ${permission} 失败`);
      return false;
    }
    
    if (!user.permissions) {
      console.log(`hasPermission: 用户没有权限属性，权限检查 ${permission} 失败`);
      return false;
    }
    
    // 确保返回布尔值而不是对象
    const result = Boolean(user.permissions.includes(permission));
    console.log(`hasPermission: 用户 ${user.username} 检查权限 ${permission}，结果: ${result}`);
    console.log(`hasPermission: 用户权限列表:`, user.permissions);
    return result;
  } catch (error) {
    console.error('权限检查失败:', error);
    return false;
  }
};

// 检查用户是否有特定角色
const hasRole = (role) => {
  try {
    const user = getUser();
    if (!user || !user.roles) return false;
    
    // 确保返回布尔值而不是对象
    return Boolean(user.roles.includes(role));
  } catch (error) {
    console.error('角色检查失败:', error);
    return false;
  }
};

const authService = {
  login,
  register,
  logout,
  forceLogout,
  getCurrentUser,
  isAuthenticated,
  getUser,
  hasPermission,
  hasRole
};

export default authService; 