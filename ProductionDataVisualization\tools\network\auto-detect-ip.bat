@echo off
title Auto IP Detection Tool

echo ========================================
echo   Auto IP Detection and Configuration Tool v2.0
echo ========================================
echo.

echo [INFO] Detecting current server network configuration...
echo.

REM Get local IP address
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /i "IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set "LOCAL_IP=%%b"
        goto :found_ip
    )
)

:found_ip
if "%LOCAL_IP%"=="" (
    echo [ERROR] Cannot detect local IP address
    pause
    exit /b 1
)

REM Clean IP address (remove spaces)
set LOCAL_IP=%LOCAL_IP: =%

echo [SUCCESS] Detected local IP address: %LOCAL_IP%
echo.

REM Detect network interface information
echo [INFO] Network interface details:
ipconfig | findstr /i "IPv4\|Adapter"
echo.

REM Generate adaptive configuration
echo [INFO] Generating adaptive configuration files...

REM Update .env file
echo HOST=0.0.0.0> ..\..\frontend\.env
echo PORT=3000>> ..\..\frontend\.env
echo BROWSER=none>> ..\..\frontend\.env
echo # REACT_APP_API_URL=auto  # Let system auto-detect>> ..\..\frontend\.env
echo CHOKIDAR_USEPOLLING=true>> ..\..\frontend\.env
echo REACT_APP_AUTO_DETECT_API=true>> ..\..\frontend\.env
echo REACT_APP_DETECTED_IP=%LOCAL_IP%>> ..\..\frontend\.env

echo [SUCCESS] .env file updated (auto-detection mode)

REM Update .env.production file
echo # Production environment config - auto generated> ..\..\frontend\.env.production
echo # Generated time: %date% %time%>> ..\..\frontend\.env.production
echo # Detected IP: %LOCAL_IP%>> ..\..\frontend\.env.production
echo.>> ..\..\frontend\.env.production
echo REACT_APP_API_URL=http://%LOCAL_IP%:5000>> ..\..\frontend\.env.production
echo REACT_APP_ENVIRONMENT=production>> ..\..\frontend\.env.production
echo REACT_APP_VERSION=1.0.0>> ..\..\frontend\.env.production
echo REACT_APP_ENABLE_DEBUG=false>> ..\..\frontend\.env.production
echo REACT_APP_LOG_LEVEL=info>> ..\..\frontend\.env.production
echo REACT_APP_CACHE_ENABLED=true>> ..\..\frontend\.env.production
echo REACT_APP_CACHE_EXPIRE_TIME=600000>> ..\..\frontend\.env.production
echo REACT_APP_MAX_FILE_SIZE=104857600>> ..\..\frontend\.env.production
echo REACT_APP_SUPPORTED_FILE_TYPES=.txt,.csv,.xls,.xlsx>> ..\..\frontend\.env.production

echo [SUCCESS] .env.production file updated

echo.
echo [INFO] Configuration file preview:
echo.
echo === .env file ===
type ..\..\frontend\.env
echo.
echo === .env.production file ===
type ..\..\frontend\.env.production
echo.

REM Generate startup script
echo [INFO] Generating adaptive startup script...

echo @echo off> ..\..\start-adaptive.bat
echo title Adaptive Frontend Server>> ..\..\start-adaptive.bat
echo echo Starting adaptive frontend server...>> ..\..\start-adaptive.bat
echo echo Detected IP address: %LOCAL_IP%>> ..\..\start-adaptive.bat
echo echo Frontend URL: http://%LOCAL_IP%:3000>> ..\..\start-adaptive.bat
echo echo API URL: http://%LOCAL_IP%:5000>> ..\..\start-adaptive.bat
echo echo.>> ..\..\start-adaptive.bat
echo cd frontend>> ..\..\start-adaptive.bat
echo set HOST=0.0.0.0>> ..\..\start-adaptive.bat
echo set PORT=3000>> ..\..\start-adaptive.bat
echo set REACT_APP_API_URL=http://%LOCAL_IP%:5000>> ..\..\start-adaptive.bat
echo npm start>> ..\..\start-adaptive.bat

echo [SUCCESS] Adaptive startup script generated: start-adaptive.bat

echo.
echo ========================================
echo   Configuration Complete!
echo ========================================
echo.
echo Network Configuration:
echo   Local IP: %LOCAL_IP%
echo   Frontend URL: http://%LOCAL_IP%:3000
echo   API URL: http://%LOCAL_IP%:5000
echo.
echo Usage:
echo   1. Run start-adaptive.bat to start frontend service
echo   2. Or manually start: cd frontend ^&^& npm start
echo   3. System will automatically use detected IP address
echo.
echo When IP address changes:
echo   Re-run this script to automatically update configuration
echo.
echo Other computers can now access via:
echo   http://%LOCAL_IP%:3000
echo.

REM Ask whether to start service immediately
set /p START_NOW="Start frontend service now? (Y/N): "
if /i "%START_NOW%"=="Y" (
    echo.
    echo [INFO] Starting frontend service...
    start "Adaptive Frontend Server" ..\..\start-adaptive.bat
    echo [SUCCESS] Frontend server startup command executed
)

echo.
pause
