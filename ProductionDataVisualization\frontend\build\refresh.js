// 强制刷新CSS文件
(function() {
  // 获取所有CSS链接
  const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
  
  // 为每个CSS链接添加时间戳参数
  cssLinks.forEach(link => {
    const url = new URL(link.href);
    url.searchParams.set('_t', new Date().getTime());
    link.href = url.toString();
  });
  
  // 动态创建新的样式元素，强制应用Typography.css
  const typographyStyle = document.createElement('style');
  typographyStyle.textContent = `
    /* 强制应用Typography.css的关键样式 */
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
      color: #262626 !important;
    }
    
    .heading-2 {
      font-size: 28px !important;
      font-weight: 600 !important;
      color: #262626 !important;
    }
    
    .heading-3 {
      font-size: 20px !important;
      font-weight: 600 !important;
      color: #262626 !important;
    }
    
    .table-header {
      font-size: 14px !important;
      font-weight: 600 !important;
      color: #262626 !important;
    }
    
    .table-cell {
      font-size: 14px !important;
      font-weight: 400 !important;
      color: #595959 !important;
    }
    
    .button-text {
      font-size: 14px !important;
      font-weight: 500 !important;
      letter-spacing: 0.3px !important;
    }
    
    .menu-item {
      font-size: 16px !important;
      font-weight: 500 !important;
      color: #ffffff !important;
    }
  `;
  document.head.appendChild(typographyStyle);
  
  console.log('CSS刷新完成，已应用Typography样式');
})(); 