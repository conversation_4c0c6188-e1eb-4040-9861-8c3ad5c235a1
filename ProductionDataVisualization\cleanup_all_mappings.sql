-- 彻底清理所有表映射和数据
USE ProductionDataVisualizationDb;

-- 1. 查看当前所有映射
SELECT '清理前的映射:' AS Status;
SELECT Id, FileName, TableName, CreatedAt 
FROM FileTableMappings 
ORDER BY CreatedAt DESC;

-- 2. 删除所有文件表映射
DELETE FROM FileTableMappings;

-- 3. 删除所有导入任务
DELETE FROM ImportTasks;

-- 4. 查看系统中所有Data_开头的表
SELECT '现有数据表:' AS Status;
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE' 
  AND TABLE_NAME LIKE 'Data_%'
ORDER BY TABLE_NAME;

-- 5. 删除所有Data_开头的表（可选，如果需要完全重置）
DECLARE @sql NVARCHAR(MAX) = '';
SELECT @sql = @sql + 'DROP TABLE [' + TABLE_NAME + '];' + CHAR(13)
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE' 
  AND TABLE_NAME LIKE 'Data_%';

-- 执行删除（取消注释下面这行来执行）
-- EXEC sp_executesql @sql;

-- 6. 显示清理后的状态
SELECT '清理后状态:' AS Status;
SELECT 'FileTableMappings' AS TableName, COUNT(*) AS RecordCount FROM FileTableMappings
UNION ALL
SELECT 'ImportTasks' AS TableName, COUNT(*) AS RecordCount FROM ImportTasks;

PRINT '数据库清理完成！所有旧映射已删除。';
