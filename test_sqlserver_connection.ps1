# 测试SQL Server连接

Write-Host "=== 测试SQL Server连接 ===" -ForegroundColor Yellow

$connectionStrings = @(
    "Server=localhost\SQLEXPRESS;Database=master;Trusted_Connection=true;TrustServerCertificate=true;",
    "Server=.\SQLEXPRESS;Database=master;Trusted_Connection=true;TrustServerCertificate=true;",
    "Server=localhost;Database=master;Trusted_Connection=true;TrustServerCertificate=true;",
    "Server=(local);Database=master;Trusted_Connection=true;TrustServerCertificate=true;"
)

$workingConnection = $null

foreach ($connStr in $connectionStrings) {
    Write-Host "`n测试连接: $connStr" -ForegroundColor Cyan
    
    try {
        $connection = New-Object System.Data.SqlClient.SqlConnection($connStr)
        $connection.Open()

        $command = $connection.CreateCommand()
        $command.CommandText = "SELECT @@VERSION"
        $result = $command.ExecuteScalar()

        Write-Host "✅ 连接成功!" -ForegroundColor Green
        Write-Host "SQL Server版本: $($result.Substring(0, 50))..." -ForegroundColor Green

        $workingConnection = $connStr
        $connection.Close()
        break
    }
    catch {
        Write-Host "❌ 连接失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

if ($workingConnection) {
    Write-Host "`n=== 检查现有数据库 ===" -ForegroundColor Yellow

    try {
        $connection = New-Object System.Data.SqlClient.SqlConnection($workingConnection)
        $connection.Open()

        $command = $connection.CreateCommand()
        $command.CommandText = "SELECT name FROM sys.databases WHERE name IN ('ProductionDataDB', 'ProductionDataVisualizationDb')"
        $reader = $command.ExecuteReader()

        $databases = @()
        while ($reader.Read()) {
            $databases += $reader["name"]
        }
        $reader.Close()

        Write-Host "找到的数据库:" -ForegroundColor Cyan
        foreach ($db in $databases) {
            Write-Host "  - $db" -ForegroundColor Green
        }

        if ($databases.Count -eq 0) {
            Write-Host "  没有找到项目数据库" -ForegroundColor Yellow
        }

        $connection.Close()

        Write-Host "`n✅ SQL Server连接正常，可以进行数据迁移" -ForegroundColor Green
        Write-Host "工作连接字符串: $workingConnection" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ 数据库检查失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}
else {
    Write-Host "`n❌ 无法连接到SQL Server" -ForegroundColor Red
    Write-Host "请检查SQL Server是否正在运行" -ForegroundColor Yellow
}
