import api from './api';

const ROLES_ENDPOINT = '/api/roles';
const PERMISSIONS_ENDPOINT = '/api/permissions';

// 获取所有角色
const getAllRoles = async () => {
  try {
    const response = await api.get(ROLES_ENDPOINT);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : { message: '获取角色列表失败' };
  }
};

// 获取单个角色
const getRoleById = async (roleId) => {
  try {
    const response = await api.get(`${ROLES_ENDPOINT}/${roleId}`);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : { message: '获取角色信息失败' };
  }
};

// 创建角色
const createRole = async (roleName, description) => {
  try {
    const response = await api.post(ROLES_ENDPOINT, { name: roleName, description });
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : { message: '创建角色失败' };
  }
};

// 更新角色
const updateRole = async (roleId, roleName, description) => {
  try {
    const response = await api.put(`${ROLES_ENDPOINT}/${roleId}`, {
      name: roleName,
      description
    });
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : { message: '更新角色信息失败' };
  }
};

// 删除角色
const deleteRole = async (roleId) => {
  try {
    const response = await api.delete(`${ROLES_ENDPOINT}/${roleId}`);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : { message: '删除角色失败' };
  }
};

// 获取所有权限
const getAllPermissions = async () => {
  try {
    const response = await api.get(PERMISSIONS_ENDPOINT);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : { message: '获取权限列表失败' };
  }
};

// 获取角色的权限
const getRolePermissions = async (roleId) => {
  try {
    const response = await api.get(`${ROLES_ENDPOINT}/${roleId}/permissions`);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : { message: '获取角色权限失败' };
  }
};

// 更新角色的权限
const updateRolePermissions = async (roleId, permissionIds) => {
  try {
    const response = await api.put(`${ROLES_ENDPOINT}/${roleId}/permissions`, {
      permissionIds
    });
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : { message: '更新角色权限失败' };
  }
};

const roleService = {
  getAllRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  getAllPermissions,
  getRolePermissions,
  updateRolePermissions
};

export default roleService; 