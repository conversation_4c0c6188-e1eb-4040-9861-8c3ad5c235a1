/**
 * 前端数据验证工具
 */

/**
 * 验证规则类型
 */
export const ValidationRules = {
  REQUIRED: 'required',
  EMAIL: 'email',
  PHONE: 'phone',
  URL: 'url',
  NUMBER: 'number',
  INTEGER: 'integer',
  MIN_LENGTH: 'minLength',
  MAX_LENGTH: 'maxLength',
  MIN_VALUE: 'minValue',
  MAX_VALUE: 'maxValue',
  PATTERN: 'pattern',
  CUSTOM: 'custom'
};

/**
 * 内置验证器
 */
const validators = {
  [ValidationRules.REQUIRED]: (value) => {
    if (value === null || value === undefined || value === '') {
      return '此字段为必填项';
    }
    if (Array.isArray(value) && value.length === 0) {
      return '此字段为必填项';
    }
    return null;
  },

  [ValidationRules.EMAIL]: (value) => {
    if (!value) return null;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value) ? null : '请输入有效的邮箱地址';
  },

  [ValidationRules.PHONE]: (value) => {
    if (!value) return null;
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(value) ? null : '请输入有效的手机号码';
  },

  [ValidationRules.URL]: (value) => {
    if (!value) return null;
    try {
      new URL(value);
      return null;
    } catch {
      return '请输入有效的URL地址';
    }
  },

  [ValidationRules.NUMBER]: (value) => {
    if (!value) return null;
    return !isNaN(Number(value)) ? null : '请输入有效的数字';
  },

  [ValidationRules.INTEGER]: (value) => {
    if (!value) return null;
    return Number.isInteger(Number(value)) ? null : '请输入有效的整数';
  },

  [ValidationRules.MIN_LENGTH]: (value, minLength) => {
    if (!value) return null;
    return value.length >= minLength ? null : `最少需要${minLength}个字符`;
  },

  [ValidationRules.MAX_LENGTH]: (value, maxLength) => {
    if (!value) return null;
    return value.length <= maxLength ? null : `最多允许${maxLength}个字符`;
  },

  [ValidationRules.MIN_VALUE]: (value, minValue) => {
    if (!value) return null;
    return Number(value) >= minValue ? null : `值不能小于${minValue}`;
  },

  [ValidationRules.MAX_VALUE]: (value, maxValue) => {
    if (!value) return null;
    return Number(value) <= maxValue ? null : `值不能大于${maxValue}`;
  },

  [ValidationRules.PATTERN]: (value, pattern) => {
    if (!value) return null;
    const regex = new RegExp(pattern);
    return regex.test(value) ? null : '格式不正确';
  },

  [ValidationRules.CUSTOM]: (value, validator) => {
    if (!value) return null;
    return validator(value);
  }
};

/**
 * 验证器类
 */
export class Validator {
  constructor() {
    this.rules = [];
  }

  /**
   * 添加验证规则
   * @param {string} type - 规则类型
   * @param {any} param - 规则参数
   * @param {string} message - 自定义错误消息
   * @returns {Validator} 返回自身以支持链式调用
   */
  addRule(type, param = null, message = null) {
    this.rules.push({ type, param, message });
    return this;
  }

  /**
   * 必填验证
   * @param {string} message - 自定义错误消息
   * @returns {Validator}
   */
  required(message = null) {
    return this.addRule(ValidationRules.REQUIRED, null, message);
  }

  /**
   * 邮箱验证
   * @param {string} message - 自定义错误消息
   * @returns {Validator}
   */
  email(message = null) {
    return this.addRule(ValidationRules.EMAIL, null, message);
  }

  /**
   * 手机号验证
   * @param {string} message - 自定义错误消息
   * @returns {Validator}
   */
  phone(message = null) {
    return this.addRule(ValidationRules.PHONE, null, message);
  }

  /**
   * URL验证
   * @param {string} message - 自定义错误消息
   * @returns {Validator}
   */
  url(message = null) {
    return this.addRule(ValidationRules.URL, null, message);
  }

  /**
   * 数字验证
   * @param {string} message - 自定义错误消息
   * @returns {Validator}
   */
  number(message = null) {
    return this.addRule(ValidationRules.NUMBER, null, message);
  }

  /**
   * 整数验证
   * @param {string} message - 自定义错误消息
   * @returns {Validator}
   */
  integer(message = null) {
    return this.addRule(ValidationRules.INTEGER, null, message);
  }

  /**
   * 最小长度验证
   * @param {number} minLength - 最小长度
   * @param {string} message - 自定义错误消息
   * @returns {Validator}
   */
  minLength(minLength, message = null) {
    return this.addRule(ValidationRules.MIN_LENGTH, minLength, message);
  }

  /**
   * 最大长度验证
   * @param {number} maxLength - 最大长度
   * @param {string} message - 自定义错误消息
   * @returns {Validator}
   */
  maxLength(maxLength, message = null) {
    return this.addRule(ValidationRules.MAX_LENGTH, maxLength, message);
  }

  /**
   * 最小值验证
   * @param {number} minValue - 最小值
   * @param {string} message - 自定义错误消息
   * @returns {Validator}
   */
  minValue(minValue, message = null) {
    return this.addRule(ValidationRules.MIN_VALUE, minValue, message);
  }

  /**
   * 最大值验证
   * @param {number} maxValue - 最大值
   * @param {string} message - 自定义错误消息
   * @returns {Validator}
   */
  maxValue(maxValue, message = null) {
    return this.addRule(ValidationRules.MAX_VALUE, maxValue, message);
  }

  /**
   * 正则表达式验证
   * @param {string|RegExp} pattern - 正则表达式
   * @param {string} message - 自定义错误消息
   * @returns {Validator}
   */
  pattern(pattern, message = null) {
    return this.addRule(ValidationRules.PATTERN, pattern, message);
  }

  /**
   * 自定义验证
   * @param {Function} validator - 验证函数
   * @param {string} message - 自定义错误消息
   * @returns {Validator}
   */
  custom(validator, message = null) {
    return this.addRule(ValidationRules.CUSTOM, validator, message);
  }

  /**
   * 执行验证
   * @param {any} value - 要验证的值
   * @returns {string|null} 错误消息或null
   */
  validate(value) {
    for (const rule of this.rules) {
      const validator = validators[rule.type];
      if (validator) {
        const error = validator(value, rule.param);
        if (error) {
          return rule.message || error;
        }
      }
    }
    return null;
  }
}

/**
 * 表单验证器
 */
export class FormValidator {
  constructor() {
    this.fields = new Map();
  }

  /**
   * 添加字段验证规则
   * @param {string} fieldName - 字段名
   * @param {Validator} validator - 验证器
   * @returns {FormValidator}
   */
  field(fieldName, validator) {
    this.fields.set(fieldName, validator);
    return this;
  }

  /**
   * 验证表单
   * @param {Object} formData - 表单数据
   * @returns {Object} 验证结果
   */
  validate(formData) {
    const errors = {};
    let isValid = true;

    for (const [fieldName, validator] of this.fields) {
      const value = formData[fieldName];
      const error = validator.validate(value);
      
      if (error) {
        errors[fieldName] = error;
        isValid = false;
      }
    }

    return {
      isValid,
      errors,
      firstError: isValid ? null : Object.values(errors)[0]
    };
  }

  /**
   * 验证单个字段
   * @param {string} fieldName - 字段名
   * @param {any} value - 字段值
   * @returns {string|null} 错误消息或null
   */
  validateField(fieldName, value) {
    const validator = this.fields.get(fieldName);
    return validator ? validator.validate(value) : null;
  }
}

/**
 * 创建验证器的便捷函数
 * @returns {Validator} 新的验证器实例
 */
export const createValidator = () => new Validator();

/**
 * 创建表单验证器的便捷函数
 * @returns {FormValidator} 新的表单验证器实例
 */
export const createFormValidator = () => new FormValidator();

/**
 * 常用验证规则预设
 */
export const CommonValidators = {
  // 用户名验证器
  username: () => createValidator()
    .required('用户名不能为空')
    .minLength(3, '用户名至少3个字符')
    .maxLength(20, '用户名最多20个字符')
    .pattern(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),

  // 密码验证器
  password: () => createValidator()
    .required('密码不能为空')
    .minLength(8, '密码至少8个字符')
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      '密码必须包含大小写字母、数字和特殊字符'),

  // 邮箱验证器
  email: () => createValidator()
    .required('邮箱不能为空')
    .email(),

  // 手机号验证器
  phone: () => createValidator()
    .phone(),

  // 中文姓名验证器
  chineseName: () => createValidator()
    .required('姓名不能为空')
    .pattern(/^[\u4e00-\u9fa5·]{2,10}$/, '请输入2-10位中文姓名'),

  // 数字验证器
  positiveNumber: () => createValidator()
    .required('数值不能为空')
    .number()
    .minValue(0, '数值不能为负数'),

  // 整数验证器
  positiveInteger: () => createValidator()
    .required('数值不能为空')
    .integer()
    .minValue(1, '数值必须大于0')
};

export default {
  Validator,
  FormValidator,
  createValidator,
  createFormValidator,
  CommonValidators,
  ValidationRules
};
