[2025-07-13 23:41:02 ERR] Hosting failed to start {"EventId":{"Id":11,"Name":"HostedServiceStartupFaulted"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","Application":"ProductionDataVisualization","Environment":"Production"}
System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.DefaultAddressStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
[2025-07-13 23:41:02 ERR] BackgroundService failed {"EventId":{"Id":9,"Name":"BackgroundServiceFaulted"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","Application":"ProductionDataVisualization","Environment":"Production"}
System.Threading.Tasks.TaskCanceledException: A task was canceled.
   at API.Middleware.RateLimitCleanupService.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\Documents\Trae_Files\Report\ProductionDataVisualization\backend\src\API\Middleware\RateLimitingMiddleware.cs:line 227
   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)
[2025-07-13 23:41:02 FTL] The HostOptions.BackgroundServiceExceptionBehavior is configured to StopHost. A BackgroundService has thrown an unhandled exception, and the IHost instance is stopping. To avoid this behavior, configure this to Ignore; however the BackgroundService will not be restarted. {"EventId":{"Id":10,"Name":"BackgroundServiceStoppingHost"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","Application":"ProductionDataVisualization","Environment":"Production"}
System.Threading.Tasks.TaskCanceledException: A task was canceled.
   at API.Middleware.RateLimitCleanupService.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\Documents\Trae_Files\Report\ProductionDataVisualization\backend\src\API\Middleware\RateLimitingMiddleware.cs:line 227
   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)
[2025-07-13 23:41:56 INF] Now listening on: http://localhost:5000 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:41:56 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:41:56 INF] Hosting environment: Production {"SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:41:56 INF] Content root path: C:\Users\<USER>\Documents\Trae_Files\Report\ProductionDataVisualization\backend\src\API {"SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:43:45 INF] HTTP GET /api/health responded 200 in 226.2184 ms {"RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.22621.1","ClientIP":"::1","SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE24G6KUL0T:00000001","ConnectionId":"0HNE24G6KUL0T","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:44:28 WRN] HTTP GET /api/health/detailed responded 200 in 21814.8393 ms {"RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.22621.1","ClientIP":"::1","SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE24G6KUL0T:00000002","ConnectionId":"0HNE24G6KUL0T","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:44:28 WRN] 慢请求检测: GET /api/health/detailed 耗时 21820ms {"SourceContext":"API.Extensions.PerformanceMonitoringMiddleware","RequestId":"0HNE24G6KUL0T:00000002","RequestPath":"/api/health/detailed","ConnectionId":"0HNE24G6KUL0T","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:44:51 INF] HTTP GET / responded 200 in 2.9313 ms {"RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE24G6KUL0U:00000001","ConnectionId":"0HNE24G6KUL0U","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:53:22 INF] HTTP GET /api/health responded 200 in 14.3425 ms {"RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE24G6KUL10:00000001","ConnectionId":"0HNE24G6KUL10","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:53:22 WRN] HTTP GET /favicon.ico responded 404 in 0.5563 ms {"RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE24G6KUL10:00000002","ConnectionId":"0HNE24G6KUL10","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:53:53 WRN] HTTP GET /api/health/detailed responded 200 in 21118.4354 ms {"RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE24G6KUL10:00000003","ConnectionId":"0HNE24G6KUL10","Application":"ProductionDataVisualization","Environment":"Production"}
[2025-07-13 23:53:53 WRN] 慢请求检测: GET /api/health/detailed 耗时 21123ms {"SourceContext":"API.Extensions.PerformanceMonitoringMiddleware","RequestId":"0HNE24G6KUL10:00000003","RequestPath":"/api/health/detailed","ConnectionId":"0HNE24G6KUL10","Application":"ProductionDataVisualization","Environment":"Production"}
