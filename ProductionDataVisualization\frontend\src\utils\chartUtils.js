// 图表工具类 - 提供图表配置生成和数据处理功能

// 默认颜色主题
export const DEFAULT_COLORS = [
  '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
  '#13c2c2', '#eb2f96', '#fa541c', '#a0d911', '#2f54eb'
];

// 医药行业专用颜色主题
export const PHARMA_COLORS = {
  primary: '#1890ff',    // 主色调
  success: '#52c41a',    // 合格/正常
  warning: '#faad14',    // 警告
  danger: '#f5222d',     // 不合格/异常
  info: '#13c2c2',       // 信息
  secondary: '#722ed1'   // 次要
};

// 异常数据检测阈值
export const ANOMALY_THRESHOLDS = {
  STATISTICAL: {
    Z_SCORE: 2.5,        // Z分数阈值
    IQR_MULTIPLIER: 1.5  // 四分位距倍数
  },
  PERCENTAGE: {
    UPPER: 95,           // 上限百分位
    LOWER: 5             // 下限百分位
  }
};

/**
 * 生成基础图表配置
 */
export const generateBaseOption = (type, data, config = {}) => {
  const baseOption = {
    backgroundColor: config.backgroundColor || 'transparent',
    textStyle: {
      fontFamily: 'Arial, sans-serif',
      fontSize: 12
    },
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut',
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#333',
      textStyle: {
        color: '#fff'
      },
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      type: 'scroll',
      orient: 'horizontal',
      left: 'center',
      top: 'top',
      textStyle: {
        fontSize: 12
      }
    },
    toolbox: {
      show: config.showToolbox || false,
      feature: {
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar'] },
        restore: { show: true },
        saveAsImage: { show: true }
      }
    }
  };

  return baseOption;
};

/**
 * 生成折线图配置
 */
export const generateLineChartOption = (data, config = {}) => {
  const baseOption = generateBaseOption('line', data, config);
  
  return {
    ...baseOption,
    xAxis: {
      type: 'category',
      data: data.categories || [],
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      axisLabel: {
        color: '#666',
        rotate: config.xAxisRotate || 0
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      axisLabel: {
        color: '#666',
        formatter: config.yAxisFormatter || '{value}'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    series: data.series?.map((item, index) => ({
      name: item.name,
      type: 'line',
      data: item.data,
      smooth: config.smooth || false,
      symbol: config.showSymbol ? 'circle' : 'none',
      symbolSize: 6,
      lineStyle: {
        width: 2,
        color: item.color || DEFAULT_COLORS[index % DEFAULT_COLORS.length]
      },
      areaStyle: config.showArea ? {
        opacity: 0.3,
        color: item.color || DEFAULT_COLORS[index % DEFAULT_COLORS.length]
      } : null,
      emphasis: {
        focus: 'series'
      }
    })) || []
  };
};

/**
 * 生成柱状图配置
 */
export const generateBarChartOption = (data, config = {}) => {
  const baseOption = generateBaseOption('bar', data, config);
  
  return {
    ...baseOption,
    xAxis: {
      type: 'category',
      data: data.categories || [],
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      axisLabel: {
        color: '#666',
        rotate: config.xAxisRotate || 0
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      axisLabel: {
        color: '#666',
        formatter: config.yAxisFormatter || '{value}'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    series: data.series?.map((item, index) => ({
      name: item.name,
      type: 'bar',
      data: item.data,
      barWidth: config.barWidth || '60%',
      itemStyle: {
        color: item.color || DEFAULT_COLORS[index % DEFAULT_COLORS.length],
        borderRadius: config.borderRadius || [2, 2, 0, 0]
      },
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      }
    })) || []
  };
};

/**
 * 生成饼图配置
 */
export const generatePieChartOption = (data, config = {}) => {
  const baseOption = generateBaseOption('pie', data, config);
  
  return {
    ...baseOption,
    series: [{
      name: config.seriesName || '数据分布',
      type: 'pie',
      radius: config.radius || ['40%', '70%'],
      center: config.center || ['50%', '50%'],
      data: data.map((item, index) => ({
        value: item.value,
        name: item.name,
        itemStyle: {
          color: item.color || DEFAULT_COLORS[index % DEFAULT_COLORS.length]
        }
      })),
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        show: config.showLabel !== false,
        formatter: config.labelFormatter || '{b}: {c} ({d}%)'
      },
      labelLine: {
        show: config.showLabelLine !== false
      }
    }]
  };
};

/**
 * 检测异常数据
 */
export const detectAnomalies = (data, method = 'zscore', threshold = null) => {
  if (!Array.isArray(data) || data.length === 0) return [];

  const numericData = data.filter(d => typeof d === 'number' && !isNaN(d));
  if (numericData.length === 0) return [];

  let anomalies = [];

  switch (method) {
    case 'zscore':
      anomalies = detectZScoreAnomalies(numericData, threshold || ANOMALY_THRESHOLDS.STATISTICAL.Z_SCORE);
      break;
    case 'iqr':
      anomalies = detectIQRAnomalies(numericData, threshold || ANOMALY_THRESHOLDS.STATISTICAL.IQR_MULTIPLIER);
      break;
    case 'percentile':
      anomalies = detectPercentileAnomalies(numericData, threshold || ANOMALY_THRESHOLDS.PERCENTAGE);
      break;
    default:
      anomalies = detectZScoreAnomalies(numericData, threshold || ANOMALY_THRESHOLDS.STATISTICAL.Z_SCORE);
  }

  return anomalies;
};

/**
 * Z分数异常检测
 */
const detectZScoreAnomalies = (data, threshold) => {
  const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
  const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
  const stdDev = Math.sqrt(variance);

  return data.map((value, index) => {
    const zScore = Math.abs((value - mean) / stdDev);
    return {
      index,
      value,
      zScore,
      isAnomaly: zScore > threshold,
      severity: zScore > threshold * 1.5 ? 'high' : zScore > threshold ? 'medium' : 'low'
    };
  }).filter(item => item.isAnomaly);
};

/**
 * IQR异常检测
 */
const detectIQRAnomalies = (data, multiplier) => {
  const sorted = [...data].sort((a, b) => a - b);
  const q1Index = Math.floor(sorted.length * 0.25);
  const q3Index = Math.floor(sorted.length * 0.75);
  const q1 = sorted[q1Index];
  const q3 = sorted[q3Index];
  const iqr = q3 - q1;
  const lowerBound = q1 - multiplier * iqr;
  const upperBound = q3 + multiplier * iqr;

  return data.map((value, index) => ({
    index,
    value,
    isAnomaly: value < lowerBound || value > upperBound,
    severity: value < lowerBound - iqr || value > upperBound + iqr ? 'high' : 'medium'
  })).filter(item => item.isAnomaly);
};

/**
 * 百分位异常检测
 */
const detectPercentileAnomalies = (data, thresholds) => {
  const sorted = [...data].sort((a, b) => a - b);
  const lowerIndex = Math.floor(sorted.length * (thresholds.LOWER / 100));
  const upperIndex = Math.floor(sorted.length * (thresholds.UPPER / 100));
  const lowerBound = sorted[lowerIndex];
  const upperBound = sorted[upperIndex];

  return data.map((value, index) => ({
    index,
    value,
    isAnomaly: value < lowerBound || value > upperBound,
    severity: 'medium'
  })).filter(item => item.isAnomaly);
};

/**
 * 为异常数据添加高亮样式
 */
export const highlightAnomalies = (option, anomalies, highlightColor = PHARMA_COLORS.danger) => {
  if (!option.series || anomalies.length === 0) return option;

  const highlightedOption = { ...option };
  
  highlightedOption.series = option.series.map(series => {
    const newSeries = { ...series };
    
    if (series.type === 'line' || series.type === 'bar') {
      newSeries.markPoint = {
        data: anomalies.map(anomaly => ({
          coord: [anomaly.index, anomaly.value],
          symbol: 'circle',
          symbolSize: 12,
          itemStyle: {
            color: highlightColor,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            formatter: '异常',
            position: 'top',
            color: highlightColor,
            fontSize: 10
          }
        }))
      };
    }
    
    return newSeries;
  });

  return highlightedOption;
};

/**
 * 格式化数值
 */
export const formatNumber = (value, type = 'default', precision = 2) => {
  if (typeof value !== 'number' || isNaN(value)) return '--';

  switch (type) {
    case 'percentage':
      return `${(value * 100).toFixed(precision)}%`;
    case 'currency':
      return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: precision })}`;
    case 'decimal':
      return value.toFixed(precision);
    case 'integer':
      return Math.round(value).toLocaleString('zh-CN');
    default:
      return value.toLocaleString('zh-CN');
  }
};
