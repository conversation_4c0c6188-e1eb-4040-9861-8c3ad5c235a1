import React from 'react';
import { Card } from 'antd';
import { motion } from 'framer-motion';

const EnhancedCard = ({ 
  children, 
  title, 
  extra,
  hoverable = true,
  glowEffect = true,
  shimmerEffect = true,
  className = '',
  style = {},
  bodyStyle = {},
  headStyle = {},
  ...props 
}) => {
  const cardVariants = {
    initial: { opacity: 0, y: 20, scale: 0.95 },
    animate: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { duration: 0.6, ease: "easeOut" }
    },
    hover: hoverable ? { 
      y: -5, 
      scale: 1.02,
      transition: { duration: 0.3, ease: "easeOut" }
    } : {}
  };

  const enhancedStyle = {
    background: `
      linear-gradient(145deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(248, 250, 252, 0.9) 50%,
        rgba(255, 255, 255, 0.95) 100%
      )
    `,
    backdropFilter: 'blur(25px)',
    borderRadius: '20px',
    border: '1px solid rgba(255, 255, 255, 0.8)',
    boxShadow: `
      0 20px 40px rgba(102, 126, 234, 0.12),
      0 10px 20px rgba(118, 75, 162, 0.08),
      0 5px 10px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(255, 255, 255, 0.5)
    `,
    overflow: 'hidden',
    position: 'relative',
    ...style
  };

  const enhancedBodyStyle = {
    padding: '24px',
    position: 'relative',
    zIndex: 2,
    ...bodyStyle
  };

  const enhancedHeadStyle = {
    background: 'transparent',
    borderBottom: '1px solid rgba(102, 126, 234, 0.1)',
    padding: '20px 24px',
    ...headStyle
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="initial"
      animate="animate"
      whileHover="hover"
      className={`enhanced-card-wrapper ${className}`}
    >
      <Card
        title={title}
        extra={extra}
        style={enhancedStyle}
        bodyStyle={enhancedBodyStyle}
        headStyle={enhancedHeadStyle}
        {...props}
      >
        {/* 闪光效果 */}
        {shimmerEffect && (
          <div className="shimmer-effect" />
        )}
        
        {/* 发光边框效果 */}
        {glowEffect && (
          <div className="glow-border" />
        )}
        
        {children}
      </Card>

      <style jsx="true">{`
        .enhanced-card-wrapper {
          position: relative;
        }

        .enhanced-card-wrapper .ant-card {
          transition: all 0.3s ease;
        }

        .enhanced-card-wrapper:hover .ant-card {
          box-shadow: 
            0 25px 50px rgba(102, 126, 234, 0.18),
            0 15px 30px rgba(118, 75, 162, 0.12),
            0 8px 16px rgba(0, 0, 0, 0.06),
            inset 0 1px 0 rgba(255, 255, 255, 0.9),
            inset 0 -1px 0 rgba(255, 255, 255, 0.5) !important;
        }

        .shimmer-effect {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            45deg, 
            transparent 30%, 
            rgba(255, 255, 255, 0.1) 50%, 
            transparent 70%
          );
          animation: shimmer 4s infinite;
          pointer-events: none;
          z-index: 1;
        }

        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }

        .glow-border {
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          background: linear-gradient(
            45deg,
            rgba(102, 126, 234, 0.3),
            rgba(118, 75, 162, 0.3),
            rgba(102, 126, 234, 0.3)
          );
          border-radius: 22px;
          opacity: 0;
          transition: opacity 0.3s ease;
          z-index: 0;
          animation: glowPulse 3s infinite;
        }

        .enhanced-card-wrapper:hover .glow-border {
          opacity: 1;
        }

        @keyframes glowPulse {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 0.6; }
        }

        /* 标题样式增强 */
        .enhanced-card-wrapper .ant-card-head-title {
          font-weight: 600;
          font-size: 18px;
          background: linear-gradient(135deg, #667eea, #764ba2);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        /* 内容区域样式 */
        .enhanced-card-wrapper .ant-card-body {
          color: #4a5568;
          line-height: 1.6;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .enhanced-card-wrapper .ant-card {
            border-radius: 16px;
          }
          
          .enhanced-card-wrapper .ant-card-body {
            padding: 20px 16px;
          }
          
          .enhanced-card-wrapper .ant-card-head {
            padding: 16px;
          }
          
          .shimmer-effect {
            display: none;
          }
        }
      `}</style>
    </motion.div>
  );
};

export default EnhancedCard;
