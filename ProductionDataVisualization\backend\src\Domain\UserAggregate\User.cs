using System;
using System.Collections.Generic;
using Domain.Common;

namespace Domain.UserAggregate
{
    /// <summary>
    /// 用户实体，作为聚合根
    /// </summary>
    public class User : Entity, IAggregateRoot
    {
        public string Username { get; private set; }
        public string Email { get; private set; }
        public string PasswordHash { get; private set; }
        public string FullName { get; private set; }
        public bool IsActive { get; private set; }
        public DateTime? LastLoginTime { get; private set; }

        private readonly List<UserRole> _userRoles = new();
        public IReadOnlyCollection<UserRole> UserRoles => _userRoles.AsReadOnly();

        // 防止无参构造函数被外部调用
        private User() { }

        public User(string username, string email, string passwordHash, string fullName)
        {
            if (string.IsNullOrWhiteSpace(username))
                throw new ArgumentException("用户名不能为空", nameof(username));

            if (string.IsNullOrWhiteSpace(email))
                throw new ArgumentException("邮箱不能为空", nameof(email));

            if (string.IsNullOrWhiteSpace(passwordHash))
                throw new ArgumentException("密码哈希不能为空", nameof(passwordHash));

            Username = username;
            Email = email;
            PasswordHash = passwordHash;
            FullName = fullName;
            IsActive = true;
        }

        public User(Guid id, string username, string email, string passwordHash, string fullName)
        {
            if (id == Guid.Empty)
                throw new ArgumentException("用户ID不能为空", nameof(id));

            if (string.IsNullOrWhiteSpace(username))
                throw new ArgumentException("用户名不能为空", nameof(username));

            if (string.IsNullOrWhiteSpace(email))
                throw new ArgumentException("邮箱不能为空", nameof(email));

            if (string.IsNullOrWhiteSpace(passwordHash))
                throw new ArgumentException("密码哈希不能为空", nameof(passwordHash));

            Id = id;
            Username = username;
            Email = email;
            PasswordHash = passwordHash;
            FullName = fullName;
            IsActive = true;
        }

        public void UpdateProfile(string email, string fullName)
        {
            if (!string.IsNullOrWhiteSpace(email))
                Email = email;

            if (!string.IsNullOrWhiteSpace(fullName))
                FullName = fullName;

            ModifiedAt = DateTime.UtcNow;
        }

        public void ChangePassword(string newPasswordHash)
        {
            if (string.IsNullOrWhiteSpace(newPasswordHash))
                throw new ArgumentException("新密码哈希不能为空", nameof(newPasswordHash));

            PasswordHash = newPasswordHash;
            ModifiedAt = DateTime.UtcNow;
        }

        public void SetActive(bool isActive)
        {
            IsActive = isActive;
            ModifiedAt = DateTime.UtcNow;
        }

        public void RecordLogin()
        {
            LastLoginTime = DateTime.UtcNow;
        }

        public void AddRole(Role role)
        {
            if (role == null)
                throw new ArgumentNullException(nameof(role));

            if (!_userRoles.Exists(ur => ur.RoleId == role.Id))
            {
                _userRoles.Add(new UserRole(this, role));
                ModifiedAt = DateTime.UtcNow;
            }
        }

        public void RemoveRole(Role role)
        {
            if (role == null)
                throw new ArgumentNullException(nameof(role));

            var userRole = _userRoles.Find(ur => ur.RoleId == role.Id);
            if (userRole != null)
            {
                _userRoles.Remove(userRole);
                ModifiedAt = DateTime.UtcNow;
            }
        }
    }
} 