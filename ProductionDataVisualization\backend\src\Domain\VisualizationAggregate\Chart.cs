using System;
using System.Collections.Generic;
using Domain.Common;
using Domain.DataAggregate;

namespace Domain.VisualizationAggregate
{
    /// <summary>
    /// 图表实体，作为聚合根
    /// </summary>
    public class Chart : Entity, IAggregateRoot
    {
        public string Title { get; private set; }
        public string Description { get; private set; }
        public string ChartType { get; private set; } // 表格、折线图、饼图、柱状图、散点图
        public Guid CreatedBy { get; private set; }
        public bool IsPublic { get; private set; }
        public string? ConfigJson { get; private set; } // 图表配置的JSON字符串

        private readonly List<ChartDataCategory> _chartDataCategories = new();
        public IReadOnlyCollection<ChartDataCategory> ChartDataCategories => _chartDataCategories.AsReadOnly();

        private readonly List<DashboardChart> _dashboardCharts = new();
        public IReadOnlyCollection<DashboardChart> DashboardCharts => _dashboardCharts.AsReadOnly();

        // 防止无参构造函数被外部调用
        private Chart() { }

        public Chart(
            string title,
            string description,
            string chartType,
            Guid createdBy,
            bool isPublic = false,
            string? configJson = null)
        {
            if (string.IsNullOrWhiteSpace(title))
                throw new ArgumentException("图表标题不能为空", nameof(title));

            if (string.IsNullOrWhiteSpace(chartType))
                throw new ArgumentException("图表类型不能为空", nameof(chartType));

            Title = title;
            Description = description ?? string.Empty;
            ChartType = chartType;
            CreatedBy = createdBy;
            IsPublic = isPublic;
            ConfigJson = configJson;
        }

        public void Update(string title, string description, string chartType, bool isPublic, string? configJson)
        {
            if (!string.IsNullOrWhiteSpace(title))
                Title = title;

            if (description != null)
                Description = description;

            if (!string.IsNullOrWhiteSpace(chartType))
                ChartType = chartType;

            IsPublic = isPublic;
            
            if (configJson != null)
                ConfigJson = configJson;

            ModifiedAt = DateTime.UtcNow;
        }

        public void AddDataCategory(DataCategory dataCategory)
        {
            if (dataCategory == null)
                throw new ArgumentNullException(nameof(dataCategory));

            if (!_chartDataCategories.Exists(cdc => cdc.DataCategoryId == dataCategory.Id))
            {
                _chartDataCategories.Add(new ChartDataCategory(this, dataCategory));
                ModifiedAt = DateTime.UtcNow;
            }
        }

        public void RemoveDataCategory(DataCategory dataCategory)
        {
            if (dataCategory == null)
                throw new ArgumentNullException(nameof(dataCategory));

            var chartDataCategory = _chartDataCategories.Find(cdc => cdc.DataCategoryId == dataCategory.Id);
            if (chartDataCategory != null)
            {
                _chartDataCategories.Remove(chartDataCategory);
                ModifiedAt = DateTime.UtcNow;
            }
        }
    }
} 