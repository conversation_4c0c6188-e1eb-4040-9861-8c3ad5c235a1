<!DOCTYPE html>
<html>
<head>
    <title>前端后端连接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔧 前端后端连接测试</h1>
    
    <div class="test-result info">
        <strong>测试目标:</strong> http://localhost:5000
    </div>
    
    <button onclick="testHealthAPI()">测试健康检查API</button>
    <button onclick="testUsersAPI()">测试用户列表API</button>
    <button onclick="testRegisterAPI()">测试用户注册API</button>
    <button onclick="clearResults()">清除结果</button>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:5000';
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testHealthAPI() {
            addResult('🔍 测试健康检查API...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/health`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    },
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ 健康检查成功!<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    addResult(`❌ 健康检查失败: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 健康检查错误: ${error.message}`, 'error');
            }
        }
        
        async function testUsersAPI() {
            addResult('🔍 测试用户列表API...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/simple-auth/users`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    },
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ 用户列表获取成功!<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    addResult(`❌ 用户列表获取失败: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 用户列表错误: ${error.message}`, 'error');
            }
        }
        
        async function testRegisterAPI() {
            addResult('🔍 测试用户注册API...', 'info');
            
            const testUser = {
                username: `testuser_${Date.now()}`,
                email: `test_${Date.now()}@example.com`,
                password: 'password123',
                fullName: '测试用户'
            };
            
            try {
                const response = await fetch(`${API_BASE}/api/simple-auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    mode: 'cors',
                    body: JSON.stringify(testUser)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ 用户注册成功!<br>用户: ${testUser.username}<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    addResult(`❌ 用户注册失败: HTTP ${response.status}<br><pre>${JSON.stringify(errorData, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`❌ 用户注册错误: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动测试健康检查
        window.onload = function() {
            addResult('🚀 页面加载完成，开始自动测试...', 'info');
            testHealthAPI();
        };
    </script>
</body>
</html>
