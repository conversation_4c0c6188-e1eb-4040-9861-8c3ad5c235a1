using System;
using Domain.Common;

namespace Domain.DataAggregate
{
    /// <summary>
    /// 阈值实体
    /// </summary>
    public class Threshold : Entity, IAggregateRoot
    {
        public Guid DataCategoryId { get; private set; }
        public string Name { get; private set; }
        public string Description { get; private set; }
        public double MinValue { get; private set; }
        public double MaxValue { get; private set; }
        public string Color { get; private set; } // 用于前端展示的颜色代码
        public bool IsActive { get; private set; }

        public DataCategory DataCategory { get; private set; }

        // 防止无参构造函数被外部调用
        private Threshold() { }

        public Threshold(
            DataCategory dataCategory,
            string name,
            string description,
            double minValue,
            double maxValue,
            string color)
        {
            if (dataCategory == null)
                throw new ArgumentNullException(nameof(dataCategory));

            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("阈值名称不能为空", nameof(name));

            if (minValue > maxValue)
                throw new ArgumentException("最小值不能大于最大值", nameof(minValue));

            DataCategoryId = dataCategory.Id;
            DataCategory = dataCategory;
            Name = name;
            Description = description ?? string.Empty;
            MinValue = minValue;
            MaxValue = maxValue;
            Color = color ?? "#FF0000"; // 默认红色
            IsActive = true;
        }

        public void Update(string name, string description, double minValue, double maxValue, string color)
        {
            if (!string.IsNullOrWhiteSpace(name))
                Name = name;

            if (description != null)
                Description = description;

            if (minValue <= maxValue)
            {
                MinValue = minValue;
                MaxValue = maxValue;
            }
            else
            {
                throw new ArgumentException("最小值不能大于最大值");
            }

            if (!string.IsNullOrWhiteSpace(color))
                Color = color;

            ModifiedAt = DateTime.UtcNow;
        }

        public void SetActive(bool isActive)
        {
            IsActive = isActive;
            ModifiedAt = DateTime.UtcNow;
        }

        public bool IsValueExceedThreshold(double value)
        {
            return value < MinValue || value > MaxValue;
        }
    }
} 