<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="眼药制剂生产数据可视化系统"
    />
    <!-- 强制浏览器不使用缓存 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!-- 引入Google字体，增强排版效果 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>生产数据可视化系统</title>
    
    <!-- 绿色主题内联样式 - 确保即时生效 -->
    <style>
      :root {
        --primary-color: #00cc66;
        --primary-hover: #00aa55;
        --primary-light: rgba(0, 204, 102, 0.1);
        --primary-shadow: rgba(0, 204, 102, 0.3);
        --dark-bg: #050b16;
        --card-bg: rgba(8, 15, 30, 0.6);
        --text-primary: #ffffff;
        --text-secondary: rgba(255, 255, 255, 0.7);
        --text-hint: rgba(255, 255, 255, 0.5);
      }
      
      /* 全局字体设置 */
      body, html {
        font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif !important;
        letter-spacing: 0.3px;
      }
      
      /* 覆盖关键样式 */
      .login-content {
        box-shadow: 0 0 50px rgba(0, 200, 83, 0.15), 0 0 100px rgba(0, 0, 0, 0.3) !important;
        border: 1px solid rgba(0, 200, 83, 0.1) !important;
        backdrop-filter: blur(15px) !important;
        background: linear-gradient(135deg, rgba(8, 15, 30, 0.7), rgba(16, 30, 60, 0.6)) !important;
        border-radius: 24px !important;
        overflow: hidden !important;
      }
      
      .login-left {
        border-right: 1px solid rgba(0, 200, 83, 0.1) !important;
        background: linear-gradient(135deg, rgba(8, 15, 30, 0.8), rgba(16, 30, 60, 0.7)) !important;
        position: relative !important;
      }
      
      /* 添加科技感网格背景 */
      .login-left::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background-image: 
          linear-gradient(rgba(0, 200, 83, 0.1) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0, 200, 83, 0.1) 1px, transparent 1px) !important;
        background-size: 40px 40px !important;
        background-position: center center !important;
        opacity: 0.3 !important;
        z-index: 0 !important;
        pointer-events: none !important;
      }
      
      /* 公司品牌区域增强 */
      .company-brand {
        position: relative !important;
        z-index: 1 !important;
      }
      
      .company-logo {
        background: linear-gradient(135deg, #009951, #00cc66) !important;
        box-shadow: 0 0 20px rgba(0, 200, 83, 0.5), 0 0 40px rgba(0, 200, 83, 0.2) !important;
        border-radius: 16px !important;
        position: relative !important;
        overflow: hidden !important;
      }
      
      /* 添加logo内部光效 */
      .company-logo::after {
        content: '' !important;
        position: absolute !important;
        top: -50% !important;
        left: -50% !important;
        width: 200% !important;
        height: 200% !important;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 60%) !important;
        opacity: 0.6 !important;
      }
      
      /* 优化公司信息文字 */
      .company-info h1 {
        font-weight: 700 !important;
        font-size: 1.6rem !important;
        letter-spacing: 1px !important;
        margin-bottom: 8px !important;
        background: linear-gradient(to right, #ffffff, #b3ffcc) !important;
        -webkit-background-clip: text !important;
        background-clip: text !important;
        color: transparent !important;
        text-shadow: 0 2px 10px rgba(0, 200, 83, 0.3) !important;
      }
      
      .company-info p {
        font-weight: 300 !important;
        color: var(--text-secondary) !important;
        letter-spacing: 2px !important;
        font-size: 0.9rem !important;
      }
      
      .tech-circle {
        border: 2px solid rgba(0, 200, 83, 0.3) !important;
        box-shadow: 0 0 15px rgba(0, 200, 83, 0.2) inset !important;
      }
      
      /* 数据统计样式增强 */
      .data-stats {
        margin-top: 20px !important;
        position: relative !important;
        z-index: 1 !important;
      }
      
      .stat-item {
        background: linear-gradient(90deg, rgba(0, 200, 83, 0.3), rgba(0, 200, 83, 0.1)) !important;
        border-radius: 6px !important;
        height: 30px !important;
        margin-bottom: 12px !important;
        position: relative !important;
        overflow: hidden !important;
      }
      
      .stat-item::after {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent) !important;
        animation: stat-shine 2s infinite !important;
      }
      
      @keyframes stat-shine {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
      }
      
      .stat-label {
        font-weight: 500 !important;
        font-size: 0.85rem !important;
        letter-spacing: 1px !important;
      }
      
      .stat-value {
        font-weight: 700 !important;
        font-size: 0.9rem !important;
        text-shadow: 0 0 10px rgba(0, 200, 83, 0.5) !important;
      }
      
      /* 系统名称样式增强 */
      .system-name {
        position: relative !important;
        z-index: 1 !important;
      }
      
      .system-name h2 {
        font-weight: 700 !important;
        font-size: 1.8rem !important;
        letter-spacing: 2px !important;
        margin-bottom: 10px !important;
        background: linear-gradient(to right, #ffffff, #00cc66) !important;
        -webkit-background-clip: text !important;
        background-clip: text !important;
        color: transparent !important;
        text-shadow: 0 2px 15px rgba(0, 200, 83, 0.4) !important;
      }
      
      .system-name p {
        font-weight: 300 !important;
        color: var(--text-secondary) !important;
        letter-spacing: 3px !important;
        font-size: 0.9rem !important;
      }
      
      /* 登录表单容器增强 */
      .login-form-container {
        position: relative !important;
        backdrop-filter: blur(10px) !important;
        background: linear-gradient(135deg, rgba(8, 15, 30, 0.6), rgba(16, 30, 60, 0.5)) !important;
        border-radius: 16px !important;
        padding: 30px !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
        border: 1px solid rgba(0, 200, 83, 0.1) !important;
        overflow: hidden !important;
      }
      
      /* 添加表单背景光效 */
      .form-background {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: radial-gradient(circle at top right, rgba(0, 200, 83, 0.1), transparent 70%) !important;
        z-index: -1 !important;
      }
      
      .form-glow {
        position: absolute !important;
        top: -50% !important;
        right: -50% !important;
        width: 300px !important;
        height: 300px !important;
        background: radial-gradient(circle, rgba(0, 200, 83, 0.2), transparent 70%) !important;
        z-index: -1 !important;
        opacity: 0.6 !important;
        animation: glow-pulse 4s infinite alternate !important;
      }
      
      @keyframes glow-pulse {
        0% { opacity: 0.4; transform: scale(1); }
        100% { opacity: 0.7; transform: scale(1.2); }
      }
      
      /* 登录按钮样式增强 */
      .login-button {
        background: linear-gradient(135deg, #008844, #00cc66) !important;
        box-shadow: 0 4px 15px rgba(0, 200, 83, 0.3) !important;
        border-radius: 8px !important;
        height: 48px !important;
        font-weight: 500 !important;
        letter-spacing: 1px !important;
        position: relative !important;
        overflow: hidden !important;
        transition: all 0.3s ease !important;
      }
      
      .login-button:hover {
        background: linear-gradient(135deg, #008844, #00cc66) !important;
        box-shadow: 0 8px 20px rgba(0, 200, 83, 0.4) !important;
        transform: translateY(-2px) !important;
      }
      
      /* 按钮内部光效 */
      .login-button::before {
        content: '' !important;
        position: absolute !important;
        top: -50% !important;
        left: -50% !important;
        width: 200% !important;
        height: 200% !important;
        background: linear-gradient(transparent, rgba(255, 255, 255, 0.2), transparent) !important;
        transform: rotate(45deg) !important;
        animation: button-shine 3s infinite !important;
      }
      
      @keyframes button-shine {
        0% { left: -50%; }
        100% { left: 100%; }
      }
      
      /* 标题样式增强 */
      .login-title {
        font-weight: 700 !important;
        font-size: 1.8rem !important;
        margin-bottom: 10px !important;
        background: linear-gradient(45deg, #ffffff, #00cc66) !important;
        -webkit-background-clip: text !important;
        background-clip: text !important;
        color: transparent !important;
        text-shadow: 0 2px 10px rgba(0, 200, 83, 0.3) !important;
        letter-spacing: 1px !important;
      }
      
      .login-subtitle {
        font-weight: 300 !important;
        color: var(--text-secondary) !important;
        font-size: 0.9rem !important;
        letter-spacing: 1px !important;
        margin-bottom: 20px !important;
        display: block !important;
      }
      
      /* 针对App.js主题颜色 */
      .ant-btn-primary {
        background-color: #00cc66 !important;
      }

      .login-page-footer {
        text-align: center !important;
        margin-top: 40px !important;
        color: var(--text-hint) !important;
        font-size: 0.85rem !important;
        font-weight: 300 !important;
        letter-spacing: 0.5px !important;
      }
      
      /* 修改登录输入框样式 */
      .ant-input, .ant-input-password {
        background-color: rgba(0, 0, 0, 0.2) !important;
        border: none !important; /* 移除内部输入框边框 */
        color: #ffffff !important;
        height: 40px !important;
        line-height: 40px !important;
        font-weight: 400 !important;
        letter-spacing: 0.5px !important;
      }
      
      .ant-input::placeholder {
        color: rgba(255, 255, 255, 0.4) !important;
        font-weight: 300 !important;
      }
      
      .ant-input-password .ant-input {
        background-color: transparent !important;
        border: none !important;
        box-shadow: none !important;
      }
      
      .ant-checkbox-checked .ant-checkbox-inner {
        background-color: #00cc66 !important;
        border-color: #00cc66 !important;
      }
      
      /* 强制应用样式，确保覆盖所有可能的CSS */
      .ant-input-affix-wrapper {
        background-color: rgba(0, 0, 0, 0.2) !important;
        border: 1px solid rgba(0, 200, 83, 0.3) !important;
        height: 40px !important;
        padding: 0 11px !important;
        display: flex !important;
        align-items: center !important;
        border-radius: 6px !important;
        transition: all 0.3s ease !important;
      }
      
      .ant-input-affix-wrapper:hover, .ant-input-affix-wrapper:focus {
        border-color: #00cc66 !important;
        box-shadow: 0 0 0 2px rgba(0, 204, 102, 0.2) !important;
      }
      
      .ant-input-affix-wrapper-focused {
        border-color: #00cc66 !important;
        box-shadow: 0 0 0 2px rgba(0, 204, 102, 0.2) !important;
      }
      
      /* 输入框焦点效果增强 */
      .input-focus-line {
        position: absolute !important;
        bottom: 0 !important;
        left: 50% !important;
        width: 0 !important;
        height: 2px !important;
        background: linear-gradient(to right, transparent, #00cc66, transparent) !important;
        transform: translateX(-50%) !important;
        transition: width 0.3s ease !important;
      }
      
      .ant-input-affix-wrapper:focus-within + .input-focus-line,
      .ant-input-affix-wrapper-focused + .input-focus-line {
        width: 100% !important;
      }
      
      .ant-checkbox-wrapper:hover .ant-checkbox-inner, 
      .ant-checkbox:hover .ant-checkbox-inner, 
      .ant-checkbox-input:focus + .ant-checkbox-inner {
        border-color: #00cc66 !important;
      }
      
      /* 修复图标对齐问题 */
      .ant-input-prefix {
        margin-right: 8px !important;
        color: rgba(0, 200, 83, 0.8) !important;
        display: flex !important;
        align-items: center !important;
      }
      
      /* 确保输入框内容垂直居中 */
      .ant-input-affix-wrapper input.ant-input {
        height: 100% !important;
        display: flex !important;
        align-items: center !important;
      }
      
      /* 确保密码框图标对齐 */
      .ant-input-password-icon {
        color: rgba(0, 200, 83, 0.8) !important;
        margin-left: 8px !important;
      }
      
      /* 修复双重边框问题 */
      .ant-form-item-control-input-content {
        box-shadow: none !important;
      }
      
      .futuristic-input {
        box-shadow: none !important;
        border: none !important;
      }
      
      /* 记住我和忘记密码样式增强 */
      .remember-forgot {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-bottom: 15px !important;
      }
      
      .remember-checkbox .ant-checkbox + span {
        color: var(--text-secondary) !important;
        font-weight: 300 !important;
        font-size: 0.9rem !important;
      }
      
      .forgot-link {
        color: rgba(0, 200, 83, 0.8) !important;
        font-size: 0.9rem !important;
        font-weight: 400 !important;
        transition: all 0.3s ease !important;
      }
      
      .forgot-link:hover {
        color: #00cc66 !important;
        text-shadow: 0 0 8px rgba(0, 200, 83, 0.3) !important;
      }
      
      /* 注册链接样式增强 */
      .register-link {
        color: #00cc66 !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        position: relative !important;
      }
      
      .register-link:hover {
        text-shadow: 0 0 8px rgba(0, 200, 83, 0.4) !important;
      }
      
      .register-link::after {
        content: '' !important;
        position: absolute !important;
        bottom: -2px !important;
        left: 0 !important;
        width: 0 !important;
        height: 1px !important;
        background-color: #00cc66 !important;
        transition: width 0.3s ease !important;
      }
      
      .register-link:hover::after {
        width: 100% !important;
      }
      
      /* 表单项间距优化 */
      .futuristic-form .ant-form-item {
        margin-bottom: 20px !important;
      }
      
      /* 错误提示样式增强 */
      .ant-form-item-explain-error {
        color: #ff4d4f !important;
        font-size: 0.8rem !important;
        margin-top: 5px !important;
      }
      
      /* 登录按钮内容样式 */
      .button-content {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
      }
      
      .button-text {
        margin-right: 8px !important;
      }
      
      .button-icon {
        font-size: 1.2rem !important;
        transition: transform 0.3s ease !important;
      }
      
      .login-button:hover .button-icon {
        transform: translateX(5px) !important;
      }
      
      /* 背景滤镜效果增强 */
      .background-filter {
        background: radial-gradient(circle at center, rgba(5, 11, 22, 0.5) 0%, rgba(5, 11, 22, 0.8) 70%, rgba(5, 11, 22, 0.95) 100%) !important;
        backdrop-filter: blur(5px) !important;
      }
      
      /* 眼药水液滴动画增强 */
      .eye-drop-container {
        filter: drop-shadow(0 0 10px rgba(0, 200, 83, 0.3)) !important;
      }
      
      .eye-drop-bottle {
        background: linear-gradient(to right, #008844, #00cc66, #008844) !important;
        box-shadow: 0 0 15px rgba(0, 200, 83, 0.5) !important;
      }
      
      .drop {
        background: radial-gradient(circle at center, rgba(0, 255, 128, 0.9), rgba(0, 200, 83, 0.7)) !important;
        filter: drop-shadow(0 0 5px rgba(0, 200, 83, 0.5)) !important;
      }
      
      /* 响应式调整 */
      @media (max-width: 992px) {
        .login-content {
          flex-direction: column !important;
          height: auto !important;
          max-height: none !important;
        }
        
        .login-left {
          border-right: none !important;
          border-bottom: 1px solid rgba(0, 200, 83, 0.1) !important;
          padding: 30px !important;
        }
        
        .company-info h1 {
          font-size: 1.4rem !important;
        }
        
        .system-name h2 {
          font-size: 1.6rem !important;
        }
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script>
      // 修改页脚公司名称和版权信息
      window.addEventListener('DOMContentLoaded', function() {
        // 使用更强的选择器并增加重试次数
        function updateFooter() {
          var footerElements = document.querySelectorAll('.login-page-footer');
          if (footerElements.length > 0) {
            footerElements.forEach(function(footer) {
              footer.innerHTML = '© ' + new Date().getFullYear() + ' 远大医药天天明制药有限公司';
            });
            return true;
          }
          return false;
        }
        
        // 立即尝试一次
        if (!updateFooter()) {
          // 如果失败，设置多次尝试
          var attempts = 0;
          var interval = setInterval(function() {
            if (updateFooter() || attempts >= 10) {
              clearInterval(interval);
            }
            attempts++;
          }, 500);
        }
      });
    </script>
  </body>
</html>
