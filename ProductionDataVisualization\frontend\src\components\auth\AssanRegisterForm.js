import React, { useState } from 'react';
import { Form, Input, Button, Alert, Typography, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, UserAddOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import authService from '../../services/authService';
import { useNavigate, Link } from 'react-router-dom';

const { Title, Text } = Typography;

const AssanRegisterForm = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const onFinish = async (values) => {
    setLoading(true);
    setError('');
    
    try {
      await authService.register(values.username, values.email, values.password);
      navigate('/dashboard');
    } catch (err) {
      setError(err.message || '注册失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="assan-register-container">
      {/* 背景装饰 */}
      <div className="background-decoration">
        <div className="decoration-circle circle-1"></div>
        <div className="decoration-circle circle-2"></div>
        <div className="decoration-circle circle-3"></div>
        <div className="decoration-circle circle-4"></div>
        <div className="decoration-circle circle-5"></div>

        {/* 粒子效果 */}
        <div className="particles">
          {[...Array(25)].map((_, i) => (
            <div key={i} className={`particle particle-${i + 1}`}></div>
          ))}
        </div>

        {/* 光线效果 */}
        <div className="light-rays">
          <div className="ray ray-1"></div>
          <div className="ray ray-2"></div>
          <div className="ray ray-3"></div>
          <div className="ray ray-4"></div>
        </div>
      </div>

      {/* 主注册卡片 */}
      <motion.div
        className="register-card"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        {/* 头部区域 */}
        <div className="register-header">
          <motion.div
            className="brand-logo"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5, type: "spring" }}
          >
            <div className="logo-icon">
              <UserAddOutlined />
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
          >
            <Title level={1} className="register-title">
              创建账户
            </Title>
            <Text className="register-subtitle">
              加入生产数据可视化平台
            </Text>
          </motion.div>
        </div>

        {/* 错误提示 */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="error-alert"
          >
            <Alert 
              message={error} 
              type="error" 
              showIcon 
              className="custom-alert"
            />
          </motion.div>
        )}

        {/* 注册表单 */}
        <Form
          name="register"
          form={form}
          onFinish={onFinish}
          layout="vertical"
          className="register-form"
        >
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            <Form.Item
              name="username"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' }
              ]}
            >
              <Input 
                prefix={<UserOutlined className="input-icon" />} 
                placeholder="用户名" 
                className="custom-input"
                size="large"
              />
            </Form.Item>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5, duration: 0.6 }}
          >
            <Form.Item
              name="email"
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}
            >
              <Input 
                prefix={<MailOutlined className="input-icon" />} 
                placeholder="邮箱地址" 
                className="custom-input"
                size="large"
              />
            </Form.Item>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
          >
            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined className="input-icon" />}
                placeholder="密码"
                className="custom-input"
                size="large"
              />
            </Form.Item>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.7, duration: 0.6 }}
          >
            <Form.Item
              name="confirmPassword"
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined className="input-icon" />}
                placeholder="确认密码"
                className="custom-input"
                size="large"
              />
            </Form.Item>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="form-options"
          >
            <Form.Item 
              name="agreement" 
              valuePropName="checked"
              rules={[
                { validator: (_, value) => value ? Promise.resolve() : Promise.reject(new Error('请同意服务条款')) }
              ]}
            >
              <Checkbox className="agreement-checkbox">
                我同意 <Link to="/terms" className="terms-link">服务条款</Link> 和 <Link to="/privacy" className="terms-link">隐私政策</Link>
              </Checkbox>
            </Form.Item>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9, duration: 0.6 }}
          >
            <Button
              type="primary"
              htmlType="submit"
              block
              loading={loading}
              className="register-button"
              size="large"
              icon={<UserAddOutlined />}
            >
              创建账户
            </Button>
          </motion.div>
        </Form>

        {/* 登录链接 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.0, duration: 0.6 }}
          className="login-section"
        >
          <Text className="login-text">
            已有账户？
            <Link to="/login" className="login-link">
              立即登录
            </Link>
          </Text>
        </motion.div>
      </motion.div>

      {/* Assan风格样式 */}
      <style jsx="true">{`
        .assan-register-container {
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
            linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          position: relative;
          overflow: hidden;
          padding: 20px;
        }

        .background-decoration {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
        }

        .decoration-circle {
          position: absolute;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.1);
          animation: float 6s ease-in-out infinite;
        }

        .circle-1 {
          width: 200px;
          height: 200px;
          top: 10%;
          left: 10%;
          animation-delay: 0s;
        }

        .circle-2 {
          width: 150px;
          height: 150px;
          top: 60%;
          right: 15%;
          animation-delay: 2s;
        }

        .circle-3 {
          width: 100px;
          height: 100px;
          bottom: 20%;
          left: 20%;
          animation-delay: 4s;
        }

        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }

        .register-card {
          width: 100%;
          max-width: 520px;
          background:
            linear-gradient(145deg,
              rgba(255, 255, 255, 0.95) 0%,
              rgba(248, 250, 252, 0.9) 50%,
              rgba(255, 255, 255, 0.95) 100%
            );
          backdrop-filter: blur(25px);
          border-radius: 24px;
          padding: 48px;
          box-shadow:
            0 32px 64px rgba(102, 126, 234, 0.15),
            0 16px 32px rgba(118, 75, 162, 0.1),
            0 8px 16px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.9),
            inset 0 -1px 0 rgba(255, 255, 255, 0.5);
          border: 1px solid rgba(255, 255, 255, 0.8);
          position: relative;
          z-index: 1;
          overflow: hidden;
        }

        .register-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
          animation: shimmer 3s infinite;
          pointer-events: none;
        }

        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }

        .register-header {
          text-align: center;
          margin-bottom: 40px;
        }

        .brand-logo {
          margin-bottom: 24px;
        }

        .logo-icon {
          width: 80px;
          height: 80px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto;
          box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
          font-size: 32px;
          color: white;
        }

        .register-title {
          font-size: 32px !important;
          font-weight: 700 !important;
          color: #1a202c !important;
          margin-bottom: 8px !important;
          line-height: 1.2 !important;
        }

        .register-subtitle {
          font-size: 16px;
          color: #718096;
          line-height: 1.5;
        }

        .error-alert {
          margin-bottom: 24px;
        }

        .custom-alert {
          border-radius: 12px;
          border: none;
          background: rgba(254, 242, 242, 0.9);
        }

        .register-form {
          margin-bottom: 24px;
        }

        .custom-input {
          height: 56px !important;
          border-radius: 12px !important;
          border: 2px solid #e2e8f0 !important;
          background: rgba(255, 255, 255, 0.8) !important;
          font-size: 16px !important;
          transition: all 0.3s ease !important;
        }

        .custom-input:hover,
        .custom-input:focus {
          border-color: #667eea !important;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
          background: rgba(255, 255, 255, 0.95) !important;
        }

        .input-icon {
          color: #a0aec0;
          font-size: 18px;
        }

        .form-options {
          margin-bottom: 32px;
        }

        .agreement-checkbox {
          color: #4a5568;
          line-height: 1.6;
        }

        .terms-link {
          color: #667eea;
          text-decoration: none;
          font-weight: 500;
        }

        .terms-link:hover {
          color: #5a67d8;
          text-decoration: underline;
        }

        .register-button {
          height: 56px !important;
          border-radius: 12px !important;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          border: none !important;
          font-size: 16px !important;
          font-weight: 600 !important;
          box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3) !important;
          transition: all 0.3s ease !important;
        }

        .register-button:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
        }

        .login-section {
          text-align: center;
          padding-top: 24px;
          border-top: 1px solid #e2e8f0;
        }

        .login-text {
          color: #718096;
          font-size: 15px;
        }

        .login-link {
          color: #667eea;
          text-decoration: none;
          font-weight: 600;
          margin-left: 8px;
          transition: color 0.3s ease;
        }

        .login-link:hover {
          color: #5a67d8;
        }

        @media (max-width: 480px) {
          .register-card {
            padding: 32px 24px;
            margin: 20px;
          }
          
          .register-title {
            font-size: 28px !important;
          }
        }
      `}</style>
    </div>
  );
};

export default AssanRegisterForm;
