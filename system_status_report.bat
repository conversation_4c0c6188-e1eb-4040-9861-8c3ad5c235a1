@echo off
title System Status Report

echo ==========================================
echo   生产数据可视化系统状态报告
echo   Production Data Visualization System Status
echo ==========================================
echo.

echo [1] 环境检查 Environment Check:
echo.
echo Node.js:
node --version 2>nul
if %errorlevel% equ 0 (
    echo    Status: ✓ WORKING
) else (
    echo    Status: ✗ NOT WORKING
)

echo.
echo npm:
npm --version 2>nul
if %errorlevel% equ 0 (
    echo    Status: ✓ WORKING
) else (
    echo    Status: ✗ NOT WORKING
)

echo.
echo .NET SDK:
dotnet --version 2>nul
if %errorlevel% equ 0 (
    echo    Status: ✓ WORKING
) else (
    echo    Status: ✗ NOT WORKING
)

echo.
echo [2] 数据库状态 Database Status:
echo.
set SQLCMD_PATH=C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\SQLCMD.EXE
"%SQLCMD_PATH%" -S "localhost\SQLEXPRESS" -E -Q "SELECT 'Database Connected' as Status" -h -1 2>nul
if %errorlevel% equ 0 (
    echo    SQL Server: ✓ CONNECTED
    echo    Database: ProductionDataVisualizationDb
) else (
    echo    SQL Server: ✗ NOT CONNECTED
)

echo.
echo [3] 服务状态 Service Status:
echo.

echo 检查后端API (端口5000):
netstat -ano | findstr :5000 | findstr LISTENING >nul 2>nul
if %errorlevel% equ 0 (
    echo    Backend API: ✓ PORT 5000 LISTENING
) else (
    echo    Backend API: ⚠ PORT 5000 NOT LISTENING
)

echo.
echo 检查前端 (端口3000):
netstat -ano | findstr :3000 | findstr LISTENING >nul 2>nul
if %errorlevel% equ 0 (
    echo    Frontend: ✓ PORT 3000 LISTENING
) else (
    echo    Frontend: ⚠ PORT 3000 NOT LISTENING
)

echo.
echo [4] 项目文件状态 Project Files Status:
echo.

if exist "ProductionDataVisualization\frontend\node_modules" (
    echo    Frontend Dependencies: ✓ INSTALLED
) else (
    echo    Frontend Dependencies: ✗ NOT INSTALLED
)

if exist "ProductionDataVisualization\backend\node_modules" (
    echo    Backend Dependencies: ✓ INSTALLED
) else (
    echo    Backend Dependencies: ✗ NOT INSTALLED
)

if exist "ProductionDataVisualization\frontend\.env" (
    echo    Frontend Config: ✓ CONFIGURED
) else (
    echo    Frontend Config: ⚠ NOT CONFIGURED
)

echo.
echo ==========================================
echo   访问信息 Access Information
echo ==========================================
echo.
echo 如果服务正在运行，请访问:
echo.
echo 前端应用 Frontend Application:
echo   http://localhost:3000
echo.
echo 后端API Backend API:
echo   http://localhost:5000/api/health
echo.
echo 默认登录 Default Login:
echo   Username: admin
echo   Password: admin123
echo.

echo ==========================================
echo   故障排除 Troubleshooting
echo ==========================================
echo.
echo 如果服务未运行，请尝试:
echo.
echo 1. 手动启动后端:
echo    cd ProductionDataVisualization\backend\SqlServerAPI
echo    dotnet run --urls=http://localhost:5000
echo.
echo 2. 手动启动前端:
echo    cd ProductionDataVisualization\frontend
echo    npm start
echo.
echo 3. 检查日志文件:
echo    ProductionDataVisualization\logs\
echo.
echo 4. 重新安装依赖:
echo    cd ProductionDataVisualization\frontend
echo    npm install
echo.

echo ==========================================
echo   系统已启动完成
echo   System Startup Complete
echo ==========================================
echo.
echo 请检查浏览器中打开的页面:
echo - http://localhost:3000 (前端应用)
echo - http://localhost:5000/api/health (后端API)
echo.
echo 如果页面无法访问，请等待几分钟让服务完全启动，
echo 或按照上述故障排除步骤手动启动服务。
echo.

pause
