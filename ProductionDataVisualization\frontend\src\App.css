/* 全局样式 - 现代简约黑白灰主题 */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: var(--font-family-primary);
  background-color: var(--bg-base);
  background-image: var(--texture-noise);
  color: var(--text-primary);
}

/* 登录页面样式 */
.login-page {
  position: relative;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  font-family: var(--font-family-primary);
}

.login-form-container {
  position: relative;
  z-index: 10;
  overflow: hidden;
}

.login-input:focus {
  border-color: var(--login-accent) !important;
  box-shadow: var(--shadow-blue-sm) !important;
}

.login-input:hover {
  border-color: var(--login-accent) !important;
}

.shake-animation {
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes shake {
  10%, 90% { transform: translateX(-1px); }
  20%, 80% { transform: translateX(2px); }
  30%, 50%, 70% { transform: translateX(-4px); }
  40%, 60% { transform: translateX(4px); }
}

/* 页面过渡动画 */
.page-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 应用头部 */
.app-header {
  background-color: var(--bg-light);
  border-bottom: 1px solid var(--border-color);
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: var(--shadow-md);
  z-index: var(--z-sticky);
}

.app-header h1 {
  color: var(--text-primary);
  margin: 0;
  font-family: var(--font-family-secondary);
  font-weight: var(--font-weight-semibold);
  letter-spacing: var(--letter-spacing-tight);
}

/* 应用内容区域 */
.app-content {
  flex: 1;
  padding: 50px;
  display: flex;
  justify-content: center;
  background-color: var(--bg-base);
  position: relative;
}

/* 微妙的装饰元素 */
.app-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(180deg, rgba(51, 51, 51, 0.03) 0%, rgba(51, 51, 51, 0) 100%);
  z-index: 0;
  pointer-events: none;
}

.app-content::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 150px;
  background: linear-gradient(0deg, rgba(51, 51, 51, 0.02) 0%, rgba(51, 51, 51, 0) 100%);
  z-index: 0;
  pointer-events: none;
}

/* 状态面板 */
.status-panel {
  background-color: var(--bg-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  padding: var(--space-lg);
  width: 100%;
  max-width: 600px;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.status-panel::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--gradient-main);
  z-index: 2;
}

.status-panel::after {
  content: "";
  position: absolute;
  top: 20px;
  right: 20px;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(51, 51, 51, 0.05) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 0;
}

/* 按钮容器 */
.button-container {
  margin: var(--space-lg) 0;
  display: flex;
  justify-content: center;
}

/* 检查按钮 */
.check-button {
  background: var(--gradient-main);
  color: white;
  border: none;
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.check-button:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.check-button:active {
  box-shadow: var(--shadow-sm);
  transform: translateY(1px);
}

/* 按钮光效 */
.check-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.check-button:hover::after {
  left: 100%;
  transition: left 0.8s ease;
}

/* 信息区域 */
.info-section {
  margin-top: var(--space-xl);
  border-top: 1px solid var(--border-color);
  padding-top: var(--space-lg);
  position: relative;
}

/* 状态样式 */
.status-ok {
  color: var(--success);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
}

.status-ok::before {
  content: "✓";
  display: inline-block;
  margin-right: var(--space-xs);
  font-weight: var(--font-weight-bold);
}

.status-error {
  color: var(--error);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
}

.status-error::before {
  content: "✗";
  display: inline-block;
  margin-right: var(--space-xs);
  font-weight: var(--font-weight-bold);
}

.status-pending {
  color: var(--warning);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
}

.status-pending::before {
  content: "⟳";
  display: inline-block;
  margin-right: var(--space-xs);
  font-weight: var(--font-weight-bold);
  animation: spin 1.5s linear infinite;
}

/* 应用页脚 */
.app-footer {
  text-align: center;
  background-color: var(--bg-light);
  border-top: 1px solid var(--border-color);
  padding: var(--space-lg);
  color: var(--text-tertiary);
  font-size: var(--font-size-sm);
  position: relative;
  z-index: var(--z-base);
}

/* 登录页面样式 */
.futuristic-login-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.three-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.card-hover {
  transition: all var(--transition-normal) var(--ease-out);
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.decoration-dot {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--primary-color);
  opacity: 0.2;
}

.decoration-circle {
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 1px solid var(--border-color);
  opacity: 0.1;
}

@media (max-width: 768px) {
  .app-content {
    padding: 20px;
  }
  
  .status-panel {
    padding: var(--space-md);
  }
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-base);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-light);
  border-radius: var(--radius-full);
  transition: all var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-hover);
}

::selection {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.enhanced-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
}

.enhanced-table th {
  background-color: var(--bg-light);
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
  padding: var(--space-md);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.enhanced-table td {
  padding: var(--space-md);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.enhanced-table tr:last-child td {
  border-bottom: none;
}

.enhanced-table tr:hover td {
  background-color: var(--bg-hover);
}

.enhanced-form .ant-form-item-label > label {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.enhanced-form .ant-input,
.enhanced-form .ant-input-password,
.enhanced-form .ant-select-selector {
  border-radius: var(--radius-md);
  border-color: var(--border-color);
  transition: all var(--transition-normal);
}

.enhanced-form .ant-input:hover,
.enhanced-form .ant-input-password:hover,
.enhanced-form .ant-select-selector:hover {
  border-color: var(--primary-hover);
}

.enhanced-form .ant-input:focus,
.enhanced-form .ant-input-password:focus,
.enhanced-form .ant-select-selector:focus,
.enhanced-form .ant-input-focused,
.enhanced-form .ant-input-password-focused,
.enhanced-form .ant-select-focused .ant-select-selector {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-focus);
}

.enhanced-card {
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  overflow: hidden;
  background-color: var(--bg-light);
  position: relative;
}

.enhanced-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.enhanced-card .ant-card-head {
  border-bottom: 1px solid var(--border-color);
  padding: var(--space-md) var(--space-lg);
}

.enhanced-card .ant-card-head-title {
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.enhanced-card .ant-card-body {
  padding: var(--space-lg);
}

.enhanced-button {
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  height: 40px;
  padding: 0 var(--space-lg);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.enhanced-button-primary {
  background: var(--gradient-main);
  border: none;
  color: white;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.enhanced-button-primary:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.enhanced-button-primary:active {
  box-shadow: var(--shadow-sm);
  transform: translateY(1px);
}

.enhanced-button-secondary {
  background: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  transition: all var(--transition-normal);
}

.enhanced-button-secondary:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background-color: var(--primary-lighter);
}

.enhanced-tag {
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  padding: 2px var(--space-sm);
  font-size: var(--font-size-xs);
  display: inline-flex;
  align-items: center;
}

.enhanced-tag-primary {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.enhanced-tag-success {
  background-color: var(--success-light);
  color: var(--success);
}

.enhanced-tag-warning {
  background-color: var(--warning-light);
  color: var(--warning);
}

.enhanced-tag-error {
  background-color: var(--error-light);
  color: var(--error);
}

.enhanced-badge .ant-badge-count {
  box-shadow: var(--shadow-sm);
}

/* 登录页面特定样式 */
.ant-input-affix-wrapper.login-input {
  height: 50px;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
}

.ant-input-affix-wrapper.login-input:hover {
  border-color: var(--login-accent);
}

.ant-input-affix-wrapper.login-input:focus,
.ant-input-affix-wrapper.login-input-focused {
  border-color: var(--login-accent);
  box-shadow: var(--shadow-blue-sm);
}

.ant-input-affix-wrapper.login-input .ant-input {
  height: 48px;
  font-size: 16px;
}

.ant-btn-primary.login-button {
  height: 50px;
  border-radius: var(--radius-lg);
  background: var(--gradient-blue);
  border: none;
  font-size: 16px;
  font-weight: var(--font-weight-medium);
  box-shadow: var(--shadow-blue-sm);
  transition: all var(--transition-normal);
}

.ant-btn-primary.login-button:hover {
  background: var(--gradient-blue);
  box-shadow: var(--shadow-blue-md);
  transform: translateY(-2px);
}

.ant-btn-primary.login-button:active {
  background: var(--gradient-blue);
  box-shadow: var(--shadow-blue-sm);
  transform: translateY(1px);
}

.ant-checkbox-wrapper.login-checkbox .ant-checkbox-inner {
  border-color: var(--login-accent);
}

.ant-checkbox-wrapper.login-checkbox .ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--login-accent);
  border-color: var(--login-accent);
}

.login-link {
  color: var(--login-accent);
  transition: all var(--transition-fast);
}

.login-link:hover {
  color: var(--info-hover);
  text-decoration: underline;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .login-page .login-content {
    padding: 20px;
  }
  
  .login-page .login-form-container {
    padding: 30px 20px;
  }
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
} 