@echo off
title Node.js Test

echo Testing Node.js installation...
echo.

echo 1. Testing direct Node.js execution:
"D:\Programs\nodejs\node.exe" --version
echo.

echo 2. Testing direct npm execution:
"D:\Programs\nodejs\npm.cmd" --version
echo.

echo 3. Adding Node.js to current session PATH:
set PATH=D:\Programs\nodejs;%PATH%
echo.

echo 4. Testing node command after PATH update:
node --version
echo.

echo 5. Testing npm command after PATH update:
npm --version
echo.

echo 6. Adding to system PATH permanently:
setx PATH "%PATH%;D:\Programs\nodejs" /M
echo.

echo Test completed. Please restart command prompt to use node and npm commands.
pause
