<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产数据可视化系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #001529;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            background-color: white;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            margin-top: 20px;
        }
        footer {
            text-align: center;
            padding: 20px;
            color: #666;
            margin-top: 20px;
        }
        .button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background-color: #40a9ff;
        }
    </style>
</head>
<body>
    <header>
        <h1>生产数据可视化系统</h1>
    </header>
    
    <div class="container">
        <div class="content">
            <h2>系统状态</h2>
            <p>系统正在启动中...</p>
            
            <div>
                <button class="button" onclick="window.location.reload()">刷新页面</button>
            </div>
            
            <div id="status" style="margin-top: 20px;">
                <h3>初始化信息</h3>
                <p>前端服务: <span style="color: #52c41a; font-weight: bold;">已启动</span></p>
                <p>后端API: <span id="api-status" style="color: #faad14; font-weight: bold;">检查中...</span></p>
            </div>
        </div>
    </div>
    
    <footer>
        生产数据可视化系统 ©2024
    </footer>
    
    <script>
        // 检查API是否可用
        fetch('http://localhost:5000/health')
            .then(response => {
                if (response.ok) {
                    document.getElementById('api-status').textContent = '已连接';
                    document.getElementById('api-status').style.color = '#52c41a';
                } else {
                    document.getElementById('api-status').textContent = '连接失败';
                    document.getElementById('api-status').style.color = '#f5222d';
                }
            })
            .catch(() => {
                document.getElementById('api-status').textContent = '无法连接';
                document.getElementById('api-status').style.color = '#f5222d';
            });
    </script>
</body>
</html> 