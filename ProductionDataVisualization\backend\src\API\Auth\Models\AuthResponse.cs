using System;
using System.Collections.Generic;

namespace API.Auth.Models
{
    /// <summary>
    /// 认证响应
    /// </summary>
    public class AuthResponse
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 全名
        /// </summary>
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// JWT令牌
        /// </summary>
        public string Token { get; set; } = string.Empty;

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// 角色列表
        /// </summary>
        public List<string> Roles { get; set; } = new List<string>();

        /// <summary>
        /// 权限列表
        /// </summary>
        public List<string> Permissions { get; set; } = new List<string>();
    }
} 