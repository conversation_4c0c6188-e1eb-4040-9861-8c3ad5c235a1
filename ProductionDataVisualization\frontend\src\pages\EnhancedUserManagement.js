import React, { useState, useEffect } from 'react';
import { 
  Button, 
  Space, 
  Modal, 
  message, 
  Typography, 
  Row, 
  Col,
  Input,
  Select,
  Tag,
  Avatar,
  Tooltip
} from 'antd';
import { 
  PlusOutlined, 
  ReloadOutlined, 
  SearchOutlined,
  UserOutlined,
  EditOutlined,
  DeleteOutlined,
  TeamOutlined,
  CrownOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import EnhancedAssanLayout from '../components/layout/EnhancedAssanLayout';
import EnhancedCard from '../components/common/EnhancedCard';
import UserTable from '../components/user/UserTable';
import UserForm from '../components/user/UserForm';
import userService from '../services/userService';
import roleService from '../services/roleService';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

const EnhancedUserManagement = () => {
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');

  // 模拟用户数据
  const mockUsers = [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      lastLogin: '2024-01-15 10:30:00',
      avatar: null
    },
    {
      id: 2,
      username: 'manager',
      email: '<EMAIL>',
      role: 'manager',
      status: 'active',
      lastLogin: '2024-01-15 09:15:00',
      avatar: null
    },
    {
      id: 3,
      username: 'operator',
      email: '<EMAIL>',
      role: 'operator',
      status: 'active',
      lastLogin: '2024-01-15 08:45:00',
      avatar: null
    },
    {
      id: 4,
      username: 'viewer',
      email: '<EMAIL>',
      role: 'viewer',
      status: 'inactive',
      lastLogin: '2024-01-14 16:20:00',
      avatar: null
    }
  ];

  const mockRoles = [
    { id: 'admin', name: '管理员', color: '#667eea', icon: <CrownOutlined /> },
    { id: 'manager', name: '经理', color: '#764ba2', icon: <TeamOutlined /> },
    { id: 'operator', name: '操作员', color: '#22c55e', icon: <UserOutlined /> },
    { id: 'viewer', name: '查看者', color: '#64748b', icon: <SafetyOutlined /> }
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      setTimeout(() => {
        setUsers(mockUsers);
        setRoles(mockRoles);
        setLoading(false);
      }, 1000);
    } catch (error) {
      message.error('加载数据失败');
      setLoading(false);
    }
  };

  const handleAddUser = () => {
    setEditingUser(null);
    setModalVisible(true);
  };

  const handleEditUser = (user) => {
    setEditingUser(user);
    setModalVisible(true);
  };

  const handleDeleteUser = (userId) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个用户吗？此操作不可撤销。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        setUsers(users.filter(user => user.id !== userId));
        message.success('用户删除成功');
      }
    });
  };

  const handleModalOk = (values) => {
    if (editingUser) {
      // 编辑用户
      setUsers(users.map(user => 
        user.id === editingUser.id ? { ...user, ...values } : user
      ));
      message.success('用户更新成功');
    } else {
      // 添加用户
      const newUser = {
        id: Date.now(),
        ...values,
        status: 'active',
        lastLogin: new Date().toLocaleString(),
        avatar: null
      };
      setUsers([...users, newUser]);
      message.success('用户添加成功');
    }
    setModalVisible(false);
  };

  const getRoleInfo = (roleId) => {
    return mockRoles.find(role => role.id === roleId) || mockRoles[0];
  };

  const getStatusColor = (status) => {
    return status === 'active' ? '#22c55e' : '#64748b';
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.username.toLowerCase().includes(searchText.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchText.toLowerCase());
    const matchesRole = selectedRole === 'all' || user.role === selectedRole;
    return matchesSearch && matchesRole;
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <EnhancedAssanLayout>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        style={{ padding: '0 24px' }}
      >
        {/* 页面标题 */}
        <motion.div variants={itemVariants}>
          <EnhancedCard 
            style={{ marginBottom: '24px' }}
            bodyStyle={{ padding: '32px' }}
          >
            <Row align="middle" justify="space-between">
              <Col>
                <Space size="large" align="center">
                  <div
                    style={{
                      width: '64px',
                      height: '64px',
                      borderRadius: '16px',
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '28px',
                      color: 'white',
                      boxShadow: '0 8px 24px rgba(102, 126, 234, 0.3)'
                    }}
                  >
                    <TeamOutlined />
                  </div>
                  <div>
                    <Title level={2} style={{ margin: 0, color: '#1a202c' }}>
                      用户管理
                    </Title>
                    <Text style={{ color: '#64748b', fontSize: '16px' }}>
                      管理系统用户和权限设置
                    </Text>
                  </div>
                </Space>
              </Col>
              <Col>
                <Space>
                  <Button 
                    icon={<ReloadOutlined />}
                    onClick={loadData}
                    loading={loading}
                    style={{
                      borderRadius: '8px',
                      height: '40px'
                    }}
                  >
                    刷新
                  </Button>
                  <Button 
                    type="primary" 
                    icon={<PlusOutlined />}
                    onClick={handleAddUser}
                    size="large"
                    style={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      border: 'none',
                      borderRadius: '8px',
                      height: '40px',
                      fontWeight: '600',
                      boxShadow: '0 4px 16px rgba(102, 126, 234, 0.3)'
                    }}
                  >
                    添加用户
                  </Button>
                </Space>
              </Col>
            </Row>
          </EnhancedCard>
        </motion.div>

        {/* 搜索和筛选 */}
        <motion.div variants={itemVariants}>
          <EnhancedCard style={{ marginBottom: '24px' }}>
            <Row gutter={[16, 16]} align="middle">
              <Col xs={24} sm={12} md={8}>
                <Search
                  placeholder="搜索用户名或邮箱"
                  allowClear
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: '100%' }}
                  size="large"
                />
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Select
                  placeholder="选择角色"
                  value={selectedRole}
                  onChange={setSelectedRole}
                  style={{ width: '100%' }}
                  size="large"
                >
                  <Option value="all">所有角色</Option>
                  {mockRoles.map(role => (
                    <Option key={role.id} value={role.id}>
                      <Space>
                        {role.icon}
                        {role.name}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col xs={24} sm={24} md={10}>
                <Space>
                  <Text style={{ color: '#64748b' }}>
                    共 {filteredUsers.length} 个用户
                  </Text>
                  {mockRoles.map(role => {
                    const count = filteredUsers.filter(user => user.role === role.id).length;
                    return count > 0 ? (
                      <Tag key={role.id} color={role.color}>
                        {role.name}: {count}
                      </Tag>
                    ) : null;
                  })}
                </Space>
              </Col>
            </Row>
          </EnhancedCard>
        </motion.div>

        {/* 用户列表 */}
        <motion.div variants={itemVariants}>
          <EnhancedCard title="用户列表">
            <Row gutter={[16, 16]}>
              {filteredUsers.map((user, index) => {
                const roleInfo = getRoleInfo(user.role);
                return (
                  <Col xs={24} sm={12} lg={8} xl={6} key={user.id}>
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1, duration: 0.4 }}
                      whileHover={{ y: -5 }}
                    >
                      <div
                        style={{
                          padding: '20px',
                          borderRadius: '16px',
                          background: 'rgba(255, 255, 255, 0.8)',
                          border: '1px solid rgba(255, 255, 255, 0.6)',
                          boxShadow: '0 8px 24px rgba(0, 0, 0, 0.08)',
                          transition: 'all 0.3s ease',
                          cursor: 'pointer'
                        }}
                      >
                        <div style={{ textAlign: 'center', marginBottom: '16px' }}>
                          <Avatar 
                            size={64} 
                            icon={<UserOutlined />}
                            style={{
                              background: `linear-gradient(135deg, ${roleInfo.color}, ${roleInfo.color}dd)`,
                              marginBottom: '12px'
                            }}
                          />
                          <div>
                            <Text strong style={{ fontSize: '16px', color: '#1a202c' }}>
                              {user.username}
                            </Text>
                            <br />
                            <Text style={{ color: '#64748b', fontSize: '14px' }}>
                              {user.email}
                            </Text>
                          </div>
                        </div>

                        <div style={{ marginBottom: '16px' }}>
                          <Space direction="vertical" size="small" style={{ width: '100%' }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Text style={{ color: '#64748b' }}>角色:</Text>
                              <Tag color={roleInfo.color}>
                                <Space size={4}>
                                  {roleInfo.icon}
                                  {roleInfo.name}
                                </Space>
                              </Tag>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Text style={{ color: '#64748b' }}>状态:</Text>
                              <Tag color={getStatusColor(user.status)}>
                                {user.status === 'active' ? '活跃' : '非活跃'}
                              </Tag>
                            </div>
                          </Space>
                        </div>

                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Tooltip title="编辑用户">
                            <Button 
                              type="text" 
                              icon={<EditOutlined />}
                              onClick={() => handleEditUser(user)}
                              style={{ color: '#667eea' }}
                            />
                          </Tooltip>
                          <Tooltip title="删除用户">
                            <Button 
                              type="text" 
                              icon={<DeleteOutlined />}
                              onClick={() => handleDeleteUser(user.id)}
                              style={{ color: '#ef4444' }}
                            />
                          </Tooltip>
                        </div>
                      </div>
                    </motion.div>
                  </Col>
                );
              })}
            </Row>
          </EnhancedCard>
        </motion.div>
      </motion.div>

      {/* 用户表单模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '添加用户'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
        style={{ top: 50 }}
      >
        <UserForm
          user={editingUser}
          roles={roles}
          onSubmit={handleModalOk}
          onCancel={() => setModalVisible(false)}
        />
      </Modal>
    </EnhancedAssanLayout>
  );
};

export default EnhancedUserManagement;
