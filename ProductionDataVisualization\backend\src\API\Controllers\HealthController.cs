using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using API.Models;
using Infrastructure.Persistence;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace API.Controllers
{
    /// <summary>
    /// 健康检查控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class HealthController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<HealthController> _logger;

        public HealthController(ApplicationDbContext context, ILogger<HealthController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 基础健康检查
        /// </summary>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult GetHealth()
        {
            try
            {
                var healthInfo = new
                {
                    Status = "Healthy",
                    Timestamp = DateTime.UtcNow,
                    Version = GetType().Assembly.GetName().Version?.ToString(),
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
                };

                return Ok(ApiResponse<object>.Ok(healthInfo, "服务运行正常"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "健康检查失败");
                return StatusCode(500, ApiResponse<object>.Fail("服务异常", ex.Message));
            }
        }

        /// <summary>
        /// 详细健康检查
        /// </summary>
        [HttpGet("detailed")]
        [AllowAnonymous]
        public async Task<IActionResult> GetDetailedHealth()
        {
            var healthChecks = new List<HealthCheckResult>();
            var overallStatus = "Healthy";

            try
            {
                // 检查数据库连接
                var dbCheck = await CheckDatabase();
                healthChecks.Add(dbCheck);

                // 检查内存使用
                var memoryCheck = CheckMemoryUsage();
                healthChecks.Add(memoryCheck);

                // 检查磁盘空间
                var diskCheck = CheckDiskSpace();
                healthChecks.Add(diskCheck);

                // 检查系统负载
                var loadCheck = CheckSystemLoad();
                healthChecks.Add(loadCheck);

                // 确定整体状态
                if (healthChecks.Any(h => h.Status == "Critical"))
                {
                    overallStatus = "Critical";
                }
                else if (healthChecks.Any(h => h.Status == "Warning"))
                {
                    overallStatus = "Warning";
                }

                var result = new
                {
                    OverallStatus = overallStatus,
                    Timestamp = DateTime.UtcNow,
                    Checks = healthChecks,
                    SystemInfo = GetSystemInfo()
                };

                return Ok(ApiResponse<object>.Ok(result, "详细健康检查完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "详细健康检查失败");
                return StatusCode(500, ApiResponse<object>.Fail("健康检查异常", ex.Message));
            }
        }

        /// <summary>
        /// 检查数据库连接
        /// </summary>
        private async Task<HealthCheckResult> CheckDatabase()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                await _context.Database.CanConnectAsync();
                stopwatch.Stop();

                var responseTime = stopwatch.ElapsedMilliseconds;
                var status = responseTime < 1000 ? "Healthy" : responseTime < 3000 ? "Warning" : "Critical";

                return new HealthCheckResult
                {
                    Name = "Database",
                    Status = status,
                    ResponseTime = responseTime,
                    Message = $"数据库连接正常，响应时间: {responseTime}ms",
                    Details = new Dictionary<string, object>
                    {
                        ["ConnectionString"] = _context.Database.GetConnectionString()?.Substring(0, 50) + "...",
                        ["Provider"] = _context.Database.ProviderName
                    }
                };
            }
            catch (Exception ex)
            {
                return new HealthCheckResult
                {
                    Name = "Database",
                    Status = "Critical",
                    Message = "数据库连接失败",
                    Error = ex.Message
                };
            }
        }

        /// <summary>
        /// 检查内存使用情况
        /// </summary>
        private HealthCheckResult CheckMemoryUsage()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var workingSet = process.WorkingSet64;
                var workingSetMB = workingSet / 1024 / 1024;

                var status = workingSetMB < 500 ? "Healthy" : workingSetMB < 1000 ? "Warning" : "Critical";

                return new HealthCheckResult
                {
                    Name = "Memory",
                    Status = status,
                    Message = $"内存使用: {workingSetMB}MB",
                    Details = new Dictionary<string, object>
                    {
                        ["WorkingSetMB"] = workingSetMB,
                        ["PrivateMemoryMB"] = process.PrivateMemorySize64 / 1024 / 1024,
                        ["VirtualMemoryMB"] = process.VirtualMemorySize64 / 1024 / 1024
                    }
                };
            }
            catch (Exception ex)
            {
                return new HealthCheckResult
                {
                    Name = "Memory",
                    Status = "Warning",
                    Message = "无法获取内存信息",
                    Error = ex.Message
                };
            }
        }

        /// <summary>
        /// 检查磁盘空间
        /// </summary>
        private HealthCheckResult CheckDiskSpace()
        {
            try
            {
                var drives = DriveInfo.GetDrives()
                    .Where(d => d.IsReady && d.DriveType == DriveType.Fixed)
                    .Select(d => new
                    {
                        Name = d.Name,
                        TotalSizeGB = d.TotalSize / 1024 / 1024 / 1024,
                        AvailableSizeGB = d.AvailableFreeSpace / 1024 / 1024 / 1024,
                        UsagePercent = (double)(d.TotalSize - d.AvailableFreeSpace) / d.TotalSize * 100
                    })
                    .ToList();

                var maxUsage = drives.Any() ? drives.Max(d => d.UsagePercent) : 0;
                var status = maxUsage < 80 ? "Healthy" : maxUsage < 90 ? "Warning" : "Critical";

                return new HealthCheckResult
                {
                    Name = "DiskSpace",
                    Status = status,
                    Message = $"磁盘使用率: {maxUsage:F1}%",
                    Details = new Dictionary<string, object>
                    {
                        ["Drives"] = drives
                    }
                };
            }
            catch (Exception ex)
            {
                return new HealthCheckResult
                {
                    Name = "DiskSpace",
                    Status = "Warning",
                    Message = "无法获取磁盘信息",
                    Error = ex.Message
                };
            }
        }

        /// <summary>
        /// 检查系统负载
        /// </summary>
        private HealthCheckResult CheckSystemLoad()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var cpuTime = process.TotalProcessorTime;
                var uptime = DateTime.UtcNow - process.StartTime.ToUniversalTime();
                var cpuUsage = cpuTime.TotalMilliseconds / uptime.TotalMilliseconds * 100;

                var status = cpuUsage < 70 ? "Healthy" : cpuUsage < 90 ? "Warning" : "Critical";

                return new HealthCheckResult
                {
                    Name = "SystemLoad",
                    Status = status,
                    Message = $"CPU使用率: {cpuUsage:F1}%",
                    Details = new Dictionary<string, object>
                    {
                        ["CpuUsagePercent"] = cpuUsage,
                        ["UptimeMinutes"] = uptime.TotalMinutes,
                        ["ThreadCount"] = process.Threads.Count
                    }
                };
            }
            catch (Exception ex)
            {
                return new HealthCheckResult
                {
                    Name = "SystemLoad",
                    Status = "Warning",
                    Message = "无法获取系统负载信息",
                    Error = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取系统信息
        /// </summary>
        private object GetSystemInfo()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                return new
                {
                    MachineName = Environment.MachineName,
                    ProcessorCount = Environment.ProcessorCount,
                    OSVersion = Environment.OSVersion.ToString(),
                    RuntimeVersion = Environment.Version.ToString(),
                    StartTime = process.StartTime,
                    Uptime = DateTime.UtcNow - process.StartTime.ToUniversalTime()
                };
            }
            catch
            {
                return new { Error = "无法获取系统信息" };
            }
        }
    }

    /// <summary>
    /// 健康检查结果
    /// </summary>
    public class HealthCheckResult
    {
        public string Name { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public long? ResponseTime { get; set; }
        public string? Error { get; set; }
        public Dictionary<string, object>? Details { get; set; }
    }
}
