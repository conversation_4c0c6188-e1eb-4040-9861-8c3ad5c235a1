import React, { useRef, useEffect, useState } from 'react';
import * as echarts from 'echarts';
import { Card, Spin, Alert, Button, Space, Tooltip } from 'antd';
import { 
  FullscreenOutlined, 
  DownloadOutlined, 
  SettingOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';

const BaseChart = ({
  title,
  option,
  loading = false,
  error = null,
  height = 400,
  width = '100%',
  theme = 'default',
  onChartReady,
  onEvents = {},
  showToolbar = true,
  showFullscreen = true,
  showDownload = true,
  showSettings = false,
  showRefresh = true,
  className = '',
  style = {},
  ...props
}) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [chartError, setChartError] = useState(null);

  // 初始化图表
  useEffect(() => {
    if (!chartRef.current || loading || error) return;

    try {
      // 销毁已存在的图表实例
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }

      // 创建新的图表实例
      chartInstance.current = echarts.init(chartRef.current, theme);

      // 设置图表配置
      if (option) {
        chartInstance.current.setOption(option, true);
      }

      // 绑定事件
      Object.keys(onEvents).forEach(eventName => {
        chartInstance.current.on(eventName, onEvents[eventName]);
      });

      // 图表准备完成回调
      if (onChartReady) {
        onChartReady(chartInstance.current);
      }

      setChartError(null);
    } catch (err) {
      console.error('Chart initialization error:', err);
      setChartError(err.message);
    }

    // 清理函数
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [option, theme, loading, error, onChartReady]);

  // 响应式处理
  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 更新图表配置
  useEffect(() => {
    if (chartInstance.current && option && !loading) {
      try {
        chartInstance.current.setOption(option, true);
        setChartError(null);
      } catch (err) {
        console.error('Chart update error:', err);
        setChartError(err.message);
      }
    }
  }, [option, loading]);

  // 全屏切换
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    setTimeout(() => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    }, 100);
  };

  // 下载图表
  const downloadChart = () => {
    if (chartInstance.current) {
      const url = chartInstance.current.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${title || 'chart'}_${new Date().getTime()}.png`;
      link.click();
    }
  };

  // 刷新图表
  const refreshChart = () => {
    if (chartInstance.current && option) {
      chartInstance.current.clear();
      chartInstance.current.setOption(option, true);
    }
  };

  // 工具栏
  const renderToolbar = () => {
    if (!showToolbar) return null;

    return (
      <div className="chart-toolbar" style={{ 
        position: 'absolute', 
        top: 8, 
        right: 8, 
        zIndex: 10,
        background: 'rgba(255, 255, 255, 0.9)',
        borderRadius: '4px',
        padding: '4px'
      }}>
        <Space size="small">
          {showRefresh && (
            <Tooltip title="刷新图表">
              <Button 
                type="text" 
                size="small" 
                icon={<ReloadOutlined />}
                onClick={refreshChart}
              />
            </Tooltip>
          )}
          {showSettings && (
            <Tooltip title="图表设置">
              <Button 
                type="text" 
                size="small" 
                icon={<SettingOutlined />}
                onClick={() => {/* 设置功能 */}}
              />
            </Tooltip>
          )}
          {showDownload && (
            <Tooltip title="下载图表">
              <Button 
                type="text" 
                size="small" 
                icon={<DownloadOutlined />}
                onClick={downloadChart}
              />
            </Tooltip>
          )}
          {showFullscreen && (
            <Tooltip title={isFullscreen ? "退出全屏" : "全屏显示"}>
              <Button 
                type="text" 
                size="small" 
                icon={<FullscreenOutlined />}
                onClick={toggleFullscreen}
              />
            </Tooltip>
          )}
        </Space>
      </div>
    );
  };

  // 渲染内容
  const renderContent = () => {
    if (loading) {
      return (
        <div style={{ 
          height, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center' 
        }}>
          <Spin size="large" tip="图表加载中..." />
        </div>
      );
    }

    if (error || chartError) {
      return (
        <div style={{ 
          height, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          padding: '20px'
        }}>
          <Alert
            message="图表加载失败"
            description={error || chartError}
            type="error"
            icon={<ExclamationCircleOutlined />}
            action={
              <Button size="small" onClick={refreshChart}>
                重试
              </Button>
            }
          />
        </div>
      );
    }

    return (
      <div style={{ position: 'relative' }}>
        {renderToolbar()}
        <div
          ref={chartRef}
          style={{
            width,
            height,
            ...style
          }}
        />
      </div>
    );
  };

  const chartCard = (
    <Card
      title={title}
      className={`base-chart ${className}`}
      style={{
        width: isFullscreen ? '100vw' : '100%',
        height: isFullscreen ? '100vh' : 'auto',
        position: isFullscreen ? 'fixed' : 'relative',
        top: isFullscreen ? 0 : 'auto',
        left: isFullscreen ? 0 : 'auto',
        zIndex: isFullscreen ? 9999 : 'auto',
        ...style
      }}
      bodyStyle={{ padding: '12px' }}
      {...props}
    >
      {renderContent()}
    </Card>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {chartCard}
    </motion.div>
  );
};

export default BaseChart;
