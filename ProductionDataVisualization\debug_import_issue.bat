@echo off
title 调试数据导入问题

echo ========================================
echo   调试数据导入问题
echo ========================================
echo.

echo [INFO] 检查后端编译...
cd backend\SqlServerAPI

echo [INFO] 清理项目...
dotnet clean

echo [INFO] 恢复依赖...
dotnet restore

echo [INFO] 编译项目...
dotnet build --verbosity normal

if %errorlevel% neq 0 (
    echo [ERROR] 后端编译失败
    pause
    exit /b 1
)

echo [SUCCESS] 后端编译成功

echo [INFO] 启动后端服务...
start "Backend Debug" cmd /k "dotnet run --verbosity normal"

echo [INFO] 等待后端启动...
timeout /t 10 /nobreak

echo [INFO] 测试API端点...
curl -X GET http://localhost:5000/api/health
echo.
curl -X GET http://localhost:5000/api/data-import/test
echo.

echo [INFO] 测试数据导入任务创建...
curl -X POST http://localhost:5000/api/data-import/tasks ^
  -H "Content-Type: application/json" ^
  -d "{\"fileName\":\"test.csv\",\"fileSize\":1024,\"totalRows\":10,\"createdBy\":\"test-user\"}"

echo.
echo ========================================
echo   调试完成
echo ========================================

pause
