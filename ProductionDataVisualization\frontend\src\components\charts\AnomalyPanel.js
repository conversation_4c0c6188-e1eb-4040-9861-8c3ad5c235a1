import React, { useState, useMemo } from 'react';
import { 
  Card, 
  List, 
  Tag, 
  Button, 
  Space, 
  Statistic, 
  Row, 
  Col,
  Alert,
  Tooltip,
  Modal,
  Descriptions,
  Progress
} from 'antd';
import { 
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  EyeOutlined,
  DownloadOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { PHARMA_COLORS, formatNumber } from '../../utils/chartUtils';

const AnomalyPanel = ({
  anomalies = [],
  data = [],
  columns = [],
  title = '异常数据面板',
  showStatistics = true,
  showDetails = true,
  showExport = true,
  onAnomalyClick,
  onExport,
  ...props
}) => {
  const [selectedAnomaly, setSelectedAnomaly] = useState(null);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [filterSeverity, setFilterSeverity] = useState('all');

  // 统计信息
  const statistics = useMemo(() => {
    const total = anomalies.length;
    const severityCount = {
      high: anomalies.filter(a => a.severity === 'high').length,
      medium: anomalies.filter(a => a.severity === 'medium').length,
      low: anomalies.filter(a => a.severity === 'low').length
    };

    const severityRate = {
      high: total > 0 ? (severityCount.high / total * 100).toFixed(1) : 0,
      medium: total > 0 ? (severityCount.medium / total * 100).toFixed(1) : 0,
      low: total > 0 ? (severityCount.low / total * 100).toFixed(1) : 0
    };

    return {
      total,
      severityCount,
      severityRate,
      dataPoints: data.length,
      anomalyRate: data.length > 0 ? (total / data.length * 100).toFixed(2) : 0
    };
  }, [anomalies, data]);

  // 过滤异常数据
  const filteredAnomalies = useMemo(() => {
    if (filterSeverity === 'all') return anomalies;
    return anomalies.filter(a => a.severity === filterSeverity);
  }, [anomalies, filterSeverity]);

  // 严重程度配置
  const severityConfig = {
    high: {
      color: PHARMA_COLORS.danger,
      icon: <ExclamationCircleOutlined />,
      label: '高风险',
      description: '需要立即处理的严重异常'
    },
    medium: {
      color: PHARMA_COLORS.warning,
      icon: <WarningOutlined />,
      label: '中风险',
      description: '需要关注的异常数据'
    },
    low: {
      color: PHARMA_COLORS.info,
      icon: <InfoCircleOutlined />,
      label: '低风险',
      description: '轻微异常，建议监控'
    }
  };

  // 查看异常详情
  const viewAnomalyDetails = (anomaly) => {
    setSelectedAnomaly(anomaly);
    setDetailsVisible(true);
    if (onAnomalyClick) {
      onAnomalyClick(anomaly);
    }
  };

  // 导出异常数据
  const exportAnomalies = () => {
    if (onExport) {
      onExport(filteredAnomalies);
    } else {
      // 默认导出为CSV
      const csvContent = [
        ['索引', '数值', '严重程度', 'Z分数', '列名'].join(','),
        ...filteredAnomalies.map(anomaly => [
          anomaly.index,
          anomaly.value,
          anomaly.severity,
          anomaly.zScore?.toFixed(3) || '',
          anomaly.column || ''
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `anomalies_${new Date().getTime()}.csv`;
      link.click();
    }
  };

  // 渲染统计卡片
  const renderStatistics = () => {
    if (!showStatistics) return null;

    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="总异常数"
              value={statistics.total}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: statistics.total > 0 ? PHARMA_COLORS.danger : PHARMA_COLORS.success }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="异常率"
              value={`${statistics.anomalyRate}%`}
              precision={2}
              valueStyle={{ color: parseFloat(statistics.anomalyRate) > 5 ? PHARMA_COLORS.danger : PHARMA_COLORS.success }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="高风险"
              value={statistics.severityCount.high}
              suffix={`(${statistics.severityRate.high}%)`}
              valueStyle={{ color: PHARMA_COLORS.danger }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="中风险"
              value={statistics.severityCount.medium}
              suffix={`(${statistics.severityRate.medium}%)`}
              valueStyle={{ color: PHARMA_COLORS.warning }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染工具栏
  const renderToolbar = () => (
    <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <Space>
        <Button
          type={filterSeverity === 'all' ? 'primary' : 'default'}
          size="small"
          onClick={() => setFilterSeverity('all')}
        >
          全部 ({statistics.total})
        </Button>
        <Button
          type={filterSeverity === 'high' ? 'primary' : 'default'}
          size="small"
          danger={filterSeverity === 'high'}
          onClick={() => setFilterSeverity('high')}
        >
          高风险 ({statistics.severityCount.high})
        </Button>
        <Button
          type={filterSeverity === 'medium' ? 'primary' : 'default'}
          size="small"
          onClick={() => setFilterSeverity('medium')}
        >
          中风险 ({statistics.severityCount.medium})
        </Button>
        <Button
          type={filterSeverity === 'low' ? 'primary' : 'default'}
          size="small"
          onClick={() => setFilterSeverity('low')}
        >
          低风险 ({statistics.severityCount.low})
        </Button>
      </Space>

      <Space>
        {showExport && (
          <Button
            icon={<DownloadOutlined />}
            size="small"
            onClick={exportAnomalies}
            disabled={filteredAnomalies.length === 0}
          >
            导出
          </Button>
        )}
      </Space>
    </div>
  );

  // 渲染异常列表
  const renderAnomalyList = () => {
    if (filteredAnomalies.length === 0) {
      return (
        <Alert
          message="暂无异常数据"
          description="当前筛选条件下没有发现异常数据"
          type="success"
          showIcon
        />
      );
    }

    return (
      <List
        size="small"
        dataSource={filteredAnomalies}
        renderItem={(anomaly, index) => {
          const config = severityConfig[anomaly.severity];
          return (
            <motion.div
              key={`${anomaly.index}-${index}`}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <List.Item
                actions={[
                  <Tooltip title="查看详情">
                    <Button
                      type="text"
                      size="small"
                      icon={<EyeOutlined />}
                      onClick={() => viewAnomalyDetails(anomaly)}
                    />
                  </Tooltip>
                ]}
                style={{
                  borderLeft: `4px solid ${config.color}`,
                  paddingLeft: '12px',
                  marginBottom: '8px',
                  backgroundColor: '#fafafa'
                }}
              >
                <List.Item.Meta
                  avatar={
                    <Tag color={config.color} icon={config.icon}>
                      {config.label}
                    </Tag>
                  }
                  title={
                    <Space>
                      <span>索引: {anomaly.index}</span>
                      <span>数值: {formatNumber(anomaly.value, 'decimal', 3)}</span>
                      {anomaly.column && <span>列: {anomaly.column}</span>}
                    </Space>
                  }
                  description={
                    <Space>
                      {anomaly.zScore && (
                        <span>Z分数: {anomaly.zScore.toFixed(3)}</span>
                      )}
                      <span style={{ color: '#666' }}>{config.description}</span>
                    </Space>
                  }
                />
              </List.Item>
            </motion.div>
          );
        }}
      />
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card title={title} {...props}>
        {renderStatistics()}
        {renderToolbar()}
        {renderAnomalyList()}

        {/* 异常详情模态框 */}
        <Modal
          title="异常数据详情"
          open={detailsVisible}
          onCancel={() => setDetailsVisible(false)}
          footer={[
            <Button key="close" onClick={() => setDetailsVisible(false)}>
              关闭
            </Button>
          ]}
          width={600}
        >
          {selectedAnomaly && (
            <Descriptions column={2} bordered size="small">
              <Descriptions.Item label="数据索引">
                {selectedAnomaly.index}
              </Descriptions.Item>
              <Descriptions.Item label="异常数值">
                {formatNumber(selectedAnomaly.value, 'decimal', 6)}
              </Descriptions.Item>
              <Descriptions.Item label="严重程度">
                <Tag color={severityConfig[selectedAnomaly.severity].color}>
                  {severityConfig[selectedAnomaly.severity].label}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="列名">
                {selectedAnomaly.column || '--'}
              </Descriptions.Item>
              {selectedAnomaly.zScore && (
                <Descriptions.Item label="Z分数">
                  {selectedAnomaly.zScore.toFixed(6)}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="检测时间">
                {new Date().toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="风险评估" span={2}>
                <div>
                  <Progress
                    percent={
                      selectedAnomaly.severity === 'high' ? 90 :
                      selectedAnomaly.severity === 'medium' ? 60 : 30
                    }
                    status={
                      selectedAnomaly.severity === 'high' ? 'exception' :
                      selectedAnomaly.severity === 'medium' ? 'active' : 'success'
                    }
                    size="small"
                  />
                  <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                    {severityConfig[selectedAnomaly.severity].description}
                  </div>
                </div>
              </Descriptions.Item>
            </Descriptions>
          )}
        </Modal>
      </Card>
    </motion.div>
  );
};

export default AnomalyPanel;
