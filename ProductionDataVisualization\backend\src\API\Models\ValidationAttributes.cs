using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace API.Models.ValidationAttributes
{
    /// <summary>
    /// 强密码验证特性
    /// </summary>
    public class StrongPasswordAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value is not string password)
                return false;

            // 至少8位，包含大小写字母、数字和特殊字符
            var regex = new Regex(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$");
            return regex.IsMatch(password);
        }

        public override string FormatErrorMessage(string name)
        {
            return $"{name}必须至少8位，包含大小写字母、数字和特殊字符";
        }
    }

    /// <summary>
    /// 用户名验证特性
    /// </summary>
    public class UsernameAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value is not string username)
                return false;

            // 3-20位，只能包含字母、数字、下划线
            var regex = new Regex(@"^[a-zA-Z0-9_]{3,20}$");
            return regex.IsMatch(username);
        }

        public override string FormatErrorMessage(string name)
        {
            return $"{name}必须是3-20位字符，只能包含字母、数字、下划线";
        }
    }

    /// <summary>
    /// 中文姓名验证特性
    /// </summary>
    public class ChineseNameAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value is not string name)
                return false;

            // 2-10位中文字符，可包含·
            var regex = new Regex(@"^[\u4e00-\u9fa5·]{2,10}$");
            return regex.IsMatch(name);
        }

        public override string FormatErrorMessage(string name)
        {
            return $"{name}必须是2-10位中文字符";
        }
    }

    /// <summary>
    /// 手机号验证特性
    /// </summary>
    public class PhoneNumberAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value is not string phone)
                return false;

            // 中国大陆手机号格式
            var regex = new Regex(@"^1[3-9]\d{9}$");
            return regex.IsMatch(phone);
        }

        public override string FormatErrorMessage(string name)
        {
            return $"{name}格式不正确";
        }
    }

    /// <summary>
    /// GUID验证特性
    /// </summary>
    public class GuidAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value is null)
                return true; // 允许为空，由Required特性处理

            if (value is string str)
                return Guid.TryParse(str, out _);

            if (value is Guid guid)
                return guid != Guid.Empty;

            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return $"{name}格式不正确";
        }
    }
}
