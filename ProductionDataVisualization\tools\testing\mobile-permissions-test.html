<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端权限测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 100%;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #fff;
            text-align: center;
            margin: 10px 0;
        }
        .test-section {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            width: 100%;
            max-width: 300px;
            transition: background 0.3s;
        }
        button:hover {
            background: #45a049;
        }
        button.warning {
            background: #FF9800;
        }
        button.danger {
            background: #F44336;
        }
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
        }
        .success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid #4CAF50;
        }
        .error {
            background: rgba(244, 67, 54, 0.3);
            border: 1px solid #F44336;
        }
        .info {
            background: rgba(33, 150, 243, 0.3);
            border: 1px solid #2196F3;
        }
        .device-info {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }
            h1 {
                font-size: 24px;
            }
            h2 {
                font-size: 20px;
            }
            button {
                padding: 15px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 移动端权限测试</h1>
        <p style="text-align: center;">测试移动设备的权限和功能</p>
        
        <div class="test-section">
            <h2>📱 设备信息</h2>
            <div id="device-info" class="device-info"></div>
        </div>
        
        <div class="test-section">
            <h2>🔐 权限测试</h2>
            <button onclick="testGeolocation()">测试地理位置权限</button>
            <button onclick="testCamera()">测试摄像头权限</button>
            <button onclick="testNotification()">测试通知权限</button>
            <button onclick="testDeviceOrientation()">测试设备方向</button>
        </div>
        
        <div class="test-section">
            <h2>🌐 网络测试</h2>
            <button onclick="testNetworkConnection()">测试网络连接</button>
            <button onclick="testAPIConnection()">测试API连接</button>
        </div>
        
        <div class="test-section">
            <h2>📊 测试结果</h2>
            <div id="test-results"></div>
            <button onclick="clearResults()" class="danger">清空结果</button>
        </div>
    </div>

    <script>
        function showResult(title, status, message) {
            const resultsDiv = document.getElementById('test-results');
            const statusClass = status === 'success' ? 'success' : 
                               status === 'error' ? 'error' : 'info';
            const statusIcon = status === 'success' ? '✅' : 
                              status === 'error' ? '❌' : 'ℹ️';
            
            const resultHtml = `
                <div class="result ${statusClass}">
                    ${statusIcon} <strong>${title}</strong><br>
                    ${message}
                </div>
            `;
            
            resultsDiv.innerHTML += resultHtml;
        }

        function updateDeviceInfo() {
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                screenWidth: screen.width,
                screenHeight: screen.height,
                windowWidth: window.innerWidth,
                windowHeight: window.innerHeight,
                devicePixelRatio: window.devicePixelRatio || 1,
                touchSupport: 'ontouchstart' in window,
                orientation: screen.orientation ? screen.orientation.type : 'unknown'
            };
            
            document.getElementById('device-info').innerHTML = `
                <strong>设备信息:</strong><br>
                用户代理: ${info.userAgent}<br>
                平台: ${info.platform}<br>
                语言: ${info.language}<br>
                Cookie启用: ${info.cookieEnabled}<br>
                在线状态: ${info.onLine}<br>
                屏幕尺寸: ${info.screenWidth}x${info.screenHeight}<br>
                窗口尺寸: ${info.windowWidth}x${info.windowHeight}<br>
                像素比: ${info.devicePixelRatio}<br>
                触摸支持: ${info.touchSupport}<br>
                屏幕方向: ${info.orientation}
            `;
        }

        async function testGeolocation() {
            if (!navigator.geolocation) {
                showResult('地理位置测试', 'error', '此设备不支持地理位置API');
                return;
            }
            
            showResult('地理位置测试', 'info', '正在请求地理位置权限...');
            
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    showResult('地理位置测试', 'success', 
                        `权限已授予<br>纬度: ${position.coords.latitude}<br>经度: ${position.coords.longitude}<br>精度: ${position.coords.accuracy}米`);
                },
                (error) => {
                    let errorMsg = '';
                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMsg = '用户拒绝了地理位置请求';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMsg = '位置信息不可用';
                            break;
                        case error.TIMEOUT:
                            errorMsg = '请求超时';
                            break;
                        default:
                            errorMsg = '未知错误';
                            break;
                    }
                    showResult('地理位置测试', 'error', `权限被拒绝或出错: ${errorMsg}`);
                }
            );
        }

        async function testCamera() {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                showResult('摄像头测试', 'error', '此设备不支持摄像头API');
                return;
            }
            
            showResult('摄像头测试', 'info', '正在请求摄像头权限...');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                showResult('摄像头测试', 'success', '摄像头权限已授予');
                // 停止摄像头流
                stream.getTracks().forEach(track => track.stop());
            } catch (error) {
                showResult('摄像头测试', 'error', `权限被拒绝或出错: ${error.message}`);
            }
        }

        async function testNotification() {
            if (!('Notification' in window)) {
                showResult('通知测试', 'error', '此设备不支持通知API');
                return;
            }
            
            if (Notification.permission === 'granted') {
                showResult('通知测试', 'success', '通知权限已授予');
                new Notification('测试通知', { body: '这是一个测试通知' });
            } else if (Notification.permission === 'denied') {
                showResult('通知测试', 'error', '通知权限被拒绝');
            } else {
                showResult('通知测试', 'info', '正在请求通知权限...');
                const permission = await Notification.requestPermission();
                if (permission === 'granted') {
                    showResult('通知测试', 'success', '通知权限已授予');
                    new Notification('测试通知', { body: '这是一个测试通知' });
                } else {
                    showResult('通知测试', 'error', '通知权限被拒绝');
                }
            }
        }

        function testDeviceOrientation() {
            if (!window.DeviceOrientationEvent) {
                showResult('设备方向测试', 'error', '此设备不支持设备方向API');
                return;
            }
            
            showResult('设备方向测试', 'info', '正在监听设备方向变化...');
            
            const handleOrientation = (event) => {
                showResult('设备方向测试', 'success', 
                    `设备方向数据:<br>Alpha: ${event.alpha?.toFixed(2) || 'N/A'}<br>Beta: ${event.beta?.toFixed(2) || 'N/A'}<br>Gamma: ${event.gamma?.toFixed(2) || 'N/A'}`);
                window.removeEventListener('deviceorientation', handleOrientation);
            };
            
            window.addEventListener('deviceorientation', handleOrientation);
            
            // 5秒后移除监听器
            setTimeout(() => {
                window.removeEventListener('deviceorientation', handleOrientation);
                showResult('设备方向测试', 'info', '设备方向监听已停止');
            }, 5000);
        }

        function testNetworkConnection() {
            const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
            
            if (connection) {
                showResult('网络连接测试', 'success', 
                    `网络信息:<br>类型: ${connection.effectiveType || 'unknown'}<br>下行速度: ${connection.downlink || 'unknown'} Mbps<br>RTT: ${connection.rtt || 'unknown'} ms`);
            } else {
                showResult('网络连接测试', 'info', '网络连接API不可用，但网络状态: ' + (navigator.onLine ? '在线' : '离线'));
            }
        }

        async function testAPIConnection() {
            showResult('API连接测试', 'info', '正在测试API连接...');
            
            try {
                const host = window.location.hostname;
                const apiUrl = host === 'localhost' || host === '127.0.0.1' ? 
                    'http://localhost:5000' : `http://${host}:5000`;
                
                const response = await fetch(`${apiUrl}/api/simple-auth/users`);
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('API连接测试', 'success', 
                        `API连接成功<br>响应状态: ${response.status}<br>用户数量: ${data.items ? data.items.length : 0}`);
                } else {
                    showResult('API连接测试', 'error', `API连接失败: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                showResult('API连接测试', 'error', `API连接异常: ${error.message}`);
            }
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // 页面加载时初始化
        window.onload = function() {
            updateDeviceInfo();
            showResult('系统初始化', 'success', '移动端权限测试工具已加载');
        };
    </script>
</body>
</html>
