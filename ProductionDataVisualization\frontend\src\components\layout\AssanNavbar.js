import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Typography } from 'antd';
import { 
  MenuOutlined, 
  UserOutlined, 
  SettingOutlined, 
  LogoutOutlined,
  DashboardOutlined,
  BarChartOutlined,
  FileTextOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import authService from '../../services/authService';

const { Header } = Layout;
const { Text } = Typography;

const AssanNavbar = ({ collapsed, onToggle }) => {
  const [scrolled, setScrolled] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      setScrolled(isScrolled);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLogout = async () => {
    try {
      await authService.logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile')
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => navigate('/settings')
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout
    }
  ];

  const navigationItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板'
    },
    {
      key: '/analytics',
      icon: <BarChartOutlined />,
      label: '数据分析'
    },
    {
      key: '/reports',
      icon: <FileTextOutlined />,
      label: '报告'
    },
    {
      key: '/team',
      icon: <TeamOutlined />,
      label: '团队'
    }
  ];

  const currentPath = location.pathname;

  return (
    <>
      <Header className={`assan-navbar ${scrolled ? 'scrolled' : ''}`}>
        <div className="navbar-content">
          {/* 左侧区域 */}
          <div className="navbar-left">
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={onToggle}
              className="menu-toggle"
            />
            
            <motion.div 
              className="brand-section"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="brand-logo">
                <div className="logo-icon">
                  <BarChartOutlined />
                </div>
              </div>
              <div className="brand-text">
                <Text className="brand-title">DataViz</Text>
                <Text className="brand-subtitle">Pro</Text>
              </div>
            </motion.div>
          </div>

          {/* 中间导航 */}
          <div className="navbar-center">
            <Menu
              mode="horizontal"
              selectedKeys={[currentPath]}
              className="main-navigation"
              items={navigationItems.map(item => ({
                ...item,
                onClick: () => navigate(item.key)
              }))}
            />
          </div>

          {/* 右侧区域 */}
          <div className="navbar-right">
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
                trigger={['click']}
                overlayClassName="user-dropdown"
              >
                <div className="user-profile">
                  <Avatar 
                    size={40} 
                    icon={<UserOutlined />}
                    className="user-avatar"
                  />
                  <div className="user-info">
                    <Text className="user-name">管理员</Text>
                    <Text className="user-role">系统管理员</Text>
                  </div>
                </div>
              </Dropdown>
            </motion.div>
          </div>
        </div>
      </Header>

      {/* Assan风格样式 */}
      <style jsx="true">{`
        .assan-navbar {
          background: rgba(255, 255, 255, 0.95) !important;
          backdrop-filter: blur(20px) !important;
          border-bottom: 1px solid rgba(0, 0, 0, 0.06) !important;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02) !important;
          padding: 0 24px !important;
          height: 72px !important;
          line-height: 72px !important;
          position: fixed !important;
          top: 0 !important;
          left: 0 !important;
          right: 0 !important;
          z-index: 1000 !important;
          transition: all 0.3s ease !important;
        }

        .assan-navbar.scrolled {
          background: rgba(255, 255, 255, 0.98) !important;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
          border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
        }

        .navbar-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 100%;
          max-width: 1400px;
          margin: 0 auto;
        }

        .navbar-left {
          display: flex;
          align-items: center;
          gap: 16px;
        }

        .menu-toggle {
          width: 40px !important;
          height: 40px !important;
          border-radius: 10px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          color: #4a5568 !important;
          transition: all 0.3s ease !important;
        }

        .menu-toggle:hover {
          background: rgba(102, 126, 234, 0.1) !important;
          color: #667eea !important;
        }

        .brand-section {
          display: flex;
          align-items: center;
          gap: 12px;
          cursor: pointer;
        }

        .brand-logo {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
        }

        .logo-icon {
          font-size: 24px;
          color: white;
        }

        .brand-text {
          display: flex;
          flex-direction: column;
          line-height: 1.2;
        }

        .brand-title {
          font-size: 20px !important;
          font-weight: 700 !important;
          color: #1a202c !important;
          margin: 0 !important;
        }

        .brand-subtitle {
          font-size: 12px !important;
          color: #667eea !important;
          font-weight: 600 !important;
          margin: 0 !important;
        }

        .navbar-center {
          flex: 1;
          display: flex;
          justify-content: center;
          max-width: 600px;
        }

        .main-navigation {
          background: transparent !important;
          border-bottom: none !important;
          line-height: 72px !important;
        }

        .main-navigation .ant-menu-item {
          border-radius: 12px !important;
          margin: 0 8px !important;
          padding: 0 20px !important;
          height: 44px !important;
          line-height: 44px !important;
          color: #4a5568 !important;
          font-weight: 500 !important;
          transition: all 0.3s ease !important;
        }

        .main-navigation .ant-menu-item:hover {
          background: rgba(102, 126, 234, 0.1) !important;
          color: #667eea !important;
        }

        .main-navigation .ant-menu-item-selected {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          color: white !important;
          box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3) !important;
        }

        .main-navigation .ant-menu-item-selected:hover {
          background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
          color: white !important;
        }

        .navbar-right {
          display: flex;
          align-items: center;
        }

        .user-profile {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 8px 16px;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .user-profile:hover {
          background: rgba(102, 126, 234, 0.1);
        }

        .user-avatar {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
        }

        .user-info {
          display: flex;
          flex-direction: column;
          line-height: 1.2;
        }

        .user-name {
          font-size: 14px !important;
          font-weight: 600 !important;
          color: #1a202c !important;
          margin: 0 !important;
        }

        .user-role {
          font-size: 12px !important;
          color: #718096 !important;
          margin: 0 !important;
        }

        /* 用户下拉菜单样式 */
        .user-dropdown .ant-dropdown-menu {
          background: rgba(255, 255, 255, 0.95) !important;
          backdrop-filter: blur(20px) !important;
          border-radius: 12px !important;
          border: 1px solid rgba(255, 255, 255, 0.8) !important;
          box-shadow: 0 16px 32px rgba(0, 0, 0, 0.1) !important;
          padding: 8px !important;
          min-width: 200px !important;
        }

        .user-dropdown .ant-dropdown-menu-item {
          border-radius: 8px !important;
          margin: 2px 0 !important;
          padding: 12px 16px !important;
          color: #4a5568 !important;
          font-weight: 500 !important;
          transition: all 0.3s ease !important;
        }

        .user-dropdown .ant-dropdown-menu-item:hover {
          background: rgba(102, 126, 234, 0.1) !important;
          color: #667eea !important;
        }

        .user-dropdown .ant-dropdown-menu-item-divider {
          background: rgba(0, 0, 0, 0.06) !important;
          margin: 8px 0 !important;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .navbar-center {
            display: none;
          }
          
          .brand-text {
            display: none;
          }
          
          .user-info {
            display: none;
          }
          
          .assan-navbar {
            padding: 0 16px !important;
          }
        }

        @media (max-width: 480px) {
          .brand-logo {
            width: 40px;
            height: 40px;
          }
          
          .logo-icon {
            font-size: 20px;
          }
          
          .user-avatar {
            width: 36px !important;
            height: 36px !important;
          }
        }
      `}</style>
    </>
  );
};

export default AssanNavbar;
