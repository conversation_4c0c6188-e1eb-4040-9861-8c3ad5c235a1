using System.Text;
using API.Auth;
using API.Extensions;
using API.Middleware;
// using Infrastructure;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.EntityFrameworkCore;
using Infrastructure.Persistence;

var builder = WebApplication.CreateBuilder(args);

// 配置Serilog日志
builder.ConfigureSerilog();

// 直接配置SQL Server数据库
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(connectionString));

// 添加基础设施层服务 - 暂时注释掉复杂的依赖
// builder.Services.AddInfrastructure(builder.Configuration);

// 配置JWT认证
var jwtSettingsSection = builder.Configuration.GetSection("JwtSettings");
builder.Services.Configure<JwtSettings>(jwtSettingsSection);

// 获取JWT设置
var jwtSettings = jwtSettingsSection.Get<JwtSettings>();
var key = Encoding.ASCII.GetBytes(jwtSettings?.SecretKey ?? "defaultsecretkey123456789012345678901234");

// 添加认证
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = jwtSettings?.Issuer,
        ValidateAudience = true,
        ValidAudience = jwtSettings?.Audience,
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
});

// 添加JWT服务
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<IPasswordService, PasswordService>();

// 添加简单用户服务
builder.Services.AddSingleton<API.Services.SimpleUserService>();

// 添加后台服务
builder.Services.AddHostedService<RateLimitCleanupService>();

// 添加CORS服务
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

// 注册服务
builder.Services.AddScoped<API.Services.SimpleUserService>();

// 添加控制器
builder.Services.AddControllers();

// 添加Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

// 全局异常处理（必须在最前面）
app.UseGlobalExceptionHandling();

// 性能监控
app.UsePerformanceMonitoring();

// 请求日志
app.UseRequestLogging();

// API限流
app.UseRateLimiting(new RateLimitOptions
{
    WindowMinutes = 1,
    MaxRequests = 100
});

// 在开发环境中启用Swagger
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// 在UseRouting之前使用CORS
app.UseCors();

app.UseRouting();

// 添加认证和授权中间件
app.UseAuthentication();
app.UseAuthorization();

// 添加一个简单的测试终结点
app.MapGet("/", () => "API服务正在运行");
app.MapGet("/health", () => "Healthy");

app.MapControllers();

app.Run();