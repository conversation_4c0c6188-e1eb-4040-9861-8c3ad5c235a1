# 数据迁移到SQL Server

Write-Host "=== 数据迁移到SQL Server ===" -ForegroundColor Yellow

# 配置
$sqliteDbPath = "SimpleBackend\bin\Release\net8.0\win-x64\publish\ProductionDataVisualization.db"
$sqlServerConnection = "Server=localhost\SQLEXPRESS;Database=ProductionDataVisualizationDb;Trusted_Connection=true;TrustServerCertificate=true;"

Write-Host "`n[1] 检查SQLite数据库..." -ForegroundColor Cyan

if (-not (Test-Path $sqliteDbPath)) {
    Write-Host "❌ SQLite数据库文件不存在: $sqliteDbPath" -ForegroundColor Red
    exit 1
}

Write-Host "✅ SQLite数据库文件存在" -ForegroundColor Green

Write-Host "`n[2] 连接到SQL Server..." -ForegroundColor Cyan

try {
    $sqlConnection = New-Object System.Data.SqlClient.SqlConnection($sqlServerConnection)
    $sqlConnection.Open()
    Write-Host "✅ SQL Server连接成功" -ForegroundColor Green
}
catch {
    Write-Host "❌ SQL Server连接失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n[3] 创建SQL Server表结构..." -ForegroundColor Cyan

$createTablesSQL = @"
-- 用户表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        Id NVARCHAR(50) PRIMARY KEY,
        Username NVARCHAR(50) UNIQUE NOT NULL,
        Email NVARCHAR(100) UNIQUE NOT NULL,
        Password NVARCHAR(255) NOT NULL,
        FullName NVARCHAR(100) NOT NULL,
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        LastLoginTime DATETIME2 NULL,
        Role NVARCHAR(50) DEFAULT 'user'
    )
    PRINT '用户表创建成功'
END

-- 导入任务表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ImportTasks' AND xtype='U')
BEGIN
    CREATE TABLE ImportTasks (
        Id NVARCHAR(50) PRIMARY KEY,
        FileName NVARCHAR(255) NOT NULL,
        FileSize BIGINT NOT NULL,
        TotalRows INT DEFAULT 0,
        ProcessedRows INT DEFAULT 0,
        Status NVARCHAR(50) DEFAULT 'Pending',
        Progress DECIMAL(5,2) DEFAULT 0,
        CreatedBy NVARCHAR(255) NOT NULL,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        StartedAt DATETIME2 NULL,
        CompletedAt DATETIME2 NULL,
        ErrorMessage NVARCHAR(MAX) NULL,
        ConfigData NVARCHAR(MAX) NULL
    )
    PRINT '导入任务表创建成功'
END

-- 文件表映射
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FileTableMappings' AND xtype='U')
BEGIN
    CREATE TABLE FileTableMappings (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        FileName NVARCHAR(255) NOT NULL UNIQUE,
        TableName NVARCHAR(255) NOT NULL UNIQUE,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        LastImportAt DATETIME2 NULL,
        TotalRows INT DEFAULT 0,
        IsActive BIT DEFAULT 1
    )
    PRINT '文件表映射创建成功'
END

-- 系统配置表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SystemSettings' AND xtype='U')
BEGIN
    CREATE TABLE SystemSettings (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        SettingKey NVARCHAR(100) NOT NULL UNIQUE,
        SettingValue NVARCHAR(MAX) NOT NULL,
        Description NVARCHAR(500) NULL,
        Category NVARCHAR(50) DEFAULT 'General',
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        UpdatedAt DATETIME2 DEFAULT GETDATE()
    )
    PRINT '系统配置表创建成功'
END

-- 角色表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Roles' AND xtype='U')
BEGIN
    CREATE TABLE Roles (
        Id NVARCHAR(50) PRIMARY KEY,
        RoleName NVARCHAR(50) NOT NULL UNIQUE,
        Description NVARCHAR(500) NULL,
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETDATE()
    )
    PRINT '角色表创建成功'
END

-- 用户角色关联表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserRoles' AND xtype='U')
BEGIN
    CREATE TABLE UserRoles (
        UserId NVARCHAR(50) NOT NULL,
        RoleId NVARCHAR(50) NOT NULL,
        AssignedAt DATETIME2 DEFAULT GETDATE(),
        PRIMARY KEY (UserId, RoleId),
        FOREIGN KEY (UserId) REFERENCES Users(Id),
        FOREIGN KEY (RoleId) REFERENCES Roles(Id)
    )
    PRINT '用户角色关联表创建成功'
END
"@

try {
    $command = $sqlConnection.CreateCommand()
    $command.CommandText = $createTablesSQL
    $command.ExecuteNonQuery()
    Write-Host "✅ SQL Server表结构创建成功" -ForegroundColor Green
}
catch {
    Write-Host "❌ 表结构创建失败: $($_.Exception.Message)" -ForegroundColor Red
    $sqlConnection.Close()
    exit 1
}

Write-Host "`n[4] 读取SQLite数据..." -ForegroundColor Cyan

try {
    # 加载SQLite程序集
    Add-Type -Path "System.Data.SQLite.dll" -ErrorAction SilentlyContinue
    
    $sqliteConnection = New-Object System.Data.SQLite.SQLiteConnection("Data Source=$sqliteDbPath")
    $sqliteConnection.Open()
    
    # 读取用户数据
    $sqliteCommand = $sqliteConnection.CreateCommand()
    $sqliteCommand.CommandText = "SELECT * FROM Users"
    $reader = $sqliteCommand.ExecuteReader()
    
    $users = @()
    while ($reader.Read()) {
        $users += @{
            Id = $reader["Id"]
            Username = $reader["Username"]
            Email = $reader["Email"]
            Password = $reader["Password"]
            FullName = $reader["FullName"]
            IsActive = $reader["IsActive"]
            CreatedAt = $reader["CreatedAt"]
            LastLoginTime = $reader["LastLoginTime"]
            Role = $reader["Role"]
        }
    }
    $reader.Close()
    $sqliteConnection.Close()
    
    Write-Host "✅ 从SQLite读取到 $($users.Count) 个用户" -ForegroundColor Green
}
catch {
    Write-Host "⚠️ SQLite数据读取失败，将创建默认数据: $($_.Exception.Message)" -ForegroundColor Yellow
    $users = @()
}

Write-Host "`n[5] 迁移数据到SQL Server..." -ForegroundColor Cyan

# 迁移用户数据
foreach ($user in $users) {
    try {
        $insertCommand = $sqlConnection.CreateCommand()
        $insertCommand.CommandText = @"
            IF NOT EXISTS (SELECT 1 FROM Users WHERE Username = @Username)
            BEGIN
                INSERT INTO Users (Id, Username, Email, Password, FullName, IsActive, CreatedAt, LastLoginTime, Role)
                VALUES (@Id, @Username, @Email, @Password, @FullName, @IsActive, @CreatedAt, @LastLoginTime, @Role)
            END
"@
        $insertCommand.Parameters.AddWithValue("@Id", $user.Id)
        $insertCommand.Parameters.AddWithValue("@Username", $user.Username)
        $insertCommand.Parameters.AddWithValue("@Email", $user.Email)
        $insertCommand.Parameters.AddWithValue("@Password", $user.Password)
        $insertCommand.Parameters.AddWithValue("@FullName", $user.FullName)
        $insertCommand.Parameters.AddWithValue("@IsActive", [bool]$user.IsActive)
        $insertCommand.Parameters.AddWithValue("@CreatedAt", $user.CreatedAt)
        $insertCommand.Parameters.AddWithValue("@LastLoginTime", [System.DBNull]::Value)
        $insertCommand.Parameters.AddWithValue("@Role", $user.Role)
        
        $insertCommand.ExecuteNonQuery()
        Write-Host "  ✅ 用户迁移成功: $($user.Username)" -ForegroundColor Green
    }
    catch {
        Write-Host "  ❌ 用户迁移失败: $($user.Username) - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n[6] 创建默认数据..." -ForegroundColor Cyan

# 确保有管理员用户
$adminCheckCommand = $sqlConnection.CreateCommand()
$adminCheckCommand.CommandText = "SELECT COUNT(*) FROM Users WHERE Username = 'admin'"
$adminExists = $adminCheckCommand.ExecuteScalar()

if ($adminExists -eq 0) {
    $createAdminCommand = $sqlConnection.CreateCommand()
    $createAdminCommand.CommandText = @"
        INSERT INTO Users (Id, Username, Email, Password, FullName, Role)
        VALUES (NEWID(), 'admin', '<EMAIL>', 'admin123', '系统管理员', 'admin')
"@
    $createAdminCommand.ExecuteNonQuery()
    Write-Host "✅ 默认管理员用户创建成功" -ForegroundColor Green
}

$sqlConnection.Close()

Write-Host "`n=== 数据迁移完成 ===" -ForegroundColor Green
Write-Host "✅ 所有用户数据已迁移到SQL Server" -ForegroundColor Green
Write-Host "✅ 数据库: ProductionDataVisualizationDb" -ForegroundColor Green
Write-Host "✅ 现在可以启动SQL Server版本的后端" -ForegroundColor Green
