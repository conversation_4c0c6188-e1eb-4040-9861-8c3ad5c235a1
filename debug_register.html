<!DOCTYPE html>
<html>
<head>
    <title>用户注册调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        input { padding: 8px; margin: 5px; border: 1px solid #ccc; border-radius: 3px; width: 200px; }
        label { display: inline-block; width: 100px; }
    </style>
</head>
<body>
    <h1>🔧 用户注册调试工具</h1>
    
    <div class="test-result info">
        <strong>后端API:</strong> http://localhost:5000
    </div>
    
    <h3>📋 注册表单</h3>
    <div>
        <label>用户名:</label>
        <input type="text" id="username" value="" placeholder="输入用户名">
    </div>
    <div>
        <label>邮箱:</label>
        <input type="email" id="email" value="" placeholder="输入邮箱">
    </div>
    <div>
        <label>密码:</label>
        <input type="password" id="password" value="password123" placeholder="输入密码">
    </div>
    <div>
        <label>全名:</label>
        <input type="text" id="fullName" value="测试用户" placeholder="输入全名">
    </div>
    
    <button onclick="generateRandomUser()">生成随机用户</button>
    <button onclick="testRegister()">测试注册</button>
    <button onclick="checkExistingUsers()">查看现有用户</button>
    <button onclick="clearResults()">清除结果</button>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:5000';
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function generateRandomUser() {
            const timestamp = Date.now();
            document.getElementById('username').value = `user_${timestamp}`;
            document.getElementById('email').value = `user_${timestamp}@example.com`;
            document.getElementById('password').value = 'password123';
            document.getElementById('fullName').value = `测试用户_${timestamp}`;
            addResult('✅ 已生成随机用户信息', 'success');
        }
        
        async function checkExistingUsers() {
            addResult('🔍 查看现有用户...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/simple-auth/users`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    },
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ 现有用户列表:<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    addResult(`❌ 获取用户列表失败: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 获取用户列表错误: ${error.message}`, 'error');
            }
        }
        
        async function testRegister() {
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const fullName = document.getElementById('fullName').value;
            
            if (!username || !email || !password || !fullName) {
                addResult('❌ 请填写所有字段', 'error');
                return;
            }
            
            const userData = {
                username: username,
                email: email,
                password: password,
                fullName: fullName
            };
            
            addResult(`🔍 测试用户注册...<br>发送数据: <pre>${JSON.stringify(userData, null, 2)}</pre>`, 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/simple-auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    mode: 'cors',
                    body: JSON.stringify(userData)
                });
                
                addResult(`📡 响应状态: ${response.status} ${response.statusText}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ 用户注册成功!<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                } else {
                    const errorData = await response.json().catch(() => ({ message: '无法解析错误响应' }));
                    addResult(`❌ 用户注册失败: HTTP ${response.status}<br>错误信息: <pre>${JSON.stringify(errorData, null, 2)}</pre>`, 'error');
                }
            } catch (error) {
                addResult(`❌ 网络错误: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动生成随机用户
        window.onload = function() {
            addResult('🚀 页面加载完成', 'info');
            generateRandomUser();
            checkExistingUsers();
        };
    </script>
</body>
</html>
