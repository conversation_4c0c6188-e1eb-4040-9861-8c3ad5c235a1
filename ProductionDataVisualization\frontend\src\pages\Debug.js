import React, { useState, useEffect } from 'react';
import { Button, Typography, Space, Card, Divider, Alert, Table } from 'antd';
import { jwtDecode } from 'jwt-decode';

const { Title, Text, Paragraph } = Typography;

const Debug = () => {
  const [jwtStatus, setJwtStatus] = useState({ success: false, message: '未测试' });
  const [renderTest, setRenderTest] = useState({ success: true, message: '未测试' });
  const [objectTest, setObjectTest] = useState(null);
  const [errorLog, setErrorLog] = useState([]);
  
  // 记录错误
  const logError = (source, message, details = null) => {
    setErrorLog(prev => [...prev, {
      key: Date.now(),
      time: new Date().toLocaleTimeString(),
      source,
      message,
      details: details ? JSON.stringify(details) : '无'
    }]);
  };
  
  // 测试jwt-decode
  const testJwtDecode = () => {
    try {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkFkbWluIFVzZXIiLCJpYXQiOjE1MTYyMzkwMjIsImV4cCI6MTkxNjIzOTAyMn0.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
      // 使用命名导入的jwtDecode函数
      const decoded = jwtDecode(mockToken);
      setJwtStatus({ 
        success: true, 
        message: `解码成功: ${JSON.stringify(decoded)}` 
      });
    } catch (error) {
      const errorMessage = `解码失败: ${error.message}`;
      setJwtStatus({ 
        success: false, 
        message: errorMessage
      });
      logError('JWT测试', errorMessage, error);
    }
  };
  
  // 测试对象渲染
  const testObjectRendering = () => {
    try {
      // 创建一个测试对象
      const testObj = { name: 'test', value: 123 };
      
      // 安全地设置对象（不直接渲染）
      setObjectTest(testObj);
      setRenderTest({
        success: true,
        message: '测试对象已创建，但未直接渲染'
      });
    } catch (error) {
      const errorMessage = `渲染测试失败: ${error.message}`;
      setRenderTest({ 
        success: false, 
        message: errorMessage
      });
      logError('对象渲染测试', errorMessage, error);
    }
  };
  
  // 故意触发对象渲染错误
  const triggerRenderError = () => {
    try {
      // 创建一个测试对象
      const testObj = { name: 'error', value: 456 };
      
      // 尝试直接渲染对象（这会导致错误，但我们会捕获它）
      const element = <div>{testObj}</div>;
      
      // 这行代码不会执行，因为上面的代码会抛出错误
      setRenderTest({
        success: false,
        message: '这行不应该被执行'
      });
    } catch (error) {
      const errorMessage = `成功捕获渲染错误: ${error.message}`;
      setRenderTest({ 
        success: true, 
        message: errorMessage
      });
      logError('故意触发渲染错误', errorMessage, error);
    }
  };
  
  // 检查localStorage中的token
  const checkLocalStorage = () => {
    const token = localStorage.getItem('token');
    if (token) {
      try {
        // 使用命名导入的jwtDecode函数
        const decoded = jwtDecode(token);
        alert(`当前token有效，解码结果: ${JSON.stringify(decoded, null, 2)}`);
      } catch (error) {
        const errorMessage = `token解码失败: ${error.message}`;
        alert(errorMessage);
        logError('localStorage检查', errorMessage, error);
      }
    } else {
      alert('localStorage中没有token');
    }
  };
  
  // 错误日志表格列定义
  const columns = [
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
    },
    {
      title: '来源',
      dataIndex: 'source',
      key: 'source',
    },
    {
      title: '错误信息',
      dataIndex: 'message',
      key: 'message',
    },
    {
      title: '详情',
      dataIndex: 'details',
      key: 'details',
      ellipsis: true,
    },
  ];

  return (
    <div style={{ padding: 24, maxWidth: 800, margin: '0 auto' }}>
      <Title level={2}>调试页面</Title>
      <Text>如果你能看到这个页面，说明基本路由和组件渲染正常。</Text>
      
      <Divider />
      
      <Card title="功能测试">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button type="primary" onClick={testJwtDecode}>测试JWT解码</Button>
          {jwtStatus.message !== '未测试' && (
            <Alert 
              message={jwtStatus.message} 
              type={jwtStatus.success ? "success" : "error"} 
              showIcon 
            />
          )}
          
          <Divider />
          
          <Button onClick={testObjectRendering}>测试对象创建</Button>
          <Button danger onClick={triggerRenderError}>触发渲染错误(已捕获)</Button>
          {renderTest.message !== '未测试' && (
            <Alert 
              message={renderTest.message} 
              type={renderTest.success ? "success" : "error"} 
              showIcon 
            />
          )}
          
          {/* 正确渲染对象的方式 */}
          {objectTest && (
            <Card size="small" title="对象属性渲染">
              <p>名称: {objectTest.name}</p>
              <p>值: {objectTest.value}</p>
              <p>JSON字符串: {JSON.stringify(objectTest)}</p>
            </Card>
          )}
          
          <Divider />
          
          <Button onClick={checkLocalStorage}>检查localStorage</Button>
        </Space>
      </Card>
      
      <Divider />
      
      <Card title="错误日志">
        {errorLog.length > 0 ? (
          <Table 
            dataSource={errorLog} 
            columns={columns} 
            pagination={false}
            size="small"
          />
        ) : (
          <Text>暂无错误记录</Text>
        )}
      </Card>
      
      <Divider />
      
      <Card title="系统信息">
        <p><strong>React版本:</strong> {React.version}</p>
        <p><strong>当前URL:</strong> {window.location.href}</p>
        <p><strong>用户代理:</strong> {navigator.userAgent}</p>
        <p><strong>当前时间:</strong> {new Date().toLocaleString()}</p>
        <p><strong>直接测试页面:</strong> <a href="/direct.html" target="_blank">打开direct.html</a></p>
        <p><strong>JWT测试页面:</strong> <a href="/jwt-test.html" target="_blank">打开jwt-test.html</a></p>
        <p><strong>React渲染测试:</strong> <a href="/react-test.html" target="_blank">打开react-test.html</a></p>
      </Card>
    </div>
  );
};

export default Debug; 