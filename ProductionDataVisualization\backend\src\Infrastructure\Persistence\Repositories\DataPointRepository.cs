using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Domain.Common;
using Domain.DataAggregate;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// 数据点仓储实现
    /// </summary>
    public class DataPointRepository : BaseRepository<DataPoint>, IDataPointRepository
    {
        public DataPointRepository(ApplicationDbContext dbContext) : base(dbContext)
        {
        }

        public async Task<IEnumerable<DataPoint>> GetByDataCategoryIdAsync(Guid dataCategoryId)
        {
            return await DbContext.DataPoints
                .Where(dp => dp.DataCategoryId == dataCategoryId)
                .OrderBy(dp => dp.Timestamp)
                .ToListAsync();
        }

        public async Task<IEnumerable<DataPoint>> GetByDataCategoryIdAndTimeRangeAsync(Guid dataCategoryId, DateTime startTime, DateTime endTime)
        {
            return await DbContext.DataPoints
                .Where(dp => dp.DataCategoryId == dataCategoryId && dp.Timestamp >= startTime && dp.Timestamp <= endTime)
                .OrderBy(dp => dp.Timestamp)
                .ToListAsync();
        }

        public async Task<IEnumerable<DataPoint>> GetLatestByDataCategoryIdAsync(Guid dataCategoryId, int count)
        {
            return await DbContext.DataPoints
                .Where(dp => dp.DataCategoryId == dataCategoryId)
                .OrderByDescending(dp => dp.Timestamp)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<DataPoint>> GetByDataSourceIdAsync(Guid dataSourceId)
        {
            return await DbContext.DataPoints
                .Include(dp => dp.DataCategory)
                .Where(dp => dp.DataCategory.DataSourceId == dataSourceId)
                .OrderBy(dp => dp.Timestamp)
                .ToListAsync();
        }

        public async Task AddRangeAsync(IEnumerable<DataPoint> dataPoints)
        {
            await DbContext.DataPoints.AddRangeAsync(dataPoints);
        }
    }
} 