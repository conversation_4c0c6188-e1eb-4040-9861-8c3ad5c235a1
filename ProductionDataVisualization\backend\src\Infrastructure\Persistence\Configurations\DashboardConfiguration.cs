using Domain.VisualizationAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 仪表盘实体配置
    /// </summary>
    public class DashboardConfiguration : IEntityTypeConfiguration<Dashboard>
    {
        public void Configure(EntityTypeBuilder<Dashboard> builder)
        {
            builder.ToTable("Dashboards");

            builder.HasKey(d => d.Id);

            builder.Property(d => d.Title)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(d => d.Description)
                .HasMaxLength(500);

            builder.Property(d => d.CreatedBy)
                .IsRequired();

            builder.Property(d => d.IsPublic)
                .IsRequired();

            builder.Property(d => d.LayoutJson)
                .HasColumnType("nvarchar(max)");

            builder.Property(d => d.CreatedAt)
                .IsRequired();

            builder.Property(d => d.ModifiedAt);

            builder.Property(d => d.CreatedBy)
                .HasMaxLength(50);

            builder.Property(d => d.ModifiedBy)
                .HasMaxLength(50);

            // 索引
            builder.HasIndex(d => d.CreatedBy);
            builder.HasIndex(d => d.IsPublic);
        }
    }
} 