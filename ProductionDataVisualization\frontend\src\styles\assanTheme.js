// Assan风格主题配置
export const assanTheme = {
  // 主色调
  colors: {
    primary: {
      50: '#f0f4ff',
      100: '#e0e7ff',
      200: '#c7d2fe',
      300: '#a5b4fc',
      400: '#818cf8',
      500: '#667eea',
      600: '#5a67d8',
      700: '#4c51bf',
      800: '#434190',
      900: '#3c366b'
    },
    secondary: {
      50: '#faf5ff',
      100: '#f3e8ff',
      200: '#e9d5ff',
      300: '#d8b4fe',
      400: '#c084fc',
      500: '#764ba2',
      600: '#9333ea',
      700: '#7c3aed',
      800: '#6b21a8',
      900: '#581c87'
    },
    gray: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a'
    },
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d'
    },
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f'
    },
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d'
    }
  },

  // 渐变色
  gradients: {
    primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    primaryHover: 'linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%)',
    secondary: 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    card: 'linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%)',
    glass: 'linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%)'
  },

  // 阴影
  shadows: {
    xs: '0 1px 2px rgba(0, 0, 0, 0.05)',
    sm: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px rgba(0, 0, 0, 0.25)',
    card: '0 32px 64px rgba(0, 0, 0, 0.08), 0 16px 32px rgba(0, 0, 0, 0.04), 0 8px 16px rgba(0, 0, 0, 0.02)',
    button: '0 4px 20px rgba(102, 126, 234, 0.3)',
    buttonHover: '0 8px 25px rgba(102, 126, 234, 0.4)',
    navbar: '0 4px 20px rgba(0, 0, 0, 0.08)',
    glass: '0 8px 32px rgba(31, 38, 135, 0.37)'
  },

  // 圆角
  borderRadius: {
    none: '0',
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '16px',
    '2xl': '20px',
    '3xl': '24px',
    full: '9999px'
  },

  // 字体
  typography: {
    fontFamily: {
      sans: [
        '-apple-system',
        'BlinkMacSystemFont',
        'Segoe UI',
        'Roboto',
        'Helvetica Neue',
        'Arial',
        'sans-serif'
      ],
      mono: [
        'SFMono-Regular',
        'Menlo',
        'Monaco',
        'Consolas',
        'Liberation Mono',
        'Courier New',
        'monospace'
      ]
    },
    fontSize: {
      xs: '12px',
      sm: '14px',
      base: '16px',
      lg: '18px',
      xl: '20px',
      '2xl': '24px',
      '3xl': '30px',
      '4xl': '36px',
      '5xl': '48px',
      '6xl': '60px'
    },
    fontWeight: {
      thin: '100',
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
      black: '900'
    },
    lineHeight: {
      none: '1',
      tight: '1.25',
      snug: '1.375',
      normal: '1.5',
      relaxed: '1.625',
      loose: '2'
    }
  },

  // 间距
  spacing: {
    0: '0',
    1: '4px',
    2: '8px',
    3: '12px',
    4: '16px',
    5: '20px',
    6: '24px',
    7: '28px',
    8: '32px',
    9: '36px',
    10: '40px',
    12: '48px',
    14: '56px',
    16: '64px',
    20: '80px',
    24: '96px',
    28: '112px',
    32: '128px'
  },

  // 断点
  breakpoints: {
    xs: '480px',
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px'
  },

  // 动画
  animation: {
    duration: {
      fast: '0.15s',
      normal: '0.3s',
      slow: '0.6s'
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
      spring: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
    }
  },

  // 组件特定样式
  components: {
    button: {
      height: {
        small: '32px',
        medium: '40px',
        large: '56px'
      },
      padding: {
        small: '0 16px',
        medium: '0 24px',
        large: '0 32px'
      }
    },
    input: {
      height: {
        small: '32px',
        medium: '40px',
        large: '56px'
      },
      borderWidth: '2px'
    },
    card: {
      padding: {
        small: '16px',
        medium: '24px',
        large: '48px'
      }
    }
  }
};

// CSS变量生成器
export const generateCSSVariables = (theme) => {
  const cssVars = {};
  
  // 颜色变量
  Object.entries(theme.colors).forEach(([colorName, colorShades]) => {
    if (typeof colorShades === 'object') {
      Object.entries(colorShades).forEach(([shade, value]) => {
        cssVars[`--color-${colorName}-${shade}`] = value;
      });
    } else {
      cssVars[`--color-${colorName}`] = colorShades;
    }
  });

  // 渐变变量
  Object.entries(theme.gradients).forEach(([name, value]) => {
    cssVars[`--gradient-${name}`] = value;
  });

  // 阴影变量
  Object.entries(theme.shadows).forEach(([name, value]) => {
    cssVars[`--shadow-${name}`] = value;
  });

  // 圆角变量
  Object.entries(theme.borderRadius).forEach(([name, value]) => {
    cssVars[`--radius-${name}`] = value;
  });

  // 间距变量
  Object.entries(theme.spacing).forEach(([name, value]) => {
    cssVars[`--spacing-${name}`] = value;
  });

  return cssVars;
};

// Ant Design主题配置
export const antdThemeConfig = {
  token: {
    colorPrimary: assanTheme.colors.primary[500],
    colorSuccess: assanTheme.colors.success[500],
    colorWarning: assanTheme.colors.warning[500],
    colorError: assanTheme.colors.error[500],
    colorInfo: assanTheme.colors.primary[500],
    
    borderRadius: parseInt(assanTheme.borderRadius.lg),
    borderRadiusLG: parseInt(assanTheme.borderRadius.xl),
    borderRadiusSM: parseInt(assanTheme.borderRadius.md),
    
    fontFamily: assanTheme.typography.fontFamily.sans.join(', '),
    fontSize: parseInt(assanTheme.typography.fontSize.base),
    fontSizeLG: parseInt(assanTheme.typography.fontSize.lg),
    fontSizeSM: parseInt(assanTheme.typography.fontSize.sm),
    
    controlHeight: parseInt(assanTheme.components.input.height.medium),
    controlHeightLG: parseInt(assanTheme.components.input.height.large),
    controlHeightSM: parseInt(assanTheme.components.input.height.small),
    
    boxShadow: assanTheme.shadows.md,
    boxShadowSecondary: assanTheme.shadows.sm,
    
    colorBgContainer: '#ffffff',
    colorBgElevated: '#ffffff',
    colorBgLayout: assanTheme.colors.gray[50],
    
    wireframe: false
  },
  components: {
    Button: {
      borderRadius: parseInt(assanTheme.borderRadius.lg),
      controlHeight: parseInt(assanTheme.components.button.height.medium),
      controlHeightLG: parseInt(assanTheme.components.button.height.large),
      controlHeightSM: parseInt(assanTheme.components.button.height.small),
      fontWeight: assanTheme.typography.fontWeight.semibold
    },
    Input: {
      borderRadius: parseInt(assanTheme.borderRadius.lg),
      controlHeight: parseInt(assanTheme.components.input.height.medium),
      controlHeightLG: parseInt(assanTheme.components.input.height.large),
      controlHeightSM: parseInt(assanTheme.components.input.height.small),
      paddingInline: parseInt(assanTheme.spacing[4])
    },
    Card: {
      borderRadius: parseInt(assanTheme.borderRadius.xl),
      boxShadow: assanTheme.shadows.card,
      paddingLG: parseInt(assanTheme.components.card.padding.large)
    },
    Menu: {
      borderRadius: parseInt(assanTheme.borderRadius.lg),
      itemBorderRadius: parseInt(assanTheme.borderRadius.lg)
    }
  }
};

export default assanTheme;
