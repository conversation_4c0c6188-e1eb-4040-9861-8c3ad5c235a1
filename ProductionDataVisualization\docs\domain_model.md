# 生产数据可视化系统 - 领域模型设计文档

## 1. 概述

本文档描述了生产数据可视化系统的领域模型设计，包括用户领域、数据领域和可视化领域的实体类、值对象、聚合根和仓储接口。

## 2. 领域模型类图

```mermaid
classDiagram
    %% 基础类和接口
    class Entity {
        +Guid Id
        +DateTime CreatedAt
        +DateTime? ModifiedAt
        +string? CreatedBy
        +string? ModifiedBy
    }
    
    class IAggregateRoot {
        <<interface>>
    }
    
    class IRepository~T~ {
        <<interface>>
        +Task~T?~ GetByIdAsync(Guid id)
        +Task~IEnumerable~T~~ GetAllAsync()
        +Task~IEnumerable~T~~ FindAsync(Expression predicate)
        +Task~T~ AddAsync(T entity)
        +Task UpdateAsync(T entity)
        +Task DeleteAsync(T entity)
        +Task~int~ SaveChangesAsync()
    }
    
    class IUnitOfWork {
        <<interface>>
        +Task~int~ SaveChangesAsync()
        +Task BeginTransactionAsync()
        +Task CommitTransactionAsync()
        +Task RollbackTransactionAsync()
    }
    
    %% 用户领域
    class User {
        +string Username
        +string Email
        +string PasswordHash
        +string FullName
        +bool IsActive
        +DateTime? LastLoginTime
        +IReadOnlyCollection~UserRole~ UserRoles
    }
    
    class Role {
        +string Name
        +string Description
        +IReadOnlyCollection~RolePermission~ RolePermissions
        +IReadOnlyCollection~UserRole~ UserRoles
    }
    
    class Permission {
        +string Name
        +string Description
        +string Code
        +IReadOnlyCollection~RolePermission~ RolePermissions
    }
    
    class UserRole {
        +Guid UserId
        +Guid RoleId
        +User User
        +Role Role
    }
    
    class RolePermission {
        +Guid RoleId
        +Guid PermissionId
        +Role Role
        +Permission Permission
    }
    
    %% 数据领域
    class DataSource {
        +string Name
        +string Description
        +string SourceType
        +string FilePath
        +DateTime ImportedAt
        +Guid ImportedBy
        +int TotalRows
        +bool IsProcessed
        +string? ProcessingError
        +IReadOnlyCollection~DataPoint~ DataPoints
    }
    
    class DataCategory {
        +string Name
        +string Description
        +string Unit
        +bool IsActive
        +IReadOnlyCollection~Threshold~ Thresholds
        +IReadOnlyCollection~DataPoint~ DataPoints
    }
    
    class DataPoint {
        +Guid DataSourceId
        +Guid DataCategoryId
        +double Value
        +DateTime Timestamp
        +string? Label
        +bool IsAbnormal
        +string? AbnormalReason
        +DataSource DataSource
        +DataCategory DataCategory
    }
    
    class Threshold {
        +Guid DataCategoryId
        +string Name
        +string Description
        +double MinValue
        +double MaxValue
        +string Color
        +bool IsActive
        +DataCategory DataCategory
    }
    
    %% 可视化领域
    class Chart {
        +string Title
        +string Description
        +string ChartType
        +Guid CreatedBy
        +bool IsPublic
        +string? ConfigJson
        +IReadOnlyCollection~ChartDataCategory~ ChartDataCategories
        +IReadOnlyCollection~DashboardChart~ DashboardCharts
    }
    
    class Dashboard {
        +string Title
        +string Description
        +Guid CreatedBy
        +bool IsPublic
        +string? LayoutJson
        +IReadOnlyCollection~DashboardChart~ DashboardCharts
    }
    
    class ChartDataCategory {
        +Guid ChartId
        +Guid DataCategoryId
        +Chart Chart
        +DataCategory DataCategory
    }
    
    class DashboardChart {
        +Guid DashboardId
        +Guid ChartId
        +int PositionX
        +int PositionY
        +int Width
        +int Height
        +Dashboard Dashboard
        +Chart Chart
    }
    
    %% 继承关系
    Entity <|-- User
    Entity <|-- Role
    Entity <|-- Permission
    Entity <|-- UserRole
    Entity <|-- RolePermission
    Entity <|-- DataSource
    Entity <|-- DataCategory
    Entity <|-- DataPoint
    Entity <|-- Threshold
    Entity <|-- Chart
    Entity <|-- Dashboard
    Entity <|-- ChartDataCategory
    Entity <|-- DashboardChart
    
    IAggregateRoot <|-- User
    IAggregateRoot <|-- Role
    IAggregateRoot <|-- Permission
    IAggregateRoot <|-- DataSource
    IAggregateRoot <|-- DataCategory
    IAggregateRoot <|-- Threshold
    IAggregateRoot <|-- Chart
    IAggregateRoot <|-- Dashboard
    
    %% 关联关系
    User "1" --> "*" UserRole
    Role "1" --> "*" UserRole
    Role "1" --> "*" RolePermission
    Permission "1" --> "*" RolePermission
    
    DataSource "1" --> "*" DataPoint
    DataCategory "1" --> "*" DataPoint
    DataCategory "1" --> "*" Threshold
    
    Chart "1" --> "*" ChartDataCategory
    Chart "1" --> "*" DashboardChart
    Dashboard "1" --> "*" DashboardChart
    DataCategory "1" --> "*" ChartDataCategory
```

## 3. 领域模型说明

### 3.1 基础类和接口

- **Entity**: 所有实体的基类，包含ID和审计属性
- **IAggregateRoot**: 标记聚合根的接口
- **IRepository<T>**: 通用仓储接口
- **IUnitOfWork**: 工作单元接口

### 3.2 用户领域

#### 实体

- **User**: 用户实体，聚合根
  - 属性：用户名、邮箱、密码哈希、全名、是否激活、最后登录时间
  - 行为：更新个人资料、修改密码、添加/移除角色

- **Role**: 角色实体，聚合根
  - 属性：名称、描述
  - 行为：更新角色信息、添加/移除权限

- **Permission**: 权限实体，聚合根
  - 属性：名称、描述、权限代码
  - 行为：更新权限信息

- **UserRole**: 用户-角色关联实体
  - 属性：用户ID、角色ID

- **RolePermission**: 角色-权限关联实体
  - 属性：角色ID、权限ID

#### 仓储接口

- **IUserRepository**: 用户仓储接口
- **IRoleRepository**: 角色仓储接口
- **IPermissionRepository**: 权限仓储接口

### 3.3 数据领域

#### 实体

- **DataSource**: 数据源实体，聚合根
  - 属性：名称、描述、源类型、文件路径、导入时间、导入人、总行数、处理状态、处理错误
  - 行为：标记处理完成、标记处理错误、更新信息、添加数据点

- **DataCategory**: 数据类别实体，聚合根
  - 属性：名称、描述、单位、是否激活
  - 行为：更新信息、设置激活状态、添加/移除阈值

- **DataPoint**: 数据点实体
  - 属性：数据源ID、数据类别ID、数值、时间戳、标签、是否异常、异常原因
  - 行为：标记为异常、标记为正常、更新数值

- **Threshold**: 阈值实体，聚合根
  - 属性：数据类别ID、名称、描述、最小值、最大值、颜色、是否激活
  - 行为：更新信息、设置激活状态、检查数值是否超出阈值

#### 仓储接口

- **IDataSourceRepository**: 数据源仓储接口
- **IDataCategoryRepository**: 数据类别仓储接口
- **IDataPointRepository**: 数据点仓储接口
- **IThresholdRepository**: 阈值仓储接口

### 3.4 可视化领域

#### 实体

- **Chart**: 图表实体，聚合根
  - 属性：标题、描述、图表类型、创建人、是否公开、配置JSON
  - 行为：更新信息、添加/移除数据类别

- **Dashboard**: 仪表盘实体，聚合根
  - 属性：标题、描述、创建人、是否公开、布局JSON
  - 行为：更新信息、添加/移除图表、更新图表位置

- **ChartDataCategory**: 图表-数据类别关联实体
  - 属性：图表ID、数据类别ID

- **DashboardChart**: 仪表盘-图表关联实体
  - 属性：仪表盘ID、图表ID、X位置、Y位置、宽度、高度
  - 行为：更新位置

#### 仓储接口

- **IChartRepository**: 图表仓储接口
- **IDashboardRepository**: 仪表盘仓储接口

## 4. 数据库设计

### 4.1 表结构

#### 用户领域表

- **Users**: 用户表
  - Id (PK), Username, Email, PasswordHash, FullName, IsActive, LastLoginTime, CreatedAt, ModifiedAt, CreatedBy, ModifiedBy

- **Roles**: 角色表
  - Id (PK), Name, Description, CreatedAt, ModifiedAt, CreatedBy, ModifiedBy

- **Permissions**: 权限表
  - Id (PK), Name, Description, Code, CreatedAt, ModifiedAt, CreatedBy, ModifiedBy

- **UserRoles**: 用户-角色关联表
  - Id (PK), UserId (FK), RoleId (FK), CreatedAt, ModifiedAt, CreatedBy, ModifiedBy

- **RolePermissions**: 角色-权限关联表
  - Id (PK), RoleId (FK), PermissionId (FK), CreatedAt, ModifiedAt, CreatedBy, ModifiedBy

#### 数据领域表

- **DataSources**: 数据源表
  - Id (PK), Name, Description, SourceType, FilePath, ImportedAt, ImportedBy, TotalRows, IsProcessed, ProcessingError, CreatedAt, ModifiedAt, CreatedBy, ModifiedBy

- **DataCategories**: 数据类别表
  - Id (PK), Name, Description, Unit, IsActive, CreatedAt, ModifiedAt, CreatedBy, ModifiedBy

- **DataPoints**: 数据点表
  - Id (PK), DataSourceId (FK), DataCategoryId (FK), Value, Timestamp, Label, IsAbnormal, AbnormalReason, CreatedAt, ModifiedAt, CreatedBy, ModifiedBy

- **Thresholds**: 阈值表
  - Id (PK), DataCategoryId (FK), Name, Description, MinValue, MaxValue, Color, IsActive, CreatedAt, ModifiedAt, CreatedBy, ModifiedBy

#### 可视化领域表

- **Charts**: 图表表
  - Id (PK), Title, Description, ChartType, CreatedBy, IsPublic, ConfigJson, CreatedAt, ModifiedAt, CreatedBy, ModifiedBy

- **Dashboards**: 仪表盘表
  - Id (PK), Title, Description, CreatedBy, IsPublic, LayoutJson, CreatedAt, ModifiedAt, CreatedBy, ModifiedBy

- **ChartDataCategories**: 图表-数据类别关联表
  - Id (PK), ChartId (FK), DataCategoryId (FK), CreatedAt, ModifiedAt, CreatedBy, ModifiedBy

- **DashboardCharts**: 仪表盘-图表关联表
  - Id (PK), DashboardId (FK), ChartId (FK), PositionX, PositionY, Width, Height, CreatedAt, ModifiedAt, CreatedBy, ModifiedBy

### 4.2 索引设计

- **Users**: Username (唯一), Email (唯一)
- **Roles**: Name (唯一)
- **Permissions**: Code (唯一)
- **UserRoles**: UserId + RoleId (唯一)
- **RolePermissions**: RoleId + PermissionId (唯一)
- **DataSources**: Name, ImportedAt, ImportedBy
- **DataCategories**: Name (唯一)
- **DataPoints**: DataSourceId, DataCategoryId, Timestamp, IsAbnormal
- **Thresholds**: DataCategoryId, IsActive
- **Charts**: CreatedBy, ChartType, IsPublic
- **Dashboards**: CreatedBy, IsPublic
- **ChartDataCategories**: ChartId + DataCategoryId (唯一)
- **DashboardCharts**: DashboardId + ChartId (唯一)

## 5. 领域服务

### 5.1 用户领域服务

- **UserService**: 用户管理服务
  - 用户注册、登录、修改密码、分配角色
- **RoleService**: 角色管理服务
  - 创建角色、分配权限
- **PermissionService**: 权限管理服务
  - 创建权限、检查用户权限

### 5.2 数据领域服务

- **DataImportService**: 数据导入服务
  - 导入数据文件、解析数据、保存数据点
- **DataCategoryService**: 数据类别管理服务
  - 创建数据类别、管理阈值
- **ThresholdService**: 阈值管理服务
  - 创建阈值、检查数据异常

### 5.3 可视化领域服务

- **ChartService**: 图表管理服务
  - 创建图表、配置图表、关联数据类别
- **DashboardService**: 仪表盘管理服务
  - 创建仪表盘、添加图表、配置布局

## 6. 总结

本领域模型设计遵循领域驱动设计(DDD)的原则，通过实体、值对象、聚合根和仓储等概念，构建了清晰的领域模型。该模型将系统分为用户、数据和可视化三个主要领域，每个领域都有自己的实体和服务。

通过这种设计，系统可以实现高内聚、低耦合的架构，便于扩展和维护。同时，领域模型的设计也符合系统的业务需求，能够支持用户管理、数据导入和可视化展示等核心功能。 