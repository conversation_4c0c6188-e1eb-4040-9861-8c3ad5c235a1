// 图表配置服务 - 管理图表配置的保存、加载和管理

import { message } from 'antd';

const STORAGE_KEY = 'chart_configs';
const TEMPLATES_KEY = 'chart_templates';

class ChartConfigService {
  constructor() {
    this.configs = this.loadConfigs();
    this.templates = this.loadTemplates();
  }

  // 从本地存储加载配置
  loadConfigs() {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Failed to load chart configs:', error);
      return {};
    }
  }

  // 保存配置到本地存储
  saveConfigs() {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.configs));
      return true;
    } catch (error) {
      console.error('Failed to save chart configs:', error);
      message.error('保存配置失败');
      return false;
    }
  }

  // 从本地存储加载模板
  loadTemplates() {
    try {
      const stored = localStorage.getItem(TEMPLATES_KEY);
      return stored ? JSON.parse(stored) : this.getDefaultTemplates();
    } catch (error) {
      console.error('Failed to load chart templates:', error);
      return this.getDefaultTemplates();
    }
  }

  // 保存模板到本地存储
  saveTemplates() {
    try {
      localStorage.setItem(TEMPLATES_KEY, JSON.stringify(this.templates));
      return true;
    } catch (error) {
      console.error('Failed to save chart templates:', error);
      message.error('保存模板失败');
      return false;
    }
  }

  // 获取默认模板
  getDefaultTemplates() {
    return {
      'line-basic': {
        id: 'line-basic',
        name: '基础折线图',
        type: 'line',
        description: '简单的折线图模板',
        config: {
          title: '数据趋势图',
          height: 400,
          smooth: false,
          showArea: false,
          showSymbol: true,
          showGrid: true,
          showLegend: true,
          animation: true,
          animationDuration: 1000
        },
        category: 'basic',
        tags: ['折线图', '趋势', '基础'],
        createdAt: new Date().toISOString(),
        isDefault: true
      },
      'bar-comparison': {
        id: 'bar-comparison',
        name: '对比柱状图',
        type: 'bar',
        description: '用于数据对比的柱状图模板',
        config: {
          title: '数据对比图',
          height: 400,
          barWidth: '60%',
          borderRadius: [4, 4, 0, 0],
          showGrid: true,
          showLegend: true,
          animation: true,
          animationDuration: 1000
        },
        category: 'comparison',
        tags: ['柱状图', '对比', '分析'],
        createdAt: new Date().toISOString(),
        isDefault: true
      },
      'pie-distribution': {
        id: 'pie-distribution',
        name: '分布饼图',
        type: 'pie',
        description: '显示数据分布的饼图模板',
        config: {
          title: '数据分布图',
          height: 400,
          radius: ['40%', '70%'],
          showLabel: true,
          showLabelLine: true,
          animation: true,
          animationDuration: 1000
        },
        category: 'distribution',
        tags: ['饼图', '分布', '占比'],
        createdAt: new Date().toISOString(),
        isDefault: true
      }
    };
  }

  // 保存图表配置
  saveConfig(id, config, metadata = {}) {
    try {
      const configData = {
        id,
        config,
        metadata: {
          name: metadata.name || `图表配置_${id}`,
          description: metadata.description || '',
          tags: metadata.tags || [],
          category: metadata.category || 'custom',
          ...metadata
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      this.configs[id] = configData;
      
      if (this.saveConfigs()) {
        message.success('配置保存成功');
        return configData;
      }
      return null;
    } catch (error) {
      console.error('Failed to save config:', error);
      message.error('保存配置失败');
      return null;
    }
  }

  // 加载图表配置
  loadConfig(id) {
    return this.configs[id] || null;
  }

  // 获取所有配置
  getAllConfigs() {
    return Object.values(this.configs);
  }

  // 删除配置
  deleteConfig(id) {
    try {
      if (this.configs[id]) {
        delete this.configs[id];
        if (this.saveConfigs()) {
          message.success('配置删除成功');
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('Failed to delete config:', error);
      message.error('删除配置失败');
      return false;
    }
  }

  // 复制配置
  duplicateConfig(id, newName) {
    try {
      const originalConfig = this.configs[id];
      if (!originalConfig) {
        message.error('原配置不存在');
        return null;
      }

      const newId = `${id}_copy_${Date.now()}`;
      const newConfig = {
        ...originalConfig,
        id: newId,
        metadata: {
          ...originalConfig.metadata,
          name: newName || `${originalConfig.metadata.name}_副本`
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      this.configs[newId] = newConfig;
      
      if (this.saveConfigs()) {
        message.success('配置复制成功');
        return newConfig;
      }
      return null;
    } catch (error) {
      console.error('Failed to duplicate config:', error);
      message.error('复制配置失败');
      return null;
    }
  }

  // 保存为模板
  saveAsTemplate(config, metadata) {
    try {
      const templateId = `template_${Date.now()}`;
      const templateData = {
        id: templateId,
        name: metadata.name,
        type: metadata.type,
        description: metadata.description || '',
        config,
        category: metadata.category || 'custom',
        tags: metadata.tags || [],
        createdAt: new Date().toISOString(),
        isDefault: false
      };

      this.templates[templateId] = templateData;
      
      if (this.saveTemplates()) {
        message.success('模板保存成功');
        return templateData;
      }
      return null;
    } catch (error) {
      console.error('Failed to save template:', error);
      message.error('保存模板失败');
      return null;
    }
  }

  // 获取所有模板
  getAllTemplates() {
    return Object.values(this.templates);
  }

  // 按类型获取模板
  getTemplatesByType(type) {
    return Object.values(this.templates).filter(template => template.type === type);
  }

  // 按分类获取模板
  getTemplatesByCategory(category) {
    return Object.values(this.templates).filter(template => template.category === category);
  }

  // 搜索模板
  searchTemplates(query) {
    const lowerQuery = query.toLowerCase();
    return Object.values(this.templates).filter(template => 
      template.name.toLowerCase().includes(lowerQuery) ||
      template.description.toLowerCase().includes(lowerQuery) ||
      template.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  // 删除模板
  deleteTemplate(id) {
    try {
      const template = this.templates[id];
      if (!template) {
        message.error('模板不存在');
        return false;
      }

      if (template.isDefault) {
        message.error('不能删除默认模板');
        return false;
      }

      delete this.templates[id];
      
      if (this.saveTemplates()) {
        message.success('模板删除成功');
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to delete template:', error);
      message.error('删除模板失败');
      return false;
    }
  }

  // 导出配置
  exportConfig(id) {
    try {
      const config = this.configs[id];
      if (!config) {
        message.error('配置不存在');
        return null;
      }

      const exportData = {
        version: '1.0',
        type: 'chart_config',
        data: config,
        exportedAt: new Date().toISOString()
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `chart_config_${id}.json`;
      link.click();
      
      URL.revokeObjectURL(url);
      message.success('配置导出成功');
      return true;
    } catch (error) {
      console.error('Failed to export config:', error);
      message.error('导出配置失败');
      return false;
    }
  }

  // 导入配置
  importConfig(file) {
    return new Promise((resolve, reject) => {
      try {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const importData = JSON.parse(e.target.result);
            
            if (importData.type !== 'chart_config') {
              message.error('无效的配置文件格式');
              reject(new Error('Invalid file format'));
              return;
            }

            const config = importData.data;
            const newId = `imported_${Date.now()}`;
            
            const importedConfig = {
              ...config,
              id: newId,
              metadata: {
                ...config.metadata,
                name: `${config.metadata.name}_导入`
              },
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            };

            this.configs[newId] = importedConfig;
            
            if (this.saveConfigs()) {
              message.success('配置导入成功');
              resolve(importedConfig);
            } else {
              reject(new Error('Failed to save imported config'));
            }
          } catch (parseError) {
            message.error('配置文件格式错误');
            reject(parseError);
          }
        };
        
        reader.onerror = () => {
          message.error('文件读取失败');
          reject(new Error('File read error'));
        };
        
        reader.readAsText(file);
      } catch (error) {
        console.error('Failed to import config:', error);
        message.error('导入配置失败');
        reject(error);
      }
    });
  }

  // 清理过期配置
  cleanupExpiredConfigs(daysToKeep = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      
      let deletedCount = 0;
      Object.keys(this.configs).forEach(id => {
        const config = this.configs[id];
        const createdDate = new Date(config.createdAt);
        
        if (createdDate < cutoffDate) {
          delete this.configs[id];
          deletedCount++;
        }
      });
      
      if (deletedCount > 0) {
        this.saveConfigs();
        message.success(`清理了 ${deletedCount} 个过期配置`);
      }
      
      return deletedCount;
    } catch (error) {
      console.error('Failed to cleanup configs:', error);
      message.error('清理配置失败');
      return 0;
    }
  }
}

// 创建单例实例
const chartConfigService = new ChartConfigService();

export default chartConfigService;
