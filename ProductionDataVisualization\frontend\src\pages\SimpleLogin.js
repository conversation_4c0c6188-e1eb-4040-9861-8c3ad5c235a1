import React, { useState } from 'react';
import { Form, Input, Button, message } from 'antd';
import { useNavigate, Link } from 'react-router-dom';
import authService from '../services/authService';

// 添加CSS动画
const styles = `
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 0.3;
      transform: scale(1);
    }
    50% {
      opacity: 0.6;
      transform: scale(1.1);
    }
  }

  @keyframes drift {
    0%, 100% {
      transform: translateX(0px) translateY(0px);
    }
    25% {
      transform: translateX(10px) translateY(-5px);
    }
    50% {
      transform: translateX(-5px) translateY(-10px);
    }
    75% {
      transform: translateX(-10px) translateY(5px);
    }
  }

  @keyframes blink {
    0%, 90%, 100% {
      transform: scaleY(1);
    }
    95% {
      transform: scaleY(0.1);
    }
  }

  @keyframes drop {
    0% {
      transform: translateY(0px) scale(1);
      opacity: 0.8;
    }
    100% {
      transform: translateY(30px) scale(0.8);
      opacity: 0.2;
    }
  }

  @keyframes chartGrow {
    0%, 100% {
      height: 20px;
    }
    50% {
      height: 35px;
    }
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }
`;

// 注入样式到页面
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.type = "text/css";
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}

const SimpleLogin = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [loginSuccess, setLoginSuccess] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 50, y: 50 });
  const navigate = useNavigate();

  // 鼠标移动事件处理
  const handleMouseMove = (e) => {
    const { clientX, clientY } = e;
    const { innerWidth, innerHeight } = window;
    const x = (clientX / innerWidth) * 100;
    const y = (clientY / innerHeight) * 100;
    setMousePosition({ x, y });
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      console.log('开始登录流程:', values.username);
      const success = await authService.login(values.username, values.password);
      console.log('登录结果:', success);

      if (success) {
        setLoginSuccess(true);
        message.success('登录成功！');

        console.log('登录成功，准备跳转到仪表板');
        console.log('当前localStorage token:', localStorage.getItem('token'));
        console.log('当前sessionStorage loginSuccessTime:', sessionStorage.getItem('loginSuccessTime'));

        // 添加延迟以显示成功动画
        setTimeout(() => {
          console.log('执行跳转到 /dashboard');
          navigate('/dashboard');
        }, 1000);
      } else {
        message.error('用户名或密码错误');
      }
    } catch (error) {
      console.error('登录失败:', error);
      message.error('登录失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = () => {
    message.info('请联系系统管理员重置密码');
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f5f5f5',
        position: 'relative',
        overflow: 'hidden'
      }}
      onMouseMove={handleMouseMove}
    >
      {/* 背景装饰元素 */}
      <div style={{
        position: 'absolute',
        top: '10%',
        left: '8%',
        width: '120px',
        height: '120px',
        borderRadius: '50%',
        background: 'linear-gradient(135deg, rgba(74, 144, 226, 0.08), rgba(80, 200, 120, 0.08))',
        animation: 'float 6s ease-in-out infinite',
        transform: 'translate(' + ((mousePosition.x - 50) * 0.3) + 'px, ' + ((mousePosition.y - 50) * 0.3) + 'px)',
        transition: 'transform 0.3s ease-out'
      }}></div>

      {/* 测试鼠标跟随元素 */}
      <div style={{
        position: 'absolute',
        top: '5%',
        right: '5%',
        width: '20px',
        height: '20px',
        borderRadius: '50%',
        background: 'rgba(255, 0, 0, 0.5)',
        transform: 'translate(' + ((mousePosition.x - 50) * 1) + 'px, ' + ((mousePosition.y - 50) * 1) + 'px)',
        transition: 'transform 0.1s ease-out',
        zIndex: 1000
      }}></div>

      <div style={{
        position: 'absolute',
        top: '15%',
        right: '12%',
        width: '80px',
        height: '80px',
        background: 'rgba(74, 144, 226, 0.06)',
        transform: 'rotate(45deg) translate(' + ((mousePosition.x - 50) * -0.2) + 'px, ' + ((mousePosition.y - 50) * -0.2) + 'px)',
        animation: 'float 4s ease-in-out infinite reverse',
        transition: 'transform 0.4s ease-out'
      }}></div>

      <div style={{
        position: 'absolute',
        bottom: '20%',
        left: '15%',
        width: '100px',
        height: '100px',
        borderRadius: '50%',
        background: 'rgba(80, 200, 120, 0.05)',
        animation: 'float 5s ease-in-out infinite'
      }}></div>

      <div style={{
        position: 'absolute',
        bottom: '30%',
        right: '8%',
        width: '60px',
        height: '60px',
        background: 'linear-gradient(45deg, rgba(74, 144, 226, 0.08), transparent)',
        borderRadius: '50%',
        animation: 'float 3s ease-in-out infinite reverse'
      }}></div>

      {/* 装饰线条 */}
      <div style={{
        position: 'absolute',
        top: '25%',
        left: '5%',
        width: '200px',
        height: '1px',
        background: 'linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.3), transparent)',
        transform: 'rotate(15deg)'
      }}></div>

      <div style={{
        position: 'absolute',
        bottom: '35%',
        right: '6%',
        width: '150px',
        height: '1px',
        background: 'linear-gradient(90deg, transparent, rgba(80, 200, 120, 0.3), transparent)',
        transform: 'rotate(-20deg)'
      }}></div>

      <div style={{
        position: 'absolute',
        top: '60%',
        left: '3%',
        width: '100px',
        height: '1px',
        background: 'linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.2), transparent)',
        transform: 'rotate(45deg)'
      }}></div>

      {/* 小圆点装饰 */}
      <div style={{
        position: 'absolute',
        top: '40%',
        left: '20%',
        width: '4px',
        height: '4px',
        borderRadius: '50%',
        background: 'rgba(74, 144, 226, 0.4)'
      }}></div>

      <div style={{
        position: 'absolute',
        top: '70%',
        right: '25%',
        width: '6px',
        height: '6px',
        borderRadius: '50%',
        background: 'rgba(80, 200, 120, 0.4)'
      }}></div>

      <div style={{
        position: 'absolute',
        top: '50%',
        right: '5%',
        width: '3px',
        height: '3px',
        borderRadius: '50%',
        background: 'rgba(74, 144, 226, 0.5)'
      }}></div>

      {/* 六边形装饰 */}
      <div style={{
        position: 'absolute',
        top: '35%',
        right: '20%',
        width: '40px',
        height: '40px',
        background: 'rgba(80, 200, 120, 0.1)',
        clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
        animation: 'rotate 20s linear infinite',
        transform: `translate(${(mousePosition.x - 50) * -0.12}px, ${(mousePosition.y - 50) * -0.12}px)`,
        transition: 'transform 0.4s ease-out'
      }}></div>

      {/* 三角形装饰 */}
      <div style={{
        position: 'absolute',
        bottom: '45%',
        left: '25%',
        width: '0',
        height: '0',
        borderLeft: '15px solid transparent',
        borderRight: '15px solid transparent',
        borderBottom: '25px solid rgba(74, 144, 226, 0.08)',
        animation: 'pulse 4s ease-in-out infinite'
      }}></div>

      {/* 波浪线装饰 */}
      <div style={{
        position: 'absolute',
        top: '80%',
        left: '10%',
        width: '120px',
        height: '2px',
        background: 'linear-gradient(90deg, transparent, rgba(80, 200, 120, 0.3), transparent)',
        borderRadius: '2px',
        transform: 'rotate(5deg)',
        animation: 'drift 8s ease-in-out infinite'
      }}></div>

      {/* 环形装饰 */}
      <div style={{
        position: 'absolute',
        top: '15%',
        left: '30%',
        width: '30px',
        height: '30px',
        border: '2px solid rgba(74, 144, 226, 0.2)',
        borderRadius: '50%',
        animation: 'pulse 6s ease-in-out infinite'
      }}></div>

      {/* 星形装饰点 */}
      <div style={{
        position: 'absolute',
        bottom: '60%',
        right: '30%',
        width: '8px',
        height: '8px',
        background: 'rgba(80, 200, 120, 0.4)',
        clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)',
        animation: 'rotate 15s linear infinite'
      }}></div>

      {/* 虚线圆弧 */}
      <div style={{
        position: 'absolute',
        top: '45%',
        left: '8%',
        width: '50px',
        height: '50px',
        border: '1px dashed rgba(74, 144, 226, 0.3)',
        borderRadius: '50%',
        borderTop: 'none',
        borderRight: 'none',
        animation: 'rotate 25s linear infinite reverse'
      }}></div>

      {/* 渐变条纹 */}
      <div style={{
        position: 'absolute',
        bottom: '20%',
        right: '15%',
        width: '80px',
        height: '4px',
        background: 'repeating-linear-gradient(90deg, rgba(80, 200, 120, 0.1) 0px, rgba(80, 200, 120, 0.1) 8px, transparent 8px, transparent 16px)',
        transform: 'rotate(-10deg)',
        animation: 'drift 10s ease-in-out infinite'
      }}></div>

      {/* 菱形装饰 */}
      <div style={{
        position: 'absolute',
        top: '65%',
        right: '8%',
        width: '20px',
        height: '20px',
        background: 'rgba(74, 144, 226, 0.15)',
        transform: 'rotate(45deg)',
        animation: 'float 7s ease-in-out infinite'
      }}></div>

      {/* 眼睛装饰 */}
      <div style={{
        position: 'absolute',
        top: '20%',
        left: '15%',
        width: '40px',
        height: '25px',
        background: 'rgba(74, 144, 226, 0.1)',
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        animation: 'blink 4s ease-in-out infinite',
        transform: 'translate(' + ((mousePosition.x - 50) * 0.4) + 'px, ' + ((mousePosition.y - 50) * 0.4) + 'px)',
        transition: 'transform 0.2s ease-out'
      }}>
        <div style={{
          width: '12px',
          height: '12px',
          background: 'rgba(74, 144, 226, 0.3)',
          borderRadius: '50%',
          transform: 'translate(' + ((mousePosition.x - 50) * 0.2) + 'px, ' + ((mousePosition.y - 50) * 0.2) + 'px)',
          transition: 'transform 0.1s ease-out'
        }}></div>
      </div>

      {/* 眼药水滴装饰 */}
      <div style={{
        position: 'absolute',
        top: '30%',
        right: '25%',
        width: '8px',
        height: '12px',
        background: 'rgba(80, 200, 120, 0.4)',
        borderRadius: '50% 50% 50% 0',
        transform: 'rotate(-45deg)',
        animation: 'drop 3s ease-in-out infinite'
      }}></div>

      <div style={{
        position: 'absolute',
        top: '75%',
        left: '20%',
        width: '6px',
        height: '10px',
        background: 'rgba(80, 200, 120, 0.3)',
        borderRadius: '50% 50% 50% 0',
        transform: 'rotate(-45deg)',
        animation: 'drop 4s ease-in-out infinite 1s'
      }}></div>

      {/* 图表柱状图装饰 */}
      <div style={{
        position: 'absolute',
        bottom: '30%',
        left: '5%',
        display: 'flex',
        alignItems: 'flex-end',
        gap: '3px'
      }}>
        <div style={{
          width: '4px',
          height: '20px',
          background: 'rgba(74, 144, 226, 0.2)',
          animation: 'chartGrow 2s ease-in-out infinite'
        }}></div>
        <div style={{
          width: '4px',
          height: '30px',
          background: 'rgba(74, 144, 226, 0.25)',
          animation: 'chartGrow 2s ease-in-out infinite 0.2s'
        }}></div>
        <div style={{
          width: '4px',
          height: '15px',
          background: 'rgba(74, 144, 226, 0.2)',
          animation: 'chartGrow 2s ease-in-out infinite 0.4s'
        }}></div>
        <div style={{
          width: '4px',
          height: '25px',
          background: 'rgba(74, 144, 226, 0.25)',
          animation: 'chartGrow 2s ease-in-out infinite 0.6s'
        }}></div>
      </div>

      {/* 饼图装饰 */}
      <div style={{
        position: 'absolute',
        top: '55%',
        right: '15%',
        width: '30px',
        height: '30px',
        borderRadius: '50%',
        background: `conic-gradient(
          rgba(74, 144, 226, 0.3) 0deg 120deg,
          rgba(80, 200, 120, 0.3) 120deg 240deg,
          rgba(74, 144, 226, 0.1) 240deg 360deg
        )`,
        animation: 'rotate 20s linear infinite',
        transform: `translate(${(mousePosition.x - 50) * -0.08}px, ${(mousePosition.y - 50) * -0.08}px)`,
        transition: 'transform 0.5s ease-out'
      }}></div>

      {/* 折线图装饰 */}
      <div style={{
        position: 'absolute',
        bottom: '50%',
        right: '5%',
        width: '50px',
        height: '30px',
        background: `linear-gradient(
          to right,
          transparent 0%,
          transparent 20%,
          rgba(80, 200, 120, 0.3) 20%,
          rgba(80, 200, 120, 0.3) 22%,
          transparent 22%,
          transparent 40%,
          rgba(80, 200, 120, 0.3) 40%,
          rgba(80, 200, 120, 0.3) 42%,
          transparent 42%,
          transparent 60%,
          rgba(80, 200, 120, 0.3) 60%,
          rgba(80, 200, 120, 0.3) 62%,
          transparent 62%,
          transparent 80%,
          rgba(80, 200, 120, 0.3) 80%,
          rgba(80, 200, 120, 0.3) 82%,
          transparent 82%
        )`,
        clipPath: 'polygon(0% 100%, 20% 60%, 40% 40%, 60% 70%, 80% 30%, 100% 50%, 100% 100%)',
        animation: 'pulse 3s ease-in-out infinite'
      }}></div>

      {/* 医药瓶装饰 */}
      <div style={{
        position: 'absolute',
        top: '40%',
        left: '35%',
        width: '12px',
        height: '20px',
        background: 'rgba(80, 200, 120, 0.2)',
        borderRadius: '2px 2px 6px 6px'
      }}>
        <div style={{
          position: 'absolute',
          top: '-3px',
          left: '3px',
          width: '6px',
          height: '4px',
          background: 'rgba(80, 200, 120, 0.3)',
          borderRadius: '2px 2px 0 0'
        }}></div>
      </div>

      {/* 分子结构装饰 */}
      <div style={{
        position: 'absolute',
        bottom: '25%',
        left: '30%',
        width: '40px',
        height: '30px',
        transform: `translate(${(mousePosition.x - 50) * 0.06}px, ${(mousePosition.y - 50) * 0.06}px)`,
        transition: 'transform 0.6s ease-out'
      }}>
        <div style={{
          position: 'absolute',
          top: '0',
          left: '0',
          width: '6px',
          height: '6px',
          background: 'rgba(74, 144, 226, 0.4)',
          borderRadius: '50%',
          transform: `translate(${(mousePosition.x - 50) * 0.03}px, ${(mousePosition.y - 50) * 0.03}px)`,
          transition: 'transform 0.3s ease-out'
        }}></div>
        <div style={{
          position: 'absolute',
          top: '15px',
          left: '20px',
          width: '5px',
          height: '5px',
          background: 'rgba(80, 200, 120, 0.4)',
          borderRadius: '50%',
          transform: `translate(${(mousePosition.x - 50) * -0.02}px, ${(mousePosition.y - 50) * -0.02}px)`,
          transition: 'transform 0.4s ease-out'
        }}></div>
        <div style={{
          position: 'absolute',
          top: '5px',
          left: '30px',
          width: '4px',
          height: '4px',
          background: 'rgba(74, 144, 226, 0.3)',
          borderRadius: '50%',
          transform: `translate(${(mousePosition.x - 50) * 0.04}px, ${(mousePosition.y - 50) * 0.04}px)`,
          transition: 'transform 0.2s ease-out'
        }}></div>
        <div style={{
          position: 'absolute',
          top: '2px',
          left: '8px',
          width: '15px',
          height: '1px',
          background: 'rgba(74, 144, 226, 0.2)',
          transform: `rotate(25deg) translate(${(mousePosition.x - 50) * 0.01}px, ${(mousePosition.y - 50) * 0.01}px)`,
          transition: 'transform 0.5s ease-out'
        }}></div>
        <div style={{
          position: 'absolute',
          top: '12px',
          left: '15px',
          width: '12px',
          height: '1px',
          background: 'rgba(80, 200, 120, 0.2)',
          transform: `rotate(-30deg) translate(${(mousePosition.x - 50) * -0.01}px, ${(mousePosition.y - 50) * -0.01}px)`,
          transition: 'transform 0.5s ease-out'
        }}></div>
      </div>
      <div style={{
        width: '100%',
        maxWidth: '420px',
        padding: '48px',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderRadius: '20px',
        boxShadow: '0 20px 60px rgba(0,0,0,0.15), 0 8px 32px rgba(0,0,0,0.1)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        position: 'relative',
        zIndex: 10,
        backdropFilter: 'blur(15px)',
        animation: 'fadeIn 0.8s ease-out',
        overflow: 'hidden'
      }}>
        {/* 卡片内部装饰渐变 */}
        <div style={{
          position: 'absolute',
          top: '-50%',
          right: '-50%',
          width: '200%',
          height: '200%',
          background: 'linear-gradient(45deg, rgba(74, 144, 226, 0.03), rgba(80, 200, 120, 0.03))',
          transform: 'rotate(15deg)',
          pointerEvents: 'none',
          zIndex: -1
        }}></div>

        {/* 内容容器 */}
        <div style={{
          position: 'relative',
          zIndex: 1
        }}>
        {/* 标题 */}
        <div style={{ textAlign: 'center', marginBottom: '40px' }}>
          <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            marginBottom: '12px'
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '12px',
              background: 'linear-gradient(135deg, #4A90E2, #50C878)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '12px',
              boxShadow: '0 4px 12px rgba(74, 144, 226, 0.3)'
            }}>
              <span style={{ color: 'white', fontSize: '18px', fontWeight: 'bold' }}>远</span>
            </div>
            <h1 style={{
              fontSize: '28px',
              margin: 0,
              background: 'linear-gradient(135deg, #4A90E2, #50C878)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: '600'
            }}>远大医药</h1>
          </div>
          <p style={{
            fontSize: '16px',
            color: '#666',
            margin: 0,
            fontWeight: '400',
            letterSpacing: '0.5px'
          }}>偏差报告系统</p>
          <div style={{
            width: '60px',
            height: '3px',
            background: 'linear-gradient(135deg, #4A90E2, #50C878)',
            margin: '16px auto 0',
            borderRadius: '2px'
          }}></div>

          {/* 安全提示 */}
          <div style={{
            marginTop: '20px',
            padding: '12px 16px',
            backgroundColor: 'rgba(74, 144, 226, 0.08)',
            border: '1px solid rgba(74, 144, 226, 0.2)',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <p style={{
              fontSize: '14px',
              color: '#4A90E2',
              margin: 0,
              fontWeight: '500'
            }}>🔒 为了您的账户安全，系统要求重新登录</p>
            <p style={{
              fontSize: '12px',
              color: '#666',
              margin: '4px 0 0 0'
            }}>请使用您的用户名和密码登录系统</p>
          </div>
        </div>

        {/* 登录表单 */}
        <Form
          form={form}
          name="login"
          onFinish={onFinish}
          layout="vertical"
        >
          <Form.Item
            label={<span style={{ color: '#333', fontWeight: '500' }}>用户名</span>}
            name="username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input
              placeholder="请输入用户名"
              style={{
                height: '48px',
                borderRadius: '12px',
                border: '2px solid #f0f0f0',
                fontSize: '16px',
                transition: 'all 0.3s ease'
              }}
            />
          </Form.Item>

          <Form.Item
            label={<span style={{ color: '#333', fontWeight: '500' }}>密码</span>}
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input
              type="password"
              placeholder="请输入密码"
              autoComplete="current-password"
              style={{
                height: '48px',
                borderRadius: '12px',
                border: '2px solid #f0f0f0',
                fontSize: '16px',
                transition: 'all 0.3s ease'
              }}
            />
          </Form.Item>

          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginBottom: '24px'
          }}>
            <label>
              <input type="checkbox" style={{ marginRight: '8px' }} />
              记住我
            </label>
            <Button 
              type="link" 
              onClick={handleForgotPassword}
              style={{ padding: 0 }}
            >
              忘记密码？
            </Button>
          </div>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              size="large"
              style={{
                height: '52px',
                borderRadius: '12px',
                fontSize: '16px',
                fontWeight: '500',
                background: 'linear-gradient(135deg, #4A90E2, #50C878)',
                border: 'none',
                boxShadow: '0 4px 16px rgba(74, 144, 226, 0.3)',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 6px 20px rgba(74, 144, 226, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 4px 16px rgba(74, 144, 226, 0.3)';
              }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        {/* 注册链接 */}
        <div style={{ textAlign: 'center', marginTop: '32px' }}>
          <span style={{ color: '#666', fontSize: '14px' }}>还没有账户？</span>
          <Link
            to="/register"
            style={{
              color: '#4A90E2',
              textDecoration: 'none',
              fontWeight: '500',
              marginLeft: '8px',
              transition: 'color 0.3s ease'
            }}
            onMouseEnter={(e) => e.target.style.color = '#50C878'}
            onMouseLeave={(e) => e.target.style.color = '#4A90E2'}
          >
            立即注册
          </Link>
        </div>
        </div>

        {/* 浮动帮助按钮 */}
        <div style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          width: '56px',
          height: '56px',
          borderRadius: '50%',
          background: 'linear-gradient(135deg, #4A90E2, #50C878)',
          boxShadow: '0 4px 20px rgba(74, 144, 226, 0.3)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          zIndex: 1000,
          transition: 'all 0.3s ease',
          animation: 'float 3s ease-in-out infinite'
        }}
        onMouseEnter={(e) => {
          e.target.style.transform = 'scale(1.1)';
          e.target.style.boxShadow = '0 6px 25px rgba(74, 144, 226, 0.4)';
        }}
        onMouseLeave={(e) => {
          e.target.style.transform = 'scale(1)';
          e.target.style.boxShadow = '0 4px 20px rgba(74, 144, 226, 0.3)';
        }}
        onClick={() => message.info('如需帮助，请联系系统管理员')}
        >
          <span style={{ color: 'white', fontSize: '24px' }}>?</span>
        </div>

        {/* 成功状态覆盖层 */}
        {loginSuccess && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'rgba(74, 144, 226, 0.1)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9999,
            animation: 'fadeIn 0.5s ease-out'
          }}>
            <div style={{
              background: 'white',
              padding: '40px',
              borderRadius: '20px',
              boxShadow: '0 20px 60px rgba(0,0,0,0.2)',
              textAlign: 'center',
              animation: 'scaleIn 0.5s ease-out'
            }}>
              <div style={{
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #4A90E2, #50C878)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 20px',
                animation: 'scaleIn 0.8s ease-out'
              }}>
                <span style={{ color: 'white', fontSize: '30px' }}>✓</span>
              </div>
              <h3 style={{ margin: '0 0 10px 0', color: '#333' }}>登录成功！</h3>
              <p style={{ margin: 0, color: '#666' }}>正在跳转到系统...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleLogin;
