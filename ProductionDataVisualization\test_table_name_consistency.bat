@echo off
title 测试表名一致性修复

echo ========================================
echo   测试表名一致性修复
echo ========================================
echo.

echo [INFO] 修复内容:
echo 1. 彻底重写GetOrCreateDataTable函数
echo 2. 确保分析和导入使用相同的表名生成逻辑
echo 3. 自动检测和删除不一致的旧映射
echo 4. 详细的日志记录每个步骤
echo.

echo [INFO] 新的表名一致性逻辑:
echo - 分析阶段: 生成 Data_注射水分配1电导率
echo - 导入阶段: 使用相同的 Data_注射水分配1电导率
echo - 映射检查: 自动删除不一致的旧映射
echo.

echo [INFO] 后端已启动在 http://localhost:5000
echo.

echo [INFO] 启动前端...
cd frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 15 /nobreak

echo.
echo ========================================
echo   测试指南
echo ========================================
echo.
echo 1. 访问: http://localhost:3000/data-import
echo 2. 上传中文文件名的数据文件
echo 3. 观察后端控制台日志:
echo    - "为文件 'xxx.csv' 生成表名: Data_xxx"
echo    - "发现现有映射: xxx" (如果有旧映射)
echo    - "表名不一致，删除旧映射: 旧名 -> 新名"
echo    - "创建新表: Data_xxx"
echo    - "开始导入数据到表 [Data_xxx]"
echo.
echo 4. 验证一致性:
echo    - 分析阶段和导入阶段使用相同表名
echo    - 无"对象名无效"错误
echo    - 数据导入成功完成
echo.
echo 5. 预期结果:
echo    - 文件分析成功
echo    - 表名一致性保证
echo    - 数据导入无500错误
echo    - 完整的数据预览
echo.

pause
