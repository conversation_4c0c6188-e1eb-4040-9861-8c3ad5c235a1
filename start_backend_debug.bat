@echo off
title Backend Debug Startup

echo ==========================================
echo   后端调试启动
echo ==========================================
echo.

cd /d "ProductionDataVisualization\backend\SqlServerAPI\bin\Release\net8.0\win-x64\publish"

echo 当前目录: %CD%
echo.

echo 检查文件:
dir SqlServerAPI.exe
echo.

echo 检查数据库:
if exist "ProductionDataVisualization.db" (
    echo SQLite数据库存在
) else (
    echo SQLite数据库不存在
)
echo.

echo 启动后端服务器...
echo 如果出现错误，窗口将保持打开状态
echo.

SqlServerAPI.exe --urls=http://localhost:5000

echo.
echo 程序已退出，退出代码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% neq 0 (
    echo 程序异常退出！
) else (
    echo 程序正常退出
)

echo.
echo 按任意键退出...
pause > nul
