import React, { useState, useEffect } from 'react';
import { Layout } from 'antd';
import { motion } from 'framer-motion';
import AssanNavbar from './AssanNavbar';

const { Content } = Layout;

const EnhancedAssanLayout = ({ children, showParticles = true, showRays = true }) => {
  const [mounted, setMounted] = useState(false);
  const [collapsed, setCollapsed] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleToggle = () => {
    setCollapsed(!collapsed);
  };

  if (!mounted) {
    return null; // 避免服务端渲染问题
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <AssanNavbar collapsed={collapsed} onToggle={handleToggle} />
      
      <Layout style={{ marginTop: '72px' }}>
        <Content className="enhanced-assan-content">
          {/* 增强版背景 */}
          <div className="enhanced-background">
            {/* 背景装饰 */}
            <div className="background-decoration">
              <div className="decoration-circle circle-1"></div>
              <div className="decoration-circle circle-2"></div>
              <div className="decoration-circle circle-3"></div>
              <div className="decoration-circle circle-4"></div>
              <div className="decoration-circle circle-5"></div>
              
              {/* 粒子效果 */}
              {showParticles && (
                <div className="particles">
                  {[...Array(15)].map((_, i) => (
                    <div key={i} className={`particle particle-${i + 1}`}></div>
                  ))}
                </div>
              )}
              
              {/* 光线效果 */}
              {showRays && (
                <div className="light-rays">
                  <div className="ray ray-1"></div>
                  <div className="ray ray-2"></div>
                  <div className="ray ray-3"></div>
                </div>
              )}
            </div>
          </div>

          {/* 内容区域 */}
          <motion.div
            className="content-wrapper"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {children}
          </motion.div>
        </Content>
      </Layout>

      {/* 增强版样式 */}
      <style jsx="true">{`
        .enhanced-assan-content {
          position: relative;
          min-height: calc(100vh - 72px);
          overflow: hidden;
        }

        .enhanced-background {
          position: fixed;
          top: 72px;
          left: 0;
          right: 0;
          bottom: 0;
          background: 
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
            linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          z-index: -1;
        }

        .background-decoration {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
        }

        .decoration-circle {
          position: absolute;
          border-radius: 50%;
          background: linear-gradient(45deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          animation: float 8s ease-in-out infinite;
          box-shadow: 
            0 8px 32px rgba(255, 255, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .circle-1 {
          width: 200px;
          height: 200px;
          top: 10%;
          left: 5%;
          animation-delay: 0s;
        }

        .circle-2 {
          width: 150px;
          height: 150px;
          top: 60%;
          right: 10%;
          animation-delay: 2.5s;
        }

        .circle-3 {
          width: 120px;
          height: 120px;
          bottom: 20%;
          left: 15%;
          animation-delay: 5s;
        }

        .circle-4 {
          width: 80px;
          height: 80px;
          top: 30%;
          right: 25%;
          animation-delay: 1.5s;
        }

        .circle-5 {
          width: 100px;
          height: 100px;
          bottom: 40%;
          right: 5%;
          animation-delay: 3.5s;
        }

        @keyframes float {
          0%, 100% { 
            transform: translateY(0px) rotate(0deg) scale(1); 
            opacity: 0.7;
          }
          33% { 
            transform: translateY(-30px) rotate(120deg) scale(1.1); 
            opacity: 0.9;
          }
          66% { 
            transform: translateY(-15px) rotate(240deg) scale(0.9); 
            opacity: 0.8;
          }
        }

        /* 粒子效果 */
        .particles {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
        }

        .particle {
          position: absolute;
          width: 3px;
          height: 3px;
          background: rgba(255, 255, 255, 0.6);
          border-radius: 50%;
          animation: particleFloat 20s infinite linear;
        }

        .particle:nth-child(odd) {
          background: rgba(102, 126, 234, 0.4);
          animation-duration: 25s;
        }

        .particle:nth-child(3n) {
          background: rgba(118, 75, 162, 0.4);
          animation-duration: 30s;
        }

        @keyframes particleFloat {
          0% {
            transform: translateY(100vh) translateX(0) rotate(0deg);
            opacity: 0;
          }
          10% {
            opacity: 1;
          }
          90% {
            opacity: 1;
          }
          100% {
            transform: translateY(-100px) translateX(100px) rotate(360deg);
            opacity: 0;
          }
        }

        /* 光线效果 */
        .light-rays {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
        }

        .ray {
          position: absolute;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
          animation: rayMove 10s infinite ease-in-out;
        }

        .ray-1 {
          width: 2px;
          height: 100%;
          left: 25%;
          animation-delay: 0s;
        }

        .ray-2 {
          width: 1px;
          height: 100%;
          left: 65%;
          animation-delay: 3s;
        }

        .ray-3 {
          width: 3px;
          height: 100%;
          left: 85%;
          animation-delay: 6s;
        }

        @keyframes rayMove {
          0%, 100% {
            opacity: 0;
            transform: translateX(-50px);
          }
          50% {
            opacity: 1;
            transform: translateX(50px);
          }
        }

        /* 粒子随机位置 */
        .particle-1 { left: 10%; animation-delay: 0s; }
        .particle-2 { left: 20%; animation-delay: 2s; }
        .particle-3 { left: 30%; animation-delay: 4s; }
        .particle-4 { left: 40%; animation-delay: 6s; }
        .particle-5 { left: 50%; animation-delay: 8s; }
        .particle-6 { left: 60%; animation-delay: 10s; }
        .particle-7 { left: 70%; animation-delay: 12s; }
        .particle-8 { left: 80%; animation-delay: 14s; }
        .particle-9 { left: 90%; animation-delay: 16s; }
        .particle-10 { left: 15%; animation-delay: 18s; }
        .particle-11 { left: 25%; animation-delay: 20s; }
        .particle-12 { left: 35%; animation-delay: 22s; }
        .particle-13 { left: 45%; animation-delay: 24s; }
        .particle-14 { left: 55%; animation-delay: 26s; }
        .particle-15 { left: 65%; animation-delay: 28s; }

        .content-wrapper {
          position: relative;
          z-index: 1;
          padding: 24px;
          min-height: calc(100vh - 96px);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .content-wrapper {
            padding: 16px;
          }
          
          .decoration-circle {
            display: none;
          }
          
          .particles {
            display: none;
          }
        }
      `}</style>
    </Layout>
  );
};

export default EnhancedAssanLayout;
