using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Domain.Common;

namespace Domain.DataAggregate
{
    /// <summary>
    /// 数据类别仓储接口
    /// </summary>
    public interface IDataCategoryRepository : IRepository<DataCategory>
    {
        Task<IEnumerable<DataCategory>> GetByDataSourceIdAsync(Guid dataSourceId);
        Task<IEnumerable<DataCategory>> GetByIdsAsync(IEnumerable<Guid> ids);
    }
} 