@echo off
title SQL Server Diagnosis and Fix

echo ==========================================
echo   SQL Server 诊断和修复工具
echo ==========================================
echo.

echo [1] 检查SQL Server安装状态...
echo.

REM 检查SQL Server安装目录
if exist "C:\Program Files\Microsoft SQL Server" (
    echo [SUCCESS] SQL Server安装目录存在
    dir "C:\Program Files\Microsoft SQL Server" | findstr /i "160"
    if %errorlevel% equ 0 (
        echo [SUCCESS] SQL Server 2022 (版本160) 已安装
    ) else (
        echo [WARNING] 未找到SQL Server 2022版本
    )
) else (
    echo [ERROR] SQL Server安装目录不存在
    goto :reinstall_needed
)

echo.
echo [2] 检查SQL Server服务...
echo.

REM 检查服务状态
sc query "MSSQL$SQLEXPRESS" >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] SQL Server Express服务已注册
    sc query "MSSQL$SQLEXPRESS" | findstr "STATE"
) else (
    echo [ERROR] SQL Server Express服务未注册
    goto :service_issue
)

echo.
echo [3] 尝试启动SQL Server服务...
echo.

REM 尝试启动服务
sc start "MSSQL$SQLEXPRESS" >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] SQL Server Express服务启动成功
) else (
    echo [WARNING] 服务启动失败，尝试其他方法...
    
    REM 尝试使用net start
    net start "SQL Server (SQLEXPRESS)" >nul 2>&1
    if %errorlevel% equ 0 (
        echo [SUCCESS] 使用net start启动成功
    ) else (
        echo [ERROR] 服务启动失败
        goto :service_issue
    )
)

echo.
echo [4] 检查SQL Server Browser服务...
echo.

sc query "SQLBrowser" >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] SQL Server Browser服务已注册
    sc start "SQLBrowser" >nul 2>&1
    if %errorlevel% equ 0 (
        echo [SUCCESS] SQL Server Browser服务启动成功
    ) else (
        echo [INFO] SQL Server Browser可能已在运行
    )
) else (
    echo [WARNING] SQL Server Browser服务未找到
)

echo.
echo [5] 测试数据库连接...
echo.

REM 查找sqlcmd
set SQLCMD_PATH=
if exist "C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\SQLCMD.EXE" (
    set SQLCMD_PATH=C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\SQLCMD.EXE
) else if exist "C:\Program Files\Microsoft SQL Server\160\Tools\Binn\SQLCMD.EXE" (
    set SQLCMD_PATH=C:\Program Files\Microsoft SQL Server\160\Tools\Binn\SQLCMD.EXE
) else (
    echo [WARNING] 未找到SQLCMD工具
    goto :no_sqlcmd
)

echo 使用SQLCMD测试连接: %SQLCMD_PATH%
echo.

REM 测试连接
echo 测试连接到 localhost\SQLEXPRESS...
"%SQLCMD_PATH%" -S "localhost\SQLEXPRESS" -E -Q "SELECT @@VERSION" -W
if %errorlevel% equ 0 (
    echo [SUCCESS] 连接到localhost\SQLEXPRESS成功!
    set SQL_INSTANCE=localhost\SQLEXPRESS
    goto :connection_success
)

echo 测试连接到 .\SQLEXPRESS...
"%SQLCMD_PATH%" -S ".\SQLEXPRESS" -E -Q "SELECT @@VERSION" -W
if %errorlevel% equ 0 (
    echo [SUCCESS] 连接到.\SQLEXPRESS成功!
    set SQL_INSTANCE=.\SQLEXPRESS
    goto :connection_success
)

echo 测试连接到 (local)\SQLEXPRESS...
"%SQLCMD_PATH%" -S "(local)\SQLEXPRESS" -E -Q "SELECT @@VERSION" -W
if %errorlevel% equ 0 (
    echo [SUCCESS] 连接到(local)\SQLEXPRESS成功!
    set SQL_INSTANCE=(local)\SQLEXPRESS
    goto :connection_success
)

goto :connection_failed

:connection_success
echo.
echo [6] 创建项目数据库...
echo.

"%SQLCMD_PATH%" -S "%SQL_INSTANCE%" -E -Q "IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'ProductionDataVisualizationDb') CREATE DATABASE ProductionDataVisualizationDb"
if %errorlevel% equ 0 (
    echo [SUCCESS] 项目数据库创建/验证成功
) else (
    echo [WARNING] 数据库创建失败
)

echo.
echo ==========================================
echo   修复完成!
echo ==========================================
echo.
echo SQL Server状态: 正常运行
echo 连接实例: %SQL_INSTANCE%
echo 项目数据库: ProductionDataVisualizationDb
echo.
echo 现在可以启动后端API了!
echo.
goto :end

:service_issue
echo.
echo ==========================================
echo   服务问题诊断
echo ==========================================
echo.
echo SQL Server服务无法启动，可能的原因:
echo 1. 服务配置问题
echo 2. 端口冲突
echo 3. 权限问题
echo 4. 安装不完整
echo.
echo 建议解决方案:
echo 1. 以管理员身份运行此脚本
echo 2. 重新配置SQL Server
echo 3. 检查Windows事件日志
echo 4. 重新安装SQL Server Express
echo.
goto :end

:connection_failed
echo.
echo ==========================================
echo   连接失败诊断
echo ==========================================
echo.
echo 无法连接到SQL Server，可能的原因:
echo 1. SQL Server服务未运行
echo 2. 网络协议未启用
echo 3. 防火墙阻止连接
echo 4. 实例名称不正确
echo.
echo 建议解决方案:
echo 1. 检查SQL Server Configuration Manager
echo 2. 启用TCP/IP协议
echo 3. 启用Named Pipes协议
echo 4. 检查防火墙设置
echo.
goto :end

:no_sqlcmd
echo.
echo ==========================================
echo   SQLCMD工具缺失
echo ==========================================
echo.
echo 未找到SQLCMD命令行工具。
echo 请安装SQL Server命令行工具或SQL Server Management Studio。
echo.
goto :end

:reinstall_needed
echo.
echo ==========================================
echo   需要重新安装
echo ==========================================
echo.
echo SQL Server未正确安装。
echo 请下载并安装SQL Server 2022 Express Edition。
echo.

:end
echo.
echo 按任意键退出...
pause > nul
