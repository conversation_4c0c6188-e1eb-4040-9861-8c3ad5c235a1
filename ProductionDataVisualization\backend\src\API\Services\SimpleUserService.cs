using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace API.Services
{
    public class SimpleUser
    {
        public string Id { get; set; }
        public string Username { get; set; }
        public string Email { get; set; }
        public string PasswordHash { get; set; }
        public string FullName { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginTime { get; set; }
        public List<string> Roles { get; set; } = new List<string>();
        public List<string> Permissions { get; set; } = new List<string>();
    }

    public class SimpleUserService
    {
        private readonly string _dataFilePath;
        private readonly ILogger<SimpleUserService> _logger;

        public SimpleUserService(ILogger<SimpleUserService> logger)
        {
            _logger = logger;
            _dataFilePath = Path.Combine(Directory.GetCurrentDirectory(), "users.json");
            InitializeDefaultUsers();
        }

        private void InitializeDefaultUsers()
        {
            if (!File.Exists(_dataFilePath))
            {
                var defaultUsers = new List<SimpleUser>
                {
                    new SimpleUser
                    {
                        Id = Guid.NewGuid().ToString(),
                        Username = "admin",
                        Email = "<EMAIL>",
                        PasswordHash = "admin123", // 在实际应用中应该加密
                        FullName = "系统管理员",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        Roles = new List<string> { "Admin" },
                        Permissions = new List<string> { "ViewUsers", "CreateUser", "EditUser", "DeleteUser", "ViewData", "ViewCharts", "ViewDashboards" }
                    },
                    new SimpleUser
                    {
                        Id = Guid.NewGuid().ToString(),
                        Username = "testuser",
                        Email = "<EMAIL>",
                        PasswordHash = "password123", // 在实际应用中应该加密
                        FullName = "测试用户",
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        Roles = new List<string> { "User" },
                        Permissions = new List<string> { "ViewData", "ViewCharts", "ViewDashboards", "ViewUsers" }
                    }
                };

                SaveUsers(defaultUsers);
                _logger.LogInformation("初始化默认用户完成");
            }
        }

        public async Task<List<SimpleUser>> GetAllUsersAsync()
        {
            try
            {
                if (!File.Exists(_dataFilePath))
                {
                    return new List<SimpleUser>();
                }

                var json = await File.ReadAllTextAsync(_dataFilePath);
                var users = JsonSerializer.Deserialize<List<SimpleUser>>(json) ?? new List<SimpleUser>();
                _logger.LogInformation($"获取到 {users.Count} 个用户");
                return users;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户列表失败");
                return new List<SimpleUser>();
            }
        }

        public async Task<SimpleUser> GetUserByUsernameAsync(string username)
        {
            var users = await GetAllUsersAsync();
            return users.FirstOrDefault(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
        }

        public async Task<SimpleUser> GetUserByEmailAsync(string email)
        {
            var users = await GetAllUsersAsync();
            return users.FirstOrDefault(u => u.Email.Equals(email, StringComparison.OrdinalIgnoreCase));
        }

        public async Task<SimpleUser> CreateUserAsync(string username, string email, string password, string fullName)
        {
            try
            {
                var users = await GetAllUsersAsync();

                // 检查用户名是否已存在
                if (users.Any(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase)))
                {
                    throw new InvalidOperationException("用户名已存在");
                }

                // 检查邮箱是否已存在
                if (users.Any(u => u.Email.Equals(email, StringComparison.OrdinalIgnoreCase)))
                {
                    throw new InvalidOperationException("邮箱已存在");
                }

                var newUser = new SimpleUser
                {
                    Id = Guid.NewGuid().ToString(),
                    Username = username,
                    Email = email,
                    PasswordHash = password, // 在实际应用中应该加密
                    FullName = fullName,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    Roles = new List<string> { "User" },
                    Permissions = new List<string> { "ViewData", "ViewCharts", "ViewDashboards", "ViewUsers" }
                };

                users.Add(newUser);
                SaveUsers(users);

                _logger.LogInformation($"用户创建成功: {username}");
                return newUser;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"创建用户失败: {username}");
                throw;
            }
        }

        public async Task<bool> ValidateUserAsync(string usernameOrEmail, string password)
        {
            var users = await GetAllUsersAsync();
            var user = users.FirstOrDefault(u => 
                (u.Username.Equals(usernameOrEmail, StringComparison.OrdinalIgnoreCase) ||
                 u.Email.Equals(usernameOrEmail, StringComparison.OrdinalIgnoreCase)) &&
                u.PasswordHash == password && u.IsActive);

            if (user != null)
            {
                user.LastLoginTime = DateTime.UtcNow;
                SaveUsers(users);
                return true;
            }

            return false;
        }

        private void SaveUsers(List<SimpleUser> users)
        {
            try
            {
                var json = JsonSerializer.Serialize(users, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_dataFilePath, json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存用户数据失败");
                throw;
            }
        }
    }
}
