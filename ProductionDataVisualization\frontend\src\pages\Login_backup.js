import React, { useEffect, useRef, useState } from 'react';
import { Typography, Card, Row, Col, Statistic, Progress, Space, Divider } from 'antd';
import { useNavigate, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Bar<PERSON>hartOutlined,
  LineChartOutlined,
  DashboardOutlined,
  DatabaseOutlined,
  MonitorOutlined,
  SafetyOutlined,
  ThunderboltOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import * as THREE from 'three';
import LoginForm from '../components/auth/LoginForm';
import authService from '../services/authService';

const { Title, Text, Paragraph } = Typography;

const Login = () => {
  const navigate = useNavigate();
  const threeContainerRef = useRef(null);
  const [isLoaded, setIsLoaded] = useState(false);
  
  useEffect(() => {
    // 如果用户已登录，重定向到首页
    if (authService.isAuthenticated()) {
      navigate('/');
    }
  }, [navigate]);

  // Three.js 背景动画
  useEffect(() => {
    if (!threeContainerRef.current) return;

    // 创建场景、相机和渲染器
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    threeContainerRef.current.appendChild(renderer.domElement);
    
    // 创建粒子系统
    const particlesGeometry = new THREE.BufferGeometry();
    const particlesCount = 2000;
    const posArray = new Float32Array(particlesCount * 3);
    
    for (let i = 0; i < particlesCount * 3; i++) {
      posArray[i] = (Math.random() - 0.5) * 15;
    }
    
    particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
    
    const particlesMaterial = new THREE.PointsMaterial({
      size: 0.01,
      color: 0x666666,
      transparent: true,
      opacity: 0.5,
      sizeAttenuation: true
    });
    
    const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
    scene.add(particlesMesh);
    
    // 设置相机位置
    camera.position.z = 3;
    
    // 创建连接线
    const lineMaterial = new THREE.LineBasicMaterial({ 
      color: 0x999999,
      opacity: 0.3,
      transparent: true
    });
    
    const lines = [];
    const lineCount = 30;
    
    for (let i = 0; i < lineCount; i++) {
      const lineGeometry = new THREE.BufferGeometry();
      const points = [];
      
      // 创建随机线条
      const startPoint = new THREE.Vector3(
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10
      );
      
      const endPoint = new THREE.Vector3(
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10
      );
      
      points.push(startPoint);
      points.push(endPoint);
      
      lineGeometry.setFromPoints(points);
      const line = new THREE.Line(lineGeometry, lineMaterial);
      scene.add(line);
      lines.push({
        line,
        startPoint,
        endPoint,
        speed: Math.random() * 0.01 + 0.002
      });
    }
    
    // 动画循环
    const clock = new THREE.Clock();
    
    const animate = () => {
      const elapsedTime = clock.getElapsedTime();
      
      // 旋转粒子系统
      particlesMesh.rotation.x = elapsedTime * 0.03;
      particlesMesh.rotation.y = elapsedTime * 0.02;
      
      // 更新线条
      lines.forEach(lineObj => {
        lineObj.line.rotation.x = elapsedTime * lineObj.speed;
        lineObj.line.rotation.y = elapsedTime * lineObj.speed * 0.8;
      });
      
      // 渲染场景
      renderer.render(scene, camera);
      
      // 请求下一帧
      requestAnimationFrame(animate);
    };
    
    animate();
    
    // 处理窗口大小变化
    const handleResize = () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    };
    
    window.addEventListener('resize', handleResize);
    
    // 鼠标移动效果
    const handleMouseMove = (event) => {
      // 将鼠标位置归一化为 -1 到 1 之间
      const mouseX = (event.clientX / window.innerWidth) * 2 - 1;
      const mouseY = -(event.clientY / window.innerHeight) * 2 + 1;
      
      // 缓慢移动相机
      camera.position.x = mouseX * 0.2;
      camera.position.y = mouseY * 0.2;
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    
    // 设置加载完成状态
    setIsLoaded(true);
    
    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('mousemove', handleMouseMove);
      
      // 清理Three.js资源
      scene.traverse((object) => {
        if (object.geometry) object.geometry.dispose();
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose());
          } else {
            object.material.dispose();
          }
        }
      });
      
      renderer.dispose();
      
      if (threeContainerRef.current) {
        threeContainerRef.current.innerHTML = '';
      }
    };
  }, []);

  // 页面容器样式
  const containerStyle = {
    display: 'flex',
    flexDirection: 'column',
    minHeight: '100vh',
    width: '100%',
    position: 'relative',
    overflow: 'hidden',
    background: 'linear-gradient(135deg, #f5f5f5 0%, #ffffff 100%)',
    fontFamily: 'Inter, sans-serif',
  };
  
  // Three.js容器样式
  const threeContainerStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    zIndex: 0
  };
  
  // 导航栏样式
  const navbarStyle = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '20px 40px',
    position: 'relative',
    zIndex: 10
  };

  // Logo样式
  const logoStyle = {
    fontWeight: 700,
    fontSize: '24px',
    color: '#333',
    textDecoration: 'none',
    display: 'flex',
    alignItems: 'center'
  };

  // 导航链接样式
  const navLinksStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '30px'
  };

  // 导航链接项样式
  const navLinkItemStyle = {
    color: '#333',
    fontWeight: 500,
    fontSize: '15px',
    textDecoration: 'none'
  };

  // 按钮样式
  const buttonStyle = {
    borderRadius: '8px',
    fontWeight: 500,
    marginLeft: '10px'
  };

  // 内容容器样式
  const contentStyle = {
    position: 'relative',
    zIndex: 1,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    padding: '20px',
    flex: 1
  };
  
  // 主内容区域样式
  const mainContentStyle = {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    maxWidth: '1200px',
    padding: '0 20px',
    marginTop: '20px'
  };
  
  // 左侧内容样式
  const leftContentStyle = {
    flex: '1',
    maxWidth: '600px',
    color: '#333',
    padding: '40px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center'
  };
  
  // 右侧表单容器样式
  const rightContentStyle = {
    flex: '1',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '40px'
  };

  // 搜索框容器样式
  const searchContainerStyle = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    width: '100%',
    maxWidth: '600px',
    marginTop: '30px'
  };

  // 搜索框样式
  const searchBoxStyle = {
    display: 'flex',
    width: '100%',
    marginTop: '20px'
  };

  // 统计数据容器样式
  const statsContainerStyle = {
    display: 'flex',
    justifyContent: 'space-around',
    width: '100%',
    maxWidth: '1000px',
    marginTop: '80px',
    marginBottom: '40px'
  };

  // 单个统计项样式
  const statItemStyle = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center'
  };

  // 文本内容动画变体
  const textVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i) => ({ 
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.2 + i * 0.1,
        duration: 0.8,
        ease: "easeOut"
      }
    })
  };

  // 导航栏动画变体
  const navbarVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  // 统计数据动画变体
  const statsVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: (i) => ({ 
      opacity: 1, 
      y: 0,
      transition: {
        delay: 0.6 + i * 0.1,
        duration: 0.8,
        ease: "easeOut"
      }
    })
  };

  return (
    <div style={containerStyle} className="login-page">
      {/* Three.js背景 */}
      <div ref={threeContainerRef} style={threeContainerStyle}></div>
      
      {/* 导航栏 */}
      <motion.nav 
        style={navbarStyle}
        variants={navbarVariants}
        initial="hidden"
        animate="visible"
      >
        <Link to="/" style={logoStyle}>
          <span style={{ color: '#333' }}>Assan</span>
          <span style={{ color: '#666', fontWeight: 400 }}>*</span>
          <span style={{ fontSize: '16px', marginLeft: '2px', color: '#666', fontWeight: 400 }}>Jobs</span>
        </Link>
        
        <div style={navLinksStyle}>
          <Link to="/" style={navLinkItemStyle}>Home</Link>
          <Link to="/jobs" style={navLinkItemStyle}>Jobs Listing</Link>
          <Link to="/pages" style={navLinkItemStyle}>Pages</Link>
          <Link to="/upload" style={navLinkItemStyle}>Upload Resume</Link>
          <Link to="/language" style={navLinkItemStyle}>Eng</Link>
          
          <Button type="default" style={buttonStyle}>Post Job</Button>
          <Button type="primary" style={buttonStyle}>Sign Up</Button>
        </div>
      </motion.nav>
      
      {/* 主要内容 */}
      <div style={contentStyle}>
        {/* 搜索区域 */}
        <motion.div
          style={searchContainerStyle}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.8 }}
        >
          <div style={{ 
            backgroundColor: '#EEF2FF', 
            padding: '8px 16px', 
            borderRadius: '20px', 
            fontSize: '14px',
            color: '#4F46E5',
            fontWeight: 500
          }}>
            Explore more than 70K Jobs
          </div>
          
          <motion.div
            custom={0}
            variants={textVariants}
            initial="hidden"
            animate="visible"
          >
            <h1 style={{
              fontSize: '42px',
              fontWeight: '700',
              marginBottom: '16px',
              marginTop: '16px',
              color: '#333',
            }}>Get Job with your</h1>
          </motion.div>
          
          <motion.div
            custom={1}
            variants={textVariants}
            initial="hidden"
            animate="visible"
          >
            <h1 style={{
              fontSize: '42px',
              fontWeight: '700',
              marginBottom: '24px',
              color: '#333',
            }}>Interests and Abilities</h1>
          </motion.div>
          
          <div style={searchBoxStyle}>
            <Input 
              size="large" 
              placeholder="Enter Job Title or Keywords" 
              prefix={<SearchOutlined />} 
              style={{ 
                borderRadius: '8px 0 0 8px', 
                height: '50px',
                borderRight: 'none'
              }}
            />
            <Select
              defaultValue="location"
              style={{ width: 200, borderRadius: 0, height: '50px' }}
              size="large"
              dropdownMatchSelectWidth={false}
            >
              <Option value="location">Choose location</Option>
              <Option value="newyork">New York</Option>
              <Option value="london">London</Option>
              <Option value="tokyo">Tokyo</Option>
            </Select>
            <Button 
              type="primary" 
              size="large" 
              style={{ 
                borderRadius: '0 8px 8px 0', 
                height: '50px',
                width: '150px',
                background: '#4CAF50'
              }}
            >
              Find Job
            </Button>
          </div>
        </motion.div>
        
        <div style={mainContentStyle} className="login-content">
          <div className="login-container" style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            flexWrap: 'wrap'
          }}>
            {/* 左侧内容 */}
            <div style={leftContentStyle} className="login-left-content">
              <motion.div
                custom={2}
                variants={textVariants}
                initial="hidden"
                animate="visible"
              >
                <p style={{
                  fontSize: '18px',
                  lineHeight: '1.6',
                  color: '#666',
                  marginBottom: '32px',
                  maxWidth: '480px'
                }}>
                  Explore more than 70K Jobs opportunities and find the right match for your skills and career goals.
                </p>
              </motion.div>
              
              <motion.div
                custom={3}
                variants={textVariants}
                initial="hidden"
                animate="visible"
              >
                <div style={{
                  display: 'flex',
                  gap: '24px',
                  marginTop: '16px'
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <div style={{
                      width: '40px',
                      height: '40px',
                      borderRadius: '50%',
                      background: 'rgba(51, 51, 51, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: '12px'
                    }}>
                      <span style={{ fontSize: '20px' }}>📊</span>
                    </div>
                    <div>
                      <h3 style={{
                        fontSize: '16px',
                        fontWeight: '600',
                        color: '#333',
                        margin: '0'
                      }}>Job Search</h3>
                      <p style={{
                        fontSize: '14px',
                        color: '#666',
                        margin: '4px 0 0 0'
                      }}>Find your perfect job match</p>
                    </div>
                  </div>
                  
                  <div style={{
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <div style={{
                      width: '40px',
                      height: '40px',
                      borderRadius: '50%',
                      background: 'rgba(51, 51, 51, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: '12px'
                    }}>
                      <span style={{ fontSize: '20px' }}>👤</span>
                    </div>
                    <div>
                      <h3 style={{
                        fontSize: '16px',
                        fontWeight: '600',
                        color: '#333',
                        margin: '0'
                      }}>Profile Builder</h3>
                      <p style={{
                        fontSize: '14px',
                        color: '#666',
                        margin: '4px 0 0 0'
                      }}>Create your professional profile</p>
                    </div>
                  </div>
                </div>
              </motion.div>
              
              <motion.div
                custom={4}
                variants={textVariants}
                initial="hidden"
                animate="visible"
              >
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginTop: '40px'
                }}>
                  <p style={{
                    fontSize: '14px',
                    color: '#666',
                    margin: '0',
                    marginRight: '20px'
                  }}>
                    Trusted by companies like
                  </p>
                  <div style={{
                    display: 'flex',
                    gap: '20px',
                    alignItems: 'center'
                  }}>
                    <img src="https://via.placeholder.com/80x30" alt="Company 1" style={{ opacity: 0.7 }} />
                    <img src="https://via.placeholder.com/80x30" alt="Company 2" style={{ opacity: 0.7 }} />
                    <img src="https://via.placeholder.com/80x30" alt="Company 3" style={{ opacity: 0.7 }} />
                  </div>
                </div>
              </motion.div>
            </div>
            
            {/* 右侧登录表单 */}
            <div style={rightContentStyle} className="login-right-content">
              <LoginForm />
            </div>
          </div>
        </div>
        
        {/* 统计数据 */}
        <div style={statsContainerStyle}>
          <motion.div 
            style={statItemStyle}
            custom={0}
            variants={statsVariants}
            initial="hidden"
            animate="visible"
          >
            <h2 style={{ fontSize: '36px', fontWeight: '700', margin: '0', color: '#333' }}>2,730+</h2>
            <p style={{ color: '#666', margin: '8px 0 0 0' }}>Jobs opportunities</p>
          </motion.div>
          
          <motion.div 
            style={statItemStyle}
            custom={1}
            variants={statsVariants}
            initial="hidden"
            animate="visible"
          >
            <h2 style={{ fontSize: '36px', fontWeight: '700', margin: '0', color: '#333' }}>440+</h2>
            <p style={{ color: '#666', margin: '8px 0 0 0' }}>Top companies</p>
          </motion.div>
          
          <motion.div 
            style={statItemStyle}
            custom={2}
            variants={statsVariants}
            initial="hidden"
            animate="visible"
          >
            <h2 style={{ fontSize: '36px', fontWeight: '700', margin: '0', color: '#333' }}>150K</h2>
            <p style={{ color: '#666', margin: '8px 0 0 0' }}>Active users</p>
          </motion.div>
        </div>
      </div>
      
      {/* 版权信息 */}
      <div style={{
        position: 'relative',
        bottom: '20px',
        left: '0',
        width: '100%',
        textAlign: 'center',
        color: '#999',
        fontSize: '14px',
        padding: '20px 0',
        zIndex: 1
      }}>
        © {new Date().getFullYear()} Assan Jobs. All rights reserved.
      </div>
      
      {/* 响应式样式 */}
      <style jsx="true">{`
        @media (max-width: 1024px) {
          .login-container {
            flex-direction: column !important;
          }
          
          .login-left-content {
            padding: 20px !important;
            text-align: center;
            align-items: center;
          }
          
          .login-left-content h1 {
            font-size: 32px !important;
          }
          
          .login-right-content {
            padding: 20px !important;
          }
        }
      `}</style>
    </div>
  );
};

export default Login; 