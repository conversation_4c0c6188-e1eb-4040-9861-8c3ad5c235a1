import React, { useState } from 'react';
import { Layout } from 'antd';
import { motion } from 'framer-motion';
import CleanAssanNavbar from './CleanAssanNavbar';

const { Content } = Layout;

const CleanAssanLayout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);

  const handleToggle = () => {
    setCollapsed(!collapsed);
  };

  return (
    <Layout style={{ minHeight: '100vh', background: '#fafbfc' }}>
      <CleanAssanNavbar collapsed={collapsed} onToggle={handleToggle} />
      
      <Layout style={{ marginTop: '80px' }}>
        <Content className="clean-assan-content">
          <motion.div
            className="content-wrapper"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
          >
            {children}
          </motion.div>
        </Content>
      </Layout>

      <style jsx="true">{`
        .clean-assan-content {
          background: #fafbfc;
          min-height: calc(100vh - 80px);
          padding: 0;
        }

        .content-wrapper {
          max-width: 1200px;
          margin: 0 auto;
          padding: 40px 24px;
        }

        @media (max-width: 768px) {
          .content-wrapper {
            padding: 24px 16px;
          }
        }
      `}</style>
    </Layout>
  );
};

export default CleanAssanLayout;
