using System;
using Domain.Common;

namespace Domain.VisualizationAggregate
{
    /// <summary>
    /// 仪表盘-图表关联实体
    /// </summary>
    public class DashboardChart : Entity
    {
        public Guid DashboardId { get; private set; }
        public Guid ChartId { get; private set; }
        public int PositionX { get; private set; }
        public int PositionY { get; private set; }
        public int Width { get; private set; }
        public int Height { get; private set; }

        public Dashboard Dashboard { get; private set; }
        public Chart Chart { get; private set; }

        // 防止无参构造函数被外部调用
        private DashboardChart() { }

        public DashboardChart(Dashboard dashboard, Chart chart, int positionX, int positionY, int width, int height)
        {
            if (dashboard == null)
                throw new ArgumentNullException(nameof(dashboard));
            
            if (chart == null)
                throw new ArgumentNullException(nameof(chart));

            if (width <= 0)
                throw new ArgumentException("宽度必须大于0", nameof(width));

            if (height <= 0)
                throw new ArgumentException("高度必须大于0", nameof(height));

            DashboardId = dashboard.Id;
            ChartId = chart.Id;
            Dashboard = dashboard;
            Chart = chart;
            PositionX = positionX;
            PositionY = positionY;
            Width = width;
            Height = height;
        }

        public void UpdatePosition(int positionX, int positionY, int width, int height)
        {
            if (width <= 0)
                throw new ArgumentException("宽度必须大于0", nameof(width));

            if (height <= 0)
                throw new ArgumentException("高度必须大于0", nameof(height));

            PositionX = positionX;
            PositionY = positionY;
            Width = width;
            Height = height;
            ModifiedAt = DateTime.UtcNow;
        }
    }
} 