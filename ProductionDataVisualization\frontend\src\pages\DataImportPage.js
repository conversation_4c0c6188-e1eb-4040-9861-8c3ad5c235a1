import React, { useState, useCallback, useEffect } from 'react';
import { Card, Steps, Button, Space, Typography, Alert, notification, Row, Col, Table, Modal, Radio } from 'antd';
import {
  UploadOutlined,
  FileSearchOutlined,
  CheckCircleOutlined,
  DatabaseOutlined,
  Bar<PERSON><PERSON>Outlined,
  EyeOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import CleanAssanLayout from '../components/layout/CleanAssanLayout';

// 导入数据导入组件
import FileUploader from '../components/dataImport/FileUploader';
import FileParser from '../components/dataImport/FileParser';
import DataValidator from '../components/dataImport/DataValidator';
import LargeFileProcessor from '../components/dataImport/LargeFileProcessor';
import ImportMonitor from '../components/dataImport/ImportMonitor';

const { Title, Text } = Typography;
const { Step } = Steps;

const DataImportPage = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [selectedFile, setSelectedFile] = useState(null);
  const [parsedData, setParsedData] = useState(null);
  const [validationResults, setValidationResults] = useState([]);
  const [importTasks, setImportTasks] = useState([]);
  const [processing, setProcessing] = useState(false);
  const [importedData, setImportedData] = useState(null);
  const [importedColumns, setImportedColumns] = useState([]);
  const [showDataPreview, setShowDataPreview] = useState(false);
  const [fileAnalysis, setFileAnalysis] = useState(null);
  const [databaseHealth, setDatabaseHealth] = useState(null);

  // 检查数据库健康状态
  const checkDatabaseHealth = useCallback(async () => {
    try {
      const response = await fetch('http://localhost:5000/api/database/health');
      if (response.ok) {
        const health = await response.json();
        setDatabaseHealth(health);
        console.log('数据库健康状态:', health);

        if (health.status !== 'healthy') {
          notification.warning({
            message: '数据库状态异常',
            description: health.message,
            duration: 8
          });
        }
      }
    } catch (error) {
      console.error('检查数据库健康状态失败:', error);
    }
  }, []);

  // 手动恢复数据库
  const recoverDatabase = useCallback(async () => {
    try {
      const response = await fetch('http://localhost:5000/api/database/recover', {
        method: 'POST'
      });

      if (response.ok) {
        notification.success({
          message: '数据库恢复成功',
          description: '数据库表结构已恢复，可以正常使用导入功能'
        });
        await checkDatabaseHealth(); // 重新检查状态
        await loadImportTasks(); // 重新加载任务
      } else {
        const error = await response.json();
        notification.error({
          message: '数据库恢复失败',
          description: error.message
        });
      }
    } catch (error) {
      notification.error({
        message: '数据库恢复失败',
        description: error.message
      });
    }
  }, [checkDatabaseHealth]);

  // 加载导入任务列表
  const loadImportTasks = useCallback(async () => {
    try {
      const response = await fetch('http://localhost:5000/api/data-import/tasks');
      if (response.ok) {
        const result = await response.json();
        const tasks = result.items || [];
        console.log('加载的导入任务:', tasks);

        // 转换后端数据格式为前端格式
        const formattedTasks = tasks.map(task => ({
          id: task.id,
          fileName: task.fileName,
          fileSize: task.fileSize,
          totalRows: task.totalRows,
          processedRows: task.processedRows,
          status: task.status.toLowerCase(), // 转换为小写
          progress: task.progress || (task.status === 'Completed' ? 100 : 0),
          startTime: task.startTime ? new Date(task.startTime) : new Date(task.createTime),
          endTime: task.endTime ? new Date(task.endTime) : null,
          createTime: new Date(task.createTime),
          errors: task.errorMessage ? [task.errorMessage] : []
        }));

        setImportTasks(formattedTasks);
      } else {
        console.error('加载导入任务失败:', response.status);
      }
    } catch (error) {
      console.error('加载导入任务失败:', error);
    }
  }, []);

  // 组件加载时获取导入任务和检查数据库健康状态
  useEffect(() => {
    checkDatabaseHealth();
    loadImportTasks();
  }, [checkDatabaseHealth, loadImportTasks]);

  // 步骤配置
  const steps = [
    {
      title: '文件上传',
      description: '选择并上传数据文件',
      icon: <UploadOutlined />,
      content: 'upload'
    },
    {
      title: '数据解析',
      description: '解析文件内容和结构',
      icon: <FileSearchOutlined />,
      content: 'parse'
    },
    {
      title: '数据验证',
      description: '验证数据质量和格式',
      icon: <CheckCircleOutlined />,
      content: 'validate'
    },
    {
      title: '数据导入',
      description: '将数据导入到系统',
      icon: <DatabaseOutlined />,
      content: 'import'
    },
    {
      title: '导入监控',
      description: '监控导入进度和结果',
      icon: <BarChartOutlined />,
      content: 'monitor'
    }
  ];

  // 文件上传处理
  const handleFileUpload = useCallback(async (file, onProgress) => {
    try {
      // 模拟上传过程
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 100));
        onProgress(i);
      }
      
      setUploadedFiles(prev => [...prev, file]);
      
      notification.success({
        message: '文件上传成功',
        description: `文件 ${file.name} 已成功上传`
      });
      
    } catch (error) {
      notification.error({
        message: '文件上传失败',
        description: error.message
      });
      throw error;
    }
  }, []);

  // 文件移除处理
  const handleFileRemove = useCallback((file) => {
    setUploadedFiles(prev => prev.filter(f => f.uid !== file.uid));
    if (selectedFile && selectedFile.uid === file.uid) {
      setSelectedFile(null);
      setParsedData(null);
    }
  }, [selectedFile]);

  // 文件解析完成处理
  const handleParseComplete = useCallback((result) => {
    setParsedData(result);
    notification.success({
      message: '文件解析完成',
      description: `成功解析 ${result.data.length} 行数据，${result.headers.length} 个字段`
    });
  }, []);

  // 文件解析错误处理
  const handleParseError = useCallback((error) => {
    notification.error({
      message: '文件解析失败',
      description: error.message
    });
  }, []);

  // 数据验证完成处理
  const handleValidationComplete = useCallback((results) => {
    setValidationResults(results);
    const errors = results.filter(r => r.severity === 'error').length;
    const warnings = results.filter(r => r.severity === 'warning').length;
    
    if (errors === 0) {
      notification.success({
        message: '数据验证通过',
        description: warnings > 0 ? `发现 ${warnings} 个警告` : '数据质量良好'
      });
    } else {
      notification.warning({
        message: '数据验证发现问题',
        description: `发现 ${errors} 个错误，${warnings} 个警告`
      });
    }
  }, []);

  // 开始导入处理
  const handleStartImport = useCallback(async () => {
    if (!parsedData || !selectedFile) {
      notification.error({
        message: '导入失败',
        description: '请先完成文件上传和解析'
      });
      return;
    }

    setProcessing(true);

    try {
      // 1. 先分析文件
      const analyzeResponse = await fetch('http://localhost:5000/api/data-import/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: selectedFile.name,
          sampleData: parsedData.data.slice(0, 1) // 发送第一行作为样本
        })
      });

      if (!analyzeResponse.ok) {
        throw new Error('文件分析失败');
      }

      const analysisResult = await analyzeResponse.json();
      setFileAnalysis(analysisResult);

      // 直接导入数据，系统会自动处理数据级别的重复检测和覆盖
      await performDataImport();

    } catch (error) {
      console.error('导入失败:', error);

      // 检查是否是数据库结构缺失错误
      if (error.message && error.message.includes('DatabaseStructureMissing')) {
        notification.error({
          message: '数据库结构缺失',
          description: '检测到数据库表结构被删除，系统正在尝试自动恢复。请稍后重试，或联系管理员手动恢复数据库结构。',
          duration: 10
        });
      } else if (error.message && error.message.includes('500 Internal Server Error')) {
        notification.error({
          message: '服务器内部错误',
          description: '可能是数据库表结构问题，请检查后端日志或尝试刷新页面重试。',
          duration: 8
        });
      } else {
        notification.error({
          message: '数据导入失败',
          description: error.message || '请检查网络连接和后端服务'
        });
      }
      setProcessing(false);
    }
  }, [parsedData, selectedFile]);

  // 执行实际的数据导入（自动处理数据级别重复检测和覆盖）
  const performDataImport = useCallback(async () => {
    try {
      // 获取当前用户信息
      const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');

      // 1. 创建导入任务
      const taskResponse = await fetch('http://localhost:5000/api/data-import/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: selectedFile.name,
          fileSize: selectedFile.size,
          totalRows: parsedData.totalRows || parsedData.data.length,
          createdBy: currentUser.id || 'unknown'
        })
      });

      if (!taskResponse.ok) {
        const errorText = await taskResponse.text();
        console.error('任务创建失败响应:', errorText);
        throw new Error(`创建导入任务失败: ${taskResponse.status} ${taskResponse.statusText}`);
      }

      const taskResult = await taskResponse.json();
      const taskId = taskResult.taskId;

      // 任务创建后立即刷新任务列表，让用户看到新任务
      console.log('任务创建成功，立即刷新任务列表...');
      await loadImportTasks();

      // 2. 保存数据到数据库（极速批量处理）
      const batchSize = 100000; // 🚀 每批100000行，稳定高性能模式
      const totalRows = parsedData.data.length;
      let processedRows = 0;

      console.log(`🚀 开始极速导入数据，总行数: ${totalRows}，批次大小: ${batchSize}`);

      // 🚀 极速并行批量处理
      const totalBatches = Math.ceil(totalRows / batchSize);
      const maxConcurrentBatches = 3; // 最多3个并发批次，平衡速度和稳定性

      console.log(`⚡ 准备并行处理 ${totalBatches} 个批次，最大并发数: ${maxConcurrentBatches}`);

      for (let batchStart = 0; batchStart < totalBatches; batchStart += maxConcurrentBatches) {
        const batchEnd = Math.min(batchStart + maxConcurrentBatches, totalBatches);
        const concurrentPromises = [];

        for (let batchIndex = batchStart; batchIndex < batchEnd; batchIndex++) {
          const startRow = batchIndex * batchSize;
          const endRow = Math.min(startRow + batchSize, totalRows);
          const batch = parsedData.data.slice(startRow, endRow);
          const isFirstBatch = batchIndex === 0;

          console.log(`⚡ 准备批次 ${batchIndex + 1}/${totalBatches}，行数: ${batch.length}`);

          const batchPromise = fetch('http://localhost:5000/api/data-import/data', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              taskId: taskId,
              fileName: selectedFile.name,
              data: batch,
              batchInfo: {
                batchIndex: batchIndex,
                batchSize: batch.length,
                totalBatches: totalBatches,
                isFirstBatch: isFirstBatch,
                isLastBatch: batchIndex === totalBatches - 1
              }
            })
          });

          concurrentPromises.push(batchPromise);
        }

        // 等待当前并发批次完成
        console.log(`🚀 执行并发批次 ${batchStart + 1}-${batchEnd}...`);
        const responses = await Promise.all(concurrentPromises);

        // 检查所有响应
        for (let i = 0; i < responses.length; i++) {
          const dataResponse = responses[i];
          const batchIndex = batchStart + i;
          const startRow = batchIndex * batchSize;
          const endRow = Math.min(startRow + batchSize, totalRows);
          const batchSize_actual = endRow - startRow;

          if (!dataResponse.ok) {
            const errorText = await dataResponse.text();
            console.error(`⚠️ 批次 ${batchIndex + 1} 导入失败:`, errorText);
            throw new Error(`批次导入失败: ${dataResponse.status} ${dataResponse.statusText}`);
          }

          const batchResult = await dataResponse.json();
          processedRows += batchResult.processedRows || batchSize_actual;

          console.log(`✅ 批次 ${batchIndex + 1} 导入成功，已处理 ${processedRows}/${totalRows} 行`);
        }

        console.log(`⚡ 并发批次 ${batchStart + 1}-${batchEnd} 全部完成`);
      }

      console.log(`🎉 所有批次导入完成，总共处理 ${processedRows} 行数据`);

      // 3. 创建本地任务对象用于显示
      const newTask = {
        id: taskId,
        fileName: selectedFile.name,
        fileSize: selectedFile.size,
        totalRows: parsedData.totalRows || parsedData.data.length,
        processedRows: processedRows,
        progress: 100,
        status: 'completed',
        createTime: new Date(),
        startTime: new Date(),
        endTime: new Date(),
        errors: []
      };

      // 重新加载导入任务列表以获取最新状态
      await loadImportTasks();
      setCurrentStep(4); // 跳转到监控页面

      notification.success({
        message: '数据导入成功',
        description: `成功导入 ${processedRows} 行数据到数据库`
      });

      // 获取导入的数据用于预览
      await loadImportedData(taskId);

    } catch (error) {
      console.error('导入失败:', error);

      // 检查是否是数据库结构缺失错误
      if (error.message && error.message.includes('DatabaseStructureMissing')) {
        notification.error({
          message: '数据库结构缺失',
          description: '检测到数据库表结构被删除，系统正在尝试自动恢复。请稍后重试，或联系管理员手动恢复数据库结构。',
          duration: 10
        });
      } else if (error.message && error.message.includes('500 Internal Server Error')) {
        notification.error({
          message: '服务器内部错误',
          description: '可能是数据库表结构问题，请检查后端日志或尝试刷新页面重试。',
          duration: 8
        });
      } else {
        notification.error({
          message: '数据导入失败',
          description: error.message || '请检查网络连接和后端服务'
        });
      }
    } finally {
      setProcessing(false);
    }
  }, [parsedData, selectedFile]);



  // 加载导入的数据
  const loadImportedData = useCallback(async (taskId) => {
    try {
      // 获取列信息
      const columnsResponse = await fetch(`http://localhost:5000/api/data-import/columns/${taskId}`);
      if (columnsResponse.ok) {
        const columnsResult = await columnsResponse.json();
        setImportedColumns(columnsResult.columns);
      }

      // 获取数据
      const dataResponse = await fetch(`http://localhost:5000/api/data-import/data/${taskId}`);
      if (dataResponse.ok) {
        const dataResult = await dataResponse.json();
        // 转换数据格式以适配Ant Design Table
        const tableData = dataResult.data.map((row, index) => ({
          key: index,
          rowIndex: row.rowIndex,
          ...row.data
        }));
        setImportedData(tableData);
        setShowDataPreview(true);
      }
    } catch (error) {
      console.error('加载导入数据失败:', error);
    }
  }, []);

  // 大文件处理完成
  const handleLargeFileComplete = useCallback((totalRows) => {
    notification.success({
      message: '大文件处理完成',
      description: `成功处理 ${totalRows} 行数据`
    });
  }, []);

  // 监听步骤变化，当进入监控页面时自动刷新任务列表
  useEffect(() => {
    if (currentStep === 3) { // 监控页面是索引3（第4步）
      console.log('检测到进入监控页面，自动刷新任务列表...');
      loadImportTasks();
    }
  }, [currentStep, loadImportTasks]);

  // 步骤导航
  const nextStep = async () => {
    if (currentStep < steps.length - 1) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);

      // 如果进入监控页面（步骤4），立即刷新任务列表
      if (newStep === 3) { // 监控页面是索引3（第4步）
        console.log('进入监控页面，刷新任务列表...');
        await loadImportTasks();
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    const stepContent = steps[currentStep].content;

    switch (stepContent) {
      case 'upload':
        return (
          <FileUploader
            onFileUpload={handleFileUpload}
            onFileRemove={handleFileRemove}
            maxFileSize={500} // 500MB
            supportedFormats={['csv', 'txt', 'xlsx', 'xls']}
            multiple={true}
            showPreview={true}
          />
        );

      case 'parse':
        return (
          <div>
            {uploadedFiles.length === 0 ? (
              <Alert
                message="请先上传文件"
                description="在进行数据解析之前，请先上传需要处理的数据文件。"
                type="info"
                showIcon
              />
            ) : (
              <Space direction="vertical" style={{ width: '100%' }} size="large">
                <Card size="small" title="选择要解析的文件">
                  <Space wrap>
                    {uploadedFiles.map(file => (
                      <Button
                        key={file.uid}
                        type={selectedFile?.uid === file.uid ? 'primary' : 'default'}
                        onClick={() => setSelectedFile(file)}
                      >
                        {file.name}
                      </Button>
                    ))}
                  </Space>
                </Card>
                
                {selectedFile && (
                  <FileParser
                    file={selectedFile}
                    onParseComplete={handleParseComplete}
                    onParseError={handleParseError}
                  />
                )}
              </Space>
            )}
          </div>
        );

      case 'validate':
        return (
          <div>
            {!parsedData ? (
              <Alert
                message="请先完成数据解析"
                description="在进行数据验证之前，请先完成文件解析步骤。"
                type="info"
                showIcon
              />
            ) : (
              <DataValidator
                data={parsedData.data}
                headers={parsedData.headers}
                dataTypes={parsedData.dataTypes}
                onValidationComplete={handleValidationComplete}
                onDataClean={(cleanedData) => {
                  setParsedData(prev => ({ ...prev, data: cleanedData }));
                  notification.success({
                    message: '数据清洗完成',
                    description: '数据已按照清洗规则进行处理'
                  });
                }}
              />
            )}
          </div>
        );

      case 'import':
        return (
          <div>
            {!parsedData ? (
              <Alert
                message="请先完成前面的步骤"
                description="在开始导入之前，请先完成文件上传、解析和验证步骤。"
                type="info"
                showIcon
              />
            ) : (
              <Space direction="vertical" style={{ width: '100%' }} size="large">
                <Card>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Card size="small" title="导入信息">
                        <Space direction="vertical">
                          <Text><strong>文件:</strong> {selectedFile?.name}</Text>
                          <Text><strong>总行数:</strong> {parsedData.totalRows}</Text>
                          <Text><strong>字段数:</strong> {parsedData.headers.length}</Text>
                          <Text><strong>验证结果:</strong> {validationResults.length === 0 ? '通过' : `${validationResults.length} 个问题`}</Text>
                        </Space>
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card size="small" title="导入选项">
                        <Space direction="vertical">
                          <Button type="primary" size="large" onClick={handleStartImport}>
                            开始标准导入
                          </Button>
                          <Text type="secondary">适用于中小型文件（&lt;100MB）</Text>
                        </Space>
                      </Card>
                    </Col>
                  </Row>
                </Card>

                {selectedFile && selectedFile.size > 100 * 1024 * 1024 && (
                  <LargeFileProcessor
                    file={selectedFile}
                    chunkSize={2 * 1024 * 1024} // 2MB chunks
                    onComplete={handleLargeFileComplete}
                    onError={(error) => {
                      notification.error({
                        message: '大文件处理失败',
                        description: error
                      });
                    }}
                  />
                )}
              </Space>
            )}
          </div>
        );

      case 'monitor':
        return (
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            {/* 添加刷新按钮 */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography.Title level={4}>导入任务监控</Typography.Title>
              <Button
                onClick={loadImportTasks}
                loading={processing}
                icon={<DatabaseOutlined />}
              >
                刷新任务列表
              </Button>
            </div>

            <ImportMonitor
              importTasks={importTasks}
              onTaskCancel={(taskId) => {
                setImportTasks(prev => prev.map(t =>
                  t.id === taskId ? { ...t, status: 'cancelled' } : t
                ));
              }}
              onTaskRetry={(taskId) => {
                setImportTasks(prev => prev.map(t =>
                  t.id === taskId ? { ...t, status: 'pending', progress: 0 } : t
                ));
              }}
              onTaskDelete={async (taskId) => {
                try {
                  const response = await fetch(`http://localhost:5000/api/data-import/tasks/${taskId}`, {
                    method: 'DELETE'
                  });

                  if (response.ok) {
                    // 从本地状态中移除任务
                    setImportTasks(prev => prev.filter(t => t.id !== taskId));
                    notification.success({
                      message: '删除成功',
                      description: '导入任务已成功删除'
                    });
                  } else {
                    const error = await response.json();
                    notification.error({
                      message: '删除失败',
                      description: error.message || '删除任务时发生错误'
                    });
                  }
                } catch (error) {
                  notification.error({
                    message: '删除失败',
                    description: '网络错误，请检查连接后重试'
                  });
                }
              }}
              onRefresh={loadImportTasks}
              realTimeUpdates={true}
            />

            {/* 数据预览 */}
            {showDataPreview && importedData && (
              <Card
                title={
                  <Space>
                    <EyeOutlined />
                    <span>导入数据预览</span>
                    <Text type="secondary">({importedData.length} 行数据)</Text>
                  </Space>
                }
                extra={
                  <Button
                    type="link"
                    onClick={() => setShowDataPreview(false)}
                  >
                    隐藏预览
                  </Button>
                }
              >
                <Table
                  columns={importedColumns}
                  dataSource={importedData}
                  scroll={{ x: 'max-content', y: 400 }}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`
                  }}
                  size="small"
                />
              </Card>
            )}
          </Space>
        );

      default:
        return null;
    }
  };

  return (
    <CleanAssanLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card>
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            <div>
              <Title level={2}>数据导入</Title>
              <Text type="secondary">
                支持CSV、TXT、Excel等格式的数据文件导入，提供完整的数据验证和清洗功能
              </Text>
            </div>

          {/* 步骤导航 */}
          <Steps current={currentStep} size="small">
            {steps.map((step, index) => (
              <Step
                key={index}
                title={step.title}
                description={step.description}
                icon={step.icon}
              />
            ))}
          </Steps>

          {/* 步骤内容 */}
          <div style={{ minHeight: '500px' }}>
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {renderStepContent()}
              </motion.div>
            </AnimatePresence>
          </div>

          {/* 步骤控制按钮 */}
          <div style={{ textAlign: 'center' }}>
            <Space>
              <Button 
                onClick={prevStep} 
                disabled={currentStep === 0}
              >
                上一步
              </Button>
              <Button 
                type="primary" 
                onClick={nextStep}
                disabled={currentStep === steps.length - 1}
              >
                下一步
              </Button>
            </Space>
          </div>
        </Space>
      </Card>


    </motion.div>
  </CleanAssanLayout>
  );
};

export default DataImportPage;
