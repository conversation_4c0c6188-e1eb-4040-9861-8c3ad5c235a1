/**
 * 应用配置管理
 */

// 默认配置
const DEFAULT_CONFIG = {
  // API配置
  api: {
    baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000',
    timeout: 10000,
    retryAttempts: 3,
    retryDelay: 1000
  },
  
  // 缓存配置
  cache: {
    defaultExpireTime: 5 * 60 * 1000, // 5分钟
    maxCacheSize: 100,
    enablePersistentCache: true
  },
  
  // 性能监控配置
  performance: {
    enableMonitoring: true,
    slowRequestThreshold: 3000, // 3秒
    errorReportingEnabled: true,
    metricsCollectionInterval: 30000 // 30秒
  },
  
  // UI配置
  ui: {
    theme: 'light',
    language: 'zh-CN',
    pageSize: 10,
    maxPageSize: 100,
    animationDuration: 300,
    debounceDelay: 300
  },
  
  // 图表配置
  charts: {
    defaultChartType: 'line',
    maxDataPoints: 10000,
    refreshInterval: 30000, // 30秒
    enableRealTimeUpdate: false,
    colorPalette: [
      '#1890ff', '#52c41a', '#faad14', '#f5222d', 
      '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16'
    ]
  },
  
  // 数据导入配置
  dataImport: {
    maxFileSize: 100 * 1024 * 1024, // 100MB
    supportedFormats: ['.txt', '.csv', '.xls', '.xlsx'],
    chunkSize: 1000, // 分块处理大小
    progressUpdateInterval: 1000 // 进度更新间隔
  },
  
  // 安全配置
  security: {
    tokenRefreshThreshold: 5 * 60 * 1000, // 5分钟
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15分钟
    passwordMinLength: 8
  },
  
  // 日志配置
  logging: {
    level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
    enableConsoleLogging: true,
    enableRemoteLogging: false,
    maxLogEntries: 1000
  }
};

/**
 * 配置管理类
 */
class ConfigManager {
  constructor() {
    this.config = { ...DEFAULT_CONFIG };
    this.listeners = new Set();
    this.loadFromStorage();
  }

  /**
   * 从本地存储加载配置
   */
  loadFromStorage() {
    try {
      const storedConfig = localStorage.getItem('app_config');
      if (storedConfig) {
        const parsedConfig = JSON.parse(storedConfig);
        this.config = this.mergeConfig(this.config, parsedConfig);
      }
    } catch (error) {
      console.warn('加载配置失败:', error);
    }
  }

  /**
   * 保存配置到本地存储
   */
  saveToStorage() {
    try {
      localStorage.setItem('app_config', JSON.stringify(this.config));
    } catch (error) {
      console.warn('保存配置失败:', error);
    }
  }

  /**
   * 深度合并配置对象
   */
  mergeConfig(target, source) {
    const result = { ...target };
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
          result[key] = this.mergeConfig(target[key] || {}, source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }
    
    return result;
  }

  /**
   * 获取配置值
   * @param {string} path - 配置路径，如 'api.timeout'
   * @param {any} defaultValue - 默认值
   * @returns {any} 配置值
   */
  get(path, defaultValue = undefined) {
    const keys = path.split('.');
    let current = this.config;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }
    
    return current;
  }

  /**
   * 设置配置值
   * @param {string} path - 配置路径
   * @param {any} value - 配置值
   */
  set(path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let current = this.config;
    
    // 创建嵌套对象路径
    for (const key of keys) {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[lastKey] = value;
    this.saveToStorage();
    this.notifyListeners(path, value);
  }

  /**
   * 批量更新配置
   * @param {Object} updates - 配置更新对象
   */
  update(updates) {
    this.config = this.mergeConfig(this.config, updates);
    this.saveToStorage();
    this.notifyListeners('*', this.config);
  }

  /**
   * 重置配置到默认值
   * @param {string} path - 要重置的配置路径，不传则重置全部
   */
  reset(path = null) {
    if (path) {
      const defaultValue = this.getDefaultValue(path);
      this.set(path, defaultValue);
    } else {
      this.config = { ...DEFAULT_CONFIG };
      this.saveToStorage();
      this.notifyListeners('*', this.config);
    }
  }

  /**
   * 获取默认配置值
   */
  getDefaultValue(path) {
    const keys = path.split('.');
    let current = DEFAULT_CONFIG;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return undefined;
      }
    }
    
    return current;
  }

  /**
   * 添加配置变更监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    this.listeners.add(listener);
    
    // 返回取消监听的函数
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * 通知所有监听器
   */
  notifyListeners(path, value) {
    this.listeners.forEach(listener => {
      try {
        listener(path, value);
      } catch (error) {
        console.error('配置监听器执行失败:', error);
      }
    });
  }

  /**
   * 获取完整配置对象
   */
  getAll() {
    return { ...this.config };
  }

  /**
   * 验证配置
   */
  validate() {
    const errors = [];
    
    // 验证API配置
    if (!this.get('api.baseURL')) {
      errors.push('API基础URL不能为空');
    }
    
    if (this.get('api.timeout') < 1000) {
      errors.push('API超时时间不能少于1秒');
    }
    
    // 验证缓存配置
    if (this.get('cache.maxCacheSize') < 1) {
      errors.push('缓存最大大小必须大于0');
    }
    
    // 验证UI配置
    const pageSize = this.get('ui.pageSize');
    const maxPageSize = this.get('ui.maxPageSize');
    if (pageSize > maxPageSize) {
      errors.push('页面大小不能超过最大页面大小');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// 创建全局配置管理器实例
const configManager = new ConfigManager();

// 导出便捷方法
export const getConfig = (path, defaultValue) => configManager.get(path, defaultValue);
export const setConfig = (path, value) => configManager.set(path, value);
export const updateConfig = (updates) => configManager.update(updates);
export const resetConfig = (path) => configManager.reset(path);
export const addConfigListener = (listener) => configManager.addListener(listener);
export const validateConfig = () => configManager.validate();

export default configManager;
