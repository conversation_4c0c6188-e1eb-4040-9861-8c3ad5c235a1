@echo off
title Simple IP Detection Tool

echo ========================================
echo   Simple IP Detection Tool
echo ========================================
echo.

echo [INFO] Detecting local IP address...

REM Get local IP address
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /i "IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set "LOCAL_IP=%%b"
        goto :found_ip
    )
)

:found_ip
if "%LOCAL_IP%"=="" (
    echo [ERROR] Cannot detect local IP address
    pause
    exit /b 1
)

REM Clean IP address
set LOCAL_IP=%LOCAL_IP: =%

echo [SUCCESS] Detected IP: %LOCAL_IP%
echo.

echo [INFO] Updating configuration files...

REM Update .env file
echo HOST=0.0.0.0> ..\..\frontend\.env
echo PORT=3000>> ..\..\frontend\.env
echo BROWSER=none>> ..\..\frontend\.env
echo CHOKIDAR_USEPOLLING=true>> ..\..\frontend\.env
echo REACT_APP_AUTO_DETECT_API=true>> ..\..\frontend\.env
echo REACT_APP_DETECTED_IP=%LOCAL_IP%>> ..\..\frontend\.env

echo [SUCCESS] .env file updated

REM Update .env.production file
echo REACT_APP_API_URL=http://%LOCAL_IP%:5000> ..\..\frontend\.env.production
echo REACT_APP_ENVIRONMENT=production>> ..\..\frontend\.env.production
echo REACT_APP_VERSION=1.0.0>> ..\..\frontend\.env.production

echo [SUCCESS] .env.production file updated

echo.
echo ========================================
echo   Configuration Complete!
echo ========================================
echo.
echo Network Configuration:
echo   Local IP: %LOCAL_IP%
echo   Frontend URL: http://%LOCAL_IP%:3000
echo   API URL: http://%LOCAL_IP%:5000
echo.
echo Next steps:
echo   1. Restart frontend service
echo   2. Access from other computers: http://%LOCAL_IP%:3000
echo.

pause
