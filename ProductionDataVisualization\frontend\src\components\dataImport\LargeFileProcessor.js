import React, { useState, useRef, useCallback } from 'react';
import { Card, Progress, Alert, Button, Space, Typography, Statistic, Row, Col, Tag } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  StopOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

const LargeFileProcessor = ({ 
  file, 
  chunkSize = 1024 * 1024, // 1MB chunks
  onProgress, 
  onChunkProcessed, 
  onComplete, 
  onError 
}) => {
  const [processing, setProcessing] = useState(false);
  const [paused, setPaused] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentChunk, setCurrentChunk] = useState(0);
  const [totalChunks, setTotalChunks] = useState(0);
  const [processedRows, setProcessedRows] = useState(0);
  const [errors, setErrors] = useState([]);
  const [startTime, setStartTime] = useState(null);
  const [estimatedTime, setEstimatedTime] = useState(null);
  const [throughput, setThroughput] = useState(0);

  const workerRef = useRef(null);
  const pausedRef = useRef(false);
  const abortControllerRef = useRef(null);

  // 创建Web Worker用于文件处理
  const createWorker = useCallback(() => {
    const workerCode = `
      self.onmessage = function(e) {
        const { chunk, chunkIndex, config } = e.data;
        
        try {
          // 处理文件块
          const reader = new FileReader();
          reader.onload = function(event) {
            const text = event.target.result;
            const lines = text.split('\\n').filter(line => line.trim());
            
            // 解析CSV数据
            const rows = lines.map((line, index) => {
              const values = line.split(config.delimiter || ',').map(v => v.trim().replace(/"/g, ''));
              return {
                rowIndex: chunkIndex * config.chunkRows + index,
                data: values,
                raw: line
              };
            });
            
            self.postMessage({
              type: 'chunk_processed',
              chunkIndex,
              rows,
              rowCount: rows.length
            });
          };
          
          reader.onerror = function(error) {
            self.postMessage({
              type: 'error',
              chunkIndex,
              error: error.message
            });
          };
          
          reader.readAsText(chunk);
          
        } catch (error) {
          self.postMessage({
            type: 'error',
            chunkIndex,
            error: error.message
          });
        }
      };
    `;

    const blob = new Blob([workerCode], { type: 'application/javascript' });
    return new Worker(URL.createObjectURL(blob));
  }, []);

  // 计算文件块数量
  const calculateChunks = useCallback((fileSize) => {
    return Math.ceil(fileSize / chunkSize);
  }, [chunkSize]);

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化时间
  const formatTime = (seconds) => {
    if (!seconds || seconds === Infinity) return '--';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // 开始处理文件
  const startProcessing = useCallback(async () => {
    if (!file) return;

    setProcessing(true);
    setPaused(false);
    setProgress(0);
    setCurrentChunk(0);
    setProcessedRows(0);
    setErrors([]);
    setStartTime(Date.now());
    pausedRef.current = false;

    const chunks = calculateChunks(file.size);
    setTotalChunks(chunks);

    // 创建AbortController用于取消操作
    abortControllerRef.current = new AbortController();

    // 创建Worker
    workerRef.current = createWorker();

    let processedChunks = 0;
    let totalRows = 0;

    workerRef.current.onmessage = (e) => {
      const { type, chunkIndex, rows, rowCount, error } = e.data;

      if (type === 'chunk_processed') {
        processedChunks++;
        totalRows += rowCount;
        
        setCurrentChunk(processedChunks);
        setProcessedRows(totalRows);
        
        const newProgress = (processedChunks / chunks) * 100;
        setProgress(newProgress);

        // 计算处理速度和预估时间
        const elapsed = (Date.now() - startTime) / 1000;
        const rowsPerSecond = totalRows / elapsed;
        setThroughput(Math.round(rowsPerSecond));

        if (processedChunks < chunks) {
          const remainingChunks = chunks - processedChunks;
          const avgTimePerChunk = elapsed / processedChunks;
          setEstimatedTime(remainingChunks * avgTimePerChunk);
        }

        if (onProgress) {
          onProgress(newProgress, processedChunks, chunks);
        }

        if (onChunkProcessed) {
          onChunkProcessed(rows, chunkIndex);
        }

        // 检查是否完成
        if (processedChunks >= chunks) {
          setProcessing(false);
          setProgress(100);
          if (onComplete) {
            onComplete(totalRows);
          }
        }
      } else if (type === 'error') {
        const errorMsg = `块 ${chunkIndex + 1} 处理失败: ${error}`;
        setErrors(prev => [...prev, errorMsg]);
        if (onError) {
          onError(errorMsg);
        }
      }
    };

    // 分块处理文件
    for (let i = 0; i < chunks; i++) {
      if (abortControllerRef.current.signal.aborted) break;
      
      // 检查暂停状态
      while (pausedRef.current && !abortControllerRef.current.signal.aborted) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);

      workerRef.current.postMessage({
        chunk,
        chunkIndex: i,
        config: {
          delimiter: ',',
          chunkRows: Math.ceil(chunkSize / 100) // 估算每块的行数
        }
      });

      // 添加小延迟以避免阻塞UI
      await new Promise(resolve => setTimeout(resolve, 10));
    }

  }, [file, chunkSize, calculateChunks, createWorker, onProgress, onChunkProcessed, onComplete, onError]);

  // 暂停处理
  const pauseProcessing = useCallback(() => {
    setPaused(true);
    pausedRef.current = true;
  }, []);

  // 恢复处理
  const resumeProcessing = useCallback(() => {
    setPaused(false);
    pausedRef.current = false;
  }, []);

  // 停止处理
  const stopProcessing = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (workerRef.current) {
      workerRef.current.terminate();
      workerRef.current = null;
    }
    setProcessing(false);
    setPaused(false);
    setProgress(0);
    setCurrentChunk(0);
    setProcessedRows(0);
    pausedRef.current = false;
  }, []);

  // 组件卸载时清理
  React.useEffect(() => {
    return () => {
      if (workerRef.current) {
        workerRef.current.terminate();
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return (
    <div className="large-file-processor">
      <Card>
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          <div>
            <Title level={4}>
              <FileTextOutlined /> 大文件处理引擎
            </Title>
            {file && (
              <Text type="secondary">
                文件: {file.name} ({formatFileSize(file.size)})
              </Text>
            )}
          </div>

          {/* 处理统计 */}
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="处理进度"
                value={progress}
                precision={1}
                suffix="%"
                valueStyle={{ color: processing ? '#1890ff' : '#52c41a' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="已处理块"
                value={currentChunk}
                suffix={`/ ${totalChunks}`}
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="已处理行数"
                value={processedRows}
                valueStyle={{ color: '#13c2c2' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="处理速度"
                value={throughput}
                suffix="行/秒"
                prefix={<ThunderboltOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
          </Row>

          {/* 进度条 */}
          <div>
            <Progress
              percent={progress}
              status={processing ? (paused ? 'normal' : 'active') : 'success'}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
            {processing && estimatedTime && (
              <div style={{ marginTop: 8 }}>
                <Space>
                  <Text type="secondary">
                    <ClockCircleOutlined /> 预计剩余时间: {formatTime(estimatedTime)}
                  </Text>
                  {paused && <Tag color="orange">已暂停</Tag>}
                </Space>
              </div>
            )}
          </div>

          {/* 控制按钮 */}
          <Space>
            {!processing ? (
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={startProcessing}
                disabled={!file}
                size="large"
              >
                开始处理
              </Button>
            ) : (
              <>
                {!paused ? (
                  <Button
                    icon={<PauseCircleOutlined />}
                    onClick={pauseProcessing}
                    size="large"
                  >
                    暂停
                  </Button>
                ) : (
                  <Button
                    type="primary"
                    icon={<PlayCircleOutlined />}
                    onClick={resumeProcessing}
                    size="large"
                  >
                    继续
                  </Button>
                )}
                <Button
                  danger
                  icon={<StopOutlined />}
                  onClick={stopProcessing}
                  size="large"
                >
                  停止
                </Button>
              </>
            )}
          </Space>

          {/* 错误信息 */}
          {errors.length > 0 && (
            <Alert
              message={`处理过程中发现 ${errors.length} 个错误`}
              type="error"
              showIcon
              description={
                <div style={{ maxHeight: '150px', overflow: 'auto' }}>
                  {errors.map((error, index) => (
                    <div key={index}>{error}</div>
                  ))}
                </div>
              }
              closable
              onClose={() => setErrors([])}
            />
          )}

          {/* 处理配置信息 */}
          <Card size="small" title="处理配置">
            <Space>
              <Text>块大小: {formatFileSize(chunkSize)}</Text>
              <Text>总块数: {totalChunks}</Text>
              <Text>并发处理: 启用</Text>
              <Text>内存优化: 启用</Text>
            </Space>
          </Card>
        </Space>
      </Card>
    </div>
  );
};

export default LargeFileProcessor;
