/* 引入全局字体样式 */
@import './styles/Typography.css';

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #050A30;
  color: #F8F9FA;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* ===== 科技感未来主题登录页面 ===== */
.futuristic-login-page {
  position: relative;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Three.js 容器 */
.three-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

/* 背景滤镜效果 */
.background-filter {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(0, 245, 255, 0.05) 0%, rgba(5, 10, 48, 0.8) 70%, rgba(3, 7, 36, 0.95) 100%);
  z-index: 1;
}

/* 内容区域 */
.login-content {
  position: relative;
  z-index: 2;
  display: flex;
  width: 90%;
  max-width: 1400px;
  height: 85vh;
  max-height: 800px;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.3), 0 0 100px rgba(0, 245, 255, 0.2);
  backdrop-filter: blur(10px);
  background: rgba(13, 23, 42, 0.7);
  border: 1px solid rgba(0, 245, 255, 0.1);
}

/* 左侧区域 */
.login-left {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(5, 10, 48, 0.9) 0%, rgba(3, 7, 36, 0.8) 100%);
  border-right: 1px solid rgba(0, 245, 255, 0.1);
}

/* 公司品牌 */
.company-brand {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  position: relative;
}

.company-logo {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: linear-gradient(135deg, #00F5FF, #9D4EDD);
  margin-right: 20px;
  position: relative;
  box-shadow: 0 0 20px rgba(0, 245, 255, 0.5), 0 0 40px rgba(0, 245, 255, 0.2);
  animation: logo-pulse 3s ease-in-out infinite;
}

@keyframes logo-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 245, 255, 0.5), 0 0 40px rgba(0, 245, 255, 0.2);
  }
  50% {
    box-shadow: 0 0 30px rgba(0, 245, 255, 0.7), 0 0 60px rgba(0, 245, 255, 0.3);
  }
}

/* 眼睛图标容器 */
.eye-icon-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.eye-icon-inner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #050A30;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
}

.eye-icon-pupil {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #00F5FF;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: pupil-move 5s ease-in-out infinite;
  box-shadow: 0 0 15px rgba(0, 245, 255, 0.8);
}

@keyframes pupil-move {
  0%, 100% { transform: translate(-50%, -50%); }
  25% { transform: translate(-30%, -40%); }
  50% { transform: translate(-60%, -50%); }
  75% { transform: translate(-50%, -60%); }
}

.eye-icon-highlight {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  top: 30%;
  left: 30%;
  z-index: 2;
}

.eye-drop {
  position: absolute;
  width: 10px;
  height: 10px;
  background: rgba(0, 245, 255, 0.8);
  border-radius: 50% 50% 50% 0;
  transform: rotate(-45deg);
  top: -20px;
  left: 50%;
  margin-left: -5px;
  animation: drop-fall 3s ease-in-out infinite;
  z-index: 3;
  filter: drop-shadow(0 0 5px rgba(0, 245, 255, 0.5));
}

@keyframes drop-fall {
  0% {
    top: -20px;
    opacity: 0;
    transform: rotate(-45deg) scale(0.8);
  }
  20% {
    opacity: 1;
    transform: rotate(-45deg) scale(1);
  }
  80% {
    opacity: 1;
    transform: rotate(-45deg) scale(1);
  }
  100% {
    top: 50px;
    opacity: 0;
    transform: rotate(-45deg) scale(0.5);
  }
}

/* 眼药水液滴容器 */
.eye-drop-container {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 120px;
  height: 180px;
  z-index: 5;
}

.eye-drop-bottle {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 80px;
  background: linear-gradient(to right, #00F5FF, #9D4EDD, #00F5FF);
  border-radius: 5px 5px 20px 20px;
  box-shadow: 0 0 15px rgba(0, 245, 255, 0.5);
}

.eye-drop-bottle::before {
  content: '';
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 15px;
  background: #9D4EDD;
  border-radius: 5px 5px 0 0;
}

.eye-drop-bottle::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 12px;
  height: 12px;
  background: #00F5FF;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0, 245, 255, 0.8);
}

.eye-drop-liquid {
  position: absolute;
  top: 90px;
  left: 50%;
  transform: translateX(-50%);
  width: 10px;
  height: 40px;
}

.drop {
  position: absolute;
  width: 10px;
  height: 16px;
  background: rgba(0, 245, 255, 0.8);
  border-radius: 50% 50% 50% 50%;
  left: 0;
  filter: drop-shadow(0 0 5px rgba(0, 245, 255, 0.5));
}

.drop1 {
  animation: drop-animation 3s infinite;
}

.drop2 {
  animation: drop-animation 3s infinite 1s;
}

.drop3 {
  animation: drop-animation 3s infinite 2s;
}

@keyframes drop-animation {
  0% {
    top: 0;
    opacity: 0;
    height: 10px;
    border-radius: 50% 50% 50% 50%;
  }
  30% {
    opacity: 1;
    height: 16px;
    border-radius: 50% 50% 50% 50%;
  }
  80% {
    opacity: 1;
    border-radius: 50% 50% 50% 50%;
  }
  90% {
    border-radius: 0 0 50% 50%;
    height: 10px;
  }
  100% {
    top: 90px;
    opacity: 0;
    height: 0;
    border-radius: 0 0 50% 50%;
  }
}

.eye-target {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 30px;
  overflow: hidden;
  border-radius: 50% 50% 0 0;
  box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.2);
}

.eye-white {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #F8F9FA;
}

.eye-iris {
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #00F5FF;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 15px rgba(0, 245, 255, 0.5);
}

.eye-pupil {
  position: absolute;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #050A30;
  top: 7.5px;
  left: 50%;
  transform: translateX(-50%);
}

.eye-pupil::after {
  content: '';
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: white;
  top: 2px;
  left: 2px;
}

/* 技术元素 */
.tech-elements {
  position: relative;
  width: 100%;
  height: 200px;
  margin-bottom: 20px;
}

.tech-circle {
  position: absolute;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  border: 2px solid rgba(0, 245, 255, 0.3);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: rotate 20s linear infinite;
}

.tech-circle::before,
.tech-circle::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  top: 50%;
  left: 50%;
}

.tech-circle::before {
  width: 120px;
  height: 120px;
  border: 1px dashed rgba(0, 245, 255, 0.2);
  transform: translate(-50%, -50%);
  animation: rotate 15s linear infinite reverse;
}

.tech-circle::after {
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(0, 245, 255, 0.3) 0%, rgba(0, 245, 255, 0) 70%);
  transform: translate(-50%, -50%);
  animation: pulse 3s ease-in-out infinite;
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

.tech-grid {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(to right, rgba(0, 245, 255, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0, 245, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.3;
}

.tech-dots {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.tech-dots::before {
  content: '';
  position: absolute;
  width: 200%;
  height: 200%;
  top: -50%;
  left: -50%;
  background-image: radial-gradient(rgba(0, 245, 255, 0.3) 1px, transparent 1px);
  background-size: 30px 30px;
  animation: drift 60s linear infinite;
}

@keyframes drift {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.tech-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  background-color: rgba(0, 245, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(0, 245, 255, 0.8), 0 0 40px rgba(0, 245, 255, 0.4);
}

.tech-pulse::before,
.tech-pulse::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background-color: transparent;
  border: 2px solid rgba(0, 245, 255, 0.5);
  opacity: 1;
}

.tech-pulse::before {
  width: 20px;
  height: 20px;
  animation: ripple 2s ease-out infinite;
}

.tech-pulse::after {
  width: 20px;
  height: 20px;
  animation: ripple 2s ease-out 0.5s infinite;
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 100px;
    height: 100px;
    opacity: 0;
  }
}

/* 数据统计 */
.data-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 30px;
}

.stat-item {
  position: relative;
  height: 30px;
  background: rgba(0, 245, 255, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  overflow: hidden;
  border-left: 2px solid rgba(0, 245, 255, 0.5);
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, rgba(0, 245, 255, 0.2), rgba(0, 245, 255, 0.05));
  border-radius: 4px;
  z-index: 0;
}

.stat-label {
  position: relative;
  z-index: 1;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
  flex: 1;
}

.stat-value {
  position: relative;
  z-index: 1;
  color: #00F5FF;
  font-size: 14px;
  font-weight: 600;
  text-shadow: 0 0 5px rgba(0, 245, 255, 0.8);
}

/* 系统名称 */
.system-name {
  text-align: center;
  margin-top: 30px;
}

.system-name h2 {
  font-size: 24px;
  font-weight: 600;
  color: #F8F9FA;
  margin: 0 0 8px;
  text-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
}

.system-name p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

/* 右侧登录表单区域 */
.login-right {
  width: 450px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background: rgba(13, 23, 42, 0.7);
}

.login-form-container {
  width: 100%;
  max-width: 360px;
  padding: 40px 30px;
  position: relative;
  z-index: 2;
}

.form-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(13, 23, 42, 0.7);
  backdrop-filter: blur(10px);
  z-index: -1;
  border-radius: 16px;
  border: 1px solid rgba(0, 245, 255, 0.1);
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
}

.form-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(0, 245, 255, 0.1) 0%, rgba(0, 245, 255, 0) 70%);
  z-index: -1;
  opacity: 0.8;
  animation: form-glow 3s infinite alternate;
}

@keyframes form-glow {
  0% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

/* 登录表单样式 */
.futuristic-login-container {
  width: 100%;
}

.login-header {
  margin-bottom: 30px;
  text-align: left;
}

.login-title {
  font-size: 28px !important;
  font-weight: 700 !important;
  color: #F8F9FA !important;
  margin-bottom: 8px !important;
  background: linear-gradient(45deg, #F8F9FA, #00F5FF);
  background-size: 200% auto;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent !important;
  animation: gradient 5s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.login-subtitle {
  font-size: 16px;
  color: #A0B1C5;
}

.login-alert {
  margin-bottom: 20px;
  border-radius: 8px;
  background: rgba(255, 56, 96, 0.1) !important;
  border: 1px solid rgba(255, 56, 96, 0.3) !important;
}

.futuristic-form .ant-form-item {
  margin-bottom: 24px;
}

/* 输入框容器 */
.input-container {
  position: relative;
}

.futuristic-input {
  height: 50px;
  border-radius: 8px;
  border: 1px solid rgba(0, 245, 255, 0.3);
  background-color: rgba(13, 23, 42, 0.5);
  color: #F8F9FA;
  padding-left: 16px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.futuristic-input:hover,
.futuristic-input:focus {
  border-color: rgba(0, 245, 255, 0.6);
  box-shadow: 0 0 0 2px rgba(0, 245, 255, 0.2), 0 0 15px rgba(0, 245, 255, 0.2);
}

.futuristic-input input {
  background-color: transparent;
  color: #F8F9FA;
  height: 48px;
}

.futuristic-input input::placeholder {
  color: rgba(160, 177, 197, 0.6);
}

.input-focus-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.8), transparent);
  transform: translateX(-50%);
  transition: width 0.3s ease;
}

.futuristic-input:focus-within + .input-focus-line {
  width: 100%;
  animation: focus-pulse 1.5s infinite;
}

@keyframes focus-pulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

.input-icon {
  color: rgba(160, 177, 197, 0.7);
}

.visible-icon, .hidden-icon {
  color: rgba(160, 177, 197, 0.7);
}

/* 记住我和忘记密码 */
.form-options {
  margin-bottom: 16px;
}

.remember-forgot {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.remember-checkbox .ant-checkbox-inner {
  background-color: rgba(13, 23, 42, 0.8);
  border-color: rgba(0, 245, 255, 0.5);
}

.remember-checkbox .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #00F5FF;
  border-color: #00F5FF;
}

.remember-checkbox .ant-checkbox-checked::after {
  border-color: #00F5FF;
}

.remember-checkbox .ant-checkbox + span {
  color: rgba(255, 255, 255, 0.8);
}

.forgot-link {
  color: rgba(0, 245, 255, 0.8);
  font-size: 14px;
  transition: all 0.3s ease;
}

.forgot-link:hover {
  color: #00F5FF;
  text-shadow: 0 0 8px rgba(0, 245, 255, 0.5);
}

/* 登录按钮 */
.login-button {
  height: 50px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #00F5FF, #9D4EDD);
  border: none;
  box-shadow: 0 4px 15px rgba(0, 245, 255, 0.3);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: linear-gradient(135deg, #33F8FF, #B36DF8);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 245, 255, 0.4);
}

.login-button:active {
  transform: translateY(0);
  box-shadow: 0 4px 10px rgba(0, 245, 255, 0.3);
}

.login-button::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 70%);
  transform: rotate(45deg);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.login-button:hover::before {
  opacity: 1;
  animation: button-shine 1.5s infinite;
}

@keyframes button-shine {
  0% {
    transform: rotate(45deg) translateX(-100%);
  }
  100% {
    transform: rotate(45deg) translateX(100%);
  }
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.button-text {
  position: relative;
  z-index: 2;
}

.button-icon {
  position: relative;
  z-index: 2;
  font-size: 18px;
  transition: transform 0.3s ease;
}

.login-button:hover .button-icon {
  transform: translateX(5px);
}

/* 页脚 */
.login-footer {
  text-align: center;
  margin-top: 24px;
}

.footer-text {
  color: #A0B1C5;
}

.register-link {
  color: rgba(0, 245, 255, 0.8);
  font-weight: 500;
  transition: all 0.3s ease;
}

.register-link:hover {
  color: #00F5FF;
  text-shadow: 0 0 8px rgba(0, 245, 255, 0.5);
}

.login-page-footer {
  text-align: center;
  margin-top: 40px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .login-content {
    width: 95%;
    height: 90vh;
  }
  
  .login-right {
    width: 400px;
  }
}

@media (max-width: 992px) {
  .login-content {
    flex-direction: column;
    height: auto;
    max-height: none;
  }
  
  .login-left {
    display: none;
  }
  
  .login-right {
    width: 100%;
    padding: 40px 0;
  }
  
  .login-form-container {
    padding: 30px 20px;
  }
}

@media (max-width: 576px) {
  .login-title {
    font-size: 24px !important;
  }
  
  .login-subtitle {
    font-size: 14px;
  }
  
  .futuristic-input {
    height: 46px;
  }
  
  .futuristic-input input {
    height: 44px;
  }
  
  .login-button {
    height: 46px;
  }
}

/* 注册页面样式 */
.register-form-container {
  max-width: 100%;
  padding: 24px;
}

.register-title {
  text-align: center;
  margin-bottom: 8px;
}

.register-subtitle {
  text-align: center;
  margin-bottom: 24px;
  color: #888;
}

.register-form-button {
  margin-top: 16px;
}

/* 布局样式 */
.logo {
  height: 64px;
  background: rgba(255, 255, 255, 0.1);
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
}

.site-layout-header {
  background: #fff;
  padding: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 表格操作按钮样式 */
.table-actions {
  display: flex;
  justify-content: flex-end;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ant-table {
    font-size: 12px;
  }
  
  .ant-btn {
    font-size: 12px;
    padding: 0 8px;
  }
} 

/* 添加复选框样式 */
.modern-checkbox .ant-checkbox-inner {
  background-color: rgba(13, 17, 23, 0.8);
  border-color: rgba(0, 200, 83, 0.5);
}

.modern-checkbox .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #00cc66;
  border-color: #00cc66;
}

.modern-checkbox .ant-checkbox-checked::after {
  border-color: #00cc66;
}

.modern-checkbox .ant-checkbox + span {
  color: rgba(255, 255, 255, 0.8);
}

/* 添加输入框发光效果 */
.input-glow-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  background: radial-gradient(circle at 50% 0%, rgba(0, 200, 83, 0.1) 0%, rgba(0, 200, 83, 0) 70%);
}

/* 按钮发光效果 */
.modern-login-button {
  position: relative;
  overflow: hidden;
}

.button-text {
  position: relative;
  z-index: 2;
}

.button-glow {
  position: absolute;
  top: -50%;
  left: -25%;
  width: 150%;
  height: 200%;
  background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.modern-login-button:hover .button-glow {
  opacity: 0.8;
  animation: button-glow 2s infinite;
}

@keyframes button-glow {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-10%) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.5;
  }
}

/* 页脚文本样式 */
.modern-footer-text {
  color: rgba(255, 255, 255, 0.5);
}

/* 添加表单输入焦点动画 */
.modern-input:focus-within {
  animation: input-pulse 1.5s infinite;
}

@keyframes input-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(0, 200, 83, 0.2);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(0, 200, 83, 0.2);
  }
}

/* 添加粒子JS初始化 */
@keyframes particles-init {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

#particles-js {
  animation: particles-init 2s ease-out forwards;
}

/* 添加登录按钮按下效果 */
.modern-login-button:active {
  transform: scale(0.98);
}

/* 添加表单项出现动画 */
.modern-login-form .ant-form-item {
  opacity: 0;
  transform: translateY(10px);
  animation: form-item-appear 0.5s ease-out forwards;
}

.modern-login-form .ant-form-item:nth-child(1) {
  animation-delay: 0.1s;
}

.modern-login-form .ant-form-item:nth-child(2) {
  animation-delay: 0.2s;
}

.modern-login-form .ant-form-item:nth-child(3) {
  animation-delay: 0.3s;
}

.modern-login-form .ant-form-item:nth-child(4) {
  animation-delay: 0.4s;
}

@keyframes form-item-appear {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加错误消息动画 */
.ant-alert-error {
  animation: error-pulse 2s infinite;
}

@keyframes error-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.2);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(255, 77, 79, 0.2);
  }
}

/* 增强表单容器的科技感 */
.modern-login-form-wrapper {
  position: relative;
}

.modern-login-form-wrapper::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: 17px;
  background: linear-gradient(45deg, rgba(0, 200, 83, 0.3), transparent, rgba(0, 200, 83, 0.3), transparent);
  background-size: 400% 400%;
  z-index: 0;
  animation: border-flow 8s ease infinite;
}

@keyframes border-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 添加科技线条装饰 */
.modern-login-right::before {
  content: '';
  position: absolute;
  width: 200px;
  height: 200px;
  top: 10%;
  right: 5%;
  background-image: 
    linear-gradient(90deg, rgba(0, 200, 83, 0.1) 1px, transparent 1px),
    linear-gradient(0deg, rgba(0, 200, 83, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  border-radius: 50%;
  opacity: 0.3;
  z-index: 0;
  animation: rotate 30s linear infinite;
}

.modern-login-right::after {
  content: '';
  position: absolute;
  width: 150px;
  height: 150px;
  bottom: 10%;
  left: 5%;
  background-image: 
    linear-gradient(90deg, rgba(0, 200, 83, 0.1) 1px, transparent 1px),
    linear-gradient(0deg, rgba(0, 200, 83, 0.1) 1px, transparent 1px);
  background-size: 15px 15px;
  border-radius: 50%;
  opacity: 0.3;
  z-index: 0;
  animation: rotate 20s linear infinite reverse;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 添加键盘交互效果 */
.modern-input:focus {
  border-color: #00cc66;
  box-shadow: 0 0 0 2px rgba(0, 200, 83, 0.2), 0 0 15px rgba(0, 200, 83, 0.3);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 添加悬浮卡片效果 */
.modern-login-form-wrapper {
  transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1);
  transition: transform 0.5s ease, box-shadow 0.5s ease;
}

.modern-login-form-wrapper:hover {
  transform: perspective(1000px) rotateX(2deg) rotateY(2deg) scale(1.01);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3), 0 0 40px rgba(0, 200, 83, 0.2);
}

/* 增强标题文字效果 */
.modern-login-title {
  background: linear-gradient(45deg, #ffffff, #00cc66);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent !important;
  animation: title-shimmer 5s infinite;
}

@keyframes title-shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .modern-login-right::before,
  .modern-login-right::after {
    display: none;
  }
  
  .modern-login-form-wrapper:hover {
    transform: none;
    box-shadow: 0 0 30px rgba(0, 200, 83, 0.1);
  }
} 