import React, { useMemo } from 'react';
import Base<PERSON>hart from './BaseChart';
import { generateLineChartOption, detectAnomalies, highlightAnomalies } from '../../utils/chartUtils';

const LineChart = ({
  data,
  title = '折线图',
  smooth = false,
  showArea = false,
  showSymbol = true,
  xAxisRotate = 0,
  yAxisFormatter,
  enableAnomalyDetection = false,
  anomalyMethod = 'zscore',
  anomalyThreshold,
  height = 400,
  loading = false,
  error = null,
  onPointClick,
  onDataZoom,
  ...props
}) => {
  // 生成图表配置
  const chartOption = useMemo(() => {
    if (!data || loading || error) return null;

    const config = {
      smooth,
      showArea,
      showSymbol,
      xAxisRotate,
      yAxisFormatter
    };

    let option = generateLineChartOption(data, config);

    // 异常检测和高亮
    if (enableAnomalyDetection && data.series && data.series.length > 0) {
      const firstSeriesData = data.series[0].data;
      if (Array.isArray(firstSeriesData)) {
        const anomalies = detectAnomalies(firstSeriesData, anomalyMethod, anomalyThreshold);
        if (anomalies.length > 0) {
          option = highlightAnomalies(option, anomalies);
        }
      }
    }

    // 添加数据缩放功能
    option.dataZoom = [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        type: 'slider',
        start: 0,
        end: 100,
        height: 20,
        bottom: 10
      }
    ];

    return option;
  }, [data, smooth, showArea, showSymbol, xAxisRotate, yAxisFormatter, 
      enableAnomalyDetection, anomalyMethod, anomalyThreshold, loading, error]);

  // 事件处理
  const chartEvents = useMemo(() => {
    const events = {};

    if (onPointClick) {
      events.click = (params) => {
        onPointClick(params);
      };
    }

    if (onDataZoom) {
      events.datazoom = (params) => {
        onDataZoom(params);
      };
    }

    return events;
  }, [onPointClick, onDataZoom]);

  return (
    <BaseChart
      title={title}
      option={chartOption}
      height={height}
      loading={loading}
      error={error}
      onEvents={chartEvents}
      {...props}
    />
  );
};

export default LineChart;
