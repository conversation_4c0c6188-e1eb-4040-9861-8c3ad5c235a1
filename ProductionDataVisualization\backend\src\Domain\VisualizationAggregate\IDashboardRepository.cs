using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Domain.Common;

namespace Domain.VisualizationAggregate
{
    /// <summary>
    /// 仪表盘仓储接口
    /// </summary>
    public interface IDashboardRepository : IRepository<Dashboard>
    {
        Task<IEnumerable<Dashboard>> GetByCreatedByAsync(Guid userId);
        Task<IEnumerable<Dashboard>> GetPublicAsync();
        Task<IEnumerable<Dashboard>> GetByChartIdAsync(Guid chartId);
    }
}