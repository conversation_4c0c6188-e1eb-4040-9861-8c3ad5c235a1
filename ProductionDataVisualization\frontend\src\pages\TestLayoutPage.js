import React from 'react';
import { Card, Typography, Space } from 'antd';
import CleanAssanLayout from '../components/layout/CleanAssanLayout';

const { Title, Text } = Typography;

const TestLayoutPage = () => {
  return (
    <CleanAssanLayout>
      <Card>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Title level={2}>测试页面</Title>
          <Text>这是一个测试页面，用于验证布局是否正常工作。</Text>
          <Text type="secondary">
            如果您能看到这个页面，说明布局组件工作正常。
          </Text>
        </Space>
      </Card>
    </CleanAssanLayout>
  );
};

export default TestLayoutPage;
