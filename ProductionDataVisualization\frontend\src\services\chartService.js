import api, { cachedGet } from './api';
import cacheManager from '../utils/cache';

const CHARTS_ENDPOINT = '/api/charts';
const DATA_ENDPOINT = '/api/data';

/**
 * 图表服务
 */
class ChartService {
  /**
   * 获取所有图表
   * @param {Object} options - 查询选项
   * @returns {Promise} 图表列表
   */
  async getCharts(options = {}) {
    const { 
      page = 1, 
      pageSize = 10, 
      chartType = null, 
      isPublic = null,
      useCache = true 
    } = options;
    
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString()
      });
      
      if (chartType) params.append('chartType', chartType);
      if (isPublic !== null) params.append('isPublic', isPublic.toString());
      
      const url = `${CHARTS_ENDPOINT}?${params.toString()}`;
      const response = useCache 
        ? await cachedGet(url, { cacheTime: 3 * 60 * 1000 }) // 3分钟缓存
        : await api.get(url);
        
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { message: '获取图表列表失败' };
    }
  }

  /**
   * 获取单个图表
   * @param {string} chartId - 图表ID
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise} 图表详情
   */
  async getChart(chartId, useCache = true) {
    try {
      const url = `${CHARTS_ENDPOINT}/${chartId}`;
      const response = useCache 
        ? await cachedGet(url, { cacheTime: 5 * 60 * 1000 }) // 5分钟缓存
        : await api.get(url);
        
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { message: '获取图表详情失败' };
    }
  }

  /**
   * 创建图表
   * @param {Object} chartData - 图表数据
   * @returns {Promise} 创建结果
   */
  async createChart(chartData) {
    try {
      const response = await api.post(CHARTS_ENDPOINT, chartData);
      
      // 清除相关缓存
      this.clearChartsCache();
      
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { message: '创建图表失败' };
    }
  }

  /**
   * 更新图表
   * @param {string} chartId - 图表ID
   * @param {Object} chartData - 图表数据
   * @returns {Promise} 更新结果
   */
  async updateChart(chartId, chartData) {
    try {
      const response = await api.put(`${CHARTS_ENDPOINT}/${chartId}`, chartData);
      
      // 清除相关缓存
      this.clearChartsCache();
      this.clearChartCache(chartId);
      
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { message: '更新图表失败' };
    }
  }

  /**
   * 删除图表
   * @param {string} chartId - 图表ID
   * @returns {Promise} 删除结果
   */
  async deleteChart(chartId) {
    try {
      const response = await api.delete(`${CHARTS_ENDPOINT}/${chartId}`);
      
      // 清除相关缓存
      this.clearChartsCache();
      this.clearChartCache(chartId);
      
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { message: '删除图表失败' };
    }
  }

  /**
   * 获取图表数据
   * @param {string} chartId - 图表ID
   * @param {Object} options - 查询选项
   * @returns {Promise} 图表数据
   */
  async getChartData(chartId, options = {}) {
    const {
      startTime = null,
      endTime = null,
      limit = 1000,
      useCache = true
    } = options;
    
    try {
      const params = new URLSearchParams({
        limit: limit.toString()
      });
      
      if (startTime) params.append('startTime', startTime);
      if (endTime) params.append('endTime', endTime);
      
      const url = `${CHARTS_ENDPOINT}/${chartId}/data?${params.toString()}`;
      const response = useCache 
        ? await cachedGet(url, { cacheTime: 1 * 60 * 1000 }) // 1分钟缓存
        : await api.get(url);
        
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { message: '获取图表数据失败' };
    }
  }

  /**
   * 获取数据类别列表
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise} 数据类别列表
   */
  async getDataCategories(useCache = true) {
    try {
      const url = `${DATA_ENDPOINT}/categories`;
      const response = useCache 
        ? await cachedGet(url, { cacheTime: 10 * 60 * 1000 }) // 10分钟缓存
        : await api.get(url);
        
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { message: '获取数据类别失败' };
    }
  }

  /**
   * 获取支持的图表类型
   * @returns {Array} 图表类型列表
   */
  getSupportedChartTypes() {
    return [
      {
        type: 'line',
        name: '折线图',
        description: '适用于显示数据随时间的变化趋势',
        icon: 'LineChartOutlined',
        features: ['时间序列', '多系列对比', '异常高亮']
      },
      {
        type: 'bar',
        name: '柱状图',
        description: '适用于比较不同类别的数据',
        icon: 'BarChartOutlined',
        features: ['分类对比', '数值排序', '异常高亮']
      },
      {
        type: 'pie',
        name: '饼图',
        description: '适用于显示数据的组成比例',
        icon: 'PieChartOutlined',
        features: ['比例显示', '分类统计', '交互式']
      },
      {
        type: 'scatter',
        name: '散点图',
        description: '适用于显示两个变量之间的关系',
        icon: 'DotChartOutlined',
        features: ['相关性分析', '异常检测', '聚类显示']
      },
      {
        type: 'table',
        name: '数据表格',
        description: '适用于显示详细的数据列表',
        icon: 'TableOutlined',
        features: ['详细数据', '排序筛选', '导出功能']
      }
    ];
  }

  /**
   * 验证图表配置
   * @param {Object} chartConfig - 图表配置
   * @returns {Object} 验证结果
   */
  validateChartConfig(chartConfig) {
    const result = {
      isValid: true,
      errors: []
    };
    
    // 检查必填字段
    if (!chartConfig.title || chartConfig.title.trim() === '') {
      result.isValid = false;
      result.errors.push('图表标题不能为空');
    }
    
    if (!chartConfig.chartType) {
      result.isValid = false;
      result.errors.push('必须选择图表类型');
    }
    
    if (!chartConfig.dataCategories || chartConfig.dataCategories.length === 0) {
      result.isValid = false;
      result.errors.push('必须选择至少一个数据类别');
    }
    
    // 检查图表类型是否支持
    const supportedTypes = this.getSupportedChartTypes().map(t => t.type);
    if (chartConfig.chartType && !supportedTypes.includes(chartConfig.chartType)) {
      result.isValid = false;
      result.errors.push('不支持的图表类型');
    }
    
    return result;
  }

  /**
   * 清除图表列表缓存
   */
  clearChartsCache() {
    const cacheKeys = cacheManager.getStats().memory.keys.filter(key => 
      key.includes('/api/charts?')
    );
    
    cacheKeys.forEach(key => {
      cacheManager.delete(key);
    });
  }

  /**
   * 清除特定图表缓存
   * @param {string} chartId - 图表ID
   */
  clearChartCache(chartId) {
    const cacheKeys = cacheManager.getStats().memory.keys.filter(key => 
      key.includes(`/api/charts/${chartId}`)
    );
    
    cacheKeys.forEach(key => {
      cacheManager.delete(key);
    });
  }

  /**
   * 清除所有图表相关缓存
   */
  clearAllCache() {
    const cacheKeys = cacheManager.getStats().memory.keys.filter(key => 
      key.includes('/api/charts') || key.includes('/api/data')
    );
    
    cacheKeys.forEach(key => {
      cacheManager.delete(key);
    });
  }
}

// 创建单例实例
const chartService = new ChartService();

export default chartService;
