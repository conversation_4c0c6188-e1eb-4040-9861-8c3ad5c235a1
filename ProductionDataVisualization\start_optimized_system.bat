@echo off
chcp 65001 > nul
title 🚀 极速优化系统启动器

echo ========================================
echo   🚀 极速优化系统启动器
echo   使用SqlBulkCopy + 并发批量处理
echo ========================================
echo.

REM 设置工作目录
cd /d %~dp0

echo [INFO] 停止现有服务...

REM 停止占用5000端口的进程（后端）
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000 ^| findstr LISTENING') do (
    echo 终止后端进程: %%a
    taskkill /F /PID %%a >nul 2>nul
)

REM 停止占用3000端口的进程（前端）
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000 ^| findstr LISTENING') do (
    echo 终止前端进程: %%a
    taskkill /F /PID %%a >nul 2>nul
)

echo [INFO] 现有服务已停止
echo.

echo [INFO] 启动极速优化后端...
echo 🚀 使用SqlBulkCopy + 10000行批次 + 并发处理

REM 启动优化后端
start "极速后端" cmd /k "cd backend\SqlServerAPI && echo 🚀 启动极速优化后端... && dotnet run"

echo [INFO] 等待后端启动...
timeout /t 15 /nobreak > nul

REM 检查后端是否启动
netstat -ano | findstr :5000 | findstr LISTENING >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ 极速后端已启动成功！
) else (
    echo ❌ 后端启动失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo [INFO] 启动前端...

REM 检查前端依赖
if not exist frontend\node_modules (
    echo [INFO] 安装前端依赖...
    cd frontend
    npm install --no-audit --no-fund
    cd ..
)

REM 创建.env文件
echo PORT=3000> frontend\.env
echo BROWSER=none>> frontend\.env
echo REACT_APP_API_URL=http://localhost:5000>> frontend\.env

REM 启动前端
start "前端" cmd /k "cd frontend && echo 🚀 启动前端... && npm start"

echo [INFO] 等待前端启动...
timeout /t 20 /nobreak > nul

REM 检查前端是否启动
netstat -ano | findstr :3000 | findstr LISTENING >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ 前端已启动成功！
) else (
    echo ❌ 前端启动失败，请检查错误信息
)

echo.
echo ========================================
echo   🚀 极速系统启动完成
echo ========================================
echo.
echo 🎯 性能优化特性:
echo ✅ SqlBulkCopy极速批量插入
echo ✅ 10000行大批次处理
echo ✅ 3路并发批量上传
echo ✅ 智能类型映射和转换
echo ✅ 实时性能监控
echo.
echo 📊 预期性能:
echo ✅ 小文件 (1K-5K行): 1-3秒
echo ✅ 中文件 (10K-50K行): 5-20秒
echo ✅ 大文件 (100K+行): 30-120秒
echo ✅ 速度: 10,000-50,000行/秒
echo.
echo 🌐 访问地址:
echo   前端: http://localhost:3000
echo   后端: http://localhost:5000
echo.
echo 📋 测试建议:
echo 1. 准备不同大小的测试文件
echo 2. 观察后端控制台的性能日志
echo 3. 注意前端的并发批次处理
echo 4. 验证SqlBulkCopy的速度提升
echo.

REM 打开浏览器
echo [INFO] 打开浏览器...
timeout /t 3 /nobreak > nul
start http://localhost:3000/data-import

echo.
echo 🎉 极速系统已启动！请开始测试性能优化效果。
echo.
echo 关键日志观察:
echo 后端: "⚡ SqlBulkCopy完成！插入 X 行，速度 X 行/秒"
echo 前端: "🚀 开始极速导入数据，批次大小: 10000"
echo.

pause
