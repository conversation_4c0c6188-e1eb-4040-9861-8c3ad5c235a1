using System;
using System.Data.SqlClient;
using Microsoft.Data.Sqlite;

class DataMigration
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== 数据迁移到SQL Server ===");
        
        var sqliteDbPath = @"SimpleBackend\bin\Release\net8.0\win-x64\publish\ProductionDataVisualization.db";
        var sqlServerConnection = "Server=localhost\\SQLEXPRESS;Database=ProductionDataVisualizationDb;Trusted_Connection=true;TrustServerCertificate=true;";
        
        Console.WriteLine("\n[1] 检查SQLite数据库...");
        if (!File.Exists(sqliteDbPath))
        {
            Console.WriteLine($"❌ SQLite数据库文件不存在: {sqliteDbPath}");
            return;
        }
        Console.WriteLine("✅ SQLite数据库文件存在");
        
        Console.WriteLine("\n[2] 连接到SQL Server...");
        try
        {
            using var sqlConnection = new SqlConnection(sqlServerConnection);
            await sqlConnection.OpenAsync();
            Console.WriteLine("✅ SQL Server连接成功");
            
            Console.WriteLine("\n[3] 创建SQL Server表结构...");
            await CreateTables(sqlConnection);
            
            Console.WriteLine("\n[4] 迁移用户数据...");
            await MigrateUsers(sqliteDbPath, sqlConnection);
            
            Console.WriteLine("\n[5] 创建默认数据...");
            await CreateDefaultData(sqlConnection);
            
            Console.WriteLine("\n=== 数据迁移完成 ===");
            Console.WriteLine("✅ 所有用户数据已迁移到SQL Server");
            Console.WriteLine("✅ 数据库: ProductionDataVisualizationDb");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 迁移失败: {ex.Message}");
        }
    }
    
    static async Task CreateTables(SqlConnection connection)
    {
        var createTablesSQL = @"
-- 用户表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        Id NVARCHAR(50) PRIMARY KEY,
        Username NVARCHAR(50) UNIQUE NOT NULL,
        Email NVARCHAR(100) UNIQUE NOT NULL,
        Password NVARCHAR(255) NOT NULL,
        FullName NVARCHAR(100) NOT NULL,
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        LastLoginTime DATETIME2 NULL,
        Role NVARCHAR(50) DEFAULT 'user'
    )
    PRINT '用户表创建成功'
END

-- 导入任务表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ImportTasks' AND xtype='U')
BEGIN
    CREATE TABLE ImportTasks (
        Id NVARCHAR(50) PRIMARY KEY,
        FileName NVARCHAR(255) NOT NULL,
        FileSize BIGINT NOT NULL,
        TotalRows INT DEFAULT 0,
        ProcessedRows INT DEFAULT 0,
        Status NVARCHAR(50) DEFAULT 'Pending',
        Progress DECIMAL(5,2) DEFAULT 0,
        CreatedBy NVARCHAR(255) NOT NULL,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        StartedAt DATETIME2 NULL,
        CompletedAt DATETIME2 NULL,
        ErrorMessage NVARCHAR(MAX) NULL,
        ConfigData NVARCHAR(MAX) NULL
    )
    PRINT '导入任务表创建成功'
END

-- 文件表映射
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FileTableMappings' AND xtype='U')
BEGIN
    CREATE TABLE FileTableMappings (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        FileName NVARCHAR(255) NOT NULL UNIQUE,
        TableName NVARCHAR(255) NOT NULL UNIQUE,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        LastImportAt DATETIME2 NULL,
        TotalRows INT DEFAULT 0,
        IsActive BIT DEFAULT 1
    )
    PRINT '文件表映射创建成功'
END

-- 系统配置表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SystemSettings' AND xtype='U')
BEGIN
    CREATE TABLE SystemSettings (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        SettingKey NVARCHAR(100) NOT NULL UNIQUE,
        SettingValue NVARCHAR(MAX) NOT NULL,
        Description NVARCHAR(500) NULL,
        Category NVARCHAR(50) DEFAULT 'General',
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        UpdatedAt DATETIME2 DEFAULT GETDATE()
    )
    PRINT '系统配置表创建成功'
END

-- 角色表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Roles' AND xtype='U')
BEGIN
    CREATE TABLE Roles (
        Id NVARCHAR(50) PRIMARY KEY,
        RoleName NVARCHAR(50) NOT NULL UNIQUE,
        Description NVARCHAR(500) NULL,
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETDATE()
    )
    PRINT '角色表创建成功'
END

-- 用户角色关联表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserRoles' AND xtype='U')
BEGIN
    CREATE TABLE UserRoles (
        UserId NVARCHAR(50) NOT NULL,
        RoleId NVARCHAR(50) NOT NULL,
        AssignedAt DATETIME2 DEFAULT GETDATE(),
        PRIMARY KEY (UserId, RoleId)
    )
    PRINT '用户角色关联表创建成功'
END";
        
        using var command = new SqlCommand(createTablesSQL, connection);
        await command.ExecuteNonQueryAsync();
        Console.WriteLine("✅ SQL Server表结构创建成功");
    }
    
    static async Task MigrateUsers(string sqliteDbPath, SqlConnection sqlConnection)
    {
        try
        {
            using var sqliteConnection = new SqliteConnection($"Data Source={sqliteDbPath}");
            await sqliteConnection.OpenAsync();
            
            var sqliteCommand = new SqliteCommand("SELECT * FROM Users", sqliteConnection);
            using var reader = await sqliteCommand.ExecuteReaderAsync();
            
            int userCount = 0;
            while (await reader.ReadAsync())
            {
                var insertCommand = new SqlCommand(@"
                    IF NOT EXISTS (SELECT 1 FROM Users WHERE Username = @Username)
                    BEGIN
                        INSERT INTO Users (Id, Username, Email, Password, FullName, IsActive, CreatedAt, Role)
                        VALUES (@Id, @Username, @Email, @Password, @FullName, @IsActive, GETDATE(), @Role)
                    END", sqlConnection);
                
                insertCommand.Parameters.AddWithValue("@Id", reader["Id"].ToString());
                insertCommand.Parameters.AddWithValue("@Username", reader["Username"].ToString());
                insertCommand.Parameters.AddWithValue("@Email", reader["Email"].ToString());
                insertCommand.Parameters.AddWithValue("@Password", reader["Password"].ToString());
                insertCommand.Parameters.AddWithValue("@FullName", reader["FullName"].ToString());
                insertCommand.Parameters.AddWithValue("@IsActive", Convert.ToInt32(reader["IsActive"]) == 1);
                insertCommand.Parameters.AddWithValue("@Role", reader["Role"].ToString());
                
                await insertCommand.ExecuteNonQueryAsync();
                userCount++;
                Console.WriteLine($"  ✅ 用户迁移成功: {reader["Username"]}");
            }
            
            Console.WriteLine($"✅ 总共迁移了 {userCount} 个用户");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ SQLite数据读取失败，将创建默认数据: {ex.Message}");
        }
    }
    
    static async Task CreateDefaultData(SqlConnection connection)
    {
        // 确保有管理员用户
        var adminCheckCommand = new SqlCommand("SELECT COUNT(*) FROM Users WHERE Username = 'admin'", connection);
        var adminExists = (int)await adminCheckCommand.ExecuteScalarAsync();
        
        if (adminExists == 0)
        {
            var createAdminCommand = new SqlCommand(@"
                INSERT INTO Users (Id, Username, Email, Password, FullName, Role)
                VALUES (NEWID(), 'admin', '<EMAIL>', 'admin123', '系统管理员', 'admin')", connection);
            await createAdminCommand.ExecuteNonQueryAsync();
            Console.WriteLine("✅ 默认管理员用户创建成功");
        }
        
        // 创建默认角色
        var roleCheckCommand = new SqlCommand("SELECT COUNT(*) FROM Roles", connection);
        var roleExists = (int)await roleCheckCommand.ExecuteScalarAsync();
        
        if (roleExists == 0)
        {
            var createRolesCommand = new SqlCommand(@"
                INSERT INTO Roles (Id, RoleName, Description) VALUES 
                (NEWID(), 'admin', '系统管理员'),
                (NEWID(), 'user', '普通用户'),
                (NEWID(), 'manager', '管理人员')", connection);
            await createRolesCommand.ExecuteNonQueryAsync();
            Console.WriteLine("✅ 默认角色创建成功");
        }
        
        // 创建默认系统配置
        var settingCheckCommand = new SqlCommand("SELECT COUNT(*) FROM SystemSettings", connection);
        var settingExists = (int)await settingCheckCommand.ExecuteScalarAsync();
        
        if (settingExists == 0)
        {
            var createSettingsCommand = new SqlCommand(@"
                INSERT INTO SystemSettings (SettingKey, SettingValue, Description, Category) VALUES 
                ('system.name', '生产数据可视化系统', '系统名称', 'System'),
                ('system.version', '1.0.0', '系统版本', 'System'),
                ('data.max_file_size', '50', '最大文件大小(MB)', 'DataImport'),
                ('data.allowed_extensions', '.xlsx,.xls,.csv', '允许的文件扩展名', 'DataImport'),
                ('ui.page_size', '10', '默认分页大小', 'UI'),
                ('security.session_timeout', '30', '会话超时时间(分钟)', 'Security')", connection);
            await createSettingsCommand.ExecuteNonQueryAsync();
            Console.WriteLine("✅ 默认系统配置创建成功");
        }
    }
}
