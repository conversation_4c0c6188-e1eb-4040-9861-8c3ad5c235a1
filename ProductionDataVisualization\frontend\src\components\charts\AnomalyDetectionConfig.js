import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Select, 
  InputNumber, 
  Switch, 
  Button, 
  Space, 
  Divider,
  Alert,
  Tooltip,
  Row,
  Col,
  Statistic
} from 'antd';
import { 
  SettingOutlined, 
  InfoCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined 
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { ANOMALY_THRESHOLDS, detectAnomalies } from '../../utils/chartUtils';

const { Option } = Select;

const AnomalyDetectionConfig = ({
  data = [],
  onConfigChange,
  onDetectionResult,
  initialConfig = {},
  visible = true
}) => {
  const [form] = Form.useForm();
  const [config, setConfig] = useState({
    enabled: false,
    method: 'zscore',
    threshold: null,
    autoThreshold: true,
    sensitivity: 'medium',
    ...initialConfig
  });
  const [detectionStats, setDetectionStats] = useState(null);
  const [previewResults, setPreviewResults] = useState([]);

  // 敏感度预设
  const sensitivityPresets = {
    low: {
      zscore: 3.0,
      iqr: 2.0,
      percentile: { UPPER: 98, LOWER: 2 }
    },
    medium: {
      zscore: 2.5,
      iqr: 1.5,
      percentile: { UPPER: 95, LOWER: 5 }
    },
    high: {
      zscore: 2.0,
      iqr: 1.2,
      percentile: { UPPER: 90, LOWER: 10 }
    }
  };

  // 获取默认阈值
  const getDefaultThreshold = (method, sensitivity) => {
    const presets = sensitivityPresets[sensitivity];
    switch (method) {
      case 'zscore':
        return presets.zscore;
      case 'iqr':
        return presets.iqr;
      case 'percentile':
        return presets.percentile;
      default:
        return ANOMALY_THRESHOLDS.STATISTICAL.Z_SCORE;
    }
  };

  // 更新配置
  const updateConfig = (newConfig) => {
    const updatedConfig = { ...config, ...newConfig };
    
    // 自动设置阈值
    if (updatedConfig.autoThreshold) {
      updatedConfig.threshold = getDefaultThreshold(updatedConfig.method, updatedConfig.sensitivity);
    }
    
    setConfig(updatedConfig);
    
    if (onConfigChange) {
      onConfigChange(updatedConfig);
    }
    
    // 实时预览检测结果
    if (updatedConfig.enabled && data.length > 0) {
      performDetection(updatedConfig);
    }
  };

  // 执行异常检测
  const performDetection = (detectionConfig = config) => {
    if (!data.length || !detectionConfig.enabled) {
      setDetectionStats(null);
      setPreviewResults([]);
      return;
    }

    try {
      const numericData = data.filter(d => typeof d === 'number' && !isNaN(d));
      if (numericData.length === 0) {
        setDetectionStats({ error: '没有有效的数值数据' });
        return;
      }

      const anomalies = detectAnomalies(
        numericData, 
        detectionConfig.method, 
        detectionConfig.threshold
      );

      const stats = {
        totalPoints: numericData.length,
        anomalies: anomalies.length,
        anomalyRate: (anomalies.length / numericData.length * 100).toFixed(2),
        severityDistribution: {
          high: anomalies.filter(a => a.severity === 'high').length,
          medium: anomalies.filter(a => a.severity === 'medium').length,
          low: anomalies.filter(a => a.severity === 'low').length
        }
      };

      setDetectionStats(stats);
      setPreviewResults(anomalies.slice(0, 10)); // 只显示前10个异常点

      if (onDetectionResult) {
        onDetectionResult(anomalies, stats);
      }
    } catch (error) {
      setDetectionStats({ error: error.message });
    }
  };

  // 表单值变化处理
  const handleFormChange = (changedValues, allValues) => {
    updateConfig(allValues);
  };

  // 重置配置
  const resetConfig = () => {
    const defaultConfig = {
      enabled: false,
      method: 'zscore',
      threshold: null,
      autoThreshold: true,
      sensitivity: 'medium'
    };
    form.setFieldsValue(defaultConfig);
    updateConfig(defaultConfig);
  };

  // 应用配置
  const applyConfig = () => {
    form.validateFields().then(values => {
      updateConfig(values);
      performDetection(values);
    });
  };

  useEffect(() => {
    form.setFieldsValue(config);
  }, [config, form]);

  useEffect(() => {
    if (config.enabled && data.length > 0) {
      performDetection();
    }
  }, [data]);

  if (!visible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        title={
          <Space>
            <SettingOutlined />
            异常检测配置
          </Space>
        }
        size="small"
        extra={
          <Space>
            <Button size="small" onClick={resetConfig}>
              重置
            </Button>
            <Button type="primary" size="small" onClick={applyConfig}>
              应用
            </Button>
          </Space>
        }
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={config}
          onValuesChange={handleFormChange}
          size="small"
        >
          <Form.Item
            name="enabled"
            valuePropName="checked"
          >
            <Switch 
              checkedChildren="启用" 
              unCheckedChildren="禁用"
              onChange={(checked) => updateConfig({ enabled: checked })}
            />
          </Form.Item>

          {config.enabled && (
            <>
              <Form.Item
                name="method"
                label={
                  <Space>
                    检测方法
                    <Tooltip title="选择异常检测算法">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                }
              >
                <Select>
                  <Option value="zscore">Z分数法</Option>
                  <Option value="iqr">四分位距法</Option>
                  <Option value="percentile">百分位法</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="sensitivity"
                label="检测敏感度"
              >
                <Select>
                  <Option value="low">低敏感度</Option>
                  <Option value="medium">中等敏感度</Option>
                  <Option value="high">高敏感度</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="autoThreshold"
                valuePropName="checked"
              >
                <Switch 
                  checkedChildren="自动阈值" 
                  unCheckedChildren="手动阈值"
                />
              </Form.Item>

              {!config.autoThreshold && (
                <Form.Item
                  name="threshold"
                  label="检测阈值"
                  rules={[{ required: true, message: '请输入检测阈值' }]}
                >
                  <InputNumber
                    min={0.1}
                    max={10}
                    step={0.1}
                    precision={1}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              )}

              <Divider />

              {/* 检测统计 */}
              {detectionStats && !detectionStats.error && (
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={8}>
                    <Col span={12}>
                      <Statistic
                        title="数据点"
                        value={detectionStats.totalPoints}
                        prefix={<CheckCircleOutlined />}
                        valueStyle={{ fontSize: '14px' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="异常点"
                        value={detectionStats.anomalies}
                        prefix={<ExclamationCircleOutlined />}
                        valueStyle={{ 
                          fontSize: '14px',
                          color: detectionStats.anomalies > 0 ? '#ff4d4f' : '#52c41a'
                        }}
                      />
                    </Col>
                  </Row>
                  <div style={{ marginTop: 8 }}>
                    <Statistic
                      title="异常率"
                      value={`${detectionStats.anomalyRate}%`}
                      valueStyle={{ fontSize: '12px' }}
                    />
                  </div>
                </div>
              )}

              {detectionStats?.error && (
                <Alert
                  message="检测失败"
                  description={detectionStats.error}
                  type="error"
                  size="small"
                  style={{ marginBottom: 16 }}
                />
              )}

              {/* 预览结果 */}
              {previewResults.length > 0 && (
                <div>
                  <div style={{ fontSize: '12px', fontWeight: 'bold', marginBottom: 8 }}>
                    异常点预览 (前10个):
                  </div>
                  <div style={{ maxHeight: '120px', overflow: 'auto' }}>
                    {previewResults.map((anomaly, index) => (
                      <div 
                        key={index}
                        style={{ 
                          fontSize: '11px', 
                          padding: '2px 0',
                          borderBottom: '1px solid #f0f0f0'
                        }}
                      >
                        <Space size="small">
                          <span>索引: {anomaly.index}</span>
                          <span>值: {anomaly.value.toFixed(2)}</span>
                          <span style={{ 
                            color: anomaly.severity === 'high' ? '#ff4d4f' : 
                                   anomaly.severity === 'medium' ? '#faad14' : '#13c2c2'
                          }}>
                            {anomaly.severity}
                          </span>
                        </Space>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}
        </Form>
      </Card>
    </motion.div>
  );
};

export default AnomalyDetectionConfig;
