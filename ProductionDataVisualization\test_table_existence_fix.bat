@echo off
title 测试表存在性检查修复

echo ========================================
echo   测试表存在性检查修复
echo ========================================
echo.

echo [INFO] 修复内容:
echo ✅ 在GetOrCreateDataTable中添加实际表存在性检查
echo ✅ 发现映射记录但表不存在时，删除无效映射
echo ✅ 在覆盖模式下检查表是否真的存在
echo ✅ 提供详细的错误信息和恢复建议
echo.

echo [INFO] 修复逻辑:
echo 1. 检查FileTableMappings中是否有映射记录
echo 2. 如果有映射，检查表是否真的存在
echo 3. 如果表不存在但有映射，删除无效映射
echo 4. 重新创建表和映射关系
echo 5. 在覆盖模式下，先验证表存在再清空
echo.

echo [INFO] 后端状态:
echo - 服务已启动: http://localhost:5000
echo - 表存在性检查: 已增强
echo - 错误处理: 已改进
echo.

echo [INFO] 启动前端...
cd frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 20 /nobreak

echo.
echo ========================================
echo   测试步骤
echo ========================================
echo.
echo 1. 模拟表结构问题:
echo    - 使用SQL Server Management Studio
echo    - 删除某个Data_开头的数据表
echo    - 但保留FileTableMappings中的映射记录
echo.
echo 2. 测试自动修复:
echo    - 访问: http://localhost:3000/data-import
echo    - 上传相同名称的文件
echo    - 观察后端日志中的修复过程
echo.
echo 3. 预期的修复日志:
echo    "表名一致，检查表是否实际存在: Data_xxx"
echo    "❌ 表不存在但有映射记录，删除无效映射: Data_xxx"
echo    "开始创建新表: Data_xxx"
echo    "✅ 表创建成功: Data_xxx"
echo.
echo 4. 测试覆盖模式:
echo    - 选择覆盖模式导入
echo    - 观察表存在性检查
echo    - 验证清空操作是否成功
echo.
echo 5. 验证结果:
echo    - 导入应该成功完成
echo    - 不再出现"对象名无效"错误
echo    - 数据正确存储到数据库
echo.

echo ========================================
echo   预期结果
echo ========================================
echo.
echo ✅ 自动检测并修复无效的表映射
echo ✅ 重新创建缺失的数据表
echo ✅ 覆盖模式正确处理表存在性
echo ✅ 提供清晰的错误信息和修复日志
echo ✅ 数据导入成功完成
echo.

pause
