@echo off
title 完整生产数据可视化系统启动

echo ==========================================
echo   完整生产数据可视化系统启动
echo   支持所有功能：注册、登录、用户管理
echo ==========================================
echo.

echo [1] 清理旧进程...
taskkill /F /IM SqlServerAPI.exe >nul 2>&1
taskkill /F /IM SimpleBackend.exe >nul 2>&1
echo    已清理旧进程

echo.
echo [2] 启动增强版SQLite后端...
cd /d "SimpleBackend\bin\Release\net8.0\win-x64\publish"

if not exist "SimpleBackend.exe" (
    echo    错误: 找不到 SimpleBackend.exe
    echo    请确保已正确构建项目
    pause
    exit /b 1
)

start /B SimpleBackend.exe
cd /d "%~dp0"

echo    等待后端启动...
timeout /t 10 /nobreak >nul

echo.
echo [3] 验证后端功能...

curl -s http://localhost:5000/api/health >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✓ 健康检查 - 正常
) else (
    echo    ✗ 健康检查 - 失败
    pause
    exit /b 1
)

curl -s http://localhost:5000/api/users >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✓ 用户管理API - 正常
) else (
    echo    ✗ 用户管理API - 失败
)

curl -s http://localhost:5000/api/auth/login >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✓ 认证API - 正常
) else (
    echo    ✗ 认证API - 失败
)

echo.
echo [4] 启动前端应用...
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✓ 前端应用已运行
) else (
    echo    启动React前端...
    cd /d "ProductionDataVisualization\frontend"
    start /B npm start
    cd /d "%~dp0"
    
    echo    等待前端启动...
    timeout /t 20 /nobreak >nul
)

echo.
echo [5] 系统功能验证...
echo.

echo 后端API状态:
curl -s http://localhost:5000/api/health
echo.
echo.

echo 用户数据统计:
curl -s http://localhost:5000/api/users | findstr "total"
echo.
echo.

echo ==========================================
echo   系统启动完成！
echo ==========================================
echo.
echo 🎉 所有功能已启用:
echo   ✓ 用户注册: http://localhost:3000/register
echo   ✓ 用户登录: http://localhost:3000/login
echo   ✓ 用户管理: 管理界面中的用户管理功能
echo   ✓ 数据导入: 支持Excel/CSV文件导入
echo   ✓ 数据可视化: 图表和仪表板
echo.
echo 🔐 默认管理员账户:
echo   用户名: admin
echo   密码: admin123
echo.
echo 📋 支持的API端点:
echo   /api/health - 系统健康检查
echo   /api/users - 用户管理 (CRUD操作)
echo   /api/auth/* - 用户认证 (登录/注册)
echo   /api/simple-auth/* - 简化认证API
echo.
echo 🚀 现在可以正常使用所有功能:
echo   - 用户注册不再出现404错误
echo   - 用户管理界面可以正常添加用户
echo   - 所有API路径都已支持
echo.

start http://localhost:3000
echo 正在打开浏览器...
echo.
echo 系统已完全就绪！
pause
