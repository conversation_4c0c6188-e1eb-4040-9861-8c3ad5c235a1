import React, { useState, useEffect } from 'react';
import { Card, Statistic, Row, Col, Progress, Button, Modal, Table, Tag } from 'antd';
import { 
  DashboardOutlined, 
  ThunderboltOutlined, 
  ExclamationCircleOutlined,
  ReloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { getPerformanceStats, resetPerformanceStats } from '../../services/api';
import cacheManager from '../../utils/cache';

/**
 * 性能监控组件
 */
const PerformanceMonitor = ({ visible, onClose }) => {
  const [stats, setStats] = useState({
    totalRequests: 0,
    slowRequests: 0,
    errorRequests: 0,
    averageResponseTime: 0,
    errorRate: '0%',
    slowRequestRate: '0%'
  });
  
  const [cacheStats, setCacheStats] = useState({
    memory: { size: 0, maxSize: 0, keys: [] },
    localStorage: { keys: [] }
  });
  
  const [refreshInterval, setRefreshInterval] = useState(null);

  // 更新统计数据
  const updateStats = () => {
    const apiStats = getPerformanceStats();
    const cacheStatsData = cacheManager.getStats();
    
    setStats(apiStats);
    setCacheStats(cacheStatsData);
  };

  // 组件挂载时开始定时更新
  useEffect(() => {
    if (visible) {
      updateStats();
      const interval = setInterval(updateStats, 2000); // 每2秒更新一次
      setRefreshInterval(interval);
      
      return () => {
        if (interval) {
          clearInterval(interval);
        }
      };
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }
  }, [visible]);

  // 重置统计数据
  const handleReset = () => {
    Modal.confirm({
      title: '确认重置',
      content: '确定要重置所有性能统计数据吗？',
      icon: <ExclamationCircleOutlined />,
      onOk: () => {
        resetPerformanceStats();
        updateStats();
      }
    });
  };

  // 清除缓存
  const handleClearCache = () => {
    Modal.confirm({
      title: '确认清除缓存',
      content: '确定要清除所有缓存数据吗？这可能会影响应用性能。',
      icon: <ExclamationCircleOutlined />,
      onOk: () => {
        cacheManager.clear();
        updateStats();
      }
    });
  };

  // 缓存详情表格列
  const cacheColumns = [
    {
      title: '缓存键',
      dataIndex: 'key',
      key: 'key',
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={type === 'memory' ? 'blue' : 'green'}>
          {type === 'memory' ? '内存' : '本地存储'}
        </Tag>
      ),
    }
  ];

  // 准备缓存数据
  const cacheData = [
    ...cacheStats.memory.keys.map(key => ({ key, type: 'memory' })),
    ...cacheStats.localStorage.keys.map(key => ({ key, type: 'localStorage' }))
  ];

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <DashboardOutlined />
          性能监控
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="clear" onClick={handleClearCache}>
          清除缓存
        </Button>,
        <Button key="reset" onClick={handleReset}>
          <ReloadOutlined />
          重置统计
        </Button>,
        <Button key="close" type="primary" onClick={onClose}>
          关闭
        </Button>
      ]}
    >
      <div style={{ marginBottom: '24px' }}>
        <Row gutter={16}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总请求数"
                value={stats.totalRequests}
                prefix={<ThunderboltOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="平均响应时间"
                value={stats.averageResponseTime}
                suffix="ms"
                precision={0}
                valueStyle={{ 
                  color: stats.averageResponseTime > 2000 ? '#cf1322' : '#3f8600' 
                }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="错误率"
                value={stats.errorRate}
                valueStyle={{ 
                  color: parseFloat(stats.errorRate) > 5 ? '#cf1322' : '#3f8600' 
                }}
                prefix={<ExclamationCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="慢请求率"
                value={stats.slowRequestRate}
                valueStyle={{ 
                  color: parseFloat(stats.slowRequestRate) > 10 ? '#cf1322' : '#3f8600' 
                }}
              />
            </Card>
          </Col>
        </Row>
      </div>

      <Card title="请求性能分析" style={{ marginBottom: '16px' }}>
        <Row gutter={16}>
          <Col span={12}>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <span>成功请求</span>
                <span>{stats.totalRequests - stats.errorRequests}</span>
              </div>
              <Progress 
                percent={stats.totalRequests > 0 ? ((stats.totalRequests - stats.errorRequests) / stats.totalRequests * 100) : 0}
                strokeColor="#52c41a"
                showInfo={false}
              />
            </div>
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <span>错误请求</span>
                <span>{stats.errorRequests}</span>
              </div>
              <Progress 
                percent={stats.totalRequests > 0 ? (stats.errorRequests / stats.totalRequests * 100) : 0}
                strokeColor="#ff4d4f"
                showInfo={false}
              />
            </div>
          </Col>
          <Col span={12}>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <span>正常请求</span>
                <span>{stats.totalRequests - stats.slowRequests}</span>
              </div>
              <Progress 
                percent={stats.totalRequests > 0 ? ((stats.totalRequests - stats.slowRequests) / stats.totalRequests * 100) : 0}
                strokeColor="#1890ff"
                showInfo={false}
              />
            </div>
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <span>慢请求</span>
                <span>{stats.slowRequests}</span>
              </div>
              <Progress 
                percent={stats.totalRequests > 0 ? (stats.slowRequests / stats.totalRequests * 100) : 0}
                strokeColor="#faad14"
                showInfo={false}
              />
            </div>
          </Col>
        </Row>
      </Card>

      <Card 
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <InfoCircleOutlined />
            缓存状态
          </div>
        }
      >
        <Row gutter={16} style={{ marginBottom: '16px' }}>
          <Col span={8}>
            <Statistic
              title="内存缓存"
              value={`${cacheStats.memory.size}/${cacheStats.memory.maxSize}`}
              suffix="项"
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="本地存储缓存"
              value={cacheStats.localStorage.keys.length}
              suffix="项"
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="总缓存项"
              value={cacheStats.memory.size + cacheStats.localStorage.keys.length}
              suffix="项"
            />
          </Col>
        </Row>
        
        <Table
          columns={cacheColumns}
          dataSource={cacheData}
          size="small"
          pagination={{
            pageSize: 5,
            showSizeChanger: false,
            showQuickJumper: false
          }}
          scroll={{ y: 200 }}
          rowKey="key"
        />
      </Card>
    </Modal>
  );
};

export default PerformanceMonitor;
