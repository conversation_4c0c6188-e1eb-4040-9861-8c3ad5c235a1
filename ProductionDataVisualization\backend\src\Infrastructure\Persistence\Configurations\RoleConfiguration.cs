using Domain.UserAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 角色实体配置
    /// </summary>
    public class RoleConfiguration : IEntityTypeConfiguration<Role>
    {
        public void Configure(EntityTypeBuilder<Role> builder)
        {
            builder.HasKey(r => r.Id);

            builder.Property(r => r.Name)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(r => r.Description)
                .HasMaxLength(200);

            builder.Property(r => r.CreatedAt)
                .IsRequired();

            builder.Property(r => r.ModifiedAt)
                .IsRequired(false);

            // 索引
            builder.HasIndex(r => r.Name).IsUnique();

            // 预定义角色数据
            builder.HasData(
                new Role(Role.AdminRoleId, "Admin", "系统管理员，拥有所有权限"),
                new Role(Role.DataImporterRoleId, "DataImporter", "数据导入员，可以导入和管理数据"),
                new Role(Role.ViewerRoleId, "Viewer", "查看者，只能查看数据和图表")
            );
        }
    }
} 