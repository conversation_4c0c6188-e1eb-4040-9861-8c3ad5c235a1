// 修复版本的认证服务 - 只使用SQL Server API，移除所有回退机制
import api from './api';

const SIMPLE_AUTH_ENDPOINT = '/api/simple-auth';

// 用户登录 - 只使用SQL Server API
const login = async (usernameOrEmail, password) => {
  console.log('🔐 开始用户登录，API地址:', api.defaults.baseURL);
  console.log('登录用户:', usernameOrEmail);
  
  try {
    const response = await api.post('/api/auth/login', {
      usernameOrEmail,
      password
    });

    if (response.data.token) {
      // 保存token和用户信息到localStorage
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify({
        id: response.data.userId,
        username: response.data.username,
        email: response.data.email,
        fullName: response.data.fullName,
        roles: response.data.roles,
        permissions: response.data.permissions
      }));
      
      // 设置登录成功保护窗口
      sessionStorage.setItem('loginSuccessTime', Date.now().toString());
      console.log('✅ 登录成功（SQL Server API）');
    }
    
    return response.data;
  } catch (error) {
    console.error('❌ 登录失败:', error.response?.data?.message || error.message);
    
    // 直接抛出错误，不使用任何回退机制
    throw error.response ? error.response.data : { message: '登录失败，请检查用户名和密码' };
  }
};

// 用户注册 - 只使用SQL Server API
const register = async (username, email, password, fullName) => {
  console.log('📝 开始用户注册，API地址:', api.defaults.baseURL);
  console.log('注册用户信息:', { username, email, fullName });
  
  try {
    const response = await api.post(`${SIMPLE_AUTH_ENDPOINT}/register`, {
      username,
      email,
      password,
      fullName
    });

    console.log('✅ 用户注册成功（SQL Server API）:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ 注册失败:', error.response?.data?.message || error.message);
    
    // 直接抛出错误，不使用localStorage回退
    throw error.response ? error.response.data : { message: '注册失败，请重试' };
  }
};

// 退出登录
const logout = () => {
  console.log('执行登出，清除认证缓存');
  
  // 清除localStorage中的所有认证相关数据
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  localStorage.removeItem('authToken');
  localStorage.removeItem('currentUser');
  localStorage.removeItem('userSession');
  localStorage.removeItem('registeredUsers'); // 清除本地注册用户
  
  // 清除sessionStorage中的所有认证相关数据
  sessionStorage.removeItem('token');
  sessionStorage.removeItem('user');
  sessionStorage.removeItem('authToken');
  sessionStorage.removeItem('currentUser');
  sessionStorage.removeItem('userSession');
  sessionStorage.removeItem('loginSuccessTime');
  
  console.log('所有认证缓存已清除');
};

// 获取当前用户信息
const getCurrentUser = async () => {
  try {
    const user = localStorage.getItem('user');
    if (user) {
      return JSON.parse(user);
    }
    return null;
  } catch (error) {
    console.error('获取当前用户信息失败:', error);
    return null;
  }
};

// 检查是否已认证
const isAuthenticated = () => {
  try {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    
    if (!token || !user) {
      return false;
    }
    
    // 简单的token有效性检查
    const userData = JSON.parse(user);
    return !!(token && userData && userData.username);
  } catch (error) {
    console.error('认证检查失败:', error);
    return false;
  }
};

// 获取用户信息
const getUser = () => {
  try {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
};

// 检查用户权限
const hasPermission = (permission) => {
  try {
    const user = getUser();
    return user && user.permissions && user.permissions.includes(permission);
  } catch (error) {
    console.error('权限检查失败:', error);
    return false;
  }
};

// 检查用户角色
const hasRole = (role) => {
  try {
    const user = getUser();
    return user && user.roles && user.roles.includes(role);
  } catch (error) {
    console.error('角色检查失败:', error);
    return false;
  }
};

// 获取用户权限列表
const getUserPermissions = () => {
  try {
    const user = getUser();
    return user ? user.permissions || [] : [];
  } catch (error) {
    console.error('获取用户权限失败:', error);
    return [];
  }
};

// 获取用户角色列表
const getUserRoles = () => {
  try {
    const user = getUser();
    return user ? user.roles || [] : [];
  } catch (error) {
    console.error('获取用户角色失败:', error);
    return [];
  }
};

const authService = {
  login,
  register,
  logout,
  getCurrentUser,
  isAuthenticated,
  getUser,
  hasPermission,
  hasRole,
  getUserPermissions,
  getUserRoles
};

export default authService;
