import React, { useEffect, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import { Spin } from 'antd';
import PropTypes from 'prop-types';

/**
 * EnhancedLineChart - 增强的折线图组件
 * 
 * 特性：
 * - 现代简约浅色主题配色
 * - 动画加载效果
 * - 响应式设计
 * - 自定义配置选项
 * - 支持面积图和普通折线图
 */
const EnhancedLineChart = ({
  data,
  xAxisData,
  series,
  title,
  subtitle,
  loading = false,
  height = '300px',
  darkMode = false,
  animation = true,
  showLegend = true,
  legendPosition = 'top',
  smooth = true,
  area = false,
  customOptions = {}
}) => {
  const [chartOptions, setChartOptions] = useState({});
  
  // 现代简约黑白灰主题配色
  const themeColors = {
    light: {
      primary: '#333333',
      secondary: '#666666',
      tertiary: '#999999',
      quaternary: '#555555',
      accent: '#777777',
      background: '#FFFFFF',
      textPrimary: '#333333',
      textSecondary: '#666666',
      axisTick: 'rgba(204, 204, 204, 0.6)',
      splitLine: 'rgba(224, 224, 224, 0.6)',
      areaColor: 'rgba(51, 51, 51, 0.1)',
      // 渐变色
      gradients: {
        main: ['#333333', 'rgba(51, 51, 51, 0.5)'],
        secondary: ['#666666', 'rgba(102, 102, 102, 0.5)'],
        tertiary: ['#999999', 'rgba(153, 153, 153, 0.5)'],
        quaternary: ['#555555', 'rgba(85, 85, 85, 0.5)'],
        accent: ['#777777', 'rgba(119, 119, 119, 0.5)']
      },
      // 面积图渐变
      areaGradients: {
        main: ['rgba(51, 51, 51, 0.2)', 'rgba(51, 51, 51, 0.02)'],
        secondary: ['rgba(102, 102, 102, 0.2)', 'rgba(102, 102, 102, 0.02)'],
        tertiary: ['rgba(153, 153, 153, 0.2)', 'rgba(153, 153, 153, 0.02)'],
        quaternary: ['rgba(85, 85, 85, 0.2)', 'rgba(85, 85, 85, 0.02)'],
        accent: ['rgba(119, 119, 119, 0.2)', 'rgba(119, 119, 119, 0.02)']
      }
    },
    dark: {
      primary: '#CCCCCC',
      secondary: '#999999',
      tertiary: '#777777',
      quaternary: '#AAAAAA',
      accent: '#888888',
      background: '#222222',
      textPrimary: '#FFFFFF',
      textSecondary: '#CCCCCC',
      axisTick: 'rgba(153, 153, 153, 0.2)',
      splitLine: 'rgba(153, 153, 153, 0.1)',
      areaColor: 'rgba(204, 204, 204, 0.1)',
      // 渐变色
      gradients: {
        main: ['#CCCCCC', 'rgba(204, 204, 204, 0.5)'],
        secondary: ['#999999', 'rgba(153, 153, 153, 0.5)'],
        tertiary: ['#777777', 'rgba(119, 119, 119, 0.5)'],
        quaternary: ['#AAAAAA', 'rgba(170, 170, 170, 0.5)'],
        accent: ['#888888', 'rgba(136, 136, 136, 0.5)']
      },
      // 面积图渐变
      areaGradients: {
        main: ['rgba(204, 204, 204, 0.3)', 'rgba(204, 204, 204, 0.05)'],
        secondary: ['rgba(153, 153, 153, 0.3)', 'rgba(153, 153, 153, 0.05)'],
        tertiary: ['rgba(119, 119, 119, 0.3)', 'rgba(119, 119, 119, 0.05)'],
        quaternary: ['rgba(170, 170, 170, 0.3)', 'rgba(170, 170, 170, 0.05)'],
        accent: ['rgba(136, 136, 136, 0.3)', 'rgba(136, 136, 136, 0.05)']
      }
    }
  };
  
  // 选择主题
  const theme = darkMode ? themeColors.dark : themeColors.light;
  
  // 默认颜色数组 - 现代简约黑白灰配色
  const defaultColors = [
    theme.primary,
    theme.secondary,
    theme.tertiary,
    theme.quaternary,
    theme.accent
  ];
  
  // 生成图表配置
  useEffect(() => {
    // 基础配置
    const baseOptions = {
      backgroundColor: theme.background,
      title: {
        text: title,
        subtext: subtitle,
        left: 'center',
        textStyle: {
          color: theme.textPrimary,
          fontWeight: 500,
          fontFamily: 'Inter, sans-serif'
        },
        subtextStyle: {
          color: theme.textSecondary,
          fontFamily: 'Inter, sans-serif'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
          lineStyle: {
            color: theme.primary,
            width: 1,
            type: 'dashed'
          }
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: 'rgba(226, 232, 240, 0.8)',
        textStyle: {
          color: theme.textPrimary
        },
        extraCssText: 'box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05); border-radius: 8px;'
      },
      legend: {
        show: showLegend,
        top: legendPosition === 'top' ? '5%' : 'auto',
        bottom: legendPosition === 'bottom' ? '5%' : 'auto',
        left: legendPosition === 'left' ? '5%' : 'center',
        right: legendPosition === 'right' ? '5%' : 'auto',
        orient: ['left', 'right'].includes(legendPosition) ? 'vertical' : 'horizontal',
        textStyle: {
          color: theme.textSecondary,
          fontFamily: 'Inter, sans-serif'
        },
        itemGap: 20
      },
      grid: {
        top: title ? '15%' : '10%',
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: theme.axisTick
          }
        },
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: theme.axisTick
          }
        },
        axisLabel: {
          color: theme.textSecondary,
          fontFamily: 'Inter, sans-serif',
          fontSize: 12,
          margin: 12
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: theme.textSecondary,
          fontFamily: 'Inter, sans-serif',
          fontSize: 12
        },
        splitLine: {
          lineStyle: {
            color: theme.splitLine,
            type: 'dashed'
          }
        }
      },
      series: series || (data ? [{
        name: '数据',
        type: 'line',
        smooth: smooth,
        symbol: 'circle',
        symbolSize: 6,
        showSymbol: false,
        data: data,
        lineStyle: {
          width: 3,
          color: theme.primary,
          shadowColor: 'rgba(51, 51, 51, 0.3)',
          shadowBlur: 5
        },
        itemStyle: {
          color: theme.primary,
          borderColor: theme.primary,
          borderWidth: 2
        },
        emphasis: {
          lineStyle: {
            width: 4,
            shadowBlur: 10
          },
          itemStyle: {
            borderWidth: 3,
            shadowBlur: 10,
            shadowColor: 'rgba(51, 51, 51, 0.5)'
          }
        },
        areaStyle: area ? {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, 
              color: theme.areaGradients.main[0]
            }, {
              offset: 1, 
              color: theme.areaGradients.main[1]
            }]
          }
        } : undefined,
        animationDelay: function(idx) {
          return animation ? idx * 50 : 0;
        }
      }] : []),
      animationEasing: 'cubicInOut',
      animationDuration: 1000,
      animationDelayUpdate: function(idx) {
        return animation ? idx * 5 : 0;
      }
    };
    
    // 如果有多个系列数据，为每个系列设置不同的渐变色
    if (series && series.length > 0) {
      const gradientTypes = Object.keys(theme.gradients);
      const areaGradientTypes = Object.keys(theme.areaGradients);
      
      baseOptions.series = series.map((item, index) => {
        const gradientType = gradientTypes[index % gradientTypes.length];
        const areaGradientType = areaGradientTypes[index % areaGradientTypes.length];
        const gradientColor = theme.gradients[gradientType][0];
        const areaGradientColors = theme.areaGradients[areaGradientType];
        
        return {
          ...item,
          smooth: item.smooth !== undefined ? item.smooth : smooth,
          symbol: item.symbol || 'circle',
          symbolSize: item.symbolSize || 6,
          showSymbol: item.showSymbol !== undefined ? item.showSymbol : false,
          lineStyle: {
            ...item.lineStyle,
            width: (item.lineStyle && item.lineStyle.width) || 3,
            color: (item.lineStyle && item.lineStyle.color) || gradientColor,
            shadowColor: 'rgba(14, 165, 233, 0.3)',
            shadowBlur: 5
          },
          itemStyle: {
            ...item.itemStyle,
            color: (item.itemStyle && item.itemStyle.color) || gradientColor,
            borderColor: (item.itemStyle && item.itemStyle.borderColor) || gradientColor,
            borderWidth: (item.itemStyle && item.itemStyle.borderWidth) || 2
          },
          emphasis: {
            ...item.emphasis,
            lineStyle: {
              ...((item.emphasis && item.emphasis.lineStyle) || {}),
              width: 4,
              shadowBlur: 10
            },
            itemStyle: {
              ...((item.emphasis && item.emphasis.itemStyle) || {}),
              borderWidth: 3,
              shadowBlur: 10,
              shadowColor: 'rgba(14, 165, 233, 0.5)'
            }
          },
          areaStyle: area ? {
            ...item.areaStyle,
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, 
                color: areaGradientColors[0]
              }, {
                offset: 1, 
                color: areaGradientColors[1]
              }]
            }
          } : item.areaStyle,
          animationDelay: function(idx) {
            return animation ? idx * 50 + index * 100 : 0;
          }
        };
      });
    }
    
    // 合并自定义选项
    const finalOptions = {
      ...baseOptions,
      ...customOptions
    };
    
    setChartOptions(finalOptions);
  }, [data, xAxisData, series, title, subtitle, darkMode, animation, showLegend, legendPosition, smooth, area, customOptions, theme]);
  
  return (
    <div style={{ position: 'relative', height }}>
      {loading ? (
        <div style={{ 
          position: 'absolute', 
          top: 0, 
          left: 0, 
          right: 0, 
          bottom: 0, 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          background: 'rgba(255, 255, 255, 0.7)',
          borderRadius: '8px',
          zIndex: 10
        }}>
          <Spin size="large" />
        </div>
      ) : null}
      <ReactECharts
        option={chartOptions}
        style={{ height: '100%', width: '100%' }}
        opts={{ renderer: 'canvas' }}
      />
    </div>
  );
};

EnhancedLineChart.propTypes = {
  data: PropTypes.array,
  xAxisData: PropTypes.array,
  series: PropTypes.array,
  title: PropTypes.string,
  subtitle: PropTypes.string,
  loading: PropTypes.bool,
  height: PropTypes.string,
  darkMode: PropTypes.bool,
  animation: PropTypes.bool,
  showLegend: PropTypes.bool,
  legendPosition: PropTypes.oneOf(['top', 'bottom', 'left', 'right']),
  smooth: PropTypes.bool,
  area: PropTypes.bool,
  customOptions: PropTypes.object
};

export default EnhancedLineChart; 