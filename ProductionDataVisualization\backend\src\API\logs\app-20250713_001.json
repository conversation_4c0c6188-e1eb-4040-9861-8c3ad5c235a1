{"@t":"2025-07-13T15:41:02.8722567Z","@mt":"Hosting failed to start","@l":"Error","@x":"System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.\r\n ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。\r\n ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。\r\n   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)\r\n   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)\r\n   at System.Net.Sockets.Socket.Bind(EndPoint localEP)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()\r\n   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   --- End of inner exception stack trace ---\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.DefaultAddressStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)\r\n   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)\r\n   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)","EventId":{"Id":11,"Name":"HostedServiceStartupFaulted"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-13T15:41:02.9503599Z","@mt":"BackgroundService failed","@l":"Error","@x":"System.Threading.Tasks.TaskCanceledException: A task was canceled.\r\n   at API.Middleware.RateLimitCleanupService.ExecuteAsync(CancellationToken stoppingToken) in C:\\Users\\<USER>\\Documents\\Trae_Files\\Report\\ProductionDataVisualization\\backend\\src\\API\\Middleware\\RateLimitingMiddleware.cs:line 227\r\n   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)","EventId":{"Id":9,"Name":"BackgroundServiceFaulted"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-13T15:41:02.9614345Z","@mt":"The HostOptions.BackgroundServiceExceptionBehavior is configured to StopHost. A BackgroundService has thrown an unhandled exception, and the IHost instance is stopping. To avoid this behavior, configure this to Ignore; however the BackgroundService will not be restarted.","@l":"Fatal","@x":"System.Threading.Tasks.TaskCanceledException: A task was canceled.\r\n   at API.Middleware.RateLimitCleanupService.ExecuteAsync(CancellationToken stoppingToken) in C:\\Users\\<USER>\\Documents\\Trae_Files\\Report\\ProductionDataVisualization\\backend\\src\\API\\Middleware\\RateLimitingMiddleware.cs:line 227\r\n   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)","EventId":{"Id":10,"Name":"BackgroundServiceStoppingHost"},"SourceContext":"Microsoft.Extensions.Hosting.Internal.Host","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-13T15:41:56.1985166Z","@mt":"Now listening on: {address}","address":"http://localhost:5000","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-13T15:41:56.2362043Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-13T15:41:56.2382763Z","@mt":"Hosting environment: {EnvName}","EnvName":"Production","SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-13T15:41:56.2398932Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"C:\\Users\\<USER>\\Documents\\Trae_Files\\Report\\ProductionDataVisualization\\backend\\src\\API","SourceContext":"Microsoft.Hosting.Lifetime","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-13T15:43:45.6858272Z","@mt":"HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms","@r":["226.2184"],"@tr":"********************************","@sp":"dc353b0e135b4227","RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.22621.1","ClientIP":"::1","RequestMethod":"GET","RequestPath":"/api/health","StatusCode":200,"Elapsed":226.2184,"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE24G6KUL0T:00000001","ConnectionId":"0HNE24G6KUL0T","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-13T15:44:28.3003707Z","@mt":"HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms","@r":["21814.8393"],"@l":"Warning","@tr":"bb80504ab61505ae5fbce4bdbf9c08e1","@sp":"bbb4cfa8863b60a4","RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.22621.1","ClientIP":"::1","RequestMethod":"GET","RequestPath":"/api/health/detailed","StatusCode":200,"Elapsed":21814.8393,"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE24G6KUL0T:00000002","ConnectionId":"0HNE24G6KUL0T","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-13T15:44:28.3067643Z","@mt":"慢请求检测: {Method} {Path} 耗时 {ElapsedMs}ms","@l":"Warning","@tr":"bb80504ab61505ae5fbce4bdbf9c08e1","@sp":"bbb4cfa8863b60a4","Method":"GET","Path":"/api/health/detailed","ElapsedMs":21820,"SourceContext":"API.Extensions.PerformanceMonitoringMiddleware","RequestId":"0HNE24G6KUL0T:00000002","RequestPath":"/api/health/detailed","ConnectionId":"0HNE24G6KUL0T","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-13T15:44:51.1130648Z","@mt":"HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms","@r":["2.9313"],"@tr":"4532b05a89c23c53c583ff75a9940b9e","@sp":"70c763324f21ff91","RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","RequestMethod":"GET","RequestPath":"/","StatusCode":200,"Elapsed":2.9313,"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE24G6KUL0U:00000001","ConnectionId":"0HNE24G6KUL0U","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-13T15:53:22.7248494Z","@mt":"HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms","@r":["14.3425"],"@tr":"77cc6ac53d17cc10b8875409ada34e23","@sp":"8fced268b13ead68","RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","RequestMethod":"GET","RequestPath":"/api/health","StatusCode":200,"Elapsed":14.3425,"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE24G6KUL10:00000001","ConnectionId":"0HNE24G6KUL10","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-13T15:53:22.8206450Z","@mt":"HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms","@r":["0.5563"],"@l":"Warning","@tr":"cf5ab91d8b439d8dc40b45d7555850eb","@sp":"66df2309602d4e6c","RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","RequestMethod":"GET","RequestPath":"/favicon.ico","StatusCode":404,"Elapsed":0.5563,"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE24G6KUL10:00000002","ConnectionId":"0HNE24G6KUL10","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-13T15:53:53.0857319Z","@mt":"HTTP {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms","@r":["21118.4354"],"@l":"Warning","@tr":"4c8fb66950456adad5bd36768c5398da","@sp":"c7b3e69c365185c3","RequestHost":"localhost:5000","RequestScheme":"http","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ClientIP":"::1","RequestMethod":"GET","RequestPath":"/api/health/detailed","StatusCode":200,"Elapsed":21118.4354,"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware","RequestId":"0HNE24G6KUL10:00000003","ConnectionId":"0HNE24G6KUL10","Application":"ProductionDataVisualization","Environment":"Production"}
{"@t":"2025-07-13T15:53:53.0909801Z","@mt":"慢请求检测: {Method} {Path} 耗时 {ElapsedMs}ms","@l":"Warning","@tr":"4c8fb66950456adad5bd36768c5398da","@sp":"c7b3e69c365185c3","Method":"GET","Path":"/api/health/detailed","ElapsedMs":21123,"SourceContext":"API.Extensions.PerformanceMonitoringMiddleware","RequestId":"0HNE24G6KUL10:00000003","RequestPath":"/api/health/detailed","ConnectionId":"0HNE24G6KUL10","Application":"ProductionDataVisualization","Environment":"Production"}
