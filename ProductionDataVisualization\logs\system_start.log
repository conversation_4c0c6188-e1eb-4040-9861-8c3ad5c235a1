系统启动时间: 周一 2025/07/28 15:41:30.26 
检查环境... 
环境检查通过 / Environment check passed. 
停止可能已经运行的服务... 
服务已停止 / Services stopped. 
启动后端API... 
使用后台方式启动.NET Core SQL Server API... 
[信息/INFO] 使用已构建的SQL Server发布版本 / Using pre-built SQL Server release version 
等待API启动... 
[成功/SUCCESS] 后端API已启动 / Backend API started successfully 
启动前端... 
创建.env文件... / Creating .env file... 
尝试启动前端... / Trying to start frontend... 
使用npm start启动开发服务器... / Starting development server with npm start... 
等待前端启动... / Waiting for frontend to start... 
[成功/SUCCESS] 前端已启动在端口3000 / Frontend started successfully on port 3000 
 
=================================================== 
   系统状态 / System Status 
=================================================== 
 
[成功/SUCCESS] 后端API正在运行 / Backend API is running 
[成功/SUCCESS] 前端服务正在运行 / Frontend service is running 
系统启动完成 / System startup completed 
打开浏览器... / Opening browser... 
系统启动器已完成任务 / System starter has completed its task 
