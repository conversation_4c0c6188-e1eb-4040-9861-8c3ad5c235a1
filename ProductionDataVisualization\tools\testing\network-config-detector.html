<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络配置自动检测器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #fff;
            text-align: center;
        }
        .info-card {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 4px solid #4CAF50;
        }
        .warning-card {
            background: rgba(255,193,7,0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 4px solid #FFC107;
        }
        .error-card {
            background: rgba(244,67,54,0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 4px solid #F44336;
        }
        .config-display {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        button:hover {
            background: #45a049;
        }
        button.warning {
            background: #FF9800;
        }
        button.danger {
            background: #F44336;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #4CAF50; }
        .status-warning { background: #FF9800; }
        .status-error { background: #F44336; }
        .network-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 网络配置自动检测器</h1>
        <p style="text-align: center;">智能检测和配置API地址，解决IP地址变化问题</p>
        
        <div class="network-info">
            <div class="info-card">
                <h3>📍 当前访问信息</h3>
                <div id="current-info"></div>
            </div>
            
            <div class="info-card">
                <h3>🔗 API配置状态</h3>
                <div id="api-status"></div>
            </div>
        </div>
        
        <div class="info-card">
            <h3>🧪 API连接测试</h3>
            <button onclick="testAllAPIs()">测试所有可能的API地址</button>
            <button onclick="testCurrentAPI()">测试当前API配置</button>
            <div id="api-test-results"></div>
        </div>
        
        <div class="info-card">
            <h3>⚙️ 推荐配置</h3>
            <div id="recommended-config"></div>
            <button onclick="generateConfig()">生成配置文件</button>
            <button onclick="copyConfig()" class="warning">复制配置到剪贴板</button>
        </div>
        
        <div class="info-card">
            <h3>📋 配置说明</h3>
            <div class="config-display" id="config-explanation"></div>
        </div>
    </div>

    <script>
        let detectedConfig = {};
        
        function updateCurrentInfo() {
            const info = {
                hostname: window.location.hostname,
                port: window.location.port,
                protocol: window.location.protocol,
                origin: window.location.origin,
                href: window.location.href,
                userAgent: navigator.userAgent.substring(0, 50) + '...'
            };
            
            document.getElementById('current-info').innerHTML = `
                <div><strong>主机名:</strong> ${info.hostname}</div>
                <div><strong>端口:</strong> ${info.port || '默认'}</div>
                <div><strong>协议:</strong> ${info.protocol}</div>
                <div><strong>完整地址:</strong> ${info.origin}</div>
                <div><strong>浏览器:</strong> ${info.userAgent}</div>
            `;
            
            return info;
        }
        
        function detectAPIConfig() {
            const currentHost = window.location.hostname;
            const protocol = window.location.protocol;
            const apiPort = 5000;
            
            let recommendedAPI = '';
            let configType = '';
            let status = 'success';
            
            if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
                recommendedAPI = 'http://localhost:5000';
                configType = '开发环境';
            } else if (/^(\d{1,3}\.){3}\d{1,3}$/.test(currentHost)) {
                recommendedAPI = `${protocol}//${currentHost}:${apiPort}`;
                configType = 'IP地址访问（自适应）';
            } else {
                recommendedAPI = `${protocol}//${currentHost}:${apiPort}`;
                configType = '域名访问';
            }
            
            detectedConfig = {
                api: recommendedAPI,
                type: configType,
                host: currentHost,
                protocol: protocol
            };
            
            document.getElementById('api-status').innerHTML = `
                <div><span class="status-indicator status-${status}"></span><strong>检测类型:</strong> ${configType}</div>
                <div><strong>推荐API地址:</strong> ${recommendedAPI}</div>
                <div><strong>前端地址:</strong> ${protocol}//${currentHost}:3000</div>
            `;
            
            return detectedConfig;
        }
        
        async function testAPI(apiUrl, name) {
            try {
                const response = await fetch(`${apiUrl}/api/simple-auth/users`, {
                    method: 'GET',
                    headers: { 'Accept': 'application/json' },
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    return {
                        success: true,
                        url: apiUrl,
                        name: name,
                        userCount: data.items ? data.items.length : 0,
                        message: `连接成功，${data.items ? data.items.length : 0} 个用户`
                    };
                } else {
                    return {
                        success: false,
                        url: apiUrl,
                        name: name,
                        message: `HTTP ${response.status} ${response.statusText}`
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    url: apiUrl,
                    name: name,
                    message: error.message
                };
            }
        }
        
        async function testAllAPIs() {
            const currentHost = window.location.hostname;
            const protocol = window.location.protocol;
            
            const testUrls = [
                { url: `${protocol}//${currentHost}:5000`, name: '当前主机' },
                { url: 'http://localhost:5000', name: 'localhost' },
                { url: 'http://127.0.0.1:5000', name: '127.0.0.1' }
            ];
            
            document.getElementById('api-test-results').innerHTML = '<div>🔄 正在测试所有可能的API地址...</div>';
            
            const results = [];
            for (const test of testUrls) {
                const result = await testAPI(test.url, test.name);
                results.push(result);
            }
            
            let html = '<h4>测试结果:</h4>';
            results.forEach(result => {
                const statusClass = result.success ? 'status-success' : 'status-error';
                const statusIcon = result.success ? '✅' : '❌';
                html += `
                    <div style="margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 5px;">
                        <span class="status-indicator ${statusClass}"></span>
                        ${statusIcon} <strong>${result.name}</strong>: ${result.url}<br>
                        <small style="margin-left: 20px;">${result.message}</small>
                    </div>
                `;
            });
            
            const workingAPIs = results.filter(r => r.success);
            if (workingAPIs.length > 0) {
                html += `<div class="info-card"><strong>✅ 发现 ${workingAPIs.length} 个可用的API地址</strong></div>`;
                detectedConfig.workingAPI = workingAPIs[0].url;
            } else {
                html += `<div class="error-card"><strong>❌ 没有发现可用的API地址</strong></div>`;
            }
            
            document.getElementById('api-test-results').innerHTML = html;
        }
        
        async function testCurrentAPI() {
            const config = detectAPIConfig();
            const result = await testAPI(config.api, '当前配置');
            
            const statusClass = result.success ? 'info-card' : 'error-card';
            const statusIcon = result.success ? '✅' : '❌';
            
            document.getElementById('api-test-results').innerHTML = `
                <div class="${statusClass}">
                    ${statusIcon} <strong>当前API测试结果:</strong><br>
                    地址: ${result.url}<br>
                    结果: ${result.message}
                </div>
            `;
        }
        
        function generateConfig() {
            const config = detectAPIConfig();
            const workingAPI = detectedConfig.workingAPI || config.api;
            
            const envConfig = `# 自动生成的环境配置
# 生成时间: ${new Date().toLocaleString()}
# 检测类型: ${config.type}

HOST=0.0.0.0
PORT=3000
BROWSER=none
CHOKIDAR_USEPOLLING=true
REACT_APP_AUTO_DETECT_API=true`;

            const envProductionConfig = `# 生产环境配置 - 自动生成
# 生成时间: ${new Date().toLocaleString()}
# 检测到的主机: ${config.host}

REACT_APP_API_URL=${workingAPI}
REACT_APP_ENVIRONMENT=production
REACT_APP_VERSION=1.0.0`;

            document.getElementById('recommended-config').innerHTML = `
                <h4>.env 文件配置:</h4>
                <div class="config-display">${envConfig}</div>
                
                <h4>.env.production 文件配置:</h4>
                <div class="config-display">${envProductionConfig}</div>
            `;
            
            detectedConfig.envConfig = envConfig;
            detectedConfig.envProductionConfig = envProductionConfig;
        }
        
        function copyConfig() {
            if (!detectedConfig.envConfig) {
                generateConfig();
            }
            
            const fullConfig = `=== .env 文件 ===
${detectedConfig.envConfig}

=== .env.production 文件 ===
${detectedConfig.envProductionConfig}`;
            
            navigator.clipboard.writeText(fullConfig).then(() => {
                alert('配置已复制到剪贴板！');
            }).catch(() => {
                const textArea = document.createElement('textarea');
                textArea.value = fullConfig;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('配置已复制到剪贴板！');
            });
        }
        
        function updateConfigExplanation() {
            document.getElementById('config-explanation').textContent = `
🔧 自适应配置说明:

1. 智能检测模式:
   - 系统会自动检测当前访问的IP地址
   - 根据访问方式（localhost/IP/域名）选择最佳API配置

2. 配置优先级:
   - 环境变量 > 预定义配置 > 智能检测 > 备用方案

3. IP地址变化处理:
   - 重新访问页面时自动检测新的IP地址
   - 无需重新配置，系统自动适应

4. 使用方法:
   - 运行网络工具目录中的自动检测脚本
   - 或手动复制上面的配置到对应文件
   - 重启前端服务应用新配置
            `;
        }
        
        // 页面加载时初始化
        window.onload = function() {
            updateCurrentInfo();
            detectAPIConfig();
            updateConfigExplanation();
            
            // 自动测试当前API
            setTimeout(testCurrentAPI, 1000);
        };
    </script>
</body>
</html>
