/* 科技感白色登录界面 */
.split-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

/* 动态不规整灰色线条 */
.tech-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

/* 不规整线条路径 */
.irregular-line {
  position: absolute;
  fill: none;
  overflow: visible;
}

.irregular-line path {
  stroke: rgba(156, 163, 175, 0.3);
  stroke-width: 1.5;
  fill: none;
  stroke-dasharray: 8, 12;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.irregular-line path:nth-child(2) {
  stroke: rgba(156, 163, 175, 0.2);
  stroke-width: 1;
  stroke-dasharray: 4, 8;
  animation-delay: 2s;
}

.irregular-line-1 {
  top: 15%;
  left: 0;
  width: 100%;
  height: 200px;
}

.irregular-line-1 path:nth-child(1) {
  animation: drawLine1 14s ease-in-out infinite;
}

.irregular-line-1 path:nth-child(2) {
  animation: drawLine1 16s ease-in-out infinite reverse;
}

.irregular-line-2 {
  top: 45%;
  right: 0;
  width: 100%;
  height: 150px;
}

.irregular-line-2 path:nth-child(1) {
  animation: drawLine2 18s ease-in-out infinite reverse;
}

.irregular-line-2 path:nth-child(2) {
  animation: drawLine2 15s ease-in-out infinite;
}

.irregular-line-3 {
  bottom: 25%;
  left: 0;
  width: 100%;
  height: 180px;
}

.irregular-line-3 path:nth-child(1) {
  animation: drawLine3 12s ease-in-out infinite;
}

.irregular-line-3 path:nth-child(2) {
  animation: drawLine3 20s ease-in-out infinite reverse;
}

/* 简化背景效果 */
.split-login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(156, 163, 175, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(156, 163, 175, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(156, 163, 175, 0.025) 0%, transparent 50%);
  background-size: 400px 400px, 350px 350px, 300px 300px;
  animation: backgroundPulse 12s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

/* 科技粒子效果 */
.split-login-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 15% 25%, rgba(59, 130, 246, 0.12) 2px, transparent 2px),
    radial-gradient(circle at 85% 75%, rgba(16, 185, 129, 0.1) 1.5px, transparent 1.5px),
    radial-gradient(circle at 45% 85%, rgba(139, 92, 246, 0.08) 1px, transparent 1px),
    radial-gradient(circle at 75% 15%, rgba(59, 130, 246, 0.06) 2.5px, transparent 2.5px),
    radial-gradient(circle at 25% 75%, rgba(16, 185, 129, 0.09) 1px, transparent 1px),
    radial-gradient(circle at 95% 45%, rgba(139, 92, 246, 0.07) 1.5px, transparent 1.5px),
    radial-gradient(circle at 5% 85%, rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    radial-gradient(circle at 65% 25%, rgba(16, 185, 129, 0.08) 2px, transparent 2px);
  background-size:
    180px 180px, 220px 220px, 160px 160px, 200px 200px,
    140px 140px, 190px 190px, 170px 170px, 210px 210px;
  animation: particleFloat 15s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes backgroundPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 1;
  }
  25% {
    transform: translateY(-10px) translateX(5px);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-5px) translateX(-3px);
    opacity: 0.9;
  }
  75% {
    transform: translateY(-15px) translateX(8px);
    opacity: 0.7;
  }
}

/* 不规整线条动画 */
@keyframes drawLine1 {
  0% {
    stroke-dashoffset: 100;
    opacity: 0.2;
    transform: translateX(-30px) translateY(10px);
  }
  25% {
    stroke-dashoffset: 50;
    opacity: 0.6;
    transform: translateX(10px) translateY(-5px);
  }
  50% {
    stroke-dashoffset: 0;
    opacity: 0.8;
    transform: translateX(20px) translateY(15px);
  }
  75% {
    stroke-dashoffset: -50;
    opacity: 0.4;
    transform: translateX(-10px) translateY(-8px);
  }
  100% {
    stroke-dashoffset: -100;
    opacity: 0.2;
    transform: translateX(-30px) translateY(10px);
  }
}

@keyframes drawLine2 {
  0% {
    stroke-dashoffset: 80;
    opacity: 0.3;
    transform: translateX(25px) translateY(-12px);
  }
  30% {
    stroke-dashoffset: 40;
    opacity: 0.7;
    transform: translateX(-15px) translateY(8px);
  }
  60% {
    stroke-dashoffset: 0;
    opacity: 0.5;
    transform: translateX(30px) translateY(-5px);
  }
  100% {
    stroke-dashoffset: -80;
    opacity: 0.3;
    transform: translateX(25px) translateY(-12px);
  }
}

@keyframes drawLine3 {
  0% {
    stroke-dashoffset: 120;
    opacity: 0.25;
    transform: translateX(-20px) translateY(8px);
  }
  40% {
    stroke-dashoffset: 60;
    opacity: 0.6;
    transform: translateX(15px) translateY(-10px);
  }
  70% {
    stroke-dashoffset: 20;
    opacity: 0.8;
    transform: translateX(-5px) translateY(12px);
  }
  100% {
    stroke-dashoffset: -120;
    opacity: 0.25;
    transform: translateX(-20px) translateY(8px);
  }
}

/* 科技数据流效果 */
.data-stream {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.data-stream::before {
  content: '01001010 11010011 10110101 01110010 11001001 10101110 01011100 11100011';
  position: absolute;
  top: 10%;
  left: -100%;
  width: 200%;
  color: rgba(59, 130, 246, 0.1);
  font-family: 'Courier New', monospace;
  font-size: 12px;
  white-space: nowrap;
  animation: dataFlow1 25s linear infinite;
}

.data-stream::after {
  content: '11010110 00101101 10011010 01100111 10110100 01010011 11001010 00111001';
  position: absolute;
  bottom: 15%;
  right: -100%;
  width: 200%;
  color: rgba(16, 185, 129, 0.08);
  font-family: 'Courier New', monospace;
  font-size: 10px;
  white-space: nowrap;
  animation: dataFlow2 20s linear infinite reverse;
}

@keyframes dataFlow1 {
  0% { transform: translateX(0); }
  100% { transform: translateX(50%); }
}

@keyframes dataFlow2 {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

/* 科技几何装饰 */
.tech-geometry {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.tech-geometry::before {
  content: '';
  position: absolute;
  top: 15%;
  right: 10%;
  width: 80px;
  height: 80px;
  border: 2px solid rgba(59, 130, 246, 0.15);
  border-radius: 50%;
  animation: geometryRotate1 20s linear infinite;
}

.tech-geometry::after {
  content: '';
  position: absolute;
  bottom: 20%;
  left: 8%;
  width: 60px;
  height: 60px;
  border: 1px solid rgba(16, 185, 129, 0.12);
  transform: rotate(45deg);
  animation: geometryRotate2 15s linear infinite reverse;
}

@keyframes geometryRotate1 {
  0% { transform: rotate(0deg) scale(1); opacity: 0.15; }
  50% { transform: rotate(180deg) scale(1.1); opacity: 0.25; }
  100% { transform: rotate(360deg) scale(1); opacity: 0.15; }
}

@keyframes geometryRotate2 {
  0% { transform: rotate(45deg) scale(1); opacity: 0.12; }
  50% { transform: rotate(225deg) scale(0.9); opacity: 0.2; }
  100% { transform: rotate(405deg) scale(1); opacity: 0.12; }
}

/* 浅色浮动粒子效果 */
.split-login-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(59, 130, 246, 0.2), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(139, 92, 246, 0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(16, 185, 129, 0.15), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(99, 102, 241, 0.15), transparent);
  background-repeat: repeat;
  background-size: 150px 150px;
  animation: particleFloat 20s linear infinite;
  pointer-events: none;
}

@keyframes particleFloat {
  0% { transform: translateY(0px) translateX(0px); }
  33% { transform: translateY(-10px) translateX(5px); }
  66% { transform: translateY(5px) translateX(-5px); }
  100% { transform: translateY(0px) translateX(0px); }
}

/* 浅色系Logo设计 */
.top-logo {
  position: fixed;
  top: 32px;
  left: 32px;
  z-index: 1000;
}

.logo {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.logo:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.12),
    0 6px 20px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 1);
}

.logo-square {
  width: 40px !important;
  height: 40px !important;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #10b981 100%) !important;
  border-radius: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow:
    0 4px 16px rgba(59, 130, 246, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  border: none !important;
  position: relative !important;
  overflow: hidden !important;
}

.logo-square::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.logo:hover .logo-square::before {
  left: 100%;
}

.logo-text {
  color: #1e293b;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 0.5px;
}

/* 移除导航链接样式 */

/* 浅色系登录容器 */
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 32px;
  max-width: 420px;
  width: 100%;
  position: relative;
  z-index: 10;
}

/* 白色登录卡片 */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  border-radius: 20px;
  padding: 32px;
  width: 100%;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.08),
    0 10px 20px rgba(0, 0, 0, 0.04),
    0 5px 10px rgba(0, 0, 0, 0.02),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.login-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 40px 80px rgba(0, 0, 0, 0.12),
    0 20px 40px rgba(0, 0, 0, 0.06),
    0 10px 20px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 1);
}

/* 顶部装饰条 */
.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #10b981 100%);
  border-radius: 24px 24px 0 0;
}

/* 内部光效 */
.login-card::after {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  height: 100px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.6) 0%, transparent 100%);
  border-radius: 23px 23px 0 0;
  pointer-events: none;
}

/* 浅色系标题设计 */
.login-title {
  color: #1e293b !important;
  font-size: 28px !important;
  font-weight: 700 !important;
  margin-bottom: 6px !important;
  text-align: center !important;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  text-shadow: none !important;
  position: relative !important;
}

.login-subtitle {
  color: #64748b;
  font-size: 14px;
  text-align: center;
  margin-bottom: 24px;
  font-weight: 400;
  line-height: 1.5;
}

/* 浅色系表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
}

.remember-label {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #475569;
  font-size: 14px;
  cursor: pointer;
  font-weight: 500;
  transition: color 0.2s ease;
}

.remember-label:hover {
  color: #1e293b;
}

.remember-checkbox {
  width: 20px;
  height: 20px;
  border-radius: 6px;
  border: 2px solid #cbd5e1;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.remember-checkbox:checked {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-color: #3b82f6;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.remember-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.remember-text {
  user-select: none;
}

.forgot-link {
  background: none !important;
  border: none !important;
  color: #3b82f6 !important;
  font-size: 14px !important;
  cursor: pointer !important;
  text-decoration: none !important;
  transition: all 0.2s ease !important;
  font-weight: 500 !important;
  position: relative !important;
}

.forgot-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.forgot-link:hover {
  color: #2563eb !important;
}

.forgot-link:hover::after {
  width: 100%;
}

/* 浅色系表单容器 */
.login-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  color: #374151;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 12px;
}

/* 白色主题输入框样式 */
.ant-input,
.ant-input-password,
.ant-input-password-input,
.form-input,
.split-login-container .ant-input,
.split-login-container .ant-input-password,
.split-login-container .ant-input-password-input,
.ant-input-affix-wrapper,
.ant-input-password-wrapper,
.split-login-container .ant-input-affix-wrapper,
.split-login-container .ant-input-password-wrapper {
  height: 56px !important;
  background: #ffffff !important;
  border: 2px solid #e2e8f0 !important;
  border-radius: 16px !important;
  color: #1e293b !important;
  font-size: 16px !important;
  padding: 0 20px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.05),
    0 2px 6px rgba(0, 0, 0, 0.03),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
  font-weight: 500 !important;
  backdrop-filter: blur(12px) !important;
  position: relative !important;
}

/* 输入框占位符样式 */
.ant-input::placeholder,
.ant-input-password::placeholder,
.form-input::placeholder,
.split-login-container .ant-input::placeholder,
.split-login-container .ant-input-password::placeholder {
  color: #94a3b8 !important;
  font-weight: 400 !important;
  font-style: italic !important;
}

/* 输入框悬停状态 */
.ant-input:hover,
.ant-input-password:hover,
.ant-input-password-input:hover,
.ant-input-affix-wrapper:hover,
.ant-input-password-wrapper:hover,
.form-input:hover,
.split-login-container .ant-input:hover,
.split-login-container .ant-input-password:hover,
.split-login-container .ant-input-password-input:hover,
.split-login-container .ant-input-affix-wrapper:hover,
.split-login-container .ant-input-password-wrapper:hover {
  border-color: #cbd5e1 !important;
  background: #ffffff !important;
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 1) !important;
  transform: translateY(-2px) !important;
}

/* 输入框聚焦状态 */
.ant-input:focus,
.ant-input-password:focus,
.ant-input-password-input:focus,
.ant-input-affix-wrapper:focus,
.ant-input-password-wrapper:focus,
.ant-input.ant-input-focused,
.ant-input-password.ant-input-focused,
.ant-input-affix-wrapper.ant-input-affix-wrapper-focused,
.ant-input-password-wrapper.ant-input-affix-wrapper-focused,
.form-input:focus,
.form-input.ant-input-focused,
.split-login-container .ant-input:focus,
.split-login-container .ant-input-password:focus,
.split-login-container .ant-input-password-input:focus,
.split-login-container .ant-input-affix-wrapper:focus,
.split-login-container .ant-input-password-wrapper:focus,
.split-login-container .ant-input.ant-input-focused,
.split-login-container .ant-input-password.ant-input-focused,
.split-login-container .ant-input-affix-wrapper.ant-input-affix-wrapper-focused,
.split-login-container .ant-input-password-wrapper.ant-input-affix-wrapper-focused {
  border-color: #3b82f6 !important;
  background: #ffffff !important;
  box-shadow:
    0 0 0 4px rgba(59, 130, 246, 0.15),
    0 12px 32px rgba(0, 0, 0, 0.1),
    0 6px 16px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 1) !important;
  transform: translateY(-2px) !important;
}

/* 白色主题密码输入框 */
.ant-input-password {
  border: 2px solid #e2e8f0 !important;
  background: #ffffff !important;
  border-radius: 16px !important;
  backdrop-filter: blur(12px) !important;
}

.ant-input-password .ant-input {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  color: #1e293b !important;
  font-weight: 500 !important;
}

.ant-input-password:hover {
  border-color: #cbd5e1 !important;
  background: #ffffff !important;
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 1) !important;
  transform: translateY(-2px) !important;
}

.ant-input-password:focus-within,
.ant-input-password.ant-input-affix-wrapper-focused {
  border-color: #3b82f6 !important;
  background: #ffffff !important;
  box-shadow:
    0 0 0 4px rgba(59, 130, 246, 0.15),
    0 12px 32px rgba(0, 0, 0, 0.1),
    0 6px 16px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 1) !important;
  transform: translateY(-2px) !important;
}

.ant-input-password:focus-within .ant-input,
.ant-input-password.ant-input-affix-wrapper-focused .ant-input {
  border: none !important;
  box-shadow: none !important;
}

/* 密码显示/隐藏按钮 */
.ant-input-password .ant-input-suffix {
  color: #94a3b8 !important;
  transition: all 0.2s ease !important;
}

.ant-input-password .ant-input-suffix:hover {
  color: #3b82f6 !important;
  transform: scale(1.1) !important;
}

.send-code-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: #ff4444;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
}

.form-group-inline {
  position: relative;
}

/* 现代登录按钮 */
.gradient-button {
  height: 56px !important;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #10b981 100%) !important;
  border: none !important;
  border-radius: 16px !important;
  color: white !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  margin-top: 24px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  letter-spacing: 0.5px !important;
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.4),
    0 4px 16px rgba(139, 92, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  position: relative !important;
  overflow: hidden !important;
  text-transform: uppercase !important;
  backdrop-filter: blur(12px) !important;
}

/* 按钮光泽效果 */
.gradient-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.gradient-button:hover::before {
  left: 100%;
}

/* 按钮内部光效 */
.gradient-button::after {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  height: 50%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  border-radius: 15px 15px 0 0;
  pointer-events: none;
}

.gradient-button:hover {
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 50%, #059669 100%) !important;
  transform: translateY(-3px) scale(1.02) !important;
  box-shadow:
    0 16px 48px rgba(59, 130, 246, 0.5),
    0 8px 24px rgba(139, 92, 246, 0.4),
    0 0 32px rgba(16, 185, 129, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.gradient-button:active {
  transform: translateY(-1px) scale(1.01) !important;
  transition: all 0.1s ease !important;
}

/* 现代底部链接 */
.bottom-links {
  margin-top: 24px;
  text-align: center;
}

.social-login {
  margin-bottom: 24px;
}

.social-text {
  color: #64748b;
  font-size: 14px;
  margin-bottom: 20px;
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.social-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.social-btn {
  padding: 16px 28px;
  border: 2px solid #e2e8f0;
  background: #ffffff;
  color: #64748b;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 2px 8px rgba(0, 0, 0, 0.03),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  position: relative;
  overflow: hidden;
}

.social-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.social-btn:hover::before {
  left: 100%;
}

.social-btn:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #1e293b;
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 1);
}

.social-btn.wechat:hover {
  border-color: #07c160;
  color: #07c160;
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(7, 193, 96, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 1);
}

.social-btn.enterprise:hover {
  border-color: #576b95;
  color: #576b95;
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(87, 107, 149, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 1);
}

.help-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.help-link {
  color: #64748b;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 8px 16px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.help-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.help-link:hover::before {
  left: 100%;
}

.help-link:hover {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.separator {
  color: #cbd5e1;
  font-size: 16px;
  font-weight: 300;
}

/* 隐藏不需要的元素 */
.brand-section,
.decorative-elements,
.system-features,
.stats-section {
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-nav {
    padding: 0 20px;
  }

  .nav-links {
    display: none;
  }

  .login-container {
    padding: 100px 20px 20px;
    max-width: 350px;
  }

  .login-title {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 80px 16px 16px;
    max-width: 320px;
  }

  .login-title {
    font-size: 24px;
    margin-bottom: 32px;
  }

  .oauth-button {
    height: 44px;
    font-size: 13px;
  }

  .form-input {
    height: 44px !important;
    font-size: 13px !important;
  }

  .gradient-button {
    height: 44px !important;
    font-size: 13px !important;
  }
}

