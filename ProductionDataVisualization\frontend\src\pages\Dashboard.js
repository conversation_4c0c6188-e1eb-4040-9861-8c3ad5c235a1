import React, { useState, useEffect } from 'react';
import { Typography, Row, Col, Statistic, Progress, Space, Divider, Card, Button, FloatButton } from 'antd';
import {
  EyeOutlined,
  LineChartOutlined,
  ExperimentOutlined,
  MedicineBoxOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  <PERSON>U<PERSON>Outlined,
  ArrowDownOutlined,
  Bar<PERSON><PERSON>Outlined,
  DashboardOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import MainLayout from '../components/layout/MainLayout';
import authService from '../services/authService';
import AnimatedCard from '../components/common/AnimatedCard';
import { EnhancedBar<PERSON>hart, Enhanced<PERSON>ine<PERSON><PERSON>, EnhancedPie<PERSON>hart } from '../components/charts';
import PerformanceMonitor from '../components/common/PerformanceMonitor';
import { useApp } from '../contexts/AppContext';
import healthService from '../services/healthService';

const { Title, Text } = Typography;

// 现代简约黑白灰主题配色配置
const THEME_COLORS = {
  primary: '#333333',    // 深灰色
  secondary: '#666666',  // 中灰色
  tertiary: '#999999',   // 浅灰色
  quaternary: '#555555', // 中深灰色
  accent: '#777777',     // 中浅灰色
  light: '#E0E0E0',      // 浅灰色
  lighter: '#F5F5F5',    // 浅灰白色
  lightest: '#FFFFFF',   // 纯白色
  dark: '#333333',       // 深灰色
  darker: '#222222',     // 更深灰色
  success: '#2E7D32',    // 深绿色
  warning: '#ED6C02',    // 橙色
  error: '#D32F2F',      // 红色
  info: '#0288D1',       // 蓝色
  white: '#FFFFFF',      // 白色
  black: '#000000',      // 黑色
  border: '#E0E0E0',     // 浅灰色边框
  shadow: 'var(--shadow-md)',
  // 渐变
  gradients: {
    main: 'linear-gradient(135deg, #333333 0%, #555555 100%)',
    secondary: 'linear-gradient(135deg, #666666 0%, #888888 100%)',
    tertiary: 'linear-gradient(135deg, #999999 0%, #BBBBBB 100%)',
    light: 'linear-gradient(135deg, #FFFFFF 0%, #F5F5F5 100%)',
    success: 'linear-gradient(135deg, #2E7D32 0%, #388E3C 100%)',
    warning: 'linear-gradient(135deg, #ED6C02 0%, #F57C00 100%)',
    error: 'linear-gradient(135deg, #D32F2F 0%, #E53935 100%)'
  }
};

// 装饰性组件 - 几何图形
const GeometricDecoration = ({ type, color, size, top, left, right, bottom, rotate, opacity }) => {
  const decorationStyles = {
    position: 'absolute',
    width: size,
    height: size,
    top: top,
    left: left,
    right: right,
    bottom: bottom,
    opacity: opacity || 0.1,
    transform: `rotate(${rotate || 0}deg)`,
    zIndex: 0
  };
  
  switch(type) {
    case 'circle':
      return (
        <div 
          style={{
            ...decorationStyles,
            borderRadius: '50%',
            background: color
          }}
        />
      );
    case 'square':
      return (
        <div 
          style={{
            ...decorationStyles,
            background: color
          }}
        />
      );
    case 'triangle':
      return (
        <div 
          style={{
            ...decorationStyles,
            width: 0,
            height: 0,
            backgroundColor: 'transparent',
            borderLeft: `${size/2}px solid transparent`,
            borderRight: `${size/2}px solid transparent`,
            borderBottom: `${size}px solid ${color}`
          }}
        />
      );
    case 'donut':
      return (
        <div 
          style={{
            ...decorationStyles,
            borderRadius: '50%',
            border: `${size/6}px solid ${color}`,
            backgroundColor: 'transparent'
          }}
        />
      );
    default:
      return null;
  }
};

// 微妙的波浪装饰
const WaveDecoration = ({ color, opacity, top, bottom }) => {
  return (
    <div 
      style={{
        position: 'absolute',
        left: 0,
        right: 0,
        top: top,
        bottom: bottom,
        height: '40px',
        opacity: opacity || 0.1,
        zIndex: 0,
        overflow: 'hidden'
      }}
    >
      <svg 
        viewBox="0 0 1200 120" 
        preserveAspectRatio="none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          transform: 'rotate(180deg)'
        }}
      >
        <path 
          d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" 
          fill={color}
          opacity="1"
        />
      </svg>
    </div>
  );
};

const Dashboard = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [performanceMonitorVisible, setPerformanceMonitorVisible] = useState(false);
  const [systemHealth, setSystemHealth] = useState({ isConnected: false, responseTime: null });
  const { state } = useApp();
  
  useEffect(() => {
    const currentUser = authService.getUser();
    setUser(currentUser);

    // 模拟数据加载
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    // 启动健康监控
    const stopHealthMonitoring = healthService.startHealthMonitoring((health) => {
      setSystemHealth(health);
    }, 30000); // 每30秒检查一次

    return () => {
      clearTimeout(timer);
      stopHealthMonitoring();
    };
  }, []);
  
  // 生产数据
  const productionData = {
    xAxis: ['1月', '2月', '3月', '4月', '5月', '6月'],
    series: [
      {
        name: '计划产量',
        type: 'bar',
        data: [320, 332, 301, 334, 390, 330],
        itemStyle: {
          borderRadius: [4, 4, 0, 0]
        }
      },
      {
        name: '实际产量',
        type: 'bar',
        data: [300, 340, 290, 330, 380, 320],
        itemStyle: {
          borderRadius: [4, 4, 0, 0]
        }
      }
    ]
  };
  
  // 质量数据
  const qualityData = {
    xAxis: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    series: [
      {
        name: 'pH值',
        type: 'line',
        smooth: true,
        data: [7.2, 7.0, 7.1, 7.3, 7.2, 7.0, 7.1]
      },
      {
        name: '澄明度',
        type: 'line',
        smooth: true,
        data: [98, 97, 99, 98, 96, 97, 98]
      },
      {
        name: '杂质含量',
        type: 'line',
        smooth: true,
        data: [0.2, 0.3, 0.1, 0.2, 0.4, 0.3, 0.2]
      }
    ]
  };
  
  // 设备状态数据
  const equipmentData = [
    { value: 18, name: '正常运行' },
    { value: 3, name: '维护中' },
    { value: 1, name: '故障' },
    { value: 2, name: '待机' }
  ];
  
  // 批次状态数据
  const batchStatus = [
    {
      id: 'B20230601',
      name: '左氧氟沙星滴眼液',
      progress: 100,
      status: 'success',
      icon: <CheckCircleOutlined style={{ color: THEME_COLORS.success }} />
    },
    {
      id: 'B20230602',
      name: '玻璃酸钠滴眼液',
      progress: 75,
      status: 'active',
      icon: <ClockCircleOutlined style={{ color: THEME_COLORS.info }} />
    },
    {
      id: 'B20230603',
      name: '盐酸环丙沙星滴眼液',
      progress: 45,
      status: 'active',
      icon: <ClockCircleOutlined style={{ color: THEME_COLORS.info }} />
    },
    {
      id: 'B20230604',
      name: '氧氟沙星滴眼液',
      progress: 10,
      status: 'exception',
      icon: <WarningOutlined style={{ color: THEME_COLORS.error }} />
    }
  ];
  
  // 统计卡片变体配置
  const statsCardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.1 * i,
        duration: 0.5,
        ease: [0.25, 0.1, 0.25, 1]
      }
    }),
    hover: {
      y: -5,
      boxShadow: 'var(--shadow-xl)',
      transition: { duration: 0.3, ease: 'easeOut' }
    }
  };
  
  return (
    <MainLayout>
      <div className="dashboard-page">
        <motion.div 
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          style={{ 
            background: 'var(--bg-light)',
            padding: 'var(--space-lg)',
            borderRadius: 'var(--radius-lg)',
            marginBottom: 'var(--space-xl)',
            border: '1px solid var(--border-color)',
            position: 'relative',
            overflow: 'hidden',
            boxShadow: 'var(--shadow-md)',
            backgroundImage: 'var(--texture-noise)'
          }}
        >
          <div style={{
            position: 'absolute',
            top: 0,
            right: 0,
            width: '200px',
            height: '200px',
            background: `radial-gradient(circle, rgba(14, 165, 233, 0.1) 0%, rgba(14, 165, 233, 0) 70%)`,
            transform: 'translate(50%, -50%)'
          }}></div>
          
          <GeometricDecoration 
            type="circle" 
            color="rgba(14, 165, 233, 0.1)" 
            size={40} 
            bottom={10} 
            left={40} 
          />
          
          <GeometricDecoration 
            type="triangle" 
            color="rgba(139, 92, 246, 0.08)" 
            size={30} 
            bottom={30} 
            left={100} 
            rotate={45}
          />
          
          <Title level={2} style={{ 
            color: 'var(--text-primary)', 
            marginBottom: 'var(--space-xs)',
            position: 'relative',
            zIndex: 1,
            fontFamily: 'var(--font-family-secondary)',
            fontWeight: 'var(--font-weight-semibold)',
            letterSpacing: 'var(--letter-spacing-tight)'
          }}>
            <EyeOutlined style={{ 
              marginRight: 'var(--space-sm)', 
              color: 'var(--primary-color)',
              textShadow: 'var(--shadow-glow)'
            }} />
            眼药制剂生产数据可视化系统
          </Title>
          
          {user && (
            <Title level={4} style={{ 
              marginBottom: 'var(--space-md)', 
              color: 'var(--text-secondary)',
              fontFamily: 'var(--font-family-primary)',
              fontWeight: 'var(--font-weight-medium)',
              position: 'relative',
              zIndex: 1
            }}>
              {user.fullName ? `${user.fullName}，欢迎回来！` : `${user.username}，欢迎回来！`}
            </Title>
          )}
          
          <Text style={{ 
            fontSize: 'var(--font-size-md)', 
            color: 'var(--text-secondary)',
            position: 'relative',
            zIndex: 1
          }}>
            今日数据概览 - 实时监控眼药水生产各环节数据
          </Text>
          
          <WaveDecoration 
            color="var(--primary-lighter)" 
            opacity={0.15} 
            bottom={0} 
          />
        </motion.div>
        
        <Row gutter={[16, 16]}>
          {[
            {
              title: '今日产量',
              value: 1254,
              suffix: '升',
              color: THEME_COLORS.primary,
              change: 5.2,
              isPositive: true,
              icon: <LineChartOutlined />
            },
            {
              title: '良品率',
              value: 98.7,
              suffix: '%',
              color: THEME_COLORS.success,
              change: 0.3,
              isPositive: true,
              icon: <CheckCircleOutlined />
            },
            {
              title: '设备运行率',
              value: 95.2,
              suffix: '%',
              color: THEME_COLORS.info,
              change: -1.5,
              isPositive: false,
              icon: <MedicineBoxOutlined />
            },
            {
              title: '能耗指数',
              value: 86.3,
              suffix: 'kWh/kL',
              color: THEME_COLORS.warning,
              change: 2.1,
              isPositive: false,
              icon: <ExperimentOutlined />
            }
          ].map((stat, index) => (
            <Col xs={24} sm={12} md={6} key={stat.title}>
              <motion.div
                custom={index}
                variants={statsCardVariants}
                initial="hidden"
                animate="visible"
                whileHover="hover"
              >
                <Card 
                  style={{ 
                    borderRadius: 'var(--radius-lg)',
                    border: '1px solid var(--border-color)',
                    overflow: 'hidden',
                    height: '100%',
                    position: 'relative',
                    background: 'var(--bg-light)',
                    boxShadow: 'var(--shadow-md)',
                    transition: 'all var(--transition-normal) var(--ease-in-out)'
                  }}
                  bodyStyle={{ 
                    padding: 'var(--space-md)',
                    position: 'relative',
                    zIndex: 1
                  }}
                >
                  <div style={{ 
                    position: 'absolute', 
                    top: 0, 
                    left: 0, 
                    height: '4px', 
                    width: '100%', 
                    background: stat.color,
                    boxShadow: `0 0 10px ${stat.color}80`
                  }} />
                  
                  <div style={{ 
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    width: '80px',
                    height: '80px',
                    background: `radial-gradient(circle, ${stat.color}10 0%, transparent 70%)`,
                    transform: 'translate(30%, -30%)'
                  }} />
                  
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    marginBottom: 'var(--space-sm)'
                  }}>
                    <Text style={{ 
                      fontSize: 'var(--font-size-md)', 
                      color: 'var(--text-secondary)',
                      fontWeight: 'var(--font-weight-medium)'
                    }}>
                      {stat.title}
                    </Text>
                    <div style={{ 
                      width: '32px', 
                      height: '32px', 
                      borderRadius: '50%', 
                      background: `${stat.color}15`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: stat.color
                    }}>
                      {stat.icon}
                    </div>
                  </div>
                  
                  <Statistic 
                    value={stat.value} 
                    suffix={stat.suffix}
                    valueStyle={{ 
                      color: 'var(--text-primary)', 
                      fontSize: 'var(--font-size-xxl)',
                      fontWeight: 'var(--font-weight-semibold)',
                      fontFamily: 'var(--font-family-secondary)'
                    }} 
                  />
                  
                  <div style={{ 
                    marginTop: 'var(--space-xs)', 
                    display: 'flex', 
                    alignItems: 'center'
                  }}>
                    <div style={{ 
                      color: stat.isPositive ? 'var(--success)' : 'var(--error)',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      {stat.isPositive ? 
                        <ArrowUpOutlined style={{ fontSize: 12 }} /> : 
                        <ArrowDownOutlined style={{ fontSize: 12 }} />
                      }
                      <span style={{ marginLeft: 4 }}>{Math.abs(stat.change)}%</span>
                    </div>
                    <span style={{ 
                      marginLeft: 8, 
                      fontSize: 'var(--font-size-sm)', 
                      color: 'var(--text-tertiary)' 
                    }}>
                      较昨日
                    </span>
                  </div>
                </Card>
              </motion.div>
            </Col>
          ))}
        </Row>
        
        <Row gutter={[16, 16]} style={{ marginTop: 'var(--space-lg)' }}>
          <Col xs={24} lg={16}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              whileHover={{
                y: -5, 
                boxShadow: 'var(--shadow-xl)', 
                transition: { duration: 0.3, ease: 'easeOut' } 
              }}
            >
              <Card 
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <BarChartOutlined style={{ color: 'var(--primary-color)', marginRight: 'var(--space-xs)' }} />
                    <span>生产数据对比</span>
                  </div>
                }
                style={{ 
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--border-color)',
                  height: '100%',
                  boxShadow: 'var(--shadow-md)',
                  position: 'relative',
                  overflow: 'hidden',
                  transition: 'all var(--transition-normal) var(--ease-in-out)'
                }}
                bodyStyle={{ padding: 'var(--space-md)' }}
              >
                <GeometricDecoration 
                  type="donut" 
                  color="rgba(14, 165, 233, 0.08)" 
                  size={60} 
                  top={20} 
                  right={20} 
                />
                
                <EnhancedBarChart 
                  series={productionData.series}
                  xAxisData={productionData.xAxis}
                  loading={loading}
                  height="300px"
                  showLegend={true}
                  animation={true}
                />
              </Card>
            </motion.div>
          </Col>
          
          <Col xs={24} lg={8}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              whileHover={{
                y: -5, 
                boxShadow: 'var(--shadow-xl)', 
                transition: { duration: 0.3, ease: 'easeOut' } 
              }}
            >
              <Card 
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <MedicineBoxOutlined style={{ color: 'var(--primary-color)', marginRight: 'var(--space-xs)' }} />
                    <span>设备状态分布</span>
                  </div>
                }
                style={{ 
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--border-color)',
                  height: '100%',
                  boxShadow: 'var(--shadow-md)',
                  position: 'relative',
                  overflow: 'hidden',
                  transition: 'all var(--transition-normal) var(--ease-in-out)'
                }}
                bodyStyle={{ padding: 'var(--space-md)' }}
              >
                <GeometricDecoration 
                  type="square" 
                  color="rgba(139, 92, 246, 0.08)" 
                  size={40} 
                  bottom={20} 
                  right={20} 
                  rotate={15}
                />
                
                <EnhancedPieChart 
                  data={equipmentData}
                  loading={loading}
                  height="300px"
                  showLegend={true}
                  animation={true}
                  doughnut={true}
                />
              </Card>
            </motion.div>
          </Col>
        </Row>
        
        <Row gutter={[16, 16]} style={{ marginTop: 'var(--space-md)' }}>
          <Col xs={24} lg={12}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              whileHover={{
                y: -5, 
                boxShadow: 'var(--shadow-xl)', 
                transition: { duration: 0.3, ease: 'easeOut' } 
              }}
            >
              <Card 
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <LineChartOutlined style={{ color: 'var(--primary-color)', marginRight: 'var(--space-xs)' }} />
                    <span>质量指标趋势</span>
                  </div>
                }
                style={{ 
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--border-color)',
                  height: '100%',
                  boxShadow: 'var(--shadow-md)',
                  position: 'relative',
                  overflow: 'hidden',
                  transition: 'all var(--transition-normal) var(--ease-in-out)'
                }}
                bodyStyle={{ padding: 'var(--space-md)' }}
              >
                <GeometricDecoration 
                  type="triangle" 
                  color="rgba(16, 185, 129, 0.08)" 
                  size={50} 
                  top={30} 
                  right={30} 
                  rotate={180}
                />
                
                <EnhancedLineChart 
                  series={qualityData.series}
                  xAxisData={qualityData.xAxis}
                  loading={loading}
                  height="300px"
                  showLegend={true}
                  animation={true}
                  smooth={true}
                />
              </Card>
            </motion.div>
          </Col>
          
          <Col xs={24} lg={12}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              whileHover={{
                y: -5, 
                boxShadow: 'var(--shadow-xl)', 
                transition: { duration: 0.3, ease: 'easeOut' } 
              }}
            >
              <Card 
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <ExperimentOutlined style={{ color: 'var(--primary-color)', marginRight: 'var(--space-xs)' }} />
                    <span>批次生产进度</span>
                  </div>
                }
                style={{ 
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--border-color)',
                  height: '100%',
                  boxShadow: 'var(--shadow-md)',
                  position: 'relative',
                  overflow: 'hidden',
                  transition: 'all var(--transition-normal) var(--ease-in-out)'
                }}
                bodyStyle={{ padding: 'var(--space-md)' }}
              >
                <GeometricDecoration 
                  type="circle" 
                  color="rgba(236, 72, 153, 0.08)" 
                  size={40} 
                  bottom={30} 
                  left={30} 
                />
                
                <Space direction="vertical" style={{ width: '100%' }}>
                  {batchStatus.map((batch, index) => (
                    <motion.div 
                      key={batch.id}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.7 + index * 0.1, duration: 0.5 }}
                      style={{ 
                        padding: 'var(--space-sm)', 
                        borderRadius: 'var(--radius-md)',
                        border: '1px solid var(--border-color)',
                        background: 'var(--bg-light)',
                        marginBottom: 'var(--space-xs)'
                      }}
                      whileHover={{
                        backgroundColor: 'var(--bg-hover)',
                        boxShadow: 'var(--shadow-sm)',
                        borderColor: 'var(--border-hover)'
                      }}
                    >
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 'var(--space-xs)' }}>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          {batch.icon}
                          <Text strong style={{ marginLeft: 'var(--space-xs)' }}>{batch.name}</Text>
                        </div>
                        <Text type="secondary">{batch.id}</Text>
                      </div>
                      <Progress 
                        percent={batch.progress} 
                        status={batch.status} 
                        strokeColor={
                          batch.status === 'success' ? THEME_COLORS.success :
                          batch.status === 'exception' ? THEME_COLORS.error :
                          THEME_COLORS.primary
                        }
                        size="small"
                      />
                    </motion.div>
                  ))}
                </Space>
              </Card>
            </motion.div>
          </Col>
        </Row>
      </div>

      {/* 性能监控悬浮按钮 */}
      <FloatButton.Group
        trigger="hover"
        type="primary"
        style={{ right: 24 }}
        icon={<SettingOutlined />}
      >
        <FloatButton
          icon={<DashboardOutlined />}
          tooltip="性能监控"
          onClick={() => setPerformanceMonitorVisible(true)}
        />
      </FloatButton.Group>

      {/* 性能监控组件 */}
      <PerformanceMonitor
        visible={performanceMonitorVisible}
        onClose={() => setPerformanceMonitorVisible(false)}
      />
    </MainLayout>
  );
};

export default Dashboard; 