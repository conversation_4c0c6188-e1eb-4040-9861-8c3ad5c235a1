# 简单SQL Server连接测试

Write-Host "测试SQL Server连接..." -ForegroundColor Yellow

$connectionString = "Server=localhost\SQLEXPRESS;Database=master;Trusted_Connection=true;TrustServerCertificate=true;"

try {
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "✅ SQL Server连接成功!" -ForegroundColor Green
    
    # 检查数据库
    $command = $connection.CreateCommand()
    $command.CommandText = "SELECT name FROM sys.databases WHERE name IN ('ProductionDataDB', 'ProductionDataVisualizationDb')"
    $reader = $command.ExecuteReader()
    
    Write-Host "现有数据库:" -ForegroundColor Cyan
    while ($reader.Read()) {
        Write-Host "  - $($reader['name'])" -ForegroundColor Green
    }
    $reader.Close()
    $connection.Close()
    
    Write-Host "✅ 可以进行数据迁移" -ForegroundColor Green
}
catch {
    Write-Host "❌ SQL Server连接失败: $($_.Exception.Message)" -ForegroundColor Red
}
