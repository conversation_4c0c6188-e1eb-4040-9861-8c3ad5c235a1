import React, { Component } from 'react';
import { Result, <PERSON><PERSON>, Card, Typography, Collapse } from 'antd';
import { BugOutlined, ReloadOutlined, HomeOutlined } from '@ant-design/icons';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    // 更新状态，下次渲染时显示错误UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error, errorInfo) {
    // 记录错误信息
    const errorDetails = {
      error: error.toString(),
      errorInfo: errorInfo.componentStack,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      errorId: this.state.errorId
    };

    console.error('组件渲染错误:', errorDetails);

    // 发送错误报告到服务器（可选）
    this.reportError(errorDetails);

    this.setState({ errorInfo });
  }

  // 发送错误报告
  reportError = async (errorDetails) => {
    try {
      // 这里可以发送错误报告到服务器
      // await api.post('/api/errors/report', errorDetails);
      console.log('错误报告已记录:', errorDetails);
    } catch (err) {
      console.error('发送错误报告失败:', err);
    }
  };

  // 重试渲染
  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: prevState.retryCount + 1
    }));
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo, errorId, retryCount } = this.state;

      // 渲染增强的错误UI
      return (
        <div style={{ padding: '24px', minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
          <Result
            status="error"
            icon={<BugOutlined />}
            title="应用渲染错误"
            subTitle={
              <div>
                <Paragraph>
                  应用在渲染过程中遇到了意外错误。这可能是由于网络问题、数据异常或代码错误导致的。
                </Paragraph>
                <Paragraph>
                  <Text type="secondary">错误ID: {errorId}</Text>
                </Paragraph>
                {retryCount > 0 && (
                  <Paragraph>
                    <Text type="warning">已重试 {retryCount} 次</Text>
                  </Paragraph>
                )}
              </div>
            }
            extra={[
              <Button
                type="primary"
                key="retry"
                icon={<ReloadOutlined />}
                onClick={this.handleRetry}
                disabled={retryCount >= 3}
              >
                {retryCount >= 3 ? '重试次数已达上限' : '重试'}
              </Button>,
              <Button
                key="refresh"
                icon={<ReloadOutlined />}
                onClick={() => window.location.reload()}
              >
                刷新页面
              </Button>,
              <Button
                key="home"
                icon={<HomeOutlined />}
                onClick={() => window.location.href = '/'}
              >
                返回首页
              </Button>
            ]}
          />

          {/* 错误详情（开发环境或调试模式下显示） */}
          {(process.env.NODE_ENV === 'development' || window.location.search.includes('debug=true')) && (
            <Card
              title="错误详情"
              style={{ marginTop: '24px', maxWidth: '800px', margin: '24px auto 0' }}
            >
              <Collapse>
                <Panel header="错误信息" key="error">
                  <Paragraph>
                    <Text code>{error?.toString()}</Text>
                  </Paragraph>
                </Panel>
                <Panel header="组件堆栈" key="stack">
                  <Paragraph>
                    <Text code style={{ whiteSpace: 'pre-wrap' }}>
                      {errorInfo?.componentStack}
                    </Text>
                  </Paragraph>
                </Panel>
                <Panel header="浏览器信息" key="browser">
                  <Paragraph>
                    <Text>用户代理: {navigator.userAgent}</Text>
                  </Paragraph>
                  <Paragraph>
                    <Text>当前URL: {window.location.href}</Text>
                  </Paragraph>
                  <Paragraph>
                    <Text>时间戳: {new Date().toLocaleString()}</Text>
                  </Paragraph>
                </Panel>
              </Collapse>
            </Card>
          )}
        </div>
      );
    }

    // 正常渲染子组件
    return this.props.children;
  }
}

export default ErrorBoundary; 