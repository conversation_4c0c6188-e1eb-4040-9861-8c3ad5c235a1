import authService from '../services/authService';

// 路由守卫 - 检查用户是否已登录
export const requireAuth = (navigate, redirectTo = '/login') => {
  if (!authService.isAuthenticated()) {
    navigate(redirectTo);
    return false;
  }
  return true;
};

// 检查用户是否有权限访问
export const checkPermission = (permission) => {
  try {
    // 确保返回布尔值而不是对象
    if (!permission) return false;
    
    // 先检查用户是否已登录
    if (!authService.isAuthenticated()) {
      console.log("checkPermission: 用户未登录");
      return false;
    }
    
    const result = authService.hasPermission(permission);
    console.log(`checkPermission: 检查权限 ${permission}，结果: ${result}`);
    return Boolean(result);
  } catch (error) {
    console.error('权限检查失败:', error);
    return false;
  }
};

// 检查用户是否有角色
export const checkRole = (role) => {
  try {
    // 确保返回布尔值而不是对象
    if (!role) return false;
    const result = authService.hasRole(role);
    return Boolean(result);
  } catch (error) {
    console.error('角色检查失败:', error);
    return false;
  }
};

// 权限守卫 - 检查用户是否有特定权限
export const requirePermission = (permission, navigate, redirectTo = '/unauthorized') => {
  if (!authService.isAuthenticated()) {
    navigate('/login');
    return false;
  }
  
  if (!authService.hasPermission(permission)) {
    navigate(redirectTo);
    return false;
  }
  
  return true;
};

// 角色守卫 - 检查用户是否有特定角色
export const requireRole = (role, navigate, redirectTo = '/unauthorized') => {
  if (!authService.isAuthenticated()) {
    navigate('/login');
    return false;
  }
  
  if (!authService.hasRole(role)) {
    navigate(redirectTo);
    return false;
  }
  
  return true;
};

// 获取当前用户
export const getCurrentUser = () => {
  return authService.getUser();
}; 