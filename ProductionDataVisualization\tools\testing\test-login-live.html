<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能实时测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #fff;
            text-align: center;
        }
        .test-section {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 14px;
            box-sizing: border-box;
        }
        input::placeholder {
            color: rgba(255,255,255,0.7);
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        button:hover {
            background: #45a049;
        }
        button.warning {
            background: #FF9800;
        }
        button.danger {
            background: #F44336;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid #4CAF50;
        }
        .error {
            background: rgba(244, 67, 54, 0.3);
            border: 1px solid #F44336;
        }
        .info {
            background: rgba(33, 150, 243, 0.3);
            border: 1px solid #2196F3;
        }
        .log-area {
            background: rgba(0,0,0,0.3);
            border: 1px solid rgba(255,255,255,0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 登录功能实时测试</h1>
        <p style="text-align: center;">测试登录API、JWT令牌和用户认证功能</p>
        
        <div class="test-section">
            <h2>📋 登录测试</h2>
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="admin" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="admin123" placeholder="输入密码">
            </div>
            
            <button onclick="testLogin()">测试登录</button>
            <button onclick="testLogout()" class="warning">测试登出</button>
            <button onclick="testTokenValidation()">验证令牌</button>
            <button onclick="clearResults()" class="danger">清空结果</button>
        </div>
        
        <div class="test-section">
            <h2>📊 测试结果</h2>
            <div id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h2>📝 详细日志</h2>
            <div id="log" class="log-area"></div>
        </div>
    </div>

    <script>
        let logContent = '';
        let currentToken = '';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warn' ? '⚠️' : 'ℹ️';
            const logEntry = `[${timestamp}] ${prefix} ${message}\n`;
            
            logContent += logEntry;
            document.getElementById('log').textContent = logContent;
            
            const logArea = document.getElementById('log');
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(logEntry);
        }

        function getApiURL() {
            const host = window.location.hostname;
            return host === 'localhost' || host === '127.0.0.1' ? 
                'http://localhost:5000' : `http://${host}:5000`;
        }

        function showResult(title, status, message, details = '') {
            const resultsDiv = document.getElementById('test-results');
            const statusClass = status === 'success' ? 'success' : 
                               status === 'error' ? 'error' : 'info';
            
            const resultHtml = `
                <div class="result ${statusClass}">
                    <strong>${title}</strong><br>
                    ${message}
                    ${details ? '<br><br><strong>详细信息:</strong><br>' + details : ''}
                </div>
            `;
            
            resultsDiv.innerHTML += resultHtml;
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showResult('输入验证', 'error', '请填写用户名和密码');
                return;
            }
            
            log('🔄 开始测试登录功能');
            log(`登录信息: 用户名=${username}, 密码=${password}`);
            
            try {
                const apiUrl = getApiURL();
                const endpoint = `${apiUrl}/api/auth/login`;
                
                log(`请求URL: ${endpoint}`);
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                const responseText = await response.text();
                log(`响应内容: ${responseText}`);
                
                if (response.ok) {
                    const data = JSON.parse(responseText);
                    currentToken = data.token;
                    log('✅ 登录测试成功', 'success');
                    showResult('登录测试', 'success', '登录成功', 
                        `用户: ${data.username}\n角色: ${data.role}\n令牌: ${data.token ? data.token.substring(0, 50) + '...' : 'N/A'}`);
                } else {
                    const errorData = responseText ? JSON.parse(responseText) : {};
                    log(`❌ 登录测试失败: ${errorData.message || response.statusText}`, 'error');
                    showResult('登录测试', 'error', 
                        `登录失败 (${response.status})`, 
                        `错误信息: ${errorData.message || response.statusText}`);
                }
            } catch (error) {
                log(`❌ 登录测试异常: ${error.message}`, 'error');
                showResult('登录测试', 'error', '请求异常', 
                    `错误类型: ${error.name}\n错误信息: ${error.message}`);
            }
        }

        async function testLogout() {
            if (!currentToken) {
                showResult('登出测试', 'error', '没有有效的登录令牌，请先登录');
                return;
            }
            
            log('🔄 开始测试登出功能');
            
            try {
                const apiUrl = getApiURL();
                const endpoint = `${apiUrl}/api/auth/logout`;
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    currentToken = '';
                    log('✅ 登出测试成功', 'success');
                    showResult('登出测试', 'success', '登出成功');
                } else {
                    log(`❌ 登出测试失败: ${response.statusText}`, 'error');
                    showResult('登出测试', 'error', `登出失败 (${response.status})`);
                }
            } catch (error) {
                log(`❌ 登出测试异常: ${error.message}`, 'error');
                showResult('登出测试', 'error', '请求异常', error.message);
            }
        }

        async function testTokenValidation() {
            if (!currentToken) {
                showResult('令牌验证', 'error', '没有有效的登录令牌，请先登录');
                return;
            }
            
            log('🔄 开始测试令牌验证');
            
            try {
                const apiUrl = getApiURL();
                const endpoint = `${apiUrl}/api/simple-auth/users`;
                
                const response = await fetch(endpoint, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Accept': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ 令牌验证成功', 'success');
                    showResult('令牌验证', 'success', '令牌有效', 
                        `成功获取用户列表，共 ${data.items ? data.items.length : 0} 个用户`);
                } else {
                    log(`❌ 令牌验证失败: ${response.statusText}`, 'error');
                    showResult('令牌验证', 'error', `令牌无效 (${response.status})`);
                }
            } catch (error) {
                log(`❌ 令牌验证异常: ${error.message}`, 'error');
                showResult('令牌验证', 'error', '请求异常', error.message);
            }
        }

        function clearResults() {
            logContent = '';
            document.getElementById('log').textContent = '';
            document.getElementById('test-results').innerHTML = '';
        }

        // 页面加载时初始化
        window.onload = function() {
            log('登录功能测试工具已加载');
            log(`API地址: ${getApiURL()}`);
        };
    </script>
</body>
</html>
