import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Progress, 
  Tag, 
  Button, 
  Space, 
  Typography, 
  Alert,
  Descriptions,
  Table,
  Divider
} from 'antd';
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  CloseCircleOutlined,
  ReloadOutlined,
  DashboardOutlined,
  ApiOutlined,
  DatabaseOutlined,
  HddOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import MainLayout from '../components/layout/MainLayout';
import healthService from '../services/healthService';
import { getPerformanceStats } from '../services/api';
import cacheManager from '../utils/cache';
import PerformanceMonitor from '../components/common/PerformanceMonitor';

const { Title, Text } = Typography;

const SystemStatus = () => {
  const [loading, setLoading] = useState(true);
  const [healthData, setHealthData] = useState(null);
  const [performanceData, setPerformanceData] = useState(null);
  const [cacheStats, setCacheStats] = useState(null);
  const [performanceMonitorVisible, setPerformanceMonitorVisible] = useState(false);

  // 获取系统状态
  const fetchSystemStatus = async () => {
    setLoading(true);
    try {
      // 获取健康检查数据
      const health = await healthService.checkDetailedHealth();
      setHealthData(health.data);

      // 获取性能统计
      const performance = getPerformanceStats();
      setPerformanceData(performance);

      // 获取缓存统计
      const cache = cacheManager.getStats();
      setCacheStats(cache);

    } catch (error) {
      console.error('获取系统状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemStatus();
    
    // 每30秒刷新一次
    const interval = setInterval(fetchSystemStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // 状态颜色映射
  const getStatusColor = (status) => {
    switch (status) {
      case 'Healthy': return 'success';
      case 'Warning': return 'warning';
      case 'Critical': return 'error';
      default: return 'default';
    }
  };

  // 状态图标映射
  const getStatusIcon = (status) => {
    switch (status) {
      case 'Healthy': return <CheckCircleOutlined />;
      case 'Warning': return <ExclamationCircleOutlined />;
      case 'Critical': return <CloseCircleOutlined />;
      default: return <ExclamationCircleOutlined />;
    }
  };

  // 健康检查表格列
  const healthColumns = [
    {
      title: '检查项',
      dataIndex: 'name',
      key: 'name',
      render: (name) => {
        const iconMap = {
          'Database': <DatabaseOutlined />,
          'Memory': <ThunderboltOutlined />,
          'DiskSpace': <HddOutlined />,
          'SystemLoad': <ThunderboltOutlined />
        };
        return (
          <Space>
            {iconMap[name] || <DashboardOutlined />}
            {name}
          </Space>
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {status}
        </Tag>
      )
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message'
    },
    {
      title: '响应时间',
      dataIndex: 'responseTime',
      key: 'responseTime',
      render: (time) => time ? `${time}ms` : '-'
    }
  ];

  return (
    <MainLayout>
      <div style={{ padding: '24px' }}>
        <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2}>
            <DashboardOutlined /> 系统状态监控
          </Title>
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={fetchSystemStatus}
              loading={loading}
            >
              刷新
            </Button>
            <Button 
              type="primary"
              icon={<DashboardOutlined />}
              onClick={() => setPerformanceMonitorVisible(true)}
            >
              性能监控
            </Button>
          </Space>
        </div>

        {/* 系统概览 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="系统状态"
                value={healthData?.overallStatus || 'Unknown'}
                valueStyle={{ 
                  color: healthData?.overallStatus === 'Healthy' ? '#3f8600' : 
                         healthData?.overallStatus === 'Warning' ? '#faad14' : '#cf1322'
                }}
                prefix={getStatusIcon(healthData?.overallStatus)}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="API请求总数"
                value={performanceData?.totalRequests || 0}
                prefix={<ApiOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="平均响应时间"
                value={performanceData?.averageResponseTime || 0}
                suffix="ms"
                precision={0}
                valueStyle={{ 
                  color: (performanceData?.averageResponseTime || 0) > 2000 ? '#cf1322' : '#3f8600'
                }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="错误率"
                value={performanceData?.errorRate || '0%'}
                valueStyle={{ 
                  color: parseFloat(performanceData?.errorRate || '0') > 5 ? '#cf1322' : '#3f8600'
                }}
                prefix={<ExclamationCircleOutlined />}
              />
            </Card>
          </Col>
        </Row>

        {/* 健康检查详情 */}
        <Card title="健康检查详情" style={{ marginBottom: '24px' }}>
          <Table
            columns={healthColumns}
            dataSource={healthData?.checks || []}
            rowKey="name"
            pagination={false}
            loading={loading}
          />
        </Card>

        {/* 缓存状态 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col span={12}>
            <Card title="缓存使用情况">
              <Descriptions column={1}>
                <Descriptions.Item label="内存缓存">
                  {cacheStats?.memory?.size || 0} / {cacheStats?.memory?.maxSize || 0} 项
                </Descriptions.Item>
                <Descriptions.Item label="本地存储缓存">
                  {cacheStats?.localStorage?.keys?.length || 0} 项
                </Descriptions.Item>
                <Descriptions.Item label="缓存使用率">
                  <Progress 
                    percent={
                      cacheStats?.memory?.maxSize > 0 
                        ? Math.round((cacheStats.memory.size / cacheStats.memory.maxSize) * 100)
                        : 0
                    }
                    status={
                      cacheStats?.memory?.maxSize > 0 && 
                      (cacheStats.memory.size / cacheStats.memory.maxSize) > 0.8 
                        ? 'exception' : 'active'
                    }
                  />
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
          <Col span={12}>
            <Card title="性能指标">
              <Descriptions column={1}>
                <Descriptions.Item label="慢请求数">
                  {performanceData?.slowRequests || 0}
                </Descriptions.Item>
                <Descriptions.Item label="错误请求数">
                  {performanceData?.errorRequests || 0}
                </Descriptions.Item>
                <Descriptions.Item label="慢请求率">
                  <Progress 
                    percent={parseFloat(performanceData?.slowRequestRate || '0')}
                    status={parseFloat(performanceData?.slowRequestRate || '0') > 10 ? 'exception' : 'active'}
                  />
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        </Row>

        {/* 系统信息 */}
        {healthData?.systemInfo && (
          <Card title="系统信息">
            <Descriptions column={2}>
              <Descriptions.Item label="机器名">
                {healthData.systemInfo.machineName}
              </Descriptions.Item>
              <Descriptions.Item label="处理器数量">
                {healthData.systemInfo.processorCount}
              </Descriptions.Item>
              <Descriptions.Item label="操作系统">
                {healthData.systemInfo.osVersion}
              </Descriptions.Item>
              <Descriptions.Item label="运行时版本">
                {healthData.systemInfo.runtimeVersion}
              </Descriptions.Item>
              <Descriptions.Item label="启动时间">
                {new Date(healthData.systemInfo.startTime).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="运行时长">
                {Math.round(healthData.systemInfo.uptime?.totalHours || 0)} 小时
              </Descriptions.Item>
            </Descriptions>
          </Card>
        )}

        {/* 性能监控组件 */}
        <PerformanceMonitor
          visible={performanceMonitorVisible}
          onClose={() => setPerformanceMonitorVisible(false)}
        />
      </div>
    </MainLayout>
  );
};

export default SystemStatus;
