using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Domain.Common;
using Domain.DataAggregate;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// 阈值仓储实现
    /// </summary>
    public class ThresholdRepository : BaseRepository<Threshold>, IThresholdRepository
    {
        public ThresholdRepository(ApplicationDbContext dbContext) : base(dbContext)
        {
        }

        public async Task<IEnumerable<Threshold>> GetByDataCategoryIdAsync(Guid dataCategoryId)
        {
            return await DbContext.Thresholds
                .Where(t => t.DataCategoryId == dataCategoryId)
                .OrderBy(t => t.MinValue)
                .ToListAsync();
        }

        public async Task<IEnumerable<Threshold>> GetActiveAsync()
        {
            return await DbContext.Thresholds
                .Where(t => t.IsActive)
                .Include(t => t.DataCategory)
                .ToListAsync();
        }
    }
} 