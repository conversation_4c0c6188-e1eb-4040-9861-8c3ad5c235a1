@echo off
title 安装XLSX依赖包

echo ========================================
echo   安装XLSX依赖包
echo ========================================
echo.

cd frontend

echo [INFO] 正在安装xlsx依赖包...
npm install xlsx --save

if %errorlevel% equ 0 (
    echo [SUCCESS] xlsx依赖包安装成功
) else (
    echo [ERROR] xlsx依赖包安装失败，尝试使用yarn...
    yarn add xlsx
    
    if %errorlevel% equ 0 (
        echo [SUCCESS] xlsx依赖包通过yarn安装成功
    ) else (
        echo [ERROR] 依赖包安装失败
        echo [INFO] 请手动运行以下命令:
        echo   cd frontend
        echo   npm install xlsx
        pause
        exit /b 1
    )
)

echo.
echo [INFO] 验证安装...
if exist "node_modules\xlsx" (
    echo [SUCCESS] xlsx依赖包已正确安装
) else (
    echo [WARNING] xlsx目录未找到，可能安装不完整
)

echo.
echo [INFO] 更新FileParser.js以启用Excel支持...

echo.
echo ========================================
echo   安装完成！
echo ========================================
echo.
echo 现在可以重新启动前端服务以使用Excel文件解析功能
echo.

pause
