@echo off
title 测试中文表名支持

echo ========================================
echo   测试中文表名支持
echo ========================================
echo.

echo [INFO] 修改内容:
echo 1. 表名格式: Data_原始文件名（保留中文）
echo 2. 例如: Data_注射水分配1电导率
echo 3. 移除时间戳，使用简洁格式
echo 4. 支持中文字符的表名验证
echo.

echo [INFO] 后端已启动在 http://localhost:5000
echo.

echo [INFO] 启动前端...
cd frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 15 /nobreak

echo.
echo ========================================
echo   测试指南
echo ========================================
echo.
echo 1. 访问: http://localhost:3000/data-import
echo 2. 上传中文文件名的数据文件，例如:
echo    - 注射水分配1电导率.csv
echo    - 生产数据监控.xlsx
echo    - 质量检测报告.csv
echo.
echo 3. 观察新的表名格式:
echo    - 文件: 注射水分配1电导率.csv
echo    - 表名: Data_注射水分配1电导率
echo    - 无时间戳，简洁明了
echo.
echo 4. 检查后端控制台日志:
echo    - "生成表名: Data_注射水分配1电导率"
echo    - "开始导入数据到表 [Data_注射水分配1电导率]"
echo    - "数据导入完成，共导入 X 行"
echo.
echo 5. 验证功能:
echo    - 文件分析成功
echo    - 重复文件检测正常
echo    - 数据导入完整
echo    - 表名保持中文原貌
echo.

pause
