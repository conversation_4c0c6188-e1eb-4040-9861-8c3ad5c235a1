# 测试用户管理API

Write-Host "=== 测试用户管理API ===" -ForegroundColor Yellow

# 1. 测试获取用户列表
Write-Host "`n1. 测试获取用户列表 (/api/users)" -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/users' -Method GET
    Write-Host "GET /api/users SUCCESS!" -ForegroundColor Green
    $response | ConvertTo-Json
} catch {
    Write-Host "GET /api/users FAILED!" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

# 2. 测试创建用户
Write-Host "`n2. 测试创建用户 (/api/users)" -ForegroundColor Cyan
$timestamp = [DateTimeOffset]::UtcNow.ToUnixTimeSeconds()
$createUserBody = @{
    username = "mgmt_user_$timestamp"
    email = "mgmt_user_$<EMAIL>"
    password = "password123"
    fullName = "管理界面用户_$timestamp"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/users' -Method POST -Body $createUserBody -ContentType 'application/json'
    Write-Host "POST /api/users SUCCESS!" -ForegroundColor Green
    $response | ConvertTo-Json
} catch {
    Write-Host "POST /api/users FAILED!" -ForegroundColor Red
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody"
    }
}

# 3. 测试健康检查
Write-Host "`n3. 测试健康检查" -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/health' -Method GET
    Write-Host "Health check SUCCESS!" -ForegroundColor Green
    $response | ConvertTo-Json
} catch {
    Write-Host "Health check FAILED!" -ForegroundColor Red
    Write-Host $_.Exception.Message
}
