import React, { useState, useEffect, useMemo } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  InputNumber, 
  Button, 
  Space, 
  Divider,
  Collapse,
  Tag,
  Row,
  Col,
  Slider,
  Switch,
  Tooltip
} from 'antd';
import { 
  FilterOutlined, 
  ClearOutlined, 
  SearchOutlined,
  DownOutlined,
  CalendarOutlined,
  NumberOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import dayjs from 'dayjs';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Panel } = Collapse;

const DataFilterPanel = ({
  data = [],
  columns = [],
  onFilterChange,
  onReset,
  initialFilters = {},
  showAdvanced = true,
  collapsible = true,
  ...props
}) => {
  const [form] = Form.useForm();
  const [filters, setFilters] = useState(initialFilters);
  const [activeFilters, setActiveFilters] = useState([]);
  const [collapsed, setCollapsed] = useState(false);

  // 分析数据类型和范围
  const columnAnalysis = useMemo(() => {
    const analysis = {};
    
    columns.forEach(column => {
      if (!column.dataIndex) return;
      
      const values = data.map(row => row[column.dataIndex]).filter(val => val != null);
      
      if (values.length === 0) {
        analysis[column.dataIndex] = { type: 'empty', values: [] };
        return;
      }

      // 检测数据类型
      const numericValues = values.filter(v => typeof v === 'number' || !isNaN(Number(v)));
      const dateValues = values.filter(v => dayjs(v).isValid());
      
      if (numericValues.length / values.length > 0.8) {
        // 数值类型
        const numbers = numericValues.map(v => Number(v));
        analysis[column.dataIndex] = {
          type: 'number',
          min: Math.min(...numbers),
          max: Math.max(...numbers),
          avg: numbers.reduce((sum, n) => sum + n, 0) / numbers.length,
          values: [...new Set(numbers)].sort((a, b) => a - b)
        };
      } else if (dateValues.length / values.length > 0.8) {
        // 日期类型
        const dates = dateValues.map(v => dayjs(v));
        const sortedDates = dates.sort((a, b) => a.valueOf() - b.valueOf());
        analysis[column.dataIndex] = {
          type: 'date',
          min: sortedDates[0],
          max: sortedDates[sortedDates.length - 1],
          values: [...new Set(dateValues)].sort()
        };
      } else {
        // 文本类型
        const uniqueValues = [...new Set(values)];
        analysis[column.dataIndex] = {
          type: 'text',
          values: uniqueValues.sort(),
          count: uniqueValues.length
        };
      }
    });
    
    return analysis;
  }, [data, columns]);

  // 应用过滤器
  const applyFilters = (newFilters = filters) => {
    const activeFilterList = Object.entries(newFilters)
      .filter(([key, value]) => {
        if (Array.isArray(value)) return value.length > 0;
        if (typeof value === 'object' && value !== null) {
          return Object.values(value).some(v => v != null && v !== '');
        }
        return value != null && value !== '';
      })
      .map(([key, value]) => ({ key, value }));

    setActiveFilters(activeFilterList);
    setFilters(newFilters);

    if (onFilterChange) {
      onFilterChange(newFilters, activeFilterList);
    }
  };

  // 重置过滤器
  const resetFilters = () => {
    form.resetFields();
    setFilters({});
    setActiveFilters([]);
    
    if (onReset) {
      onReset();
    }
    
    if (onFilterChange) {
      onFilterChange({}, []);
    }
  };

  // 表单值变化处理
  const handleFormChange = (changedValues, allValues) => {
    applyFilters(allValues);
  };

  // 渲染数值范围过滤器
  const renderNumberFilter = (column, analysis) => (
    <Row gutter={8}>
      <Col span={12}>
        <Form.Item
          name={[column.dataIndex, 'min']}
          label="最小值"
        >
          <InputNumber
            min={analysis.min}
            max={analysis.max}
            placeholder={`≥ ${analysis.min}`}
            style={{ width: '100%' }}
            size="small"
          />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          name={[column.dataIndex, 'max']}
          label="最大值"
        >
          <InputNumber
            min={analysis.min}
            max={analysis.max}
            placeholder={`≤ ${analysis.max}`}
            style={{ width: '100%' }}
            size="small"
          />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          name={[column.dataIndex, 'range']}
          label="范围选择"
        >
          <Slider
            range
            min={analysis.min}
            max={analysis.max}
            step={(analysis.max - analysis.min) / 100}
            marks={{
              [analysis.min]: analysis.min.toFixed(1),
              [analysis.max]: analysis.max.toFixed(1)
            }}
          />
        </Form.Item>
      </Col>
    </Row>
  );

  // 渲染日期范围过滤器
  const renderDateFilter = (column, analysis) => (
    <Form.Item
      name={[column.dataIndex, 'range']}
      label="日期范围"
    >
      <RangePicker
        style={{ width: '100%' }}
        size="small"
        placeholder={['开始日期', '结束日期']}
      />
    </Form.Item>
  );

  // 渲染文本选择过滤器
  const renderTextFilter = (column, analysis) => {
    if (analysis.count <= 20) {
      // 选项较少，使用多选
      return (
        <Form.Item
          name={[column.dataIndex, 'values']}
          label="选择值"
        >
          <Select
            mode="multiple"
            placeholder="选择筛选值"
            style={{ width: '100%' }}
            size="small"
            maxTagCount={3}
          >
            {analysis.values.map(value => (
              <Option key={value} value={value}>
                {value}
              </Option>
            ))}
          </Select>
        </Form.Item>
      );
    } else {
      // 选项较多，使用搜索
      return (
        <Form.Item
          name={[column.dataIndex, 'search']}
          label="搜索"
        >
          <Input
            placeholder="输入搜索关键词"
            prefix={<SearchOutlined />}
            size="small"
          />
        </Form.Item>
      );
    }
  };

  // 渲染过滤器面板
  const renderFilterPanels = () => {
    const panels = columns
      .filter(column => column.dataIndex && columnAnalysis[column.dataIndex])
      .map(column => {
        const analysis = columnAnalysis[column.dataIndex];
        if (analysis.type === 'empty') return null;

        let filterContent;
        switch (analysis.type) {
          case 'number':
            filterContent = renderNumberFilter(column, analysis);
            break;
          case 'date':
            filterContent = renderDateFilter(column, analysis);
            break;
          case 'text':
            filterContent = renderTextFilter(column, analysis);
            break;
          default:
            return null;
        }

        return (
          <Panel
            header={
              <Space>
                {analysis.type === 'number' && <NumberOutlined />}
                {analysis.type === 'date' && <CalendarOutlined />}
                {analysis.type === 'text' && <SearchOutlined />}
                {column.title}
                <Tag size="small">{analysis.type}</Tag>
              </Space>
            }
            key={column.dataIndex}
          >
            {filterContent}
          </Panel>
        );
      })
      .filter(Boolean);

    return (
      <Collapse
        size="small"
        ghost
        expandIcon={({ isActive }) => <DownOutlined rotate={isActive ? 180 : 0} />}
      >
        {panels}
      </Collapse>
    );
  };

  // 渲染活动过滤器标签
  const renderActiveFilters = () => {
    if (activeFilters.length === 0) return null;

    return (
      <div style={{ marginBottom: 16 }}>
        <div style={{ marginBottom: 8, fontSize: '12px', color: '#666' }}>
          活动过滤器:
        </div>
        <Space wrap>
          {activeFilters.map(({ key, value }, index) => {
            const column = columns.find(col => col.dataIndex === key);
            const columnTitle = column?.title || key;
            
            let displayValue = '';
            if (Array.isArray(value)) {
              displayValue = `${value.length} 项`;
            } else if (typeof value === 'object' && value !== null) {
              const parts = [];
              if (value.min != null) parts.push(`≥${value.min}`);
              if (value.max != null) parts.push(`≤${value.max}`);
              if (value.search) parts.push(`"${value.search}"`);
              displayValue = parts.join(', ');
            } else {
              displayValue = String(value);
            }

            return (
              <Tag
                key={`${key}-${index}`}
                closable
                onClose={() => {
                  const newFilters = { ...filters };
                  delete newFilters[key];
                  form.setFieldsValue({ [key]: undefined });
                  applyFilters(newFilters);
                }}
                color="blue"
              >
                {columnTitle}: {displayValue}
              </Tag>
            );
          })}
        </Space>
      </div>
    );
  };

  useEffect(() => {
    form.setFieldsValue(filters);
  }, [filters, form]);

  const content = (
    <div>
      {renderActiveFilters()}
      
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleFormChange}
        size="small"
      >
        {/* 快速搜索 */}
        <Form.Item
          name="globalSearch"
          label="全局搜索"
        >
          <Input
            placeholder="搜索所有字段..."
            prefix={<SearchOutlined />}
            allowClear
          />
        </Form.Item>

        <Divider />

        {/* 高级过滤器 */}
        {showAdvanced && (
          <div>
            <div style={{ marginBottom: 16, fontSize: '14px', fontWeight: 'bold' }}>
              高级过滤器
            </div>
            {renderFilterPanels()}
          </div>
        )}
      </Form>

      <Divider />

      {/* 操作按钮 */}
      <Space style={{ width: '100%', justifyContent: 'center' }}>
        <Button
          icon={<ClearOutlined />}
          onClick={resetFilters}
          disabled={activeFilters.length === 0}
        >
          清除过滤器
        </Button>
        <Button type="primary" icon={<FilterOutlined />}>
          应用过滤器 ({activeFilters.length})
        </Button>
      </Space>
    </div>
  );

  if (collapsible) {
    return (
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card
          title={
            <Space>
              <FilterOutlined />
              数据过滤器
              {activeFilters.length > 0 && (
                <Tag color="blue">{activeFilters.length}</Tag>
              )}
            </Space>
          }
          size="small"
          extra={
            <Button
              type="text"
              size="small"
              onClick={() => setCollapsed(!collapsed)}
            >
              {collapsed ? '展开' : '收起'}
            </Button>
          }
          {...props}
        >
          {!collapsed && content}
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        title={
          <Space>
            <FilterOutlined />
            数据过滤器
            {activeFilters.length > 0 && (
              <Tag color="blue">{activeFilters.length}</Tag>
            )}
          </Space>
        }
        size="small"
        {...props}
      >
        {content}
      </Card>
    </motion.div>
  );
};

export default DataFilterPanel;
