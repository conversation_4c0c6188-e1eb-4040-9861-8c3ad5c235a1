@echo off
title 测试导入修复

echo ========================================
echo   测试导入修复
echo ========================================
echo.

echo [INFO] 修复内容:
echo 1. 增加请求大小限制到500MB
echo 2. 改进错误日志显示
echo 3. 添加分批处理支持
echo 4. 修复覆盖模式逻辑
echo.

echo [INFO] 启动后端服务...
cd backend\SqlServerAPI

echo [DEBUG] 检查编译...
dotnet build
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] 编译失败，请检查代码
    pause
    exit /b 1
)

echo [DEBUG] 启动服务...
start "Backend" cmd /k "dotnet run"

echo [INFO] 等待后端启动...
timeout /t 10 /nobreak

echo [INFO] 测试API连接...
curl -s http://localhost:5000/api/health
echo.

echo [INFO] 启动前端...
cd ..\..\frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 15 /nobreak

echo.
echo ========================================
echo   测试指南
echo ========================================
echo.
echo 1. 访问: http://localhost:3000/data-import
echo 2. 上传大数据文件（超过1000行）
echo 3. 观察控制台日志：
echo    - 前端：分批处理进度
echo    - 后端：详细错误信息
echo 4. 检查是否成功导入全部数据
echo.
echo 如果仍有问题，请检查：
echo - 后端控制台的详细错误信息
echo - 前端浏览器控制台的网络错误
echo - 数据文件大小和格式
echo.

pause
