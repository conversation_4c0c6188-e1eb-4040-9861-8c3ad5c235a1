import React, { useState } from 'react';
import { Form, Input, Button, message, Typography } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, PhoneOutlined, EyeOutlined } from '@ant-design/icons';
import { useNavigate, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import authService from '../services/authService';
import './SplitScreenLogin.css'; // 使用相同的样式文件

const { Title } = Typography;

const Register = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const onFinish = async (values) => {
    setLoading(true);
    try {
      // 这里应该调用注册API
      console.log('注册信息:', values);
      message.success('注册成功！');
      navigate('/login');
    } catch (error) {
      message.error('注册失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="split-login-container">
      {/* 顶部Logo */}
      <div className="top-logo">
        <div className="logo">
          <div className="logo-square">
            <EyeOutlined style={{ fontSize: 20, color: 'white' }} />
          </div>
          <span className="logo-text">远大医药</span>
        </div>
      </div>

      {/* 主注册区域 */}
      <motion.div
        className="login-container"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="login-card">
          <h1 className="login-title">创建账户</h1>
          <p className="login-subtitle">注册新账户以开始使用系统</p>

          {/* 注册表单 */}
          <Form
            form={form}
            name="register"
            onFinish={onFinish}
            className="login-form"
            layout="vertical"
          >
            <div className="form-group">
              <label className="form-label">用户名</label>
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' }
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入用户名"
                  className="form-input"
                />
              </Form.Item>
            </div>

            <div className="form-group">
              <label className="form-label">邮箱</label>
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="请输入邮箱地址"
                  className="form-input"
                />
              </Form.Item>
            </div>

            <div className="form-group">
              <label className="form-label">手机号</label>
              <Form.Item
                name="phone"
                rules={[
                  { required: true, message: '请输入手机号' },
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                ]}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder="请输入手机号"
                  className="form-input"
                />
              </Form.Item>
            </div>

            <div className="form-group">
              <label className="form-label">密码</label>
              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请输入密码"
                  className="form-input"
                />
              </Form.Item>
            </div>

            <div className="form-group">
              <label className="form-label">确认密码</label>
              <Form.Item
                name="confirmPassword"
                dependencies={['password']}
                rules={[
                  { required: true, message: '请确认密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请再次输入密码"
                  className="form-input"
                />
              </Form.Item>
            </div>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                className="gradient-button"
                block
              >
                创建账户
              </Button>
            </Form.Item>
          </Form>

          {/* 底部链接 */}
          <div className="bottom-links">
            <div className="help-links">
              <Link to="/login" className="help-link">已有账户？立即登录</Link>
              <span className="separator">·</span>
              <Link to="/help" className="help-link">帮助中心</Link>
              <span className="separator">·</span>
              <Link to="/contact" className="help-link">技术支持</Link>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Register;