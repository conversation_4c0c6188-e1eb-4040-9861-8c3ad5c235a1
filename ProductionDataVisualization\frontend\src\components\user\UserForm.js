import React, { useEffect } from 'react';
import { Form, Input, Button, Select, Switch } from 'antd';
import { UserOutlined, MailOutlined, LockOutlined } from '@ant-design/icons';

const { Option } = Select;

const UserForm = ({
  user,
  roles,
  onSubmit,
  onCancel,
  loading = false
}) => {
  const [form] = Form.useForm();
  
  useEffect(() => {
    if (user) {
      form.setFieldsValue({
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        role: user.role,
        department: user.department
        // 密码字段不设置初始值，保持为空
      });
    } else {
      form.resetFields();
    }
  }, [user, form]);

  const handleFinish = (values) => {
    onSubmit(values);
    if (!user) {
      form.resetFields();
    }
  };
  
  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleFinish}
      size="large"
      style={{ marginTop: '24px' }}
    >
      <Form.Item
        name="username"
        label="用户名"
        rules={[
          { required: true, message: '请输入用户名' },
          { min: 3, message: '用户名至少3个字符' },
          { max: 20, message: '用户名最多20个字符' }
        ]}
      >
        <Input
          prefix={<UserOutlined />}
          placeholder="请输入用户名"
          style={{
            height: '44px',
            borderRadius: '8px',
            border: '1px solid #d1d5db',
            fontSize: '16px'
          }}
        />
      </Form.Item>
      
      <Form.Item
        name="email"
        label="邮箱地址"
        rules={[
          { required: true, message: '请输入邮箱地址' },
          { type: 'email', message: '请输入有效的邮箱地址' }
        ]}
      >
        <Input
          prefix={<MailOutlined />}
          placeholder="请输入邮箱地址"
          style={{
            height: '44px',
            borderRadius: '8px',
            border: '1px solid #d1d5db',
            fontSize: '16px'
          }}
        />
      </Form.Item>

      <Form.Item
        name="password"
        label={user ? "新密码（留空则不修改）" : "密码"}
        rules={user ? [] : [
          { required: true, message: '请输入密码' },
          { min: 6, message: '密码至少6个字符' }
        ]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder={user ? "留空则不修改密码" : "请输入密码"}
          style={{
            height: '44px',
            borderRadius: '8px',
            border: '1px solid #d1d5db',
            fontSize: '16px'
          }}
        />
      </Form.Item>

      <Form.Item
        name="fullName"
        label="全名"
        rules={[{ required: true, message: '请输入全名' }]}
      >
        <Input
          placeholder="请输入全名"
          style={{
            height: '44px',
            borderRadius: '8px',
            border: '1px solid #d1d5db',
            fontSize: '16px'
          }}
        />
      </Form.Item>

      <Form.Item
        name="role"
        label="角色"
        rules={[{ required: true, message: '请选择角色' }]}
      >
        <Select
          placeholder="请选择角色"
          style={{
            height: '44px',
            borderRadius: '8px'
          }}
        >
          {roles && roles.map(role => (
            <Option key={role.id} value={role.id}>{role.name}</Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item
        name="department"
        label="部门"
        rules={[{ required: true, message: '请输入部门' }]}
      >
        <Input
          placeholder="请输入部门"
          style={{
            height: '44px',
            borderRadius: '8px',
            border: '1px solid #d1d5db',
            fontSize: '16px'
          }}
        />
      </Form.Item>

      <Form.Item style={{ marginTop: '32px', marginBottom: 0 }}>
        <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px' }}>
          <Button
            onClick={onCancel}
            style={{
              height: '44px',
              borderRadius: '8px',
              padding: '0 24px',
              fontWeight: '500'
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            style={{
              height: '44px',
              background: '#6366f1',
              borderColor: '#6366f1',
              borderRadius: '8px',
              padding: '0 24px',
              fontWeight: '600',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
            }}
          >
            {user ? '更新用户' : '添加用户'}
          </Button>
        </div>
      </Form.Item>

      <style jsx="true">{`
        .ant-form-item-label > label {
          color: #374151;
          font-weight: 600;
          font-size: 14px;
        }

        .ant-input:hover,
        .ant-select:hover .ant-select-selector {
          border-color: #9ca3af;
        }

        .ant-input:focus,
        .ant-input-focused,
        .ant-select-focused .ant-select-selector {
          border-color: #6366f1;
          box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .ant-select-selector {
          height: 44px !important;
          border-radius: 8px !important;
          border: 1px solid #d1d5db !important;
          font-size: 16px !important;
        }

        .ant-btn-primary:hover {
          background: #5856eb;
          border-color: #5856eb;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }
      `}</style>
    </Form>
  );
};

export default UserForm; 