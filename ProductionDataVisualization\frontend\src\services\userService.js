import api from './api';

const USERS_ENDPOINT = '/api/users';
const SIMPLE_USERS_ENDPOINT = '/api/simple-auth/users';

// 获取本地注册用户
const getLocalUsers = () => {
  try {
    const registeredUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]');
    return registeredUsers.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.fullName,
      isActive: true,
      roles: user.roles || ['User'],
      permissions: user.permissions || ['ViewData', 'ViewCharts', 'ViewDashboards', 'ViewUsers'],
      createdAt: user.createdAt,
      lastLoginTime: null,
      source: 'local' // 标记为本地用户
    }));
  } catch (error) {
    console.error('获取本地用户失败:', error);
    return [];
  }
};

// 获取模拟用户
const getMockUsers = () => {
  return [
    {
      id: 'admin-001',
      username: 'admin',
      email: '<EMAIL>',
      fullName: '系统管理员',
      isActive: true,
      roles: ['Admin'],
      permissions: ['ViewUsers', 'CreateUser', 'EditUser', 'DeleteUser', 'ViewData', 'ViewCharts', 'ViewDashboards'],
      createdAt: '2024-01-01T00:00:00Z',
      lastLoginTime: new Date().toISOString(),
      source: 'system' // 标记为系统用户
    },
    {
      id: 'user-001',
      username: 'testuser',
      email: '<EMAIL>',
      fullName: '测试用户',
      isActive: true,
      roles: ['User'],
      permissions: ['ViewData', 'ViewCharts', 'ViewDashboards'],
      createdAt: '2024-01-01T00:00:00Z',
      lastLoginTime: null,
      source: 'system' // 标记为系统用户
    }
  ];
};

// 获取所有用户（带缓存）
const getAllUsers = async (page = 1, pageSize = 10, useCache = true) => {
  console.log('userService.getAllUsers 被调用，参数:', { page, pageSize, useCache });

  // 方法1: 使用现有api实例调用简单API
  try {
    console.log('🔄 方法1: 尝试调用简单API:', SIMPLE_USERS_ENDPOINT);
    const response = await api.get(SIMPLE_USERS_ENDPOINT);
    console.log('✅ 简单API调用成功:', response.data);

    // 验证响应数据格式
    if (response.data && response.data.items && Array.isArray(response.data.items)) {
      console.log(`✅ 获取到 ${response.data.items.length} 个用户`);
      return response.data;
    } else {
      console.warn('⚠️ 简单API响应格式不正确:', response.data);
      throw new Error('API响应格式不正确');
    }
  } catch (simpleApiError) {
    console.warn('⚠️ 方法1失败，尝试方法2:', simpleApiError.message);

    // 方法2: 使用fetch直接调用简单API（避免axios拦截器干扰）
    try {
      console.log('🔄 方法2: 使用fetch直接调用简单API');
      const apiUrl = api.defaults.baseURL + SIMPLE_USERS_ENDPOINT;
      console.log('- Fetch URL:', apiUrl);

      const fetchResponse = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        mode: 'cors'
      });

      if (!fetchResponse.ok) {
        throw new Error(`HTTP ${fetchResponse.status}`);
      }

      const data = await fetchResponse.json();
      console.log('✅ Fetch调用成功:', data);

      if (data && data.items && Array.isArray(data.items)) {
        console.log(`✅ 通过fetch获取到 ${data.items.length} 个用户`);
        return data;
      } else {
        throw new Error('Fetch响应格式不正确');
      }
    } catch (fetchError) {
      console.error('❌ 方法2失败，使用本地数据:', fetchError.message);

      // 方法3: 所有API都失败时，返回本地注册用户 + 模拟用户
      console.error('❌ 所有API调用失败，使用本地数据');
      const localUsers = getLocalUsers();
      const mockUsers = getMockUsers();
      const allUsers = [...mockUsers, ...localUsers];

      console.log('本地用户:', localUsers);
      console.log('模拟用户:', mockUsers);
      console.log('合并后的所有用户:', allUsers);

      // 简单分页
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedUsers = allUsers.slice(startIndex, endIndex);

      const result = {
        items: paginatedUsers,
        totalCount: allUsers.length,
        page: page,
        pageSize: pageSize
      };

      console.log('返回的本地用户数据:', result);
      return result;
    }
  }
};

// 获取单个用户
const getUserById = async (userId) => {
  try {
    const response = await api.get(`${USERS_ENDPOINT}/${userId}`);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : { message: '获取用户信息失败' };
  }
};

// 创建用户
const createUser = async (userData) => {
  try {
    console.log('创建用户请求:', userData);

    // 使用用户管理API端点 - 测试证明这个接口是工作的
    const createRequest = {
      username: userData.username,
      email: userData.email,
      password: userData.password,
      fullName: userData.fullName,
      roleIds: [] // 暂时不分配特定角色，使用默认角色
    };

    console.log('发送创建用户请求到:', USERS_ENDPOINT);
    console.log('请求数据:', createRequest);

    const response = await api.post(USERS_ENDPOINT, createRequest);
    console.log('创建用户成功:', response.data);

    return response.data;
  } catch (error) {
    console.error('创建用户失败:', error);

    // 提供更详细的错误信息
    if (error.response) {
      console.error('错误响应:', error.response.data);
      console.error('错误状态:', error.response.status);
      throw error.response.data;
    } else if (error.request) {
      console.error('请求失败:', error.request);
      throw { message: '网络请求失败，请检查网络连接' };
    } else {
      console.error('配置错误:', error.message);
      throw { message: '请求配置错误: ' + error.message };
    }
  }
};

// 更新用户
const updateUser = async (userId, userData) => {
  try {
    console.log('更新用户请求:', userId, userData);

    // 构建更新用户的请求数据
    const updateRequest = {
      email: userData.email,
      fullName: userData.fullName,
      isActive: userData.isActive !== undefined ? userData.isActive : true,
      newPassword: userData.newPassword || userData.password // 支持密码更新
    };

    console.log('发送更新用户请求到:', `${USERS_ENDPOINT}/${userId}`);
    const response = await api.put(`${USERS_ENDPOINT}/${userId}`, updateRequest);
    console.log('更新用户成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('更新用户失败:', error);
    throw error.response ? error.response.data : { message: '更新用户信息失败' };
  }
};

// 删除用户
const deleteUser = async (userId) => {
  try {
    console.log('删除用户请求:', userId);
    console.log('发送删除用户请求到:', `${USERS_ENDPOINT}/${userId}`);

    const response = await api.delete(`${USERS_ENDPOINT}/${userId}`);
    console.log('删除用户成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('删除用户失败:', error);
    throw error.response ? error.response.data : { message: '删除用户失败' };
  }
};

// 更新用户角色
const updateUserRoles = async (userId, roleIds) => {
  try {
    const response = await api.put(`${USERS_ENDPOINT}/${userId}/roles`, { roleIds });
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : { message: '更新用户角色失败' };
  }
};

// 更改用户密码
const changePassword = async (userId, currentPassword, newPassword) => {
  try {
    const response = await api.put(`${USERS_ENDPOINT}/${userId}/password`, {
      currentPassword,
      newPassword
    });
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : { message: '更改密码失败' };
  }
};

const userService = {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  updateUserRoles,
  changePassword
};

export default userService; 