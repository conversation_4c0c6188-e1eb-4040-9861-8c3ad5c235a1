using Microsoft.Data.Sqlite;
using System;

class Program
{
    static async Task Main(string[] args)
    {
        try
        {
            Console.WriteLine("测试SQLite连接...");
            
            var connectionString = "Data Source=test.db;Cache=Shared;";
            using var connection = new SqliteConnection(connectionString);
            await connection.OpenAsync();
            
            Console.WriteLine("SQLite连接成功！");
            
            // 创建测试表
            var createTableCommand = new SqliteCommand(@"
                CREATE TABLE IF NOT EXISTS TestUsers (
                    Id TEXT PRIMARY KEY,
                    Username TEXT UNIQUE NOT NULL,
                    Email TEXT UNIQUE NOT NULL
                )", connection);
            
            await createTableCommand.ExecuteNonQueryAsync();
            Console.WriteLine("测试表创建成功！");
            
            // 插入测试数据
            var insertCommand = new SqliteCommand(@"
                INSERT OR REPLACE INTO TestUsers (Id, Username, Email) 
                VALUES ('1', 'admin', '<EMAIL>')", connection);
            
            await insertCommand.ExecuteNonQueryAsync();
            Console.WriteLine("测试数据插入成功！");
            
            // 查询数据
            var selectCommand = new SqliteCommand("SELECT COUNT(*) FROM TestUsers", connection);
            var count = Convert.ToInt32(await selectCommand.ExecuteScalarAsync());
            
            Console.WriteLine($"数据库中有 {count} 条记录");
            Console.WriteLine("SQLite测试完成！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"错误: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }
        
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
