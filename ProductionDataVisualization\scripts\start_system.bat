@echo off
chcp 65001 > nul
title 生产数据可视化系统启动器 / Production Data Visualization System Starter

echo ===================================================
echo    生产数据可视化系统启动器 v2.0 (优化版)
echo    Production Data Visualization System Starter v2.0
echo    包含性能监控、错误处理和缓存优化
echo ===================================================
echo.
echo 正在启动完整系统，请稍候...
echo Starting complete system, please wait...
echo.

REM 设置工作目录为脚本所在目录
cd /d %~dp0

REM 创建日志目录（如果不存在）
if not exist ..\logs mkdir ..\logs

REM 创建启动日志文件
echo 系统启动时间: %date% %time% > ..\logs\system_start.log

REM 检查环境
echo 检查环境... 
echo 检查环境... >> ..\logs\system_start.log

REM 检查npm
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误/ERROR] 未找到npm。请先安装Node.js
    echo npm not found. Please install Node.js first. >> ..\logs\system_start.log
    pause
    exit /b 1
)

REM 检查dotnet
where dotnet >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误/ERROR] 未找到.NET SDK。请先安装.NET SDK
    echo .NET SDK not found. Please install .NET SDK first. >> ..\logs\system_start.log
    pause
    exit /b 1
)

echo 环境检查通过 / Environment check passed.
echo 环境检查通过 / Environment check passed. >> ..\logs\system_start.log
echo.

REM 停止可能已经运行的服务
echo 停止可能已经运行的服务...
echo 停止可能已经运行的服务... >> ..\logs\system_start.log

REM 停止占用5000端口的进程（后端）
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000 ^| findstr LISTENING') do (
    echo 终止后端进程 / Terminating backend process: %%a
    echo 终止后端进程 / Terminating backend process: %%a >> ..\logs\system_start.log
    taskkill /F /PID %%a >nul 2>nul
)

REM 停止占用3000或3001端口的进程（前端）
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000 ^| findstr LISTENING') do (
    echo 终止前端进程 / Terminating frontend process: %%a
    echo 终止前端进程 / Terminating frontend process: %%a >> ..\logs\system_start.log
    taskkill /F /PID %%a >nul 2>nul
)

for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3001 ^| findstr LISTENING') do (
    echo 终止前端进程 / Terminating frontend process: %%a
    echo 终止前端进程 / Terminating frontend process: %%a >> ..\logs\system_start.log
    taskkill /F /PID %%a >nul 2>nul
)

echo 服务已停止 / Services stopped.
echo 服务已停止 / Services stopped. >> ..\logs\system_start.log
echo.

REM 启动后端API（使用start /b使其在后台运行）
echo 启动后端API...
echo 启动后端API... >> ..\logs\system_start.log

REM 使用wmic创建一个后台进程运行.NET Core SQL Server API
echo 使用后台方式启动.NET Core SQL Server API...
echo 使用后台方式启动.NET Core SQL Server API... >> ..\logs\system_start.log

REM 检查是否有已构建的发布版本
if exist "%~dp0\..\backend\SqlServerAPI\bin\Release\net8.0\win-x64\publish\SqlServerAPI.exe" (
    echo [信息/INFO] 使用已构建的SQL Server发布版本 / Using pre-built SQL Server release version
    echo [信息/INFO] 使用已构建的SQL Server发布版本 / Using pre-built SQL Server release version >> ..\logs\system_start.log
    wmic process call create "cmd.exe /c cd /d %~dp0\..\backend\SqlServerAPI\bin\Release\net8.0\win-x64\publish && SqlServerAPI.exe > %~dp0\..\logs\backend.log 2>&1"
) else (
    echo [信息/INFO] 使用开发模式启动SQL Server API / Starting SQL Server API in development mode
    echo [信息/INFO] 使用开发模式启动SQL Server API / Starting SQL Server API in development mode >> ..\logs\system_start.log
    wmic process call create "cmd.exe /c cd /d %~dp0\..\backend\SqlServerAPI && dotnet run --urls=http://localhost:5000 > %~dp0\..\logs\backend.log 2>&1"
)

REM 等待API启动
echo 等待API启动...
echo 等待API启动... >> ..\logs\system_start.log
timeout /t 10 /nobreak > nul

REM 检查API是否成功启动
set api_started=false
netstat -ano | findstr :5000 | findstr LISTENING >nul 2>nul
if %errorlevel% equ 0 (
    set api_started=true
    echo [成功/SUCCESS] 后端API已启动 / Backend API started successfully
    echo [成功/SUCCESS] 后端API已启动 / Backend API started successfully >> ..\logs\system_start.log
) else (
    echo [警告/WARNING] 未检测到API在5000端口启动 / API not detected on port 5000
    echo [警告/WARNING] 未检测到API在5000端口启动 / API not detected on port 5000 >> ..\logs\system_start.log
    echo 继续尝试启动前端... / Continuing to start frontend...
    echo 继续尝试启动前端... / Continuing to start frontend... >> ..\logs\system_start.log
)

REM 启动前端
echo 启动前端...
echo 启动前端... >> ..\logs\system_start.log

REM 检查node_modules是否存在
if not exist ..\frontend\node_modules (
    echo [警告/WARNING] 前端依赖未安装，正在安装... / Frontend dependencies not installed, installing...
    echo [警告/WARNING] 前端依赖未安装，正在安装... / Frontend dependencies not installed, installing... >> ..\logs\system_start.log
    
    REM 使用后台方式安装依赖
    wmic process call create "cmd.exe /c cd /d %~dp0\..\frontend && npm install --no-audit --no-fund > %~dp0\..\logs\npm_install.log 2>&1"
    
    echo 等待依赖安装完成... / Waiting for dependencies installation...
    echo 等待依赖安装完成... / Waiting for dependencies installation... >> ..\logs\system_start.log
    timeout /t 30 /nobreak > nul
)

REM 创建.env文件
echo 创建.env文件... / Creating .env file...
echo 创建.env文件... / Creating .env file... >> ..\logs\system_start.log
echo PORT=3000> ..\frontend\.env
echo BROWSER=none>> ..\frontend\.env
echo REACT_APP_API_URL=http://localhost:5000>> ..\frontend\.env

REM 尝试多种方式启动前端
echo 尝试启动前端... / Trying to start frontend...
echo 尝试启动前端... / Trying to start frontend... >> ..\logs\system_start.log

REM 方法1: 使用npm start启动开发服务器
echo 使用npm start启动开发服务器... / Starting development server with npm start...
echo 使用npm start启动开发服务器... / Starting development server with npm start... >> ..\logs\system_start.log

REM 使用后台方式启动开发服务器
wmic process call create "cmd.exe /c cd /d %~dp0\..\frontend && npm start > %~dp0\..\logs\frontend_dev.log 2>&1"

REM 等待前端启动
echo 等待前端启动... / Waiting for frontend to start...
echo 等待前端启动... / Waiting for frontend to start... >> ..\logs\system_start.log
timeout /t 10 /nobreak > nul

REM 检查前端是否启动
set frontend_started=false
netstat -ano | findstr :3000 | findstr LISTENING >nul 2>nul
if %errorlevel% equ 0 (
    set frontend_started=true
    echo [成功/SUCCESS] 前端已启动在端口3000 / Frontend started successfully on port 3000
    echo [成功/SUCCESS] 前端已启动在端口3000 / Frontend started successfully on port 3000 >> ..\logs\system_start.log
  ) else (
    echo [警告/WARNING] 未检测到前端在3000端口启动 / Frontend not detected on port 3000
    echo [警告/WARNING] 未检测到前端在3000端口启动 / Frontend not detected on port 3000 >> ..\logs\system_start.log
    echo 尝试使用direct.html... / Trying direct.html...
    echo 尝试使用direct.html... / Trying direct.html... >> ..\logs\system_start.log
)

REM 系统状态总结
echo.
echo ===================================================
echo    系统状态 / System Status
echo ===================================================
echo.
echo. >> ..\logs\system_start.log
echo =================================================== >> ..\logs\system_start.log
echo    系统状态 / System Status >> ..\logs\system_start.log
echo =================================================== >> ..\logs\system_start.log
echo. >> ..\logs\system_start.log

if "%api_started%"=="true" (
    echo [成功/SUCCESS] 后端API正在运行 / Backend API is running
    echo [成功/SUCCESS] 后端API正在运行 / Backend API is running >> ..\logs\system_start.log
) else (
    echo [错误/ERROR] 后端API未运行 / Backend API is not running
    echo [错误/ERROR] 后端API未运行 / Backend API is not running >> ..\logs\system_start.log
)

if "%frontend_started%"=="true" (
    echo [成功/SUCCESS] 前端服务正在运行 / Frontend service is running
    echo [成功/SUCCESS] 前端服务正在运行 / Frontend service is running >> ..\logs\system_start.log
) else (
    echo [警告/WARNING] 前端服务可能未运行 / Frontend service might not be running
    echo 将使用direct.html作为备用 / Will use direct.html as backup
    echo [警告/WARNING] 前端服务可能未运行 / Frontend service might not be running >> ..\logs\system_start.log
    echo 将使用direct.html作为备用 / Will use direct.html as backup >> ..\logs\system_start.log
)

echo.
echo ================================
echo 系统启动完成！ / System startup complete!
echo ================================
echo 系统启动完成 / System startup completed >> ..\logs\system_start.log
echo.
echo 本机访问地址:
echo   前端: http://localhost:3000
echo   后端API: http://localhost:5000
echo.
echo 局域网访问地址 (其他电脑可通过以下地址访问):
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4" ^| findstr /v "127.0.0.1"') do (
    set ip=%%i
    set ip=!ip: =!
    if not "!ip!"=="" (
        echo   前端: http://!ip!:3000
        echo   后端API: http://!ip!:5000
        goto :found_ip
    )
)
:found_ip
echo.
echo 注意: 如果其他电脑无法访问，请以管理员身份运行 configure_network.bat
echo.

REM 打开浏览器
echo 打开浏览器... / Opening browser...
echo 打开浏览器... / Opening browser... >> ..\logs\system_start.log

if "%frontend_started%"=="true" (
    start http://localhost:3000
) else (
    echo [警告/WARNING] 前端未启动，尝试直接访问后端API / Frontend not started, trying to access backend API directly
    echo [警告/WARNING] 前端未启动，尝试直接访问后端API / Frontend not started, trying to access backend API directly >> ..\logs\system_start.log
    echo 请手动访问以下地址 / Please manually access the following URLs:
    echo   前端: http://localhost:3000
    echo   后端API: http://localhost:5000/api/health
    start http://localhost:3000
)

echo.
echo ==========================================
echo   SQL Server系统启动完成 / SQL Server System Started
echo ==========================================
echo.
echo 🎉 系统信息 / System Information:
echo   ✓ 数据库: SQL Server (ProductionDataVisualizationDb)
echo   ✓ 后端API: http://localhost:5000
echo   ✓ 前端应用: http://localhost:3000
echo   ✓ 数据存储: 长期存储在SQL Server中 / Long-term storage in SQL Server
echo.
echo 🔐 默认登录 / Default Login:
echo   用户名/Username: admin
echo   密码/Password: admin123
echo.
echo 📊 数据特点 / Data Features:
echo   ✓ 用户信息: 存储在SQL Server Users表
echo   ✓ 导入数据: 存储在SQL Server动态表
echo   ✓ 长期保存: 所有数据持久化存储
echo.
echo 如需停止系统，请运行stop_system.bat / To stop the system, run stop_system.bat
echo.
echo 此窗口可以安全关闭，系统将继续在后台运行 / This window can be safely closed, the system will continue running in the background
echo 系统启动器已完成任务 / System starter has completed its task >> ..\logs\system_start.log

timeout /t 15
exit