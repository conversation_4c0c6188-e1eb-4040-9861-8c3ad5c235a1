# 🔧 启动错误解决方案

## ❌ **问题分析**

您遇到的错误："Windows 找不到文件 direct.html" 的根本原因：

### 🔍 **错误根源**
1. **错误的启动脚本**: 您使用的是 `start_system.bat`，这是旧版本的脚本
2. **路径配置问题**: 旧脚本试图访问不存在的 `direct.html` 文件
3. **版本不匹配**: 旧脚本启动SQLite版本，但我们已迁移到SQL Server

### 📂 **文件位置说明**
- **您使用的**: `start_system.bat` (旧版本，在scripts目录下)
- **应该使用**: `start_system_correct.bat` (新版本，SQL Server版本)

## ✅ **立即解决方案**

### 🚀 **方案1: 使用正确的启动脚本** (推荐)

**不要使用** `start_system.bat`，请使用：
```bash
start_system_correct.bat
```

这个脚本会：
- ✅ 启动SQL Server版本的后端
- ✅ 正确配置所有路径
- ✅ 验证系统状态
- ✅ 自动打开正确的URL

### 🔧 **方案2: 手动启动** (如果脚本有问题)

#### 步骤1: 启动SQL Server后端
```bash
cd ProductionDataVisualization\backend\SqlServerAPI\bin\Release\net8.0\win-x64\publish
SqlServerAPI.exe
```

#### 步骤2: 启动前端 (新窗口)
```bash
cd ProductionDataVisualization\frontend
npm start
```

#### 步骤3: 访问系统
打开浏览器访问: http://localhost:3000

### 🗑️ **方案3: 清理并重新开始**

如果仍有问题，执行以下清理步骤：

1. **停止所有相关进程**:
   ```bash
   taskkill /F /IM SimpleBackend.exe
   taskkill /F /IM SqlServerAPI.exe
   taskkill /F /IM node.exe
   ```

2. **使用新的启动脚本**:
   ```bash
   start_system_correct.bat
   ```

## 📋 **系统版本说明**

### ❌ **旧版本 (不要使用)**
- **脚本**: `start_system.bat` 
- **后端**: SimpleBackend.exe (SQLite)
- **数据库**: SQLite文件
- **问题**: 路径错误，版本过时

### ✅ **新版本 (正确版本)**
- **脚本**: `start_system_correct.bat`
- **后端**: SqlServerAPI.exe (SQL Server)
- **数据库**: SQL Server (ProductionDataVisualizationDb)
- **优势**: 长期存储，企业级

## 🎯 **验证系统正常运行**

启动后，检查以下内容：

### ✅ **后端验证**
访问: http://localhost:5000/api/health
应该看到: `{"status":"healthy",...}`

### ✅ **前端验证**
访问: http://localhost:3000
应该看到: 登录页面

### ✅ **数据库验证**
- 用户名: admin
- 密码: admin123
- 应该能正常登录

## 🚨 **常见问题解决**

### 问题1: "找不到SqlServerAPI.exe"
**解决**: 先构建SQL Server后端
```bash
cd ProductionDataVisualization\backend\SqlServerAPI
dotnet publish -c Release
```

### 问题2: "SQL Server连接失败"
**解决**: 启动SQL Server服务
```bash
net start "SQL Server (SQLEXPRESS)"
```

### 问题3: "端口被占用"
**解决**: 清理端口
```bash
netstat -ano | findstr :5000
taskkill /F /PID [进程ID]
```

### 问题4: "前端启动失败"
**解决**: 重新安装依赖
```bash
cd ProductionDataVisualization\frontend
npm install
npm start
```

## 📞 **如果问题仍然存在**

### 🔍 **诊断步骤**
1. 检查SQL Server是否运行
2. 检查端口5000和3000是否被占用
3. 检查文件路径是否正确
4. 查看错误日志

### 💡 **联系支持**
如果以上方案都无法解决问题，请提供：
- 具体错误信息
- 使用的启动脚本名称
- 系统环境信息

## 🎉 **成功标志**

当系统正常启动后，您应该看到：
- ✅ 浏览器自动打开 http://localhost:3000
- ✅ 显示登录页面
- ✅ 可以使用 admin/admin123 登录
- ✅ 所有数据保存在SQL Server中

**记住: 请使用 `start_system_correct.bat` 而不是 `start_system.bat`！** 🚀
