import React, { useState, useRef, useEffect } from 'react';
import { Form, Input, Button, Alert, Typography, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { motion, useAnimation } from 'framer-motion';
import authService from '../../services/authService';
import { useNavigate, Link } from 'react-router-dom';

const { Title, Text } = Typography;

const ElegantLoginForm = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const formRef = useRef(null);
  const controls = useAnimation();

  // 鼠标跟踪效果
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e) => {
      if (formRef.current) {
        const rect = formRef.current.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    };

    const formElement = formRef.current;
    if (formElement) {
      formElement.addEventListener('mousemove', handleMouseMove);
      return () => formElement.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  const onFinish = async (values) => {
    setLoading(true);
    setError('');
    
    try {
      await authService.login(values.username, values.password);
      
      // 成功动画
      await controls.start({
        scale: [1, 1.02, 1],
        opacity: [1, 0.9, 0],
        transition: { duration: 0.6 }
      });
      
      navigate('/dashboard');
    } catch (err) {
      setError(err.message || '登录失败，请检查用户名和密码');
      
      // 错误震动动画
      controls.start({
        x: [0, -5, 5, -5, 5, 0],
        transition: { duration: 0.4 }
      });
    } finally {
      setLoading(false);
    }
  };

  // Assan风格样式定义
  const containerStyle = {
    width: '100%',
    maxWidth: '480px',
    padding: '48px',
    borderRadius: '16px',
    background: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(20px)',
    border: '1px solid rgba(255, 255, 255, 0.8)',
    boxShadow: `
      0 32px 64px rgba(0, 0, 0, 0.08),
      0 16px 32px rgba(0, 0, 0, 0.04),
      0 8px 16px rgba(0, 0, 0, 0.02)
    `,
    position: 'relative',
    overflow: 'hidden'
  };

  const headerStyle = {
    textAlign: 'center',
    marginBottom: '32px',
    position: 'relative'
  };

  const logoStyle = {
    width: '80px',
    height: '80px',
    borderRadius: '50%',
    background: `
      linear-gradient(135deg, 
        #667eea 0%, 
        #764ba2 100%
      )
    `,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    margin: '0 auto 20px',
    boxShadow: `
      0 8px 24px rgba(102, 126, 234, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3)
    `,
    position: 'relative',
    overflow: 'hidden'
  };

  const inputStyle = {
    height: '50px',
    fontSize: '16px',
    borderRadius: '12px',
    border: '1px solid rgba(203, 213, 225, 0.6)',
    background: 'rgba(255, 255, 255, 0.7)',
    backdropFilter: 'blur(10px)',
    color: '#334155',
    transition: 'all 0.3s ease',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)'
  };

  const buttonStyle = {
    height: '50px',
    fontSize: '16px',
    fontWeight: '600',
    borderRadius: '12px',
    background: `
      linear-gradient(135deg, 
        #667eea 0%, 
        #764ba2 100%
      )
    `,
    border: 'none',
    boxShadow: `
      0 4px 15px rgba(102, 126, 234, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2)
    `,
    position: 'relative',
    overflow: 'hidden'
  };

  return (
    <motion.div
      ref={formRef}
      animate={controls}
      style={containerStyle}
      className="elegant-login-form"
    >
      {/* 精致的鼠标跟随光效 */}
      <div
        style={{
          position: 'absolute',
          top: mousePosition.y - 80,
          left: mousePosition.x - 80,
          width: '160px',
          height: '160px',
          background: 'radial-gradient(circle, rgba(102, 126, 234, 0.08) 0%, transparent 70%)',
          borderRadius: '50%',
          pointerEvents: 'none',
          transition: 'all 0.3s ease'
        }}
      />

      {/* 表单头部 */}
      <div style={headerStyle}>
        <motion.div
          style={logoStyle}
          whileHover={{ scale: 1.05, rotate: 2 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          {/* Logo内部光效 */}
          <div style={{
            position: 'absolute',
            top: '15%',
            left: '15%',
            width: '70%',
            height: '70%',
            background: 'radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%)',
            borderRadius: '50%'
          }} />
          <UserOutlined style={{ fontSize: '36px', color: '#fff', zIndex: 2 }} />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
        >
          <Title level={2} style={{ 
            color: '#1e293b', 
            fontSize: '28px', 
            fontWeight: '600',
            marginBottom: '8px',
            background: 'linear-gradient(135deg, #667eea, #764ba2)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>
            系统登录
          </Title>
          <Text style={{ 
            color: '#64748b', 
            fontSize: '16px'
          }}>
            欢迎回到生产数据可视化平台
          </Text>
        </motion.div>
      </div>

      {/* 错误提示 */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          style={{ marginBottom: '24px' }}
        >
          <Alert 
            message={error} 
            type="error" 
            showIcon 
            style={{
              borderRadius: '12px',
              border: '1px solid rgba(239, 68, 68, 0.2)',
              background: 'rgba(254, 242, 242, 0.8)',
              backdropFilter: 'blur(10px)'
            }}
          />
        </motion.div>
      )}

      {/* 登录表单 */}
      <Form
        name="login"
        form={form}
        onFinish={onFinish}
        size="large"
        layout="vertical"
      >
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: '请输入用户名!' }]}
            style={{ marginBottom: '20px' }}
          >
            <Input 
              prefix={<UserOutlined style={{ color: '#94a3b8' }} />} 
              placeholder="用户名" 
              style={inputStyle}
              className="elegant-input"
            />
          </Form.Item>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码!' }]}
            style={{ marginBottom: '20px' }}
          >
            <Input.Password
              prefix={<LockOutlined style={{ color: '#94a3b8' }} />}
              placeholder="密码"
              style={inputStyle}
              className="elegant-input"
            />
          </Form.Item>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5, duration: 0.6 }}
          style={{ marginBottom: '24px' }}
        >
          <Form.Item name="remember" valuePropName="checked">
            <Checkbox style={{ color: '#64748b' }}>
              记住我
            </Checkbox>
          </Form.Item>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.6, duration: 0.6 }}
        >
          <Button
            type="primary"
            htmlType="submit"
            block
            loading={loading}
            style={buttonStyle}
            className="elegant-button"
            icon={<LoginOutlined />}
          >
            <span style={{ position: 'relative', zIndex: 2 }}>登录</span>
          </Button>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.6 }}
          style={{ 
            textAlign: 'center', 
            marginTop: '24px'
          }}
        >
          <Text style={{ color: '#64748b' }}>
            还没有账户？
            <Link 
              to="/register" 
              style={{ 
                color: '#667eea',
                marginLeft: '8px',
                textDecoration: 'none',
                fontWeight: '500'
              }}
            >
              立即注册
            </Link>
          </Text>
        </motion.div>
      </Form>

      {/* CSS样式 */}
      <style jsx="true">{`
        .elegant-input:focus,
        .elegant-input:hover {
          border-color: #667eea !important;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08) !important;
          background: rgba(255, 255, 255, 0.9) !important;
        }
        
        .elegant-button:hover {
          transform: translateY(-1px);
          box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
        }
        
        .elegant-button:active {
          transform: translateY(0);
        }
        
        .elegant-login-form::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
          animation: shimmer 4s infinite;
          pointer-events: none;
        }
        
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </motion.div>
  );
};

export default ElegantLoginForm;
