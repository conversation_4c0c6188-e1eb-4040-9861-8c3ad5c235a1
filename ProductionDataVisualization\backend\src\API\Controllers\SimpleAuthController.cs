using Microsoft.AspNetCore.Mvc;
using API.Services;
using System.ComponentModel.DataAnnotations;

namespace API.Controllers
{
    [ApiController]
    [Route("api/simple-auth")]
    public class SimpleAuthController : ControllerBase
    {
        private readonly SimpleUserService _userService;
        private readonly ILogger<SimpleAuthController> _logger;

        public SimpleAuthController(SimpleUserService userService, ILogger<SimpleAuthController> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterRequest request)
        {
            try
            {
                _logger.LogInformation($"收到注册请求: {request.Username}");

                var user = await _userService.CreateUserAsync(
                    request.Username,
                    request.Email,
                    request.Password,
                    request.FullName
                );

                _logger.LogInformation($"用户注册成功: {user.Username}");

                return Ok(new
                {
                    message = "注册成功",
                    userId = user.Id,
                    username = user.Username
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning($"注册失败: {ex.Message}");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册过程中发生错误");
                return StatusCode(500, new { message = "注册失败，请稍后再试" });
            }
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            try
            {
                _logger.LogInformation($"收到登录请求: {request.Username}");

                var isValid = await _userService.ValidateUserAsync(request.Username, request.Password);
                if (!isValid)
                {
                    _logger.LogWarning($"登录失败: 用户名或密码错误 - {request.Username}");
                    return Unauthorized(new { message = "用户名或密码错误" });
                }

                var user = await _userService.GetUserByUsernameAsync(request.Username) ??
                          await _userService.GetUserByEmailAsync(request.Username);

                if (user == null)
                {
                    return Unauthorized(new { message = "用户不存在" });
                }

                _logger.LogInformation($"用户登录成功: {user.Username}");

                return Ok(new
                {
                    userId = user.Id,
                    username = user.Username,
                    email = user.Email,
                    fullName = user.FullName,
                    token = $"simple_token_{user.Id}_{DateTime.UtcNow.Ticks}",
                    roles = user.Roles,
                    permissions = user.Permissions
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登录过程中发生错误");
                return StatusCode(500, new { message = "登录失败，请稍后再试" });
            }
        }

        [HttpGet("users")]
        public async Task<IActionResult> GetUsers()
        {
            try
            {
                _logger.LogInformation("收到获取用户列表请求");

                var users = await _userService.GetAllUsersAsync();
                var userDtos = users.Select(u => new
                {
                    Id = u.Id,
                    Username = u.Username,
                    Email = u.Email,
                    FullName = u.FullName,
                    IsActive = u.IsActive,
                    LastLoginTime = u.LastLoginTime,
                    CreatedAt = u.CreatedAt,
                    Roles = u.Roles,
                    Permissions = u.Permissions
                });

                _logger.LogInformation($"返回 {users.Count} 个用户");

                return Ok(new
                {
                    items = userDtos,
                    totalCount = users.Count,
                    page = 1,
                    pageSize = users.Count
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户列表失败");
                return StatusCode(500, new { message = "获取用户列表失败，请稍后再试" });
            }
        }
    }

    public class RegisterRequest
    {
        [Required]
        public string Username { get; set; }

        [Required]
        [EmailAddress]
        public string Email { get; set; }

        [Required]
        public string Password { get; set; }

        [Required]
        public string FullName { get; set; }
    }

    public class LoginRequest
    {
        [Required]
        public string Username { get; set; }

        [Required]
        public string Password { get; set; }
    }
}
