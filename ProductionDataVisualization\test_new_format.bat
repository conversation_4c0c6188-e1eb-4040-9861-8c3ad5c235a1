@echo off
title 测试新数据格式

echo ========================================
echo   测试新数据格式
echo ========================================
echo.

echo [INFO] 启动后端服务...
cd backend\SqlServerAPI
start "Backend" cmd /k "dotnet run"

echo [INFO] 等待后端启动...
timeout /t 10 /nobreak

echo [INFO] 测试API端点...
echo.
echo [TEST] 健康检查:
curl -s http://localhost:5000/api/health
echo.
echo.

echo [TEST] 数据导入测试:
curl -s http://localhost:5000/api/data-import/test
echo.
echo.

echo [INFO] 启动前端...
cd ..\..\frontend
start "Frontend" cmd /k "npm start"

echo [INFO] 等待前端启动...
timeout /t 15 /nobreak

echo.
echo ========================================
echo   测试完成
echo ========================================
echo.
echo 请访问以下地址测试:
echo 1. 数据导入: http://localhost:3000/data-import
echo 2. 后端健康: http://localhost:5000/api/health
echo 3. 导入测试: http://localhost:5000/api/data-import/test
echo.
echo 新功能:
echo - 数据以行为单位存储（JSON格式）
echo - 导入后自动显示数据预览
echo - 保持原始文件的行列结构
echo.

pause
