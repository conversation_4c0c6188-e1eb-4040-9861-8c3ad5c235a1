@echo off
title Node.js Final Verification and Project Setup

echo ==========================================
echo   Node.js Final Verification Report
echo ==========================================
echo.

REM Set PATH for this session
set "PATH=D:\Programs\nodejs;%PATH%"

echo [✓] Node.js Installation Status:
echo     Location: D:\Programs\nodejs\
echo     Node.js Version: 
node --version
echo     npm Version: 
npm --version
echo.

echo [✓] Environment Configuration:
echo     - Node.js added to current session PATH
echo     - User PATH environment variable updated
echo.

echo [✓] Project Dependencies Test:
echo.

REM Test frontend dependencies
if exist "ProductionDataVisualization\frontend\package.json" (
    echo [INFO] Testing frontend npm access...
    cd ProductionDataVisualization\frontend
    echo     Frontend directory: %CD%
    echo     npm version in frontend:
    npm --version
    echo     [SUCCESS] npm is accessible in frontend directory
    cd ..\..
    echo.
) else (
    echo [WARNING] Frontend package.json not found
    echo.
)

REM Test backend dependencies  
if exist "ProductionDataVisualization\backend\package.json" (
    echo [INFO] Testing backend npm access...
    cd ProductionDataVisualization\backend
    echo     Backend directory: %CD%
    echo     npm version in backend:
    npm --version
    echo     [SUCCESS] npm is accessible in backend directory
    cd ..\..
    echo.
) else (
    echo [INFO] Backend package.json not found (may use .NET only)
    echo.
)

echo ==========================================
echo   Configuration Summary
echo ==========================================
echo.
echo ✓ Node.js v22.17.1 is installed and working
echo ✓ npm v10.9.2 is installed and working  
echo ✓ PATH is configured for current session
echo ✓ User environment variable updated
echo ✓ Project directories are accessible
echo.
echo ==========================================
echo   Next Steps
echo ==========================================
echo.
echo 1. IMPORTANT: Close this command prompt window
echo 2. Open a NEW command prompt window
echo 3. Test commands in new window:
echo    - node --version
echo    - npm --version
echo.
echo 4. If commands work, proceed with project setup:
echo    - cd ProductionDataVisualization\frontend
echo    - npm install
echo.
echo 5. If commands don't work in new window:
echo    - Restart your computer
echo    - Or manually add D:\Programs\nodejs to system PATH
echo.
echo ==========================================
echo   Manual PATH Configuration (if needed)
echo ==========================================
echo.
echo If Node.js commands don't work after restart:
echo.
echo 1. Right-click "This PC" → Properties
echo 2. Click "Advanced system settings"
echo 3. Click "Environment Variables"
echo 4. In "System variables", find and select "Path"
echo 5. Click "Edit"
echo 6. Click "New"
echo 7. Add: D:\Programs\nodejs
echo 8. Click "OK" on all dialogs
echo 9. Restart command prompt
echo.

echo Press any key to exit...
pause > nul
