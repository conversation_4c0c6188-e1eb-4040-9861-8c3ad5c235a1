import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Dropdown, Avatar, Space, Typography, Badge, message } from 'antd';
import { 
  MenuUnfoldOutlined, 
  MenuFoldOutlined, 
  UserOutlined, 
  DashboardOutlined, 
  <PERSON><PERSON><PERSON>Outlined, 
  DatabaseOutlined, 
  TeamOutlined, 
  SettingOutlined, 
  LogoutOutlined,
  EyeOutlined,
  ExperimentOutlined,
  Line<PERSON>hartOutlined,
  MedicineBoxOutlined,
  BellOutlined,
  QuestionCircleOutlined,
  MonitorOutlined
} from '@ant-design/icons';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import authService from '../../services/authService';
import { checkPermission } from '../../utils/auth';
import { Permissions } from '../../utils/permissions';

const { Header, Sider, Content } = Layout;
const { Text, Title } = Typography;

// 布局样式常量
const LAYOUT_STYLES = {
  sider: {
    background: 'var(--gradient-sidebar)',
    boxShadow: 'var(--shadow-lg)',
    position: 'relative',
    zIndex: 'var(--z-fixed)',
    overflow: 'hidden',
    border: 'none',
    borderRight: '1px solid var(--border-color)',
    backgroundImage: 'var(--texture-noise)'
  },
  logo: {
    height: '64px', 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    color: 'white',
    background: 'linear-gradient(135deg, #333333 0%, #555555 100%)',
    borderBottom: '1px solid var(--border-color)',
    position: 'relative',
    overflow: 'hidden'
  },
  logoGlow: {
    position: 'absolute', 
    width: '100%', 
    height: '100%', 
    backgroundImage: 'radial-gradient(circle at 10% 10%, rgba(51, 51, 51, 0.3) 0%, transparent 70%)',
    opacity: 0.7,
    animation: 'var(--animation-glow)'
  },
  logoText: {
    fontWeight: 'var(--font-weight-semibold)', 
    fontSize: 'var(--font-size-md)',
    letterSpacing: '0.5px',
    color: 'var(--text-inverse)',
    marginLeft: 'var(--space-xs)',
    textShadow: '0 0 10px rgba(51, 51, 51, 0.5)'
  },
  menu: {
    background: 'transparent',
    borderRight: 'none',
    padding: 'var(--space-sm) 0'
  },
  menuItem: {
    fontSize: 'var(--font-size-md)',
    fontWeight: 'var(--font-weight-medium)',
    color: 'var(--text-secondary)',
    margin: '4px var(--space-sm)',
    borderRadius: 'var(--radius-md)',
    overflow: 'hidden',
    transition: 'all var(--transition-normal) var(--ease-in-out)'
  },
  menuItemActive: {
    background: 'rgba(51, 51, 51, 0.1) !important',
    boxShadow: 'var(--shadow-md)',
    color: '#333333 !important',
    textShadow: '0 0 5px rgba(51, 51, 51, 0.3)'
  },
  menuIcon: {
    fontSize: 'var(--font-size-md)',
    marginRight: 'var(--space-xs)'
  },
  footer: {
    padding: 'var(--space-md)', 
    position: 'absolute', 
    bottom: 0, 
    width: '100%',
    textAlign: 'center',
    borderTop: '1px solid var(--border-color)',
    backgroundImage: 'var(--texture-noise)',
    opacity: 0.9
  },
  footerText: {
    color: 'var(--text-tertiary)', 
    fontSize: 'var(--font-size-xs)',
    fontWeight: 'var(--font-weight-light)'
  },
  header: {
    padding: 0, 
    display: 'flex', 
    justifyContent: 'space-between', 
    alignItems: 'center',
    boxShadow: 'var(--shadow-md)',
    position: 'relative',
    zIndex: 'var(--z-sticky)',
    background: 'var(--bg-light)',
    borderBottom: '1px solid var(--border-color)',
    backgroundImage: 'var(--texture-noise)',
    opacity: 0.95
  },
  collapseButton: {
    fontSize: 'var(--font-size-md)', 
    width: 64, 
    height: 64, 
    color: '#333333',
    background: 'transparent',
    border: 'none',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'all var(--transition-normal) var(--ease-in-out)',
    textShadow: '0 0 5px rgba(51, 51, 51, 0.3)'
  },
  headerTitle: {
    position: 'absolute', 
    left: '50%', 
    top: '50%', 
    transform: 'translate(-50%, -50%)',
    display: 'flex',
    alignItems: 'center'
  },
  headerTitleIcon: {
    fontSize: 'var(--font-size-lg)', 
    color: '#333333', 
    marginRight: 'var(--space-xs)',
    textShadow: '0 0 8px rgba(51, 51, 51, 0.4)'
  },
  headerTitleText: {
    fontSize: 'var(--font-size-lg)', 
    fontWeight: 'var(--font-weight-medium)', 
    color: 'var(--text-primary)',
    letterSpacing: 'var(--letter-spacing-wide)'
  },
  headerActions: {
    display: 'flex',
    alignItems: 'center',
    marginRight: 'var(--space-md)'
  },
  actionButton: {
    fontSize: 'var(--font-size-md)',
    width: 40,
    height: 40,
    borderRadius: 'var(--radius-full)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'var(--text-secondary)',
    background: 'transparent',
    border: 'none',
    margin: '0 var(--space-xs)',
    transition: 'all var(--transition-fast) var(--ease-in-out)',
    cursor: 'pointer',
    '&:hover': {
      color: '#333333',
      textShadow: '0 0 8px rgba(51, 51, 51, 0.4)'
    }
  },
  userInfo: {
    display: 'flex',
    alignItems: 'center',
    padding: '0 var(--space-sm)',
    height: 40,
    borderRadius: 'var(--radius-full)',
    cursor: 'pointer',
    transition: 'all var(--transition-fast) var(--ease-in-out)',
    '&:hover': {
      background: 'rgba(51, 51, 51, 0.1)'
    }
  },
  avatar: {
    background: 'linear-gradient(135deg, #333333, #666666)',
    boxShadow: 'var(--shadow-glow)'
  },
  username: {
    fontSize: 'var(--font-size-sm)',
    fontWeight: 'var(--font-weight-medium)',
    color: 'var(--text-primary)',
    marginLeft: 'var(--space-xs)'
  },
  content: {
    margin: 'var(--space-md)',
    padding: 'var(--space-xl)',
    minHeight: 280,
    background: 'var(--bg-light)',
    overflow: 'auto',
    borderRadius: 'var(--radius-lg)',
    boxShadow: 'var(--shadow-lg)',
    border: '1px solid var(--border-color)',
    transition: 'all var(--transition-normal) var(--ease-in-out)',
    position: 'relative',
    backgroundImage: 'var(--texture-noise)',
    opacity: 0.98
  },
  contentDecoration: {
    position: 'absolute',
    top: 20,
    right: 20,
    width: 100,
    height: 100,
    background: 'rgba(51, 51, 51, 0.05)',
    borderRadius: 'var(--radius-full)',
    opacity: 0.5,
    filter: 'blur(40px)',
    zIndex: 0
  },
  badge: {
    backgroundColor: '#333333',
    boxShadow: 'var(--shadow-glow)'
  },
  notificationItem: {
    borderBottom: '1px solid var(--border-color)',
    padding: 'var(--space-sm)',
    transition: 'all var(--transition-fast) var(--ease-in-out)',
    '&:hover': {
      background: 'rgba(51, 51, 51, 0.05)'
    }
  },
  notificationTitle: {
    color: 'var(--text-primary)',
    fontWeight: 'var(--font-weight-medium)',
    fontSize: 'var(--font-size-sm)'
  },
  notificationTime: {
    color: 'var(--text-tertiary)',
    fontSize: 'var(--font-size-xs)'
  },
  notificationContent: {
    color: 'var(--text-secondary)',
    fontSize: 'var(--font-size-sm)',
    marginTop: 'var(--space-xs)'
  },
  decorationDot1: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    width: 8,
    height: 8,
    borderRadius: 'var(--radius-full)',
    background: '#333333',
    opacity: 0.3
  },
  decorationDot2: {
    position: 'absolute',
    bottom: 50,
    left: 40,
    width: 4,
    height: 4,
    borderRadius: 'var(--radius-full)',
    background: '#666666',
    opacity: 0.2
  },
  decorationDot3: {
    position: 'absolute',
    bottom: 40,
    left: 55,
    width: 6,
    height: 6,
    borderRadius: 'var(--radius-full)',
    background: '#999999',
    opacity: 0.2
  }
};

const MainLayout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [user, setUser] = useState(null);
  const location = useLocation();
  const navigate = useNavigate();
  
  useEffect(() => {
    console.log("MainLayout - 组件挂载，获取用户信息");
    // 获取用户信息，但不进行认证检查
    // 认证检查已经由App.js中的ProtectedRoute组件处理
    const currentUser = authService.getUser();
    console.log("MainLayout - 当前用户:", currentUser);
    setUser(currentUser);
    
    // 监听认证错误事件
    const handleAuthError = () => {
      console.log("MainLayout - 收到认证错误事件");
      navigate('/login');
    };
    
    window.addEventListener('authError', handleAuthError);
    
    return () => {
      window.removeEventListener('authError', handleAuthError);
    };
  }, [navigate]);
  
  const handleLogout = () => {
    console.log("MainLayout - 用户登出");
    authService.logout();
    navigate('/login');
  };
  
  // 获取当前页面图标
  const getCurrentPageIcon = () => {
    const path = location.pathname;
    
    if (path === '/') return <DashboardOutlined />;
    if (path.startsWith('/data')) return <DatabaseOutlined />;
    if (path.startsWith('/charts')) return <BarChartOutlined />;
    if (path.startsWith('/production')) return <LineChartOutlined />;
    if (path.startsWith('/quality')) return <ExperimentOutlined />;
    if (path.startsWith('/formula')) return <MedicineBoxOutlined />;
    if (path.startsWith('/users')) return <TeamOutlined />;
    if (path.startsWith('/settings')) return <SettingOutlined />;
    
    return <DashboardOutlined />;
  };
  
  // 获取当前页面标题
  const getCurrentPageTitle = () => {
    const path = location.pathname;
    
    if (path === '/') return '系统概览';
    if (path.startsWith('/data')) return '数据管理';
    if (path.startsWith('/charts')) return '可视化管理';
    if (path.startsWith('/production')) return '生产监控';
    if (path.startsWith('/quality')) return '质量控制';
    if (path.startsWith('/formula')) return '配方管理';
    if (path.startsWith('/users')) return '用户管理';
    if (path.startsWith('/settings')) return '系统设置';
    
    return '系统概览';
  };
  
  // 显示通知
  const showNotifications = () => {
    message.info('通知功能正在开发中');
  };
  
  // 菜单项动画变体
  const menuItemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: (i) => ({
      opacity: 1,
      x: 0,
      transition: {
        delay: 0.05 * i,
        duration: 0.3,
        ease: [0.25, 0.1, 0.25, 1]
      }
    })
  };
  
  // 用户菜单
  const userMenu = (
    <Menu style={{ 
      padding: 'var(--space-xs)',
      borderRadius: 'var(--radius-md)',
      boxShadow: 'var(--shadow-dropdown)'
    }}>
      <Menu.Item key="profile" icon={<UserOutlined style={{ color: '#333333' }} />}>
        <Link to="/profile">
          <span>个人资料</span>
        </Link>
      </Menu.Item>
      <Menu.Item key="settings" icon={<SettingOutlined style={{ color: '#333333' }} />}>
        <Link to="/user-settings">
          <span>账户设置</span>
        </Link>
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item 
        key="logout" 
        icon={<LogoutOutlined style={{ color: 'var(--error)' }} />} 
        onClick={handleLogout}
        style={{ color: 'var(--error)' }}
      >
        <span>退出登录</span>
      </Menu.Item>
    </Menu>
  );
  
  // 通知菜单
  const notificationMenu = (
    <Menu style={{ 
      width: 300,
      padding: 'var(--space-xs)',
      borderRadius: 'var(--radius-md)',
      boxShadow: 'var(--shadow-dropdown)'
    }}>
      <div style={{ padding: 'var(--space-xs) var(--space-sm)', borderBottom: '1px solid var(--border-color)' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text strong>通知</Text>
          <Button type="link" size="small">全部标记为已读</Button>
        </div>
      </div>
      <Menu.Item key="notification1">
        <div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Badge status="processing" />
            <Text strong style={{ marginLeft: 'var(--space-xs)' }}>系统更新</Text>
          </div>
          <Text type="secondary" style={{ fontSize: 'var(--font-size-sm)' }}>
            系统将于今晚22:00进行维护更新
          </Text>
          <div style={{ fontSize: 'var(--font-size-xs)', color: 'var(--text-tertiary)', marginTop: 'var(--space-xs)' }}>
            10分钟前
          </div>
        </div>
      </Menu.Item>
      <Menu.Item key="notification2">
        <div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Badge status="warning" />
            <Text strong style={{ marginLeft: 'var(--space-xs)' }}>设备预警</Text>
          </div>
          <Text type="secondary" style={{ fontSize: 'var(--font-size-sm)' }}>
            3号生产线温度异常，请检查
          </Text>
          <div style={{ fontSize: 'var(--font-size-xs)', color: 'var(--text-tertiary)', marginTop: 'var(--space-xs)' }}>
            30分钟前
          </div>
        </div>
      </Menu.Item>
      <Menu.Item key="notification3">
        <div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Badge status="success" />
            <Text strong style={{ marginLeft: 'var(--space-xs)' }}>任务完成</Text>
          </div>
          <Text type="secondary" style={{ fontSize: 'var(--font-size-sm)' }}>
            批次B20230602已完成生产
          </Text>
          <div style={{ fontSize: 'var(--font-size-xs)', color: 'var(--text-tertiary)', marginTop: 'var(--space-xs)' }}>
            2小时前
          </div>
        </div>
      </Menu.Item>
      <div style={{ padding: 'var(--space-xs) var(--space-sm)', borderTop: '1px solid var(--border-color)', textAlign: 'center' }}>
        <Button type="link">查看全部</Button>
      </div>
    </Menu>
  );

  // 检查用户是否有特定权限
  const hasPermission = (permission) => {
    // 使用utils/auth中的checkPermission函数，确保返回布尔值
    const result = checkPermission(permission);
    return result;
  };
  
  // 菜单项配置
  const menuItems = [
    {
      key: "/",
      icon: <DashboardOutlined />,
      label: "首页",
      permission: true
    },
    {
      key: "/data",
      icon: <DatabaseOutlined />,
      label: "数据管理",
      permission: hasPermission(Permissions.VIEW_DATA)
    },
    {
      key: "/charts",
      icon: <BarChartOutlined />,
      label: "可视化管理",
      permission: hasPermission(Permissions.VIEW_CHARTS)
    },
    {
      key: "/production",
      icon: <LineChartOutlined />,
      label: "生产监控",
      permission: hasPermission(Permissions.VIEW_CHARTS)
    },
    {
      key: "/quality",
      icon: <ExperimentOutlined />,
      label: "质量控制",
      permission: hasPermission(Permissions.VIEW_CHARTS)
    },
    {
      key: "/formula",
      icon: <MedicineBoxOutlined />,
      label: "配方管理",
      permission: hasPermission(Permissions.VIEW_CHARTS)
    },
    {
      key: "/users",
      icon: <TeamOutlined />,
      label: "用户管理",
      permission: hasPermission(Permissions.VIEW_USERS)
    },
    {
      key: "/system-status",
      icon: <MonitorOutlined />,
      label: "系统状态",
      permission: hasPermission(Permissions.VIEW_USERS) // 管理员权限
    },
    {
      key: "/settings",
      icon: <SettingOutlined />,
      label: "系统设置",
      permission: hasPermission(Permissions.VIEW_SYSTEM_SETTINGS)
    }
  ];
  
  // 过滤有权限的菜单项
  const filteredMenuItems = menuItems.filter(item => item.permission);
  
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={LAYOUT_STYLES.sider}
        width={240}
      >
        <div style={LAYOUT_STYLES.logo}>
          <div style={LAYOUT_STYLES.logoGlow}></div>
          <EyeOutlined style={{ fontSize: 24, color: 'white' }} />
          {!collapsed && (
            <motion.span 
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -10 }}
              transition={{ duration: 0.3 }}
              style={LAYOUT_STYLES.logoText}
            >
              生产数据可视化
            </motion.span>
          )}
        </div>
        
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[location.pathname]}
          style={LAYOUT_STYLES.menu}
        >
          {menuItems.map((item) => (
            <Menu.Item 
              key={item.key} 
              icon={React.cloneElement(item.icon, { style: LAYOUT_STYLES.menuIcon })} 
              style={{
                ...LAYOUT_STYLES.menuItem,
                ...(location.pathname === item.key ? LAYOUT_STYLES.menuItemActive : {})
              }}
              disabled={item.permission && !hasPermission(item.permission)}
            >
              <Link to={item.key}>{item.label}</Link>
            </Menu.Item>
          ))}
        </Menu>
        
        <div style={LAYOUT_STYLES.footer}>
          <Text style={LAYOUT_STYLES.footerText}>
            © {new Date().getFullYear()} 眼药制剂生产数据可视化系统
          </Text>
          <div style={LAYOUT_STYLES.decorationDot1}></div>
          <div style={LAYOUT_STYLES.decorationDot2}></div>
          <div style={LAYOUT_STYLES.decorationDot3}></div>
        </div>
      </Sider>
      
      <Layout>
        <Header style={LAYOUT_STYLES.header}>
          <Button 
            type="text" 
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={LAYOUT_STYLES.collapseButton}
          />
          
          <div style={LAYOUT_STYLES.headerTitle}>
            <span style={LAYOUT_STYLES.headerTitleIcon}>
              {getCurrentPageIcon()}
            </span>
            <span style={LAYOUT_STYLES.headerTitleText}>
              {getCurrentPageTitle()}
            </span>
          </div>
          
          <div style={LAYOUT_STYLES.headerActions}>
            <motion.div 
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Badge count={3} style={LAYOUT_STYLES.badge}>
                <Button 
                  type="text" 
                  icon={<BellOutlined />} 
                  onClick={showNotifications}
                  style={LAYOUT_STYLES.actionButton}
                />
              </Badge>
            </motion.div>
            
            <motion.div 
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button 
                type="text" 
                icon={<QuestionCircleOutlined />} 
                style={LAYOUT_STYLES.actionButton}
              />
            </motion.div>
            
            <Dropdown 
              overlay={userMenu} 
              placement="bottomRight"
              trigger={['click']}
            >
              <motion.div 
                style={LAYOUT_STYLES.userInfo}
                whileHover={{ backgroundColor: 'rgba(51, 51, 51, 0.1)' }}
              >
                <Avatar 
                  size="small" 
                  icon={<UserOutlined />} 
                  style={LAYOUT_STYLES.avatar}
                />
                {user && (
                  <span style={LAYOUT_STYLES.username}>
                    {user.fullName || user.username}
                  </span>
                )}
              </motion.div>
            </Dropdown>
          </div>
        </Header>
        
        <Content style={LAYOUT_STYLES.content}>
          <div style={LAYOUT_STYLES.contentDecoration}></div>
          <div style={{ position: 'relative', zIndex: 1 }}>
            {children}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout; 