import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './index.css';

// 简化的入口文件
try {
  // 确保DOM挂载点存在
  const rootElement = document.getElementById('root');
  
  if (!rootElement) {
    throw new Error('找不到ID为"root"的DOM元素，React应用无法挂载');
  }
  
  // 创建React根节点并渲染应用
  const root = ReactDOM.createRoot(rootElement);
  root.render(
    <React.StrictMode>
      <App />
    </React.StrictMode>
  );
  
  console.log('Application rendered successfully');
} catch (error) {
  console.error('Error rendering application:', error);
  
  // 显示错误信息在页面上
  document.body.innerHTML = `
    <div style="padding: 20px; color: red; font-family: sans-serif;">
      <h1>应用程序渲染错误</h1>
      <p><strong>错误信息:</strong> ${error.message}</p>
      <p><strong>错误类型:</strong> ${error.name}</p>
      <pre style="background: #f5f5f5; padding: 10px; overflow: auto; max-height: 300px; border: 1px solid #ddd;">${error.stack}</pre>
      <div style="margin-top: 20px;">
        <button onclick="location.reload()">重新加载页面</button>
        <button onclick="localStorage.clear(); location.reload();">清除缓存并重新加载</button>
      </div>
    </div>
  `;
}