import React, { useState, useEffect } from 'react';
import {
  Button,
  Space,
  Modal,
  message,
  Typography,
  Row,
  Col,
  Input,
  Select,
  Tag,
  Avatar,
  Table,
  Tooltip,
  Dropdown,
  Form
} from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  UserOutlined,
  EditOutlined,
  DeleteOutlined,
  TeamOutlined,
  CrownOutlined,
  SafetyOutlined,
  MoreOutlined,
  LockOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import CleanAssanLayout from '../components/layout/CleanAssanLayout';
import CleanCard from '../components/common/CleanCard';
import UserForm from '../components/user/UserForm';
import userService from '../services/userService';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

const CleanUserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [changingPasswordUser, setChangingPasswordUser] = useState(null);

  // 模拟用户数据
  const mockUsers = [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      lastLogin: '2024-01-15 10:30:00',
      avatar: null,
      department: '系统管理部'
    },
    {
      id: 2,
      username: 'manager',
      email: '<EMAIL>',
      role: 'manager',
      status: 'active',
      lastLogin: '2024-01-15 09:15:00',
      avatar: null,
      department: '生产管理部'
    },
    {
      id: 3,
      username: 'operator',
      email: '<EMAIL>',
      role: 'operator',
      status: 'active',
      lastLogin: '2024-01-15 08:45:00',
      avatar: null,
      department: '生产车间'
    },
    {
      id: 4,
      username: 'viewer',
      email: '<EMAIL>',
      role: 'viewer',
      status: 'inactive',
      lastLogin: '2024-01-14 16:20:00',
      avatar: null,
      department: '质量检测部'
    }
  ];

  const roleConfig = {
    // 支持小写角色名
    admin: { name: '系统管理员', color: '#6366f1', icon: <CrownOutlined /> },
    manager: { name: '经理', color: '#10b981', icon: <TeamOutlined /> },
    operator: { name: '操作员', color: '#f59e0b', icon: <UserOutlined /> },
    user: { name: '普通用户', color: '#3b82f6', icon: <UserOutlined /> },
    viewer: { name: '查看者', color: '#64748b', icon: <SafetyOutlined /> },
    // 支持大写角色名（SQL Server返回的格式）
    Admin: { name: '系统管理员', color: '#6366f1', icon: <CrownOutlined /> },
    Manager: { name: '经理', color: '#10b981', icon: <TeamOutlined /> },
    Operator: { name: '操作员', color: '#f59e0b', icon: <UserOutlined /> },
    User: { name: '普通用户', color: '#3b82f6', icon: <UserOutlined /> },
    Viewer: { name: '查看者', color: '#64748b', icon: <SafetyOutlined /> }
  };

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      console.log('CleanUserManagement - 开始加载用户数据');
      const response = await userService.getAllUsers(1, 100, false); // 获取所有用户，不使用缓存
      console.log('CleanUserManagement - 用户数据加载成功:', response);

      // 转换数据格式以匹配组件期望的格式
      const formattedUsers = response.items.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.roles && user.roles.length > 0 ? user.roles[0] : 'User', // 保持原始大小写格式
        status: user.isActive ? 'active' : 'inactive',
        lastLogin: user.lastLoginTime || null,
        avatar: null,
        department: user.department || '未设置',
        fullName: user.fullName || user.username,
        source: user.source || 'api' // 标记数据来源
      }));

      console.log('CleanUserManagement - 格式化后的用户数据:', formattedUsers);
      setUsers(formattedUsers);
    } catch (error) {
      console.error('CleanUserManagement - 加载数据失败:', error);
      message.error('加载数据失败: ' + (error.message || '未知错误'));

      // 如果加载失败，使用备用的模拟数据
      setUsers(mockUsers);
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = () => {
    setEditingUser(null);
    setModalVisible(true);
  };

  const handleEditUser = (user) => {
    setEditingUser(user);
    setModalVisible(true);
  };

  const handleChangePassword = (user) => {
    setChangingPasswordUser(user);
    setPasswordModalVisible(true);
  };

  const handlePasswordChange = async (values) => {
    try {
      await userService.updateUser(changingPasswordUser.id, {
        email: changingPasswordUser.email,
        fullName: changingPasswordUser.fullName,
        isActive: changingPasswordUser.status === 'active',
        newPassword: values.newPassword
      });
      message.success('密码修改成功');
      setPasswordModalVisible(false);
      setChangingPasswordUser(null);
    } catch (error) {
      console.error('修改密码失败:', error);
      message.error('修改密码失败: ' + (error.message || '未知错误'));
    }
  };

  const handleDeleteUser = (userId) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个用户吗？此操作不可撤销。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 调用删除API
          await userService.deleteUser(userId);
          message.success('用户删除成功');
          // 重新加载数据
          loadData();
        } catch (error) {
          console.error('删除用户失败:', error);
          message.error('删除用户失败: ' + (error.message || '未知错误'));
        }
      }
    });
  };

  const handleModalOk = async (values) => {
    try {
      if (editingUser) {
        // 更新用户
        await userService.updateUser(editingUser.id, {
          email: values.email,
          fullName: values.fullName,
          department: values.department,
          isActive: values.status === 'active',
          newPassword: values.password // 如果提供了新密码
        });
        message.success('用户更新成功');
      } else {
        // 创建新用户
        await userService.createUser({
          username: values.username,
          email: values.email,
          password: values.password,
          fullName: values.fullName,
          department: values.department,
          isActive: true
        });
        message.success('用户添加成功');
      }
      setModalVisible(false);
      // 重新加载数据
      loadData();
    } catch (error) {
      console.error('保存用户失败:', error);
      message.error('保存用户失败: ' + (error.message || '未知错误'));
    }
  };

  const getStatusColor = (status) => {
    return status === 'active' ? '#10b981' : '#64748b';
  };

  const getStatusText = (status) => {
    return status === 'active' ? '活跃' : '非活跃';
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.username.toLowerCase().includes(searchText.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchText.toLowerCase());
    const matchesRole = selectedRole === 'all' || user.role === selectedRole;
    return matchesSearch && matchesRole;
  });

  const columns = [
    {
      title: '用户',
      dataIndex: 'username',
      key: 'username',
      render: (text, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Avatar 
            size={40} 
            icon={<UserOutlined />}
            style={{
              background: roleConfig[record.role]?.color || '#6366f1'
            }}
          />
          <div>
            <div style={{ fontWeight: '600', color: '#111827' }}>{text}</div>
            <div style={{ fontSize: '14px', color: '#6b7280' }}>{record.email}</div>
          </div>
        </div>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role) => {
        const config = roleConfig[role];
        return (
          <Tag color={config?.color} icon={config?.icon}>
            {config?.name}
          </Tag>
        );
      },
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
      render: (text) => (
        <Text style={{ color: '#374151' }}>{text}</Text>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '最后登录',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      render: (text) => (
        <Text style={{ color: '#6b7280', fontSize: '14px' }}>{text}</Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        const items = [
          {
            key: 'edit',
            label: '编辑',
            icon: <EditOutlined />,
            onClick: () => handleEditUser(record)
          },
          {
            key: 'password',
            label: '修改密码',
            icon: <LockOutlined />,
            onClick: () => handleChangePassword(record)
          },
          {
            key: 'delete',
            label: '删除',
            icon: <DeleteOutlined />,
            danger: true,
            onClick: () => handleDeleteUser(record.id)
          }
        ];

        return (
          <Dropdown menu={{ items }} trigger={['click']}>
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        );
      },
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 }
    }
  };

  return (
    <CleanAssanLayout>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* 页面标题 */}
        <motion.div variants={itemVariants} style={{ marginBottom: '24px' }}>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'flex-start',
            marginBottom: '8px'
          }}>
            <div>
              <Title level={1} style={{ 
                margin: 0, 
                fontSize: '32px',
                fontWeight: '700',
                color: '#111827',
                lineHeight: '1.2'
              }}>
                用户管理
              </Title>
              <Text style={{ 
                fontSize: '16px',
                color: '#6b7280',
                display: 'block',
                marginTop: '8px'
              }}>
                管理系统用户和权限设置
              </Text>
            </div>
            <Space>
              <Button 
                icon={<ReloadOutlined />}
                onClick={loadData}
                loading={loading}
                style={{
                  borderRadius: '8px',
                  height: '44px',
                  fontWeight: '500'
                }}
              >
                刷新
              </Button>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={handleAddUser}
                style={{
                  background: '#6366f1',
                  borderColor: '#6366f1',
                  borderRadius: '8px',
                  height: '44px',
                  padding: '0 24px',
                  fontWeight: '600',
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
                }}
              >
                添加用户
              </Button>
            </Space>
          </div>
        </motion.div>

        {/* 搜索和筛选 */}
        <motion.div variants={itemVariants} style={{ marginBottom: '24px' }}>
          <CleanCard>
            <Row gutter={[16, 16]} align="middle">
              <Col xs={24} sm={12} md={8}>
                <Search
                  placeholder="搜索用户名或邮箱"
                  allowClear
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ width: '100%' }}
                  size="large"
                />
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Select
                  placeholder="选择角色"
                  value={selectedRole}
                  onChange={setSelectedRole}
                  style={{ width: '100%' }}
                  size="large"
                >
                  <Option value="all">所有角色</Option>
                  {Object.entries(roleConfig).map(([key, config]) => (
                    <Option key={key} value={key}>
                      <Space>
                        {config.icon}
                        {config.name}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col xs={24} sm={24} md={10}>
                <Space wrap>
                  <Text style={{ color: '#6b7280', fontWeight: '500' }}>
                    共 {filteredUsers.length} 个用户
                  </Text>
                  {Object.entries(roleConfig).map(([key, config]) => {
                    const count = filteredUsers.filter(user => user.role === key).length;
                    return count > 0 ? (
                      <Tag key={key} color={config.color}>
                        {config.name}: {count}
                      </Tag>
                    ) : null;
                  })}
                </Space>
              </Col>
            </Row>
          </CleanCard>
        </motion.div>

        {/* 用户表格 */}
        <motion.div variants={itemVariants}>
          <CleanCard title="用户列表">
            <Table
              columns={columns}
              dataSource={filteredUsers}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }}
              style={{
                '& .ant-table-thead > tr > th': {
                  background: '#f8fafc',
                  fontWeight: '600',
                  color: '#374151'
                }
              }}
            />
          </CleanCard>
        </motion.div>
      </motion.div>

      {/* 用户表单模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '添加用户'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
        style={{ top: 50 }}
      >
        <UserForm
          user={editingUser}
          roles={Object.entries(roleConfig).map(([key, config]) => ({
            id: key,
            name: config.name
          }))}
          onSubmit={handleModalOk}
          onCancel={() => setModalVisible(false)}
        />
      </Modal>

      {/* 密码修改模态框 */}
      <Modal
        title="修改密码"
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false);
          setChangingPasswordUser(null);
        }}
        footer={null}
        width={500}
        style={{ top: 100 }}
      >
        <Form
          layout="vertical"
          onFinish={handlePasswordChange}
          style={{ marginTop: '24px' }}
        >
          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入新密码"
              style={{
                height: '44px',
                borderRadius: '8px',
                border: '1px solid #d1d5db',
                fontSize: '16px'
              }}
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="确认密码"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请再次输入新密码"
              style={{
                height: '44px',
                borderRadius: '8px',
                border: '1px solid #d1d5db',
                fontSize: '16px'
              }}
            />
          </Form.Item>

          <Form.Item style={{ marginTop: '32px', marginBottom: 0 }}>
            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px' }}>
              <Button
                onClick={() => {
                  setPasswordModalVisible(false);
                  setChangingPasswordUser(null);
                }}
                style={{
                  height: '44px',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: '500'
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                style={{
                  height: '44px',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: '500',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: 'none'
                }}
              >
                确认修改
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </CleanAssanLayout>
  );
};

export default CleanUserManagement;
