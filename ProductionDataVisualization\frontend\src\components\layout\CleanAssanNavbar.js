import React, { useState } from 'react';
import { Layout, Menu, Button, Space, Avatar, Dropdown, Drawer } from 'antd';
import {
  DashboardOutlined,
  TeamOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  MenuOutlined,
  CloudUploadOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import authService from '../../services/authService';
import { checkPermission } from '../../utils/auth';
import { Permissions } from '../../utils/permissions';

const { Header } = Layout;

const CleanAssanNavbar = ({ collapsed, onToggle }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const user = authService.getUser();
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);

  // 检查用户权限的辅助函数
  const hasPermission = (permission) => {
    const result = checkPermission(permission);
    console.log(`CleanAssanNavbar - 权限检查 ${permission}:`, result);
    return result;
  };

  // 菜单项配置（包含权限检查）
  const allMenuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
      permission: true // 仪表板对所有用户开放
    },
    {
      key: '/data-import',
      icon: <CloudUploadOutlined />,
      label: '数据导入',
      permission: true // 数据导入对所有用户开放
    },
    {
      key: '/data-visualization',
      icon: <BarChartOutlined />,
      label: '数据可视化',
      permission: true // 数据可视化对所有用户开放
    },
    {
      key: '/users',
      icon: <TeamOutlined />,
      label: '用户管理',
      permission: hasPermission(Permissions.VIEW_USERS)
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      permission: hasPermission(Permissions.VIEW_SYSTEM_SETTINGS)
    }
  ];

  // 过滤有权限的菜单项
  const menuItems = allMenuItems.filter(item => item.permission);

  console.log('CleanAssanNavbar - 当前用户:', user);
  console.log('CleanAssanNavbar - 所有菜单项:', allMenuItems);
  console.log('CleanAssanNavbar - 过滤后的菜单项:', menuItems);

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => {
        authService.logout();
        navigate('/login');
      }
    }
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
    setMobileMenuVisible(false); // 移动端菜单点击后关闭
  };

  const handleMobileMenuToggle = () => {
    setMobileMenuVisible(!mobileMenuVisible);
  };

  return (
    <Header className="clean-assan-navbar">
      <div className="navbar-content">
        {/* Logo区域 */}
        <div className="logo-section">
          <div className="logo">
            <span className="logo-text">Assan</span>
            <span className="logo-badge">Pro</span>
          </div>
        </div>

        {/* 导航菜单 */}
        <div className="nav-menu">
          <Menu
            mode="horizontal"
            selectedKeys={[location.pathname]}
            items={menuItems}
            onClick={handleMenuClick}
            style={{
              background: 'transparent',
              border: 'none',
              fontSize: '15px',
              fontWeight: '500'
            }}
          />
        </div>

        {/* 用户区域 */}
        <div className="user-section">
          <Space size="middle">
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <div className="user-info">
                <Avatar 
                  size={36} 
                  icon={<UserOutlined />}
                  style={{ 
                    background: '#6366f1',
                    cursor: 'pointer'
                  }}
                />
                <span className="username">{user?.username || '管理员'}</span>
              </div>
            </Dropdown>
          </Space>
        </div>

        {/* 移动端菜单按钮 */}
        <div className="mobile-menu">
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={handleMobileMenuToggle}
            style={{ color: '#374151' }}
          />
        </div>
      </div>

      <style jsx="true">{`
        .clean-assan-navbar {
          background: #ffffff;
          border-bottom: 1px solid #e5e7eb;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          z-index: 1000;
          height: 80px;
          padding: 0;
        }

        .navbar-content {
          max-width: 1200px;
          margin: 0 auto;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 24px;
        }

        .logo-section {
          display: flex;
          align-items: center;
        }

        .logo {
          display: flex;
          align-items: center;
          gap: 8px;
          cursor: pointer;
        }

        .logo-text {
          font-size: 28px;
          font-weight: 700;
          color: #111827;
          letter-spacing: -0.5px;
        }

        .logo-badge {
          background: #6366f1;
          color: white;
          font-size: 12px;
          font-weight: 600;
          padding: 2px 8px;
          border-radius: 12px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .nav-menu {
          flex: 1;
          display: flex;
          justify-content: center;
          margin: 0 40px;
        }

        .nav-menu .ant-menu {
          min-width: 400px;
          justify-content: center;
        }

        .nav-menu .ant-menu-item {
          color: #6b7280;
          border-radius: 8px;
          margin: 0 4px;
          padding: 0 16px;
          height: 40px;
          line-height: 40px;
          transition: all 0.2s ease;
        }

        .nav-menu .ant-menu-item:hover {
          color: #6366f1;
          background: #f8fafc;
        }

        .nav-menu .ant-menu-item-selected {
          color: #6366f1;
          background: #f1f5f9;
          font-weight: 600;
        }

        .nav-menu .ant-menu-item-selected::after {
          display: none;
        }

        .user-section {
          display: flex;
          align-items: center;
        }

        .user-info {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 8px 12px;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .user-info:hover {
          background: #f8fafc;
        }

        .username {
          font-size: 15px;
          font-weight: 500;
          color: #374151;
        }

        .mobile-menu {
          display: none;
        }

        @media (max-width: 768px) {
          .navbar-content {
            padding: 0 16px;
          }

          .nav-menu {
            display: none;
          }

          .mobile-menu {
            display: block;
          }

          .username {
            display: none;
          }
        }
      `}</style>

      {/* 移动端抽屉菜单 */}
      <Drawer
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div className="logo">
              <span className="logo-text">Assan</span>
              <span className="logo-badge">Pro</span>
            </div>
          </div>
        }
        placement="left"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        width={280}
        styles={{
          body: { padding: 0 },
          header: {
            borderBottom: '1px solid #f0f0f0',
            background: '#fafbfc'
          }
        }}
      >
        <Menu
          mode="vertical"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{
            border: 'none',
            fontSize: '16px',
            fontWeight: '500'
          }}
        />

        {/* 移动端用户信息 */}
        <div style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          padding: '20px',
          borderTop: '1px solid #f0f0f0',
          background: '#fafbfc'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            marginBottom: '16px'
          }}>
            <Avatar
              size={40}
              icon={<UserOutlined />}
              style={{ background: '#6366f1' }}
            />
            <div>
              <div style={{ fontWeight: '500', color: '#374151' }}>
                {user?.username || '管理员'}
              </div>
              <div style={{ fontSize: '14px', color: '#6b7280' }}>
                {user?.email || '<EMAIL>'}
              </div>
            </div>
          </div>
          <Button
            type="primary"
            danger
            icon={<LogoutOutlined />}
            onClick={() => {
              authService.logout();
              navigate('/login');
              setMobileMenuVisible(false);
            }}
            style={{
              width: '100%',
              height: '44px',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: '500'
            }}
          >
            退出登录
          </Button>
        </div>
      </Drawer>
    </Header>
  );
};

export default CleanAssanNavbar;
